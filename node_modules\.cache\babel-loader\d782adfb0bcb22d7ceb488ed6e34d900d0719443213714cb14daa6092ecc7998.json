{"ast": null, "code": "import React,{useState}from'react';import styled from'styled-components';import{InstrumentSelector}from'../../components/InstrumentSelector/InstrumentSelector';import{getInstrumentTemplate,suggestKeyForInstrument}from'../../utils/instrumentTemplates';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PageContainer=styled.div`\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n`;const PageHeader=styled.div`\n  text-align: center;\n  margin-bottom: 3rem;\n`;const PageTitle=styled.h1`\n  color: white;\n  margin: 0 0 1rem 0;\n  font-size: 2.5rem;\n  font-weight: 700;\n`;const PageSubtitle=styled.p`\n  color: rgba(255,255,255,0.8);\n  font-size: 1.2rem;\n  margin: 0;\n`;const SetupForm=styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  margin-bottom: 2rem;\n`;const FormSection=styled.div`\n  margin-bottom: 2rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;const SectionTitle=styled.h3`\n  color: #2c3e50;\n  margin: 0 0 1rem 0;\n  font-size: 1.3rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;const FormRow=styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1rem;\n`;const FormGroup=styled.div`\n  label {\n    display: block;\n    margin-bottom: 0.5rem;\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input, select {\n    width: 100%;\n    padding: 0.75rem;\n    border: 2px solid #e9ecef;\n    border-radius: 8px;\n    font-size: 1rem;\n    transition: border-color 0.2s;\n    box-sizing: border-box;\n    \n    &:focus {\n      outline: none;\n      border-color: #667eea;\n    }\n  }\n`;const ActionButtons=styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-top: 2rem;\n`;const ActionButton=styled.button`\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props=>props.variant==='primary'?`\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n    }\n  `:`\n    background: #6c757d;\n    color: white;\n    \n    &:hover {\n      background: #5a6268;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;const PreviewCard=styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  border: 2px solid #e9ecef;\n  margin-top: 1rem;\n`;const PreviewTitle=styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;const PreviewDetail=styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;const PreviewLabel=styled.span`\n  font-weight: 600;\n  color: #495057;\n`;const PreviewValue=styled.span`\n  color: #666;\n`;export const NewScorePage=_ref=>{let{onCreateScore,onCancel}=_ref;const[config,setConfig]=useState({title:'Nova Partitura',composer:'',instrument:'piano',key:'C',keyMode:'major',timeSignatureNum:4,timeSignatureDen:4,tempo:120});const handleInstrumentChange=instrument=>{const suggestedKey=suggestKeyForInstrument(instrument);setConfig(prev=>({...prev,instrument,key:suggestedKey}));};const handleSubmit=()=>{if(!config.title.trim()){alert('Por favor, digite um título para a partitura');return;}onCreateScore(config);};const instrumentTemplate=getInstrumentTemplate(config.instrument);const notes=['C','C#','D','D#','E','F','F#','G','G#','A','A#','B'];return/*#__PURE__*/_jsxs(PageContainer,{children:[/*#__PURE__*/_jsxs(PageHeader,{children:[/*#__PURE__*/_jsx(PageTitle,{children:\"\\uD83C\\uDFBC Nova Partitura\"}),/*#__PURE__*/_jsx(PageSubtitle,{children:\"Configure sua nova composi\\xE7\\xE3o musical\"})]}),/*#__PURE__*/_jsxs(SetupForm,{children:[/*#__PURE__*/_jsxs(FormSection,{children:[/*#__PURE__*/_jsx(SectionTitle,{children:\"\\uD83D\\uDCDD Informa\\xE7\\xF5es B\\xE1sicas\"}),/*#__PURE__*/_jsxs(FormRow,{children:[/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(\"label\",{children:\"T\\xEDtulo da Partitura:\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:config.title,onChange:e=>setConfig(prev=>({...prev,title:e.target.value})),placeholder:\"Digite o t\\xEDtulo...\",autoFocus:true})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(\"label\",{children:\"Compositor (opcional):\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:config.composer,onChange:e=>setConfig(prev=>({...prev,composer:e.target.value})),placeholder:\"Nome do compositor...\"})]})]})]}),/*#__PURE__*/_jsxs(FormSection,{children:[/*#__PURE__*/_jsx(SectionTitle,{children:\"\\uD83C\\uDFB9 Instrumento\"}),/*#__PURE__*/_jsx(InstrumentSelector,{selectedInstrument:config.instrument,onInstrumentChange:handleInstrumentChange,compact:false})]}),/*#__PURE__*/_jsxs(FormSection,{children:[/*#__PURE__*/_jsx(SectionTitle,{children:\"\\uD83C\\uDFB5 Configura\\xE7\\xF5es Musicais\"}),/*#__PURE__*/_jsxs(FormRow,{children:[/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(\"label\",{children:\"Tonalidade:\"}),/*#__PURE__*/_jsx(\"select\",{value:config.key,onChange:e=>setConfig(prev=>({...prev,key:e.target.value})),children:notes.map(note=>/*#__PURE__*/_jsx(\"option\",{value:note,children:note},note))})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(\"label\",{children:\"Modo:\"}),/*#__PURE__*/_jsxs(\"select\",{value:config.keyMode,onChange:e=>setConfig(prev=>({...prev,keyMode:e.target.value})),children:[/*#__PURE__*/_jsx(\"option\",{value:\"major\",children:\"Maior\"}),/*#__PURE__*/_jsx(\"option\",{value:\"minor\",children:\"Menor\"})]})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(\"label\",{children:\"Compasso:\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'0.5rem',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"select\",{value:config.timeSignatureNum,onChange:e=>setConfig(prev=>({...prev,timeSignatureNum:parseInt(e.target.value)})),style:{flex:1},children:[/*#__PURE__*/_jsx(\"option\",{value:2,children:\"2\"}),/*#__PURE__*/_jsx(\"option\",{value:3,children:\"3\"}),/*#__PURE__*/_jsx(\"option\",{value:4,children:\"4\"}),/*#__PURE__*/_jsx(\"option\",{value:6,children:\"6\"}),/*#__PURE__*/_jsx(\"option\",{value:9,children:\"9\"}),/*#__PURE__*/_jsx(\"option\",{value:12,children:\"12\"})]}),/*#__PURE__*/_jsx(\"span\",{children:\"/\"}),/*#__PURE__*/_jsxs(\"select\",{value:config.timeSignatureDen,onChange:e=>setConfig(prev=>({...prev,timeSignatureDen:parseInt(e.target.value)})),style:{flex:1},children:[/*#__PURE__*/_jsx(\"option\",{value:2,children:\"2\"}),/*#__PURE__*/_jsx(\"option\",{value:4,children:\"4\"}),/*#__PURE__*/_jsx(\"option\",{value:8,children:\"8\"}),/*#__PURE__*/_jsx(\"option\",{value:16,children:\"16\"})]})]})]}),/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(\"label\",{children:\"Tempo (BPM):\"}),/*#__PURE__*/_jsx(\"input\",{type:\"number\",min:\"40\",max:\"200\",value:config.tempo,onChange:e=>setConfig(prev=>({...prev,tempo:parseInt(e.target.value)||120}))})]})]})]}),/*#__PURE__*/_jsxs(PreviewCard,{children:[/*#__PURE__*/_jsx(PreviewTitle,{children:\"\\uD83D\\uDCCB Resumo da Configura\\xE7\\xE3o\"}),/*#__PURE__*/_jsxs(PreviewDetail,{children:[/*#__PURE__*/_jsx(PreviewLabel,{children:\"T\\xEDtulo:\"}),/*#__PURE__*/_jsx(PreviewValue,{children:config.title})]}),config.composer&&/*#__PURE__*/_jsxs(PreviewDetail,{children:[/*#__PURE__*/_jsx(PreviewLabel,{children:\"Compositor:\"}),/*#__PURE__*/_jsx(PreviewValue,{children:config.composer})]}),/*#__PURE__*/_jsxs(PreviewDetail,{children:[/*#__PURE__*/_jsx(PreviewLabel,{children:\"Instrumento:\"}),/*#__PURE__*/_jsxs(PreviewValue,{children:[instrumentTemplate.emoji,\" \",instrumentTemplate.name]})]}),/*#__PURE__*/_jsxs(PreviewDetail,{children:[/*#__PURE__*/_jsx(PreviewLabel,{children:\"Tonalidade:\"}),/*#__PURE__*/_jsxs(PreviewValue,{children:[config.key,\" \",config.keyMode==='major'?'Maior':'Menor']})]}),/*#__PURE__*/_jsxs(PreviewDetail,{children:[/*#__PURE__*/_jsx(PreviewLabel,{children:\"Compasso:\"}),/*#__PURE__*/_jsxs(PreviewValue,{children:[config.timeSignatureNum,\"/\",config.timeSignatureDen]})]}),/*#__PURE__*/_jsxs(PreviewDetail,{children:[/*#__PURE__*/_jsx(PreviewLabel,{children:\"Tempo:\"}),/*#__PURE__*/_jsxs(PreviewValue,{children:[config.tempo,\" BPM\"]})]})]}),/*#__PURE__*/_jsxs(ActionButtons,{children:[/*#__PURE__*/_jsx(ActionButton,{variant:\"secondary\",onClick:onCancel,children:\"\\u274C Cancelar\"}),/*#__PURE__*/_jsx(ActionButton,{variant:\"primary\",onClick:handleSubmit,children:\"\\u2705 Criar Partitura\"})]})]})]});};", "map": {"version": 3, "names": ["React", "useState", "styled", "InstrumentSelector", "getInstrumentTemplate", "suggestKeyForInstrument", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON>", "div", "<PERSON><PERSON><PERSON><PERSON>", "Page<PERSON><PERSON>le", "h1", "PageSubtitle", "p", "SetupForm", "FormSection", "SectionTitle", "h3", "FormRow", "FormGroup", "ActionButtons", "ActionButton", "button", "props", "variant", "PreviewCard", "PreviewTitle", "h4", "PreviewDetail", "PreviewLabel", "span", "PreviewValue", "NewScorePage", "_ref", "onCreateScore", "onCancel", "config", "setConfig", "title", "composer", "instrument", "key", "keyMode", "timeSignatureNum", "timeSignatureDen", "tempo", "handleInstrumentChange", "<PERSON><PERSON><PERSON>", "prev", "handleSubmit", "trim", "alert", "instrumentTemplate", "notes", "children", "type", "value", "onChange", "e", "target", "placeholder", "autoFocus", "selectedInstrument", "onInstrumentChange", "compact", "map", "note", "style", "display", "gap", "alignItems", "parseInt", "flex", "min", "max", "emoji", "name", "onClick"], "sources": ["D:/Dev/partitura_digital/src/pages/NewScore/NewScorePage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { InstrumentType, NoteName } from '../../types/music';\nimport { InstrumentSelector } from '../../components/InstrumentSelector/InstrumentSelector';\nimport { getInstrumentTemplate, suggestKeyForInstrument } from '../../utils/instrumentTemplates';\n\nconst PageContainer = styled.div`\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n\nconst PageHeader = styled.div`\n  text-align: center;\n  margin-bottom: 3rem;\n`;\n\nconst PageTitle = styled.h1`\n  color: white;\n  margin: 0 0 1rem 0;\n  font-size: 2.5rem;\n  font-weight: 700;\n`;\n\nconst PageSubtitle = styled.p`\n  color: rgba(255,255,255,0.8);\n  font-size: 1.2rem;\n  margin: 0;\n`;\n\nconst SetupForm = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  margin-bottom: 2rem;\n`;\n\nconst FormSection = styled.div`\n  margin-bottom: 2rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\nconst SectionTitle = styled.h3`\n  color: #2c3e50;\n  margin: 0 0 1rem 0;\n  font-size: 1.3rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n\nconst FormRow = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1rem;\n`;\n\nconst FormGroup = styled.div`\n  label {\n    display: block;\n    margin-bottom: 0.5rem;\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input, select {\n    width: 100%;\n    padding: 0.75rem;\n    border: 2px solid #e9ecef;\n    border-radius: 8px;\n    font-size: 1rem;\n    transition: border-color 0.2s;\n    box-sizing: border-box;\n    \n    &:focus {\n      outline: none;\n      border-color: #667eea;\n    }\n  }\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-top: 2rem;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n    }\n  ` : `\n    background: #6c757d;\n    color: white;\n    \n    &:hover {\n      background: #5a6268;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst PreviewCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  border: 2px solid #e9ecef;\n  margin-top: 1rem;\n`;\n\nconst PreviewTitle = styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;\n\nconst PreviewDetail = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\nconst PreviewLabel = styled.span`\n  font-weight: 600;\n  color: #495057;\n`;\n\nconst PreviewValue = styled.span`\n  color: #666;\n`;\n\ninterface NewScorePageProps {\n  onCreateScore: (config: ScoreConfig) => void;\n  onCancel: () => void;\n}\n\nexport interface ScoreConfig {\n  title: string;\n  composer: string;\n  instrument: InstrumentType;\n  key: NoteName;\n  keyMode: 'major' | 'minor';\n  timeSignatureNum: number;\n  timeSignatureDen: number;\n  tempo: number;\n}\n\nexport const NewScorePage: React.FC<NewScorePageProps> = ({ onCreateScore, onCancel }) => {\n  const [config, setConfig] = useState<ScoreConfig>({\n    title: 'Nova Partitura',\n    composer: '',\n    instrument: 'piano',\n    key: 'C' as NoteName,\n    keyMode: 'major',\n    timeSignatureNum: 4,\n    timeSignatureDen: 4,\n    tempo: 120\n  });\n\n  const handleInstrumentChange = (instrument: InstrumentType) => {\n    const suggestedKey = suggestKeyForInstrument(instrument);\n    setConfig(prev => ({\n      ...prev,\n      instrument,\n      key: suggestedKey as NoteName\n    }));\n  };\n\n  const handleSubmit = () => {\n    if (!config.title.trim()) {\n      alert('Por favor, digite um título para a partitura');\n      return;\n    }\n    \n    onCreateScore(config);\n  };\n\n  const instrumentTemplate = getInstrumentTemplate(config.instrument);\n  const notes: NoteName[] = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];\n\n  return (\n    <PageContainer>\n      <PageHeader>\n        <PageTitle>🎼 Nova Partitura</PageTitle>\n        <PageSubtitle>Configure sua nova composição musical</PageSubtitle>\n      </PageHeader>\n\n      <SetupForm>\n        <FormSection>\n          <SectionTitle>📝 Informações Básicas</SectionTitle>\n          <FormRow>\n            <FormGroup>\n              <label>Título da Partitura:</label>\n              <input\n                type=\"text\"\n                value={config.title}\n                onChange={(e) => setConfig(prev => ({ ...prev, title: e.target.value }))}\n                placeholder=\"Digite o título...\"\n                autoFocus\n              />\n            </FormGroup>\n            <FormGroup>\n              <label>Compositor (opcional):</label>\n              <input\n                type=\"text\"\n                value={config.composer}\n                onChange={(e) => setConfig(prev => ({ ...prev, composer: e.target.value }))}\n                placeholder=\"Nome do compositor...\"\n              />\n            </FormGroup>\n          </FormRow>\n        </FormSection>\n\n        <FormSection>\n          <SectionTitle>🎹 Instrumento</SectionTitle>\n          <InstrumentSelector\n            selectedInstrument={config.instrument}\n            onInstrumentChange={handleInstrumentChange}\n            compact={false}\n          />\n        </FormSection>\n\n        <FormSection>\n          <SectionTitle>🎵 Configurações Musicais</SectionTitle>\n          <FormRow>\n            <FormGroup>\n              <label>Tonalidade:</label>\n              <select\n                value={config.key}\n                onChange={(e) => setConfig(prev => ({ ...prev, key: e.target.value as NoteName }))}\n              >\n                {notes.map(note => (\n                  <option key={note} value={note}>{note}</option>\n                ))}\n              </select>\n            </FormGroup>\n            <FormGroup>\n              <label>Modo:</label>\n              <select\n                value={config.keyMode}\n                onChange={(e) => setConfig(prev => ({ ...prev, keyMode: e.target.value as 'major' | 'minor' }))}\n              >\n                <option value=\"major\">Maior</option>\n                <option value=\"minor\">Menor</option>\n              </select>\n            </FormGroup>\n            <FormGroup>\n              <label>Compasso:</label>\n              <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>\n                <select\n                  value={config.timeSignatureNum}\n                  onChange={(e) => setConfig(prev => ({ ...prev, timeSignatureNum: parseInt(e.target.value) }))}\n                  style={{ flex: 1 }}\n                >\n                  <option value={2}>2</option>\n                  <option value={3}>3</option>\n                  <option value={4}>4</option>\n                  <option value={6}>6</option>\n                  <option value={9}>9</option>\n                  <option value={12}>12</option>\n                </select>\n                <span>/</span>\n                <select\n                  value={config.timeSignatureDen}\n                  onChange={(e) => setConfig(prev => ({ ...prev, timeSignatureDen: parseInt(e.target.value) }))}\n                  style={{ flex: 1 }}\n                >\n                  <option value={2}>2</option>\n                  <option value={4}>4</option>\n                  <option value={8}>8</option>\n                  <option value={16}>16</option>\n                </select>\n              </div>\n            </FormGroup>\n            <FormGroup>\n              <label>Tempo (BPM):</label>\n              <input\n                type=\"number\"\n                min=\"40\"\n                max=\"200\"\n                value={config.tempo}\n                onChange={(e) => setConfig(prev => ({ ...prev, tempo: parseInt(e.target.value) || 120 }))}\n              />\n            </FormGroup>\n          </FormRow>\n        </FormSection>\n\n        <PreviewCard>\n          <PreviewTitle>📋 Resumo da Configuração</PreviewTitle>\n          <PreviewDetail>\n            <PreviewLabel>Título:</PreviewLabel>\n            <PreviewValue>{config.title}</PreviewValue>\n          </PreviewDetail>\n          {config.composer && (\n            <PreviewDetail>\n              <PreviewLabel>Compositor:</PreviewLabel>\n              <PreviewValue>{config.composer}</PreviewValue>\n            </PreviewDetail>\n          )}\n          <PreviewDetail>\n            <PreviewLabel>Instrumento:</PreviewLabel>\n            <PreviewValue>{instrumentTemplate.emoji} {instrumentTemplate.name}</PreviewValue>\n          </PreviewDetail>\n          <PreviewDetail>\n            <PreviewLabel>Tonalidade:</PreviewLabel>\n            <PreviewValue>{config.key} {config.keyMode === 'major' ? 'Maior' : 'Menor'}</PreviewValue>\n          </PreviewDetail>\n          <PreviewDetail>\n            <PreviewLabel>Compasso:</PreviewLabel>\n            <PreviewValue>{config.timeSignatureNum}/{config.timeSignatureDen}</PreviewValue>\n          </PreviewDetail>\n          <PreviewDetail>\n            <PreviewLabel>Tempo:</PreviewLabel>\n            <PreviewValue>{config.tempo} BPM</PreviewValue>\n          </PreviewDetail>\n        </PreviewCard>\n\n        <ActionButtons>\n          <ActionButton variant=\"secondary\" onClick={onCancel}>\n            ❌ Cancelar\n          </ActionButton>\n          <ActionButton variant=\"primary\" onClick={handleSubmit}>\n            ✅ Criar Partitura\n          </ActionButton>\n        </ActionButtons>\n      </SetupForm>\n    </PageContainer>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CAEtC,OAASC,kBAAkB,KAAQ,wDAAwD,CAC3F,OAASC,qBAAqB,CAAEC,uBAAuB,KAAQ,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjG,KAAM,CAAAC,aAAa,CAAGR,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGV,MAAM,CAACS,GAAG;AAC7B;AACA;AACA,CAAC,CAED,KAAM,CAAAE,SAAS,CAAGX,MAAM,CAACY,EAAE;AAC3B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGb,MAAM,CAACc,CAAC;AAC7B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,SAAS,CAAGf,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAO,WAAW,CAAGhB,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAQ,YAAY,CAAGjB,MAAM,CAACkB,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,OAAO,CAAGnB,MAAM,CAACS,GAAG;AAC1B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAW,SAAS,CAAGpB,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAY,aAAa,CAAGrB,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAa,YAAY,CAAGtB,MAAM,CAACuB,MAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,EAAIA,KAAK,CAACC,OAAO,GAAK,SAAS,CAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG1B,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAkB,YAAY,CAAG3B,MAAM,CAAC4B,EAAE;AAC9B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,aAAa,CAAG7B,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAqB,YAAY,CAAG9B,MAAM,CAAC+B,IAAI;AAChC;AACA;AACA,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGhC,MAAM,CAAC+B,IAAI;AAChC;AACA,CAAC,CAkBD,MAAO,MAAM,CAAAE,YAAyC,CAAGC,IAAA,EAAiC,IAAhC,CAAEC,aAAa,CAAEC,QAAS,CAAC,CAAAF,IAAA,CACnF,KAAM,CAACG,MAAM,CAAEC,SAAS,CAAC,CAAGvC,QAAQ,CAAc,CAChDwC,KAAK,CAAE,gBAAgB,CACvBC,QAAQ,CAAE,EAAE,CACZC,UAAU,CAAE,OAAO,CACnBC,GAAG,CAAE,GAAe,CACpBC,OAAO,CAAE,OAAO,CAChBC,gBAAgB,CAAE,CAAC,CACnBC,gBAAgB,CAAE,CAAC,CACnBC,KAAK,CAAE,GACT,CAAC,CAAC,CAEF,KAAM,CAAAC,sBAAsB,CAAIN,UAA0B,EAAK,CAC7D,KAAM,CAAAO,YAAY,CAAG7C,uBAAuB,CAACsC,UAAU,CAAC,CACxDH,SAAS,CAACW,IAAI,GAAK,CACjB,GAAGA,IAAI,CACPR,UAAU,CACVC,GAAG,CAAEM,YACP,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAE,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CAACb,MAAM,CAACE,KAAK,CAACY,IAAI,CAAC,CAAC,CAAE,CACxBC,KAAK,CAAC,8CAA8C,CAAC,CACrD,OACF,CAEAjB,aAAa,CAACE,MAAM,CAAC,CACvB,CAAC,CAED,KAAM,CAAAgB,kBAAkB,CAAGnD,qBAAqB,CAACmC,MAAM,CAACI,UAAU,CAAC,CACnE,KAAM,CAAAa,KAAiB,CAAG,CAAC,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAC,CAE3F,mBACE/C,KAAA,CAACC,aAAa,EAAA+C,QAAA,eACZhD,KAAA,CAACG,UAAU,EAAA6C,QAAA,eACTlD,IAAA,CAACM,SAAS,EAAA4C,QAAA,CAAC,6BAAiB,CAAW,CAAC,cACxClD,IAAA,CAACQ,YAAY,EAAA0C,QAAA,CAAC,6CAAqC,CAAc,CAAC,EACxD,CAAC,cAEbhD,KAAA,CAACQ,SAAS,EAAAwC,QAAA,eACRhD,KAAA,CAACS,WAAW,EAAAuC,QAAA,eACVlD,IAAA,CAACY,YAAY,EAAAsC,QAAA,CAAC,2CAAsB,CAAc,CAAC,cACnDhD,KAAA,CAACY,OAAO,EAAAoC,QAAA,eACNhD,KAAA,CAACa,SAAS,EAAAmC,QAAA,eACRlD,IAAA,UAAAkD,QAAA,CAAO,yBAAoB,CAAO,CAAC,cACnClD,IAAA,UACEmD,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEpB,MAAM,CAACE,KAAM,CACpBmB,QAAQ,CAAGC,CAAC,EAAKrB,SAAS,CAACW,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEV,KAAK,CAAEoB,CAAC,CAACC,MAAM,CAACH,KAAM,CAAC,CAAC,CAAE,CACzEI,WAAW,CAAC,uBAAoB,CAChCC,SAAS,MACV,CAAC,EACO,CAAC,cACZvD,KAAA,CAACa,SAAS,EAAAmC,QAAA,eACRlD,IAAA,UAAAkD,QAAA,CAAO,wBAAsB,CAAO,CAAC,cACrClD,IAAA,UACEmD,IAAI,CAAC,MAAM,CACXC,KAAK,CAAEpB,MAAM,CAACG,QAAS,CACvBkB,QAAQ,CAAGC,CAAC,EAAKrB,SAAS,CAACW,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAET,QAAQ,CAAEmB,CAAC,CAACC,MAAM,CAACH,KAAM,CAAC,CAAC,CAAE,CAC5EI,WAAW,CAAC,uBAAuB,CACpC,CAAC,EACO,CAAC,EACL,CAAC,EACC,CAAC,cAEdtD,KAAA,CAACS,WAAW,EAAAuC,QAAA,eACVlD,IAAA,CAACY,YAAY,EAAAsC,QAAA,CAAC,0BAAc,CAAc,CAAC,cAC3ClD,IAAA,CAACJ,kBAAkB,EACjB8D,kBAAkB,CAAE1B,MAAM,CAACI,UAAW,CACtCuB,kBAAkB,CAAEjB,sBAAuB,CAC3CkB,OAAO,CAAE,KAAM,CAChB,CAAC,EACS,CAAC,cAEd1D,KAAA,CAACS,WAAW,EAAAuC,QAAA,eACVlD,IAAA,CAACY,YAAY,EAAAsC,QAAA,CAAC,2CAAyB,CAAc,CAAC,cACtDhD,KAAA,CAACY,OAAO,EAAAoC,QAAA,eACNhD,KAAA,CAACa,SAAS,EAAAmC,QAAA,eACRlD,IAAA,UAAAkD,QAAA,CAAO,aAAW,CAAO,CAAC,cAC1BlD,IAAA,WACEoD,KAAK,CAAEpB,MAAM,CAACK,GAAI,CAClBgB,QAAQ,CAAGC,CAAC,EAAKrB,SAAS,CAACW,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEP,GAAG,CAAEiB,CAAC,CAACC,MAAM,CAACH,KAAkB,CAAC,CAAC,CAAE,CAAAF,QAAA,CAElFD,KAAK,CAACY,GAAG,CAACC,IAAI,eACb9D,IAAA,WAAmBoD,KAAK,CAAEU,IAAK,CAAAZ,QAAA,CAAEY,IAAI,EAAxBA,IAAiC,CAC/C,CAAC,CACI,CAAC,EACA,CAAC,cACZ5D,KAAA,CAACa,SAAS,EAAAmC,QAAA,eACRlD,IAAA,UAAAkD,QAAA,CAAO,OAAK,CAAO,CAAC,cACpBhD,KAAA,WACEkD,KAAK,CAAEpB,MAAM,CAACM,OAAQ,CACtBe,QAAQ,CAAGC,CAAC,EAAKrB,SAAS,CAACW,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEN,OAAO,CAAEgB,CAAC,CAACC,MAAM,CAACH,KAA2B,CAAC,CAAC,CAAE,CAAAF,QAAA,eAEhGlD,IAAA,WAAQoD,KAAK,CAAC,OAAO,CAAAF,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpClD,IAAA,WAAQoD,KAAK,CAAC,OAAO,CAAAF,QAAA,CAAC,OAAK,CAAQ,CAAC,EAC9B,CAAC,EACA,CAAC,cACZhD,KAAA,CAACa,SAAS,EAAAmC,QAAA,eACRlD,IAAA,UAAAkD,QAAA,CAAO,WAAS,CAAO,CAAC,cACxBhD,KAAA,QAAK6D,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAhB,QAAA,eACnEhD,KAAA,WACEkD,KAAK,CAAEpB,MAAM,CAACO,gBAAiB,CAC/Bc,QAAQ,CAAGC,CAAC,EAAKrB,SAAS,CAACW,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEL,gBAAgB,CAAE4B,QAAQ,CAACb,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAC,CAAC,CAAE,CAC9FW,KAAK,CAAE,CAAEK,IAAI,CAAE,CAAE,CAAE,CAAAlB,QAAA,eAEnBlD,IAAA,WAAQoD,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlD,IAAA,WAAQoD,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlD,IAAA,WAAQoD,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlD,IAAA,WAAQoD,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlD,IAAA,WAAQoD,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlD,IAAA,WAAQoD,KAAK,CAAE,EAAG,CAAAF,QAAA,CAAC,IAAE,CAAQ,CAAC,EACxB,CAAC,cACTlD,IAAA,SAAAkD,QAAA,CAAM,GAAC,CAAM,CAAC,cACdhD,KAAA,WACEkD,KAAK,CAAEpB,MAAM,CAACQ,gBAAiB,CAC/Ba,QAAQ,CAAGC,CAAC,EAAKrB,SAAS,CAACW,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEJ,gBAAgB,CAAE2B,QAAQ,CAACb,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAC,CAAC,CAAE,CAC9FW,KAAK,CAAE,CAAEK,IAAI,CAAE,CAAE,CAAE,CAAAlB,QAAA,eAEnBlD,IAAA,WAAQoD,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlD,IAAA,WAAQoD,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlD,IAAA,WAAQoD,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BlD,IAAA,WAAQoD,KAAK,CAAE,EAAG,CAAAF,QAAA,CAAC,IAAE,CAAQ,CAAC,EACxB,CAAC,EACN,CAAC,EACG,CAAC,cACZhD,KAAA,CAACa,SAAS,EAAAmC,QAAA,eACRlD,IAAA,UAAAkD,QAAA,CAAO,cAAY,CAAO,CAAC,cAC3BlD,IAAA,UACEmD,IAAI,CAAC,QAAQ,CACbkB,GAAG,CAAC,IAAI,CACRC,GAAG,CAAC,KAAK,CACTlB,KAAK,CAAEpB,MAAM,CAACS,KAAM,CACpBY,QAAQ,CAAGC,CAAC,EAAKrB,SAAS,CAACW,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEH,KAAK,CAAE0B,QAAQ,CAACb,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,EAAI,GAAI,CAAC,CAAC,CAAE,CAC3F,CAAC,EACO,CAAC,EACL,CAAC,EACC,CAAC,cAEdlD,KAAA,CAACmB,WAAW,EAAA6B,QAAA,eACVlD,IAAA,CAACsB,YAAY,EAAA4B,QAAA,CAAC,2CAAyB,CAAc,CAAC,cACtDhD,KAAA,CAACsB,aAAa,EAAA0B,QAAA,eACZlD,IAAA,CAACyB,YAAY,EAAAyB,QAAA,CAAC,YAAO,CAAc,CAAC,cACpClD,IAAA,CAAC2B,YAAY,EAAAuB,QAAA,CAAElB,MAAM,CAACE,KAAK,CAAe,CAAC,EAC9B,CAAC,CACfF,MAAM,CAACG,QAAQ,eACdjC,KAAA,CAACsB,aAAa,EAAA0B,QAAA,eACZlD,IAAA,CAACyB,YAAY,EAAAyB,QAAA,CAAC,aAAW,CAAc,CAAC,cACxClD,IAAA,CAAC2B,YAAY,EAAAuB,QAAA,CAAElB,MAAM,CAACG,QAAQ,CAAe,CAAC,EACjC,CAChB,cACDjC,KAAA,CAACsB,aAAa,EAAA0B,QAAA,eACZlD,IAAA,CAACyB,YAAY,EAAAyB,QAAA,CAAC,cAAY,CAAc,CAAC,cACzChD,KAAA,CAACyB,YAAY,EAAAuB,QAAA,EAAEF,kBAAkB,CAACuB,KAAK,CAAC,GAAC,CAACvB,kBAAkB,CAACwB,IAAI,EAAe,CAAC,EACpE,CAAC,cAChBtE,KAAA,CAACsB,aAAa,EAAA0B,QAAA,eACZlD,IAAA,CAACyB,YAAY,EAAAyB,QAAA,CAAC,aAAW,CAAc,CAAC,cACxChD,KAAA,CAACyB,YAAY,EAAAuB,QAAA,EAAElB,MAAM,CAACK,GAAG,CAAC,GAAC,CAACL,MAAM,CAACM,OAAO,GAAK,OAAO,CAAG,OAAO,CAAG,OAAO,EAAe,CAAC,EAC7E,CAAC,cAChBpC,KAAA,CAACsB,aAAa,EAAA0B,QAAA,eACZlD,IAAA,CAACyB,YAAY,EAAAyB,QAAA,CAAC,WAAS,CAAc,CAAC,cACtChD,KAAA,CAACyB,YAAY,EAAAuB,QAAA,EAAElB,MAAM,CAACO,gBAAgB,CAAC,GAAC,CAACP,MAAM,CAACQ,gBAAgB,EAAe,CAAC,EACnE,CAAC,cAChBtC,KAAA,CAACsB,aAAa,EAAA0B,QAAA,eACZlD,IAAA,CAACyB,YAAY,EAAAyB,QAAA,CAAC,QAAM,CAAc,CAAC,cACnChD,KAAA,CAACyB,YAAY,EAAAuB,QAAA,EAAElB,MAAM,CAACS,KAAK,CAAC,MAAI,EAAc,CAAC,EAClC,CAAC,EACL,CAAC,cAEdvC,KAAA,CAACc,aAAa,EAAAkC,QAAA,eACZlD,IAAA,CAACiB,YAAY,EAACG,OAAO,CAAC,WAAW,CAACqD,OAAO,CAAE1C,QAAS,CAAAmB,QAAA,CAAC,iBAErD,CAAc,CAAC,cACflD,IAAA,CAACiB,YAAY,EAACG,OAAO,CAAC,SAAS,CAACqD,OAAO,CAAE5B,YAAa,CAAAK,QAAA,CAAC,wBAEvD,CAAc,CAAC,EACF,CAAC,EACP,CAAC,EACC,CAAC,CAEpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}