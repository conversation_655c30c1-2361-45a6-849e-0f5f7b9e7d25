{"ast": null, "code": "export const INSTRUMENT_TEMPLATES=[{id:'piano',name:'Piano',emoji:'🎹',clef:'treble',range:{lowest:{note:'A',octave:0},highest:{note:'C',octave:8}},description:'Instrumento de teclas com ampla extensão e capacidade polifônica',commonKeys:['C','G','D','A','E','F','Bb','Eb','Ab'],staffCount:2// Clave de sol e fá\n},{id:'guitar',name:'Viol<PERSON>/Guitarra',emoji:'🎸',clef:'treble',range:{lowest:{note:'E',octave:2},highest:{note:'E',octave:6}},transposition:-12,// Soa uma oitava abaixo do escrito\ntuning:['E2','A2','D3','G3','B3','E4'],description:'Instrumento de cordas dedilhadas, versátil para diversos estilos',commonKeys:['E','A','D','G','C','F','B','Em','Am','Dm'],staffCount:1},{id:'violin',name:'Violino',emoji:'🎻',clef:'treble',range:{lowest:{note:'G',octave:3},highest:{note:'E',octave:7}},tuning:['G3','D4','A4','E5'],description:'Instrumento de cordas friccionadas, líder da família das cordas',commonKeys:['G','D','A','E','C','F','Bb'],staffCount:1},{id:'flute',name:'Flauta',emoji:'🪈',clef:'treble',range:{lowest:{note:'C',octave:4},highest:{note:'D',octave:7}},description:'Instrumento de sopro da família das madeiras, som brilhante',commonKeys:['C','G','D','A','F','Bb'],staffCount:1},{id:'trumpet',name:'Trompete',emoji:'🎺',clef:'treble',range:{lowest:{note:'F#',octave:3},highest:{note:'C',octave:6}},transposition:-2,// Instrumento em Bb\ndescription:'Instrumento de sopro da família dos metais, som brilhante e penetrante',commonKeys:['Bb','F','C','G','D','Eb'],staffCount:1},{id:'drums',name:'Bateria',emoji:'🥁',clef:'treble',// Usa clave de sol mas com notação especial\nrange:{lowest:{note:'C',octave:3},highest:{note:'C',octave:6}},description:'Conjunto de instrumentos de percussão para ritmo e acompanhamento',commonKeys:['C'],// Bateria não tem tonalidade específica\nstaffCount:1},{id:'bass',name:'Baixo',emoji:'🎸',clef:'bass',range:{lowest:{note:'E',octave:1},highest:{note:'G',octave:4}},transposition:-12,// Soa uma oitava abaixo\ntuning:['E1','A1','D2','G2'],description:'Instrumento de cordas graves, base harmônica e rítmica',commonKeys:['E','A','D','G','C','F','B'],staffCount:1},{id:'other',name:'Outro',emoji:'🎵',clef:'treble',range:{lowest:{note:'C',octave:3},highest:{note:'C',octave:6}},description:'Configuração genérica para outros instrumentos',commonKeys:['C','G','D','A','E','F','Bb'],staffCount:1}];// Função para obter template por ID\nexport const getInstrumentTemplate=id=>{return INSTRUMENT_TEMPLATES.find(template=>template.id===id)||INSTRUMENT_TEMPLATES[INSTRUMENT_TEMPLATES.length-1];};// Função para obter todas as categorias de instrumentos\nexport const getInstrumentCategories=()=>{return{keyboard:['piano'],strings:['guitar','violin','bass'],winds:['flute','trumpet'],percussion:['drums'],other:['other']};};// Função para verificar se uma nota está no range do instrumento\nexport const isNoteInRange=(note,octave,instrument)=>{const template=getInstrumentTemplate(instrument);const{lowest,highest}=template.range;// Converter notas para números para comparação\nconst noteToNumber=(noteName,oct)=>{const noteValues={C:0,'C#':1,D:2,'D#':3,E:4,F:5,'F#':6,G:7,'G#':8,A:9,'A#':10,B:11};return oct*12+(noteValues[noteName]||0);};const noteNumber=noteToNumber(note,octave);const lowestNumber=noteToNumber(lowest.note,lowest.octave);const highestNumber=noteToNumber(highest.note,highest.octave);return noteNumber>=lowestNumber&&noteNumber<=highestNumber;};// Função para sugerir tonalidade baseada no instrumento\nexport const suggestKeyForInstrument=instrument=>{const template=getInstrumentTemplate(instrument);return template.commonKeys[0];// Retorna a tonalidade mais comum\n};// Função para obter informações de afinação\nexport const getTuningInfo=instrument=>{const template=getInstrumentTemplate(instrument);return template.tuning||null;};// Função para aplicar transposição do instrumento\nexport const applyInstrumentTransposition=(note,octave,instrument)=>{const template=getInstrumentTemplate(instrument);if(!template.transposition){return{note,octave};}// Lógica de transposição (simplificada)\nconst noteValues={C:0,'C#':1,D:2,'D#':3,E:4,F:5,'F#':6,G:7,'G#':8,A:9,'A#':10,B:11};const noteNames=['C','C#','D','D#','E','F','F#','G','G#','A','A#','B'];let noteNumber=octave*12+(noteValues[note]||0);noteNumber+=template.transposition;const newOctave=Math.floor(noteNumber/12);const newNoteIndex=noteNumber%12;const newNote=noteNames[newNoteIndex];return{note:newNote,octave:newOctave};};", "map": {"version": 3, "names": ["INSTRUMENT_TEMPLATES", "id", "name", "emoji", "clef", "range", "lowest", "note", "octave", "highest", "description", "commonKeys", "staffCount", "transposition", "tuning", "getInstrumentTemplate", "find", "template", "length", "getInstrumentCategories", "keyboard", "strings", "winds", "percussion", "other", "isNoteInRange", "instrument", "noteToNumber", "noteName", "oct", "noteValues", "C", "D", "E", "F", "G", "A", "B", "noteNumber", "lowestNumber", "highestNumber", "suggestKeyForInstrument", "getTuningInfo", "applyInstrumentTransposition", "noteNames", "newOctave", "Math", "floor", "newNoteIndex", "newNote"], "sources": ["D:/Dev/partitura_digital/src/utils/instrumentTemplates.ts"], "sourcesContent": ["import { InstrumentType, ClefType, NoteName } from '../types/music';\n\nexport interface InstrumentTemplate {\n  id: InstrumentType;\n  name: string;\n  emoji: string;\n  clef: ClefType;\n  range: {\n    lowest: { note: NoteName; octave: number };\n    highest: { note: NoteName; octave: number };\n  };\n  transposition?: number; // Semitons para transposição\n  tuning?: string[]; // Para instrumentos de corda\n  description: string;\n  commonKeys: string[]; // Tonalidades mais comuns\n  staffCount: number; // Número de pautas (1 para maioria, 2 para piano)\n}\n\nexport const INSTRUMENT_TEMPLATES: InstrumentTemplate[] = [\n  {\n    id: 'piano',\n    name: 'Piano',\n    emoji: '🎹',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'A', octave: 0 },\n      highest: { note: 'C', octave: 8 }\n    },\n    description: 'Instrumento de teclas com ampla extensão e capacidade polifônica',\n    commonKeys: ['C', 'G', 'D', 'A', 'E', 'F', 'Bb', 'Eb', 'Ab'],\n    staffCount: 2 // Clave de sol e fá\n  },\n  {\n    id: 'guitar',\n    name: 'Violão/Guitarra',\n    emoji: '🎸',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'E', octave: 2 },\n      highest: { note: 'E', octave: 6 }\n    },\n    transposition: -12, // Soa uma oitava abaixo do escrito\n    tuning: ['E2', 'A2', 'D3', 'G3', 'B3', 'E4'],\n    description: 'Instrumento de cordas dedilhadas, versátil para diversos estilos',\n    commonKeys: ['E', 'A', 'D', 'G', 'C', 'F', 'B', 'Em', 'Am', 'Dm'],\n    staffCount: 1\n  },\n  {\n    id: 'violin',\n    name: 'Violino',\n    emoji: '🎻',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'G', octave: 3 },\n      highest: { note: 'E', octave: 7 }\n    },\n    tuning: ['G3', 'D4', 'A4', 'E5'],\n    description: 'Instrumento de cordas friccionadas, líder da família das cordas',\n    commonKeys: ['G', 'D', 'A', 'E', 'C', 'F', 'Bb'],\n    staffCount: 1\n  },\n  {\n    id: 'flute',\n    name: 'Flauta',\n    emoji: '🪈',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'C', octave: 4 },\n      highest: { note: 'D', octave: 7 }\n    },\n    description: 'Instrumento de sopro da família das madeiras, som brilhante',\n    commonKeys: ['C', 'G', 'D', 'A', 'F', 'Bb'],\n    staffCount: 1\n  },\n  {\n    id: 'trumpet',\n    name: 'Trompete',\n    emoji: '🎺',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'F#', octave: 3 },\n      highest: { note: 'C', octave: 6 }\n    },\n    transposition: -2, // Instrumento em Bb\n    description: 'Instrumento de sopro da família dos metais, som brilhante e penetrante',\n    commonKeys: ['Bb', 'F', 'C', 'G', 'D', 'Eb'],\n    staffCount: 1\n  },\n  {\n    id: 'drums',\n    name: 'Bateria',\n    emoji: '🥁',\n    clef: 'treble', // Usa clave de sol mas com notação especial\n    range: {\n      lowest: { note: 'C', octave: 3 },\n      highest: { note: 'C', octave: 6 }\n    },\n    description: 'Conjunto de instrumentos de percussão para ritmo e acompanhamento',\n    commonKeys: ['C'], // Bateria não tem tonalidade específica\n    staffCount: 1\n  },\n  {\n    id: 'bass',\n    name: 'Baixo',\n    emoji: '🎸',\n    clef: 'bass',\n    range: {\n      lowest: { note: 'E', octave: 1 },\n      highest: { note: 'G', octave: 4 }\n    },\n    transposition: -12, // Soa uma oitava abaixo\n    tuning: ['E1', 'A1', 'D2', 'G2'],\n    description: 'Instrumento de cordas graves, base harmônica e rítmica',\n    commonKeys: ['E', 'A', 'D', 'G', 'C', 'F', 'B'],\n    staffCount: 1\n  },\n  {\n    id: 'other',\n    name: 'Outro',\n    emoji: '🎵',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'C', octave: 3 },\n      highest: { note: 'C', octave: 6 }\n    },\n    description: 'Configuração genérica para outros instrumentos',\n    commonKeys: ['C', 'G', 'D', 'A', 'E', 'F', 'Bb'],\n    staffCount: 1\n  }\n];\n\n// Função para obter template por ID\nexport const getInstrumentTemplate = (id: InstrumentType): InstrumentTemplate => {\n  return INSTRUMENT_TEMPLATES.find(template => template.id === id) || INSTRUMENT_TEMPLATES[INSTRUMENT_TEMPLATES.length - 1];\n};\n\n// Função para obter todas as categorias de instrumentos\nexport const getInstrumentCategories = () => {\n  return {\n    keyboard: ['piano'],\n    strings: ['guitar', 'violin', 'bass'],\n    winds: ['flute', 'trumpet'],\n    percussion: ['drums'],\n    other: ['other']\n  };\n};\n\n// Função para verificar se uma nota está no range do instrumento\nexport const isNoteInRange = (\n  note: NoteName,\n  octave: number,\n  instrument: InstrumentType\n): boolean => {\n  const template = getInstrumentTemplate(instrument);\n  const { lowest, highest } = template.range;\n\n  // Converter notas para números para comparação\n  const noteToNumber = (noteName: NoteName, oct: number): number => {\n    const noteValues: { [key: string]: number } = {\n      C: 0, 'C#': 1, D: 2, 'D#': 3, E: 4, F: 5, 'F#': 6,\n      G: 7, 'G#': 8, A: 9, 'A#': 10, B: 11\n    };\n    return oct * 12 + (noteValues[noteName] || 0);\n  };\n\n  const noteNumber = noteToNumber(note, octave);\n  const lowestNumber = noteToNumber(lowest.note, lowest.octave);\n  const highestNumber = noteToNumber(highest.note, highest.octave);\n\n  return noteNumber >= lowestNumber && noteNumber <= highestNumber;\n};\n\n// Função para sugerir tonalidade baseada no instrumento\nexport const suggestKeyForInstrument = (instrument: InstrumentType): string => {\n  const template = getInstrumentTemplate(instrument);\n  return template.commonKeys[0]; // Retorna a tonalidade mais comum\n};\n\n// Função para obter informações de afinação\nexport const getTuningInfo = (instrument: InstrumentType): string[] | null => {\n  const template = getInstrumentTemplate(instrument);\n  return template.tuning || null;\n};\n\n// Função para aplicar transposição do instrumento\nexport const applyInstrumentTransposition = (\n  note: NoteName,\n  octave: number,\n  instrument: InstrumentType\n): { note: NoteName; octave: number } => {\n  const template = getInstrumentTemplate(instrument);\n\n  if (!template.transposition) {\n    return { note, octave };\n  }\n\n  // Lógica de transposição (simplificada)\n  const noteValues: { [key: string]: number } = {\n    C: 0, 'C#': 1, D: 2, 'D#': 3, E: 4, F: 5, 'F#': 6,\n    G: 7, 'G#': 8, A: 9, 'A#': 10, B: 11\n  };\n  const noteNames: NoteName[] = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];\n\n  let noteNumber = octave * 12 + (noteValues[note] || 0);\n  noteNumber += template.transposition;\n\n  const newOctave = Math.floor(noteNumber / 12);\n  const newNoteIndex = noteNumber % 12;\n  const newNote = noteNames[newNoteIndex] as NoteName;\n\n  return { note: newNote, octave: newOctave };\n};\n"], "mappings": "AAkBA,MAAO,MAAM,CAAAA,oBAA0C,CAAG,CACxD,CACEC,EAAE,CAAE,OAAO,CACXC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,CACLC,MAAM,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAAC,CAChCC,OAAO,CAAE,CAAEF,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAClC,CAAC,CACDE,WAAW,CAAE,kEAAkE,CAC/EC,UAAU,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CAC5DC,UAAU,CAAE,CAAE;AAChB,CAAC,CACD,CACEX,EAAE,CAAE,QAAQ,CACZC,IAAI,CAAE,iBAAiB,CACvBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,CACLC,MAAM,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAAC,CAChCC,OAAO,CAAE,CAAEF,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAClC,CAAC,CACDK,aAAa,CAAE,CAAC,EAAE,CAAE;AACpBC,MAAM,CAAE,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CAC5CJ,WAAW,CAAE,kEAAkE,CAC/EC,UAAU,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACjEC,UAAU,CAAE,CACd,CAAC,CACD,CACEX,EAAE,CAAE,QAAQ,CACZC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,CACLC,MAAM,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAAC,CAChCC,OAAO,CAAE,CAAEF,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAClC,CAAC,CACDM,MAAM,CAAE,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CAChCJ,WAAW,CAAE,iEAAiE,CAC9EC,UAAU,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAC,CAChDC,UAAU,CAAE,CACd,CAAC,CACD,CACEX,EAAE,CAAE,OAAO,CACXC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,CACLC,MAAM,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAAC,CAChCC,OAAO,CAAE,CAAEF,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAClC,CAAC,CACDE,WAAW,CAAE,6DAA6D,CAC1EC,UAAU,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAC,CAC3CC,UAAU,CAAE,CACd,CAAC,CACD,CACEX,EAAE,CAAE,SAAS,CACbC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,CACLC,MAAM,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEC,MAAM,CAAE,CAAE,CAAC,CACjCC,OAAO,CAAE,CAAEF,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAClC,CAAC,CACDK,aAAa,CAAE,CAAC,CAAC,CAAE;AACnBH,WAAW,CAAE,wEAAwE,CACrFC,UAAU,CAAE,CAAC,IAAI,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAC,CAC5CC,UAAU,CAAE,CACd,CAAC,CACD,CACEX,EAAE,CAAE,OAAO,CACXC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,QAAQ,CAAE;AAChBC,KAAK,CAAE,CACLC,MAAM,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAAC,CAChCC,OAAO,CAAE,CAAEF,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAClC,CAAC,CACDE,WAAW,CAAE,mEAAmE,CAChFC,UAAU,CAAE,CAAC,GAAG,CAAC,CAAE;AACnBC,UAAU,CAAE,CACd,CAAC,CACD,CACEX,EAAE,CAAE,MAAM,CACVC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,CACLC,MAAM,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAAC,CAChCC,OAAO,CAAE,CAAEF,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAClC,CAAC,CACDK,aAAa,CAAE,CAAC,EAAE,CAAE;AACpBC,MAAM,CAAE,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CAChCJ,WAAW,CAAE,wDAAwD,CACrEC,UAAU,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC/CC,UAAU,CAAE,CACd,CAAC,CACD,CACEX,EAAE,CAAE,OAAO,CACXC,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,QAAQ,CACdC,KAAK,CAAE,CACLC,MAAM,CAAE,CAAEC,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAAC,CAChCC,OAAO,CAAE,CAAEF,IAAI,CAAE,GAAG,CAAEC,MAAM,CAAE,CAAE,CAClC,CAAC,CACDE,WAAW,CAAE,gDAAgD,CAC7DC,UAAU,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAC,CAChDC,UAAU,CAAE,CACd,CAAC,CACF,CAED;AACA,MAAO,MAAM,CAAAG,qBAAqB,CAAId,EAAkB,EAAyB,CAC/E,MAAO,CAAAD,oBAAoB,CAACgB,IAAI,CAACC,QAAQ,EAAIA,QAAQ,CAAChB,EAAE,GAAKA,EAAE,CAAC,EAAID,oBAAoB,CAACA,oBAAoB,CAACkB,MAAM,CAAG,CAAC,CAAC,CAC3H,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,uBAAuB,CAAGA,CAAA,GAAM,CAC3C,MAAO,CACLC,QAAQ,CAAE,CAAC,OAAO,CAAC,CACnBC,OAAO,CAAE,CAAC,QAAQ,CAAE,QAAQ,CAAE,MAAM,CAAC,CACrCC,KAAK,CAAE,CAAC,OAAO,CAAE,SAAS,CAAC,CAC3BC,UAAU,CAAE,CAAC,OAAO,CAAC,CACrBC,KAAK,CAAE,CAAC,OAAO,CACjB,CAAC,CACH,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,aAAa,CAAGA,CAC3BlB,IAAc,CACdC,MAAc,CACdkB,UAA0B,GACd,CACZ,KAAM,CAAAT,QAAQ,CAAGF,qBAAqB,CAACW,UAAU,CAAC,CAClD,KAAM,CAAEpB,MAAM,CAAEG,OAAQ,CAAC,CAAGQ,QAAQ,CAACZ,KAAK,CAE1C;AACA,KAAM,CAAAsB,YAAY,CAAGA,CAACC,QAAkB,CAAEC,GAAW,GAAa,CAChE,KAAM,CAAAC,UAAqC,CAAG,CAC5CC,CAAC,CAAE,CAAC,CAAE,IAAI,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAE,IAAI,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAE,IAAI,CAAE,CAAC,CACjDC,CAAC,CAAE,CAAC,CAAE,IAAI,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAE,IAAI,CAAE,EAAE,CAAEC,CAAC,CAAE,EACpC,CAAC,CACD,MAAO,CAAAR,GAAG,CAAG,EAAE,EAAIC,UAAU,CAACF,QAAQ,CAAC,EAAI,CAAC,CAAC,CAC/C,CAAC,CAED,KAAM,CAAAU,UAAU,CAAGX,YAAY,CAACpB,IAAI,CAAEC,MAAM,CAAC,CAC7C,KAAM,CAAA+B,YAAY,CAAGZ,YAAY,CAACrB,MAAM,CAACC,IAAI,CAAED,MAAM,CAACE,MAAM,CAAC,CAC7D,KAAM,CAAAgC,aAAa,CAAGb,YAAY,CAAClB,OAAO,CAACF,IAAI,CAAEE,OAAO,CAACD,MAAM,CAAC,CAEhE,MAAO,CAAA8B,UAAU,EAAIC,YAAY,EAAID,UAAU,EAAIE,aAAa,CAClE,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,uBAAuB,CAAIf,UAA0B,EAAa,CAC7E,KAAM,CAAAT,QAAQ,CAAGF,qBAAqB,CAACW,UAAU,CAAC,CAClD,MAAO,CAAAT,QAAQ,CAACN,UAAU,CAAC,CAAC,CAAC,CAAE;AACjC,CAAC,CAED;AACA,MAAO,MAAM,CAAA+B,aAAa,CAAIhB,UAA0B,EAAsB,CAC5E,KAAM,CAAAT,QAAQ,CAAGF,qBAAqB,CAACW,UAAU,CAAC,CAClD,MAAO,CAAAT,QAAQ,CAACH,MAAM,EAAI,IAAI,CAChC,CAAC,CAED;AACA,MAAO,MAAM,CAAA6B,4BAA4B,CAAGA,CAC1CpC,IAAc,CACdC,MAAc,CACdkB,UAA0B,GACa,CACvC,KAAM,CAAAT,QAAQ,CAAGF,qBAAqB,CAACW,UAAU,CAAC,CAElD,GAAI,CAACT,QAAQ,CAACJ,aAAa,CAAE,CAC3B,MAAO,CAAEN,IAAI,CAAEC,MAAO,CAAC,CACzB,CAEA;AACA,KAAM,CAAAsB,UAAqC,CAAG,CAC5CC,CAAC,CAAE,CAAC,CAAE,IAAI,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAE,IAAI,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAE,IAAI,CAAE,CAAC,CACjDC,CAAC,CAAE,CAAC,CAAE,IAAI,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAE,IAAI,CAAE,EAAE,CAAEC,CAAC,CAAE,EACpC,CAAC,CACD,KAAM,CAAAO,SAAqB,CAAG,CAAC,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAC,CAE/F,GAAI,CAAAN,UAAU,CAAG9B,MAAM,CAAG,EAAE,EAAIsB,UAAU,CAACvB,IAAI,CAAC,EAAI,CAAC,CAAC,CACtD+B,UAAU,EAAIrB,QAAQ,CAACJ,aAAa,CAEpC,KAAM,CAAAgC,SAAS,CAAGC,IAAI,CAACC,KAAK,CAACT,UAAU,CAAG,EAAE,CAAC,CAC7C,KAAM,CAAAU,YAAY,CAAGV,UAAU,CAAG,EAAE,CACpC,KAAM,CAAAW,OAAO,CAAGL,SAAS,CAACI,YAAY,CAAa,CAEnD,MAAO,CAAEzC,IAAI,CAAE0C,OAAO,CAAEzC,MAAM,CAAEqC,SAAU,CAAC,CAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}