{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ScoreEditor\\\\ScoreEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { ScoreService } from '../../services/scoreService';\nimport { v4 as uuidv4 } from 'uuid';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n_c = EditorContainer;\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c2 = EditorHeader;\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n_c3 = EditorTitle;\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n_c4 = EditorActions;\nconst ActionButton = styled.button`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c5 = ActionButton;\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n_c6 = EditorContent;\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n_c7 = ToolPanel;\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n_c8 = ToolSection;\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n_c9 = ToolGrid;\nconst ToolButton = styled.button`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n_c0 = ToolButton;\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n_c1 = ScoreCanvas;\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n_c10 = StaffContainer;\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n_c11 = Staff;\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n_c12 = GuestWarning;\nexport const ScoreEditor = ({\n  scoreId\n}) => {\n  _s();\n  const {\n    currentUser\n  } = useAuth();\n  const [title, setTitle] = useState('Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState('note');\n  const [selectedDuration, setSelectedDuration] = useState('quarter');\n  const [selectedNote, setSelectedNote] = useState('C');\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const handleSave = async () => {\n    if (!currentUser) {\n      alert('Você precisa estar logado para salvar partituras!');\n      return;\n    }\n    setIsSaving(true);\n    try {\n      // Criar dados básicos da partitura\n      const scoreData = {\n        title,\n        key: {\n          note: 'C',\n          mode: 'major'\n        },\n        timeSignature: {\n          numerator: 4,\n          denominator: 4\n        },\n        tempo: 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: 'treble',\n          instrument: 'piano',\n          measures: []\n        }],\n        lyrics: []\n      };\n      if (scoreId) {\n        await ScoreService.updateScore(scoreId, scoreData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n      alert('Partitura salva com sucesso!');\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      alert('Erro ao salvar partitura');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n  const durations = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n  return /*#__PURE__*/_jsxDEV(EditorContainer, {\n    children: [/*#__PURE__*/_jsxDEV(EditorHeader, {\n      children: [/*#__PURE__*/_jsxDEV(EditorTitle, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Editor de Partituras\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: title,\n          onChange: e => setTitle(e.target.value),\n          placeholder: \"Nome da partitura...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorActions, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handlePlay,\n          variant: \"primary\",\n          children: isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handleSave,\n          variant: \"secondary\",\n          disabled: !currentUser || isSaving,\n          children: isSaving ? '💾 Salvando...' : '💾 Salvar'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditorContent, {\n      children: [/*#__PURE__*/_jsxDEV(ToolPanel, {\n        children: [!currentUser && /*#__PURE__*/_jsxDEV(GuestWarning, {\n          children: \"\\u26A0\\uFE0F Modo visitante: suas altera\\xE7\\xF5es n\\xE3o ser\\xE3o salvas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFB5 Ferramentas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: [/*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'note',\n              onClick: () => setSelectedTool('note'),\n              children: \"\\uD83C\\uDFB5 Nota\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'rest',\n              onClick: () => setSelectedTool('rest'),\n              children: \"\\uD83C\\uDFBC Pausa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'chord',\n              onClick: () => setSelectedTool('chord'),\n              children: \"\\uD83C\\uDFB9 Acorde\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u23F1\\uFE0F Dura\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: durations.map(duration => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedDuration === duration,\n              onClick: () => setSelectedDuration(duration),\n              children: duration === 'whole' ? '𝅝' : duration === 'half' ? '𝅗𝅥' : duration === 'quarter' ? '♩' : duration === 'eighth' ? '♫' : '♬'\n            }, duration, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFBC Notas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: notes.map(note => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedNote === note,\n              onClick: () => setSelectedNote(note),\n              children: note\n            }, note, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScoreCanvas, {\n        children: /*#__PURE__*/_jsxDEV(StaffContainer, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 1rem 0',\n              color: '#495057'\n            },\n            children: [\"Partitura: \", title]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Staff, {\n            viewBox: \"0 0 800 200\",\n            children: [[0, 1, 2, 3, 4].map(line => /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"50\",\n              y1: 50 + line * 20,\n              x2: \"750\",\n              y2: 50 + line * 20,\n              stroke: \"#333\",\n              strokeWidth: \"1\"\n            }, line, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"20\",\n              y: \"90\",\n              fontSize: \"40\",\n              fill: \"#333\",\n              children: \"\\uD834\\uDD1E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"75\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"95\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"110\",\n              y1: \"50\",\n              x2: \"110\",\n              y2: \"130\",\n              stroke: \"#333\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"120\",\n              y: \"40\",\n              width: \"600\",\n              height: \"100\",\n              fill: \"transparent\",\n              style: {\n                cursor: 'pointer'\n              },\n              onClick: e => {\n                // TODO: Adicionar nota na posição clicada\n                console.log('Clicou para adicionar nota');\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"100\",\n              fontSize: \"14\",\n              fill: \"#999\",\n              textAnchor: \"middle\",\n              children: \"Clique aqui para adicionar notas\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 237,\n    columnNumber: 5\n  }, this);\n};\n_s(ScoreEditor, \"Q1rU2Qlh68MMpsBBct84Fqd08/4=\", false, function () {\n  return [useAuth];\n});\n_c13 = ScoreEditor;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"EditorContainer\");\n$RefreshReg$(_c2, \"EditorHeader\");\n$RefreshReg$(_c3, \"EditorTitle\");\n$RefreshReg$(_c4, \"EditorActions\");\n$RefreshReg$(_c5, \"ActionButton\");\n$RefreshReg$(_c6, \"EditorContent\");\n$RefreshReg$(_c7, \"ToolPanel\");\n$RefreshReg$(_c8, \"ToolSection\");\n$RefreshReg$(_c9, \"ToolGrid\");\n$RefreshReg$(_c0, \"ToolButton\");\n$RefreshReg$(_c1, \"ScoreCanvas\");\n$RefreshReg$(_c10, \"StaffContainer\");\n$RefreshReg$(_c11, \"Staff\");\n$RefreshReg$(_c12, \"GuestWarning\");\n$RefreshReg$(_c13, \"ScoreEditor\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "ScoreService", "v4", "uuidv4", "jsxDEV", "_jsxDEV", "EditorC<PERSON><PERSON>", "div", "_c", "Editor<PERSON><PERSON>er", "_c2", "EditorT<PERSON>le", "_c3", "EditorActions", "_c4", "ActionButton", "button", "props", "variant", "_c5", "Editor<PERSON><PERSON><PERSON>", "_c6", "ToolPanel", "_c7", "ToolSection", "_c8", "ToolGrid", "_c9", "<PERSON><PERSON><PERSON><PERSON>on", "active", "_c0", "ScoreCanvas", "_c1", "StaffC<PERSON>r", "_c10", "Staff", "svg", "_c11", "Guest<PERSON><PERSON>ning", "_c12", "ScoreEditor", "scoreId", "_s", "currentUser", "title", "setTitle", "selectedTool", "setSelectedTool", "selectedDuration", "setSelectedDuration", "selected<PERSON><PERSON>", "setSelectedNote", "isPlaying", "setIsPlaying", "isSaving", "setIsSaving", "handleSave", "alert", "scoreData", "key", "note", "mode", "timeSignature", "numerator", "denominator", "tempo", "staffs", "id", "clef", "instrument", "measures", "lyrics", "updateScore", "createScore", "uid", "error", "console", "handlePlay", "durations", "notes", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "e", "target", "placeholder", "onClick", "disabled", "map", "duration", "style", "margin", "color", "viewBox", "line", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "x", "y", "fontSize", "fill", "width", "height", "cursor", "log", "textAnchor", "_c13", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ScoreEditor/ScoreEditor.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Score, MusicalNote, NoteName, NoteDuration, ClefType, InstrumentType } from '../../types/music';\nimport { ScoreService } from '../../services/scoreService';\nimport { v4 as uuidv4 } from 'uuid';\n\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n\nconst ToolButton = styled.button<{ active?: boolean }>`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n\ninterface ScoreEditorProps {\n  scoreId?: string;\n}\n\nexport const ScoreEditor: React.FC<ScoreEditorProps> = ({ scoreId }) => {\n  const { currentUser } = useAuth();\n  const [title, setTitle] = useState('Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState<'note' | 'rest' | 'chord'>('note');\n  const [selectedDuration, setSelectedDuration] = useState<NoteDuration>('quarter');\n  const [selectedNote, setSelectedNote] = useState<NoteName>('C');\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n\n  const handleSave = async () => {\n    if (!currentUser) {\n      alert('Você precisa estar logado para salvar partituras!');\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      // Criar dados básicos da partitura\n      const scoreData = {\n        title,\n        key: { note: 'C' as NoteName, mode: 'major' as const },\n        timeSignature: { numerator: 4, denominator: 4 },\n        tempo: 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: 'treble' as ClefType,\n          instrument: 'piano' as InstrumentType,\n          measures: []\n        }],\n        lyrics: []\n      };\n\n      if (scoreId) {\n        await ScoreService.updateScore(scoreId, scoreData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n      \n      alert('Partitura salva com sucesso!');\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      alert('Erro ao salvar partitura');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n\n  const durations: NoteDuration[] = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes: NoteName[] = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n\n  return (\n    <EditorContainer>\n      <EditorHeader>\n        <EditorTitle>\n          <h2>Editor de Partituras</h2>\n          <input\n            type=\"text\"\n            value={title}\n            onChange={(e) => setTitle(e.target.value)}\n            placeholder=\"Nome da partitura...\"\n          />\n        </EditorTitle>\n        <EditorActions>\n          <ActionButton onClick={handlePlay} variant=\"primary\">\n            {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}\n          </ActionButton>\n          <ActionButton \n            onClick={handleSave} \n            variant=\"secondary\"\n            disabled={!currentUser || isSaving}\n          >\n            {isSaving ? '💾 Salvando...' : '💾 Salvar'}\n          </ActionButton>\n        </EditorActions>\n      </EditorHeader>\n\n      <EditorContent>\n        <ToolPanel>\n          {!currentUser && (\n            <GuestWarning>\n              ⚠️ Modo visitante: suas alterações não serão salvas\n            </GuestWarning>\n          )}\n          \n          <ToolSection>\n            <h3>🎵 Ferramentas</h3>\n            <ToolGrid>\n              <ToolButton \n                active={selectedTool === 'note'}\n                onClick={() => setSelectedTool('note')}\n              >\n                🎵 Nota\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'rest'}\n                onClick={() => setSelectedTool('rest')}\n              >\n                🎼 Pausa\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'chord'}\n                onClick={() => setSelectedTool('chord')}\n              >\n                🎹 Acorde\n              </ToolButton>\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>⏱️ Duração</h3>\n            <ToolGrid>\n              {durations.map(duration => (\n                <ToolButton\n                  key={duration}\n                  active={selectedDuration === duration}\n                  onClick={() => setSelectedDuration(duration)}\n                >\n                  {duration === 'whole' ? '𝅝' : \n                   duration === 'half' ? '𝅗𝅥' :\n                   duration === 'quarter' ? '♩' :\n                   duration === 'eighth' ? '♫' : '♬'}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>🎼 Notas</h3>\n            <ToolGrid>\n              {notes.map(note => (\n                <ToolButton\n                  key={note}\n                  active={selectedNote === note}\n                  onClick={() => setSelectedNote(note)}\n                >\n                  {note}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n        </ToolPanel>\n\n        <ScoreCanvas>\n          <StaffContainer>\n            <h3 style={{ margin: '0 0 1rem 0', color: '#495057' }}>Partitura: {title}</h3>\n            <Staff viewBox=\"0 0 800 200\">\n              {/* Linhas da pauta */}\n              {[0, 1, 2, 3, 4].map(line => (\n                <line\n                  key={line}\n                  x1=\"50\"\n                  y1={50 + line * 20}\n                  x2=\"750\"\n                  y2={50 + line * 20}\n                  stroke=\"#333\"\n                  strokeWidth=\"1\"\n                />\n              ))}\n              \n              {/* Clave de Sol */}\n              <text x=\"20\" y=\"90\" fontSize=\"40\" fill=\"#333\">𝄞</text>\n              \n              {/* Compasso 4/4 */}\n              <text x=\"80\" y=\"75\" fontSize=\"16\" fill=\"#333\">4</text>\n              <text x=\"80\" y=\"95\" fontSize=\"16\" fill=\"#333\">4</text>\n              \n              {/* Linha divisória */}\n              <line x1=\"110\" y1=\"50\" x2=\"110\" y2=\"130\" stroke=\"#333\" strokeWidth=\"2\"/>\n              \n              {/* Área clicável para adicionar notas */}\n              <rect \n                x=\"120\" \n                y=\"40\" \n                width=\"600\" \n                height=\"100\" \n                fill=\"transparent\" \n                style={{ cursor: 'pointer' }}\n                onClick={(e) => {\n                  // TODO: Adicionar nota na posição clicada\n                  console.log('Clicou para adicionar nota');\n                }}\n              />\n              \n              {/* Placeholder para notas */}\n              <text x=\"400\" y=\"100\" fontSize=\"14\" fill=\"#999\" textAnchor=\"middle\">\n                Clique aqui para adicionar notas\n              </text>\n            </Staff>\n          </StaffContainer>\n        </ScoreCanvas>\n      </EditorContent>\n    </EditorContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAgB,OAAO;AAC/C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AAEpD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGP,MAAM,CAACQ,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,eAAe;AAUrB,MAAMG,YAAY,GAAGV,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,YAAY;AASlB,MAAME,WAAW,GAAGZ,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GA1BID,WAAW;AA4BjB,MAAME,aAAa,GAAGd,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGhB,MAAM,CAACiB,MAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhCIJ,YAAY;AAkClB,MAAMK,aAAa,GAAGrB,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,aAAa;AAMnB,MAAME,SAAS,GAAGvB,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GANID,SAAS;AAQf,MAAME,WAAW,GAAGzB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GATID,WAAW;AAWjB,MAAME,QAAQ,GAAG3B,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,QAAQ;AAMd,MAAME,UAAU,GAAG7B,MAAM,CAACiB,MAA4B;AACtD;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D,WAAWZ,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,OAAO,GAAG,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMZ,KAAK,IAAI,CAACA,KAAK,CAACY,MAAM,IAAI,sBAAsB;AACtD;AACA,CAAC;AAACC,GAAA,GAdIF,UAAU;AAgBhB,MAAMG,WAAW,GAAGhC,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GARID,WAAW;AAUjB,MAAME,cAAc,GAAGlC,MAAM,CAACQ,GAAG;AACjC;AACA;AACA,CAAC;AAAC2B,IAAA,GAHID,cAAc;AAKpB,MAAME,KAAK,GAAGpC,MAAM,CAACqC,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAPIF,KAAK;AASX,MAAMG,YAAY,GAAGvC,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GATID,YAAY;AAelB,OAAO,MAAME,WAAuC,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM;IAAEC;EAAY,CAAC,GAAG3C,OAAO,CAAC,CAAC;EACjC,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,gBAAgB,CAAC;EACpD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAA4B,MAAM,CAAC;EACnF,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnD,QAAQ,CAAe,SAAS,CAAC;EACjF,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAW,GAAG,CAAC;EAC/D,MAAM,CAACsD,SAAS,EAAEC,YAAY,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwD,QAAQ,EAAEC,WAAW,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAM0D,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACb,WAAW,EAAE;MAChBc,KAAK,CAAC,mDAAmD,CAAC;MAC1D;IACF;IAEAF,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF;MACA,MAAMG,SAAS,GAAG;QAChBd,KAAK;QACLe,GAAG,EAAE;UAAEC,IAAI,EAAE,GAAe;UAAEC,IAAI,EAAE;QAAiB,CAAC;QACtDC,aAAa,EAAE;UAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;QAC/CC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,CAAC;UACPC,EAAE,EAAEhE,MAAM,CAAC,CAAC;UACZiE,IAAI,EAAE,QAAoB;UAC1BC,UAAU,EAAE,OAAyB;UACrCC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFC,MAAM,EAAE;MACV,CAAC;MAED,IAAI9B,OAAO,EAAE;QACX,MAAMxC,YAAY,CAACuE,WAAW,CAAC/B,OAAO,EAAEiB,SAAS,CAAC;MACpD,CAAC,MAAM;QACL,MAAMzD,YAAY,CAACwE,WAAW,CAACf,SAAS,EAAEf,WAAW,CAAC+B,GAAG,CAAC;MAC5D;MAEAjB,KAAK,CAAC,8BAA8B,CAAC;IACvC,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvClB,KAAK,CAAC,0BAA0B,CAAC;IACnC,CAAC,SAAS;MACRF,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMsB,UAAU,GAAGA,CAAA,KAAM;IACvBxB,YAAY,CAAC,CAACD,SAAS,CAAC;IACxB;EACF,CAAC;EAED,MAAM0B,SAAyB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC;EACrF,MAAMC,KAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAE7D,oBACE1E,OAAA,CAACC,eAAe;IAAA0E,QAAA,gBACd3E,OAAA,CAACI,YAAY;MAAAuE,QAAA,gBACX3E,OAAA,CAACM,WAAW;QAAAqE,QAAA,gBACV3E,OAAA;UAAA2E,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7B/E,OAAA;UACEgF,IAAI,EAAC,MAAM;UACXC,KAAK,EAAE1C,KAAM;UACb2C,QAAQ,EAAGC,CAAC,IAAK3C,QAAQ,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC1CI,WAAW,EAAC;QAAsB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACd/E,OAAA,CAACQ,aAAa;QAAAmE,QAAA,gBACZ3E,OAAA,CAACU,YAAY;UAAC4E,OAAO,EAAEd,UAAW;UAAC3D,OAAO,EAAC,SAAS;UAAA8D,QAAA,EACjD5B,SAAS,GAAG,WAAW,GAAG;QAAe;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACf/E,OAAA,CAACU,YAAY;UACX4E,OAAO,EAAEnC,UAAW;UACpBtC,OAAO,EAAC,WAAW;UACnB0E,QAAQ,EAAE,CAACjD,WAAW,IAAIW,QAAS;UAAA0B,QAAA,EAElC1B,QAAQ,GAAG,gBAAgB,GAAG;QAAW;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEf/E,OAAA,CAACe,aAAa;MAAA4D,QAAA,gBACZ3E,OAAA,CAACiB,SAAS;QAAA0D,QAAA,GACP,CAACrC,WAAW,iBACXtC,OAAA,CAACiC,YAAY;UAAA0C,QAAA,EAAC;QAEd;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CACf,eAED/E,OAAA,CAACmB,WAAW;UAAAwD,QAAA,gBACV3E,OAAA;YAAA2E,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB/E,OAAA,CAACqB,QAAQ;YAAAsD,QAAA,gBACP3E,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,MAAO;cAChC6C,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,MAAM,CAAE;cAAAiC,QAAA,EACxC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/E,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,MAAO;cAChC6C,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,MAAM,CAAE;cAAAiC,QAAA,EACxC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/E,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,OAAQ;cACjC6C,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,OAAO,CAAE;cAAAiC,QAAA,EACzC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEd/E,OAAA,CAACmB,WAAW;UAAAwD,QAAA,gBACV3E,OAAA;YAAA2E,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB/E,OAAA,CAACqB,QAAQ;YAAAsD,QAAA,EACNF,SAAS,CAACe,GAAG,CAACC,QAAQ,iBACrBzF,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEmB,gBAAgB,KAAK8C,QAAS;cACtCH,OAAO,EAAEA,CAAA,KAAM1C,mBAAmB,CAAC6C,QAAQ,CAAE;cAAAd,QAAA,EAE5Cc,QAAQ,KAAK,OAAO,GAAG,IAAI,GAC3BA,QAAQ,KAAK,MAAM,GAAG,MAAM,GAC5BA,QAAQ,KAAK,SAAS,GAAG,GAAG,GAC5BA,QAAQ,KAAK,QAAQ,GAAG,GAAG,GAAG;YAAG,GAP7BA,QAAQ;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQH,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEd/E,OAAA,CAACmB,WAAW;UAAAwD,QAAA,gBACV3E,OAAA;YAAA2E,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjB/E,OAAA,CAACqB,QAAQ;YAAAsD,QAAA,EACND,KAAK,CAACc,GAAG,CAACjC,IAAI,iBACbvD,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEqB,YAAY,KAAKU,IAAK;cAC9B+B,OAAO,EAAEA,CAAA,KAAMxC,eAAe,CAACS,IAAI,CAAE;cAAAoB,QAAA,EAEpCpB;YAAI,GAJAA,IAAI;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKC,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEZ/E,OAAA,CAAC0B,WAAW;QAAAiD,QAAA,eACV3E,OAAA,CAAC4B,cAAc;UAAA+C,QAAA,gBACb3E,OAAA;YAAI0F,KAAK,EAAE;cAAEC,MAAM,EAAE,YAAY;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAjB,QAAA,GAAC,aAAW,EAACpC,KAAK;UAAA;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9E/E,OAAA,CAAC8B,KAAK;YAAC+D,OAAO,EAAC,aAAa;YAAAlB,QAAA,GAEzB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACa,GAAG,CAACM,IAAI,iBACvB9F,OAAA;cAEE+F,EAAE,EAAC,IAAI;cACPC,EAAE,EAAE,EAAE,GAAGF,IAAI,GAAG,EAAG;cACnBG,EAAE,EAAC,KAAK;cACRC,EAAE,EAAE,EAAE,GAAGJ,IAAI,GAAG,EAAG;cACnBK,MAAM,EAAC,MAAM;cACbC,WAAW,EAAC;YAAG,GANVN,IAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV,CACF,CAAC,eAGF/E,OAAA;cAAMqG,CAAC,EAAC,IAAI;cAACC,CAAC,EAAC,IAAI;cAACC,QAAQ,EAAC,IAAI;cAACC,IAAI,EAAC,MAAM;cAAA7B,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGvD/E,OAAA;cAAMqG,CAAC,EAAC,IAAI;cAACC,CAAC,EAAC,IAAI;cAACC,QAAQ,EAAC,IAAI;cAACC,IAAI,EAAC,MAAM;cAAA7B,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtD/E,OAAA;cAAMqG,CAAC,EAAC,IAAI;cAACC,CAAC,EAAC,IAAI;cAACC,QAAQ,EAAC,IAAI;cAACC,IAAI,EAAC,MAAM;cAAA7B,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGtD/E,OAAA;cAAM+F,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,KAAK;cAACC,MAAM,EAAC,MAAM;cAACC,WAAW,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,eAGxE/E,OAAA;cACEqG,CAAC,EAAC,KAAK;cACPC,CAAC,EAAC,IAAI;cACNG,KAAK,EAAC,KAAK;cACXC,MAAM,EAAC,KAAK;cACZF,IAAI,EAAC,aAAa;cAClBd,KAAK,EAAE;gBAAEiB,MAAM,EAAE;cAAU,CAAE;cAC7BrB,OAAO,EAAGH,CAAC,IAAK;gBACd;gBACAZ,OAAO,CAACqC,GAAG,CAAC,4BAA4B,CAAC;cAC3C;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGF/E,OAAA;cAAMqG,CAAC,EAAC,KAAK;cAACC,CAAC,EAAC,KAAK;cAACC,QAAQ,EAAC,IAAI;cAACC,IAAI,EAAC,MAAM;cAACK,UAAU,EAAC,QAAQ;cAAAlC,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB,CAAC;AAAC1C,EAAA,CAtMWF,WAAuC;EAAA,QAC1BxC,OAAO;AAAA;AAAAmH,IAAA,GADpB3E,WAAuC;AAAA,IAAAhC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAA4E,IAAA;AAAAC,YAAA,CAAA5G,EAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}