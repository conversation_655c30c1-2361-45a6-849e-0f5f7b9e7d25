[{"D:\\Dev\\partitura_digital\\src\\index.tsx": "1", "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts": "2", "D:\\Dev\\partitura_digital\\src\\App.tsx": "3", "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx": "4", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx": "5", "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx": "6", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx": "7", "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts": "8", "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx": "9", "D:\\Dev\\partitura_digital\\src\\pages\\ScoresList\\ScoresList.tsx": "10", "D:\\Dev\\partitura_digital\\src\\components\\ScoreEditor\\ScoreEditor.tsx": "11", "D:\\Dev\\partitura_digital\\src\\services\\scoreService.ts": "12", "D:\\Dev\\partitura_digital\\src\\components\\ChordView\\ChordView.tsx": "13", "D:\\Dev\\partitura_digital\\src\\components\\MusicalNote\\MusicalNote.tsx": "14", "D:\\Dev\\partitura_digital\\src\\components\\LyricsEditor\\LyricsEditor.tsx": "15", "D:\\Dev\\partitura_digital\\src\\utils\\instrumentTemplates.ts": "16", "D:\\Dev\\partitura_digital\\src\\components\\InstrumentSelector\\InstrumentSelector.tsx": "17", "D:\\Dev\\partitura_digital\\src\\pages\\NewScore\\NewScorePage.tsx": "18", "D:\\Dev\\partitura_digital\\src\\components\\StaffSystem\\StaffSystem.tsx": "19"}, {"size": 554, "mtime": 1752362684897, "results": "20", "hashOfConfig": "21"}, {"size": 425, "mtime": 1752362684670, "results": "22", "hashOfConfig": "21"}, {"size": 4778, "mtime": 1752367608115, "results": "23", "hashOfConfig": "21"}, {"size": 2208, "mtime": 1752362838593, "results": "24", "hashOfConfig": "21"}, {"size": 8024, "mtime": 1752366228237, "results": "25", "hashOfConfig": "21"}, {"size": 5172, "mtime": 1752366031168, "results": "26", "hashOfConfig": "21"}, {"size": 5984, "mtime": 1752366201199, "results": "27", "hashOfConfig": "21"}, {"size": 1065, "mtime": 1752364240490, "results": "28", "hashOfConfig": "21"}, {"size": 1336, "mtime": 1752363322439, "results": "29", "hashOfConfig": "21"}, {"size": 7973, "mtime": 1752366126702, "results": "30", "hashOfConfig": "21"}, {"size": 19762, "mtime": 1752369449607, "results": "31", "hashOfConfig": "21"}, {"size": 3446, "mtime": 1752362859185, "results": "32", "hashOfConfig": "21"}, {"size": 8687, "mtime": 1752369461955, "results": "33", "hashOfConfig": "21"}, {"size": 5659, "mtime": 1752366965153, "results": "34", "hashOfConfig": "21"}, {"size": 8820, "mtime": 1752367223964, "results": "35", "hashOfConfig": "21"}, {"size": 6544, "mtime": 1752369338249, "results": "36", "hashOfConfig": "21"}, {"size": 7097, "mtime": 1752367431020, "results": "37", "hashOfConfig": "21"}, {"size": 9878, "mtime": 1752367564761, "results": "38", "hashOfConfig": "21"}, {"size": 6585, "mtime": 1752369302694, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uw9qz0", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Dev\\partitura_digital\\src\\index.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts", [], [], "D:\\Dev\\partitura_digital\\src\\App.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx", ["97"], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts", [], [], "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\pages\\ScoresList\\ScoresList.tsx", ["98"], [], "D:\\Dev\\partitura_digital\\src\\components\\ScoreEditor\\ScoreEditor.tsx", ["99", "100", "101", "102", "103"], [], "D:\\Dev\\partitura_digital\\src\\services\\scoreService.ts", [], [], "D:\\Dev\\partitura_digital\\src\\components\\ChordView\\ChordView.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\MusicalNote\\MusicalNote.tsx", ["104"], [], "D:\\Dev\\partitura_digital\\src\\components\\LyricsEditor\\LyricsEditor.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\utils\\instrumentTemplates.ts", [], [], "D:\\Dev\\partitura_digital\\src\\components\\InstrumentSelector\\InstrumentSelector.tsx", ["105", "106"], [], "D:\\Dev\\partitura_digital\\src\\pages\\NewScore\\NewScorePage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\StaffSystem\\StaffSystem.tsx", ["107"], [], {"ruleId": "108", "severity": 1, "message": "109", "line": 4, "column": 17, "nodeType": "110", "messageId": "111", "endLine": 4, "endColumn": 30}, {"ruleId": "112", "severity": 1, "message": "113", "line": 172, "column": 6, "nodeType": "114", "endLine": 172, "endColumn": 19, "suggestions": "115"}, {"ruleId": "108", "severity": 1, "message": "116", "line": 164, "column": 7, "nodeType": "110", "messageId": "111", "endLine": 164, "endColumn": 12}, {"ruleId": "112", "severity": 1, "message": "117", "line": 214, "column": 6, "nodeType": "114", "endLine": 214, "endColumn": 28, "suggestions": "118"}, {"ruleId": "112", "severity": 1, "message": "119", "line": 225, "column": 6, "nodeType": "114", "endLine": 225, "endColumn": 56, "suggestions": "120"}, {"ruleId": "108", "severity": 1, "message": "121", "line": 334, "column": 11, "nodeType": "110", "messageId": "111", "endLine": 334, "endColumn": 15}, {"ruleId": "108", "severity": 1, "message": "122", "line": 343, "column": 11, "nodeType": "110", "messageId": "111", "endLine": 343, "endColumn": 21}, {"ruleId": "108", "severity": 1, "message": "123", "line": 2, "column": 35, "nodeType": "110", "messageId": "111", "endLine": 2, "endColumn": 47}, {"ruleId": "108", "severity": 1, "message": "124", "line": 4, "column": 55, "nodeType": "110", "messageId": "111", "endLine": 4, "endColumn": 78}, {"ruleId": "108", "severity": 1, "message": "125", "line": 143, "column": 23, "nodeType": "110", "messageId": "111", "endLine": 143, "endColumn": 37}, {"ruleId": "108", "severity": 1, "message": "126", "line": 2, "column": 39, "nodeType": "110", "messageId": "111", "endLine": 2, "endColumn": 47}, "@typescript-eslint/no-unused-vars", "'IconComponent' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadScores'. Either include it or remove the dependency array.", "ArrayExpression", ["127"], "'Staff' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'loadScore'. Either include it or remove the dependency array.", ["128"], "React Hook React.useEffect has a missing dependency: 'handleSave'. Either include it or remove the dependency array.", ["129"], "'rect' is assigned a value but never used.", "'staffLines' is assigned a value but never used.", "'NoteDuration' is defined but never used.", "'getInstrumentCategories' is defined but never used.", "'setShowDetails' is assigned a value but never used.", "'ClefType' is defined but never used.", {"desc": "130", "fix": "131"}, {"desc": "132", "fix": "133"}, {"desc": "134", "fix": "135"}, "Update the dependencies array to be: [currentUser, loadScores]", {"range": "136", "text": "137"}, "Update the dependencies array to be: [scoreId, currentUser, loadScore]", {"range": "138", "text": "139"}, "Update the dependencies array to be: [placedNotes, lyrics, title, currentUser, scoreId, handleSave]", {"range": "140", "text": "141"}, [3676, 3689], "[currentUser, loadScores]", [5538, 5560], "[scoreId, currentUser, loadScore]", [5901, 5951], "[placedNotes, lyrics, title, currentUser, scoreId, handleSave]"]