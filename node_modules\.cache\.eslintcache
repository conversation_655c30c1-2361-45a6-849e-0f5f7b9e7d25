[{"D:\\Dev\\partitura_digital\\src\\index.tsx": "1", "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts": "2", "D:\\Dev\\partitura_digital\\src\\App.tsx": "3", "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx": "4", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx": "5", "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx": "6", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx": "7", "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts": "8", "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx": "9", "D:\\Dev\\partitura_digital\\src\\pages\\ScoresList\\ScoresList.tsx": "10", "D:\\Dev\\partitura_digital\\src\\components\\ScoreEditor\\ScoreEditor.tsx": "11", "D:\\Dev\\partitura_digital\\src\\services\\scoreService.ts": "12", "D:\\Dev\\partitura_digital\\src\\components\\ChordView\\ChordView.tsx": "13"}, {"size": 554, "mtime": 1752362684897, "results": "14", "hashOfConfig": "15"}, {"size": 425, "mtime": 1752362684670, "results": "16", "hashOfConfig": "15"}, {"size": 3969, "mtime": 1752366159904, "results": "17", "hashOfConfig": "15"}, {"size": 2208, "mtime": 1752362838593, "results": "18", "hashOfConfig": "15"}, {"size": 8024, "mtime": 1752366228237, "results": "19", "hashOfConfig": "15"}, {"size": 5172, "mtime": 1752366031168, "results": "20", "hashOfConfig": "15"}, {"size": 5984, "mtime": 1752366201199, "results": "21", "hashOfConfig": "15"}, {"size": 1065, "mtime": 1752364240490, "results": "22", "hashOfConfig": "15"}, {"size": 1336, "mtime": 1752363322439, "results": "23", "hashOfConfig": "15"}, {"size": 7973, "mtime": 1752366126702, "results": "24", "hashOfConfig": "15"}, {"size": 21248, "mtime": 1752366730661, "results": "25", "hashOfConfig": "15"}, {"size": 3446, "mtime": 1752362859185, "results": "26", "hashOfConfig": "15"}, {"size": 3989, "mtime": 1752366585887, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uw9qz0", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Dev\\partitura_digital\\src\\index.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts", [], [], "D:\\Dev\\partitura_digital\\src\\App.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx", ["67"], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts", [], [], "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\pages\\ScoresList\\ScoresList.tsx", ["68"], [], "D:\\Dev\\partitura_digital\\src\\components\\ScoreEditor\\ScoreEditor.tsx", ["69", "70", "71", "72", "73", "74"], [], "D:\\Dev\\partitura_digital\\src\\services\\scoreService.ts", [], [], "D:\\Dev\\partitura_digital\\src\\components\\ChordView\\ChordView.tsx", ["75", "76"], [], {"ruleId": "77", "severity": 1, "message": "78", "line": 4, "column": 17, "nodeType": "79", "messageId": "80", "endLine": 4, "endColumn": 30}, {"ruleId": "81", "severity": 1, "message": "82", "line": 172, "column": 6, "nodeType": "83", "endLine": 172, "endColumn": 19, "suggestions": "84"}, {"ruleId": "77", "severity": 1, "message": "85", "line": 1, "column": 27, "nodeType": "79", "messageId": "80", "endLine": 1, "endColumn": 33}, {"ruleId": "77", "severity": 1, "message": "86", "line": 4, "column": 10, "nodeType": "79", "messageId": "80", "endLine": 4, "endColumn": 15}, {"ruleId": "81", "severity": 1, "message": "87", "line": 200, "column": 6, "nodeType": "83", "endLine": 200, "endColumn": 28, "suggestions": "88"}, {"ruleId": "81", "severity": 1, "message": "89", "line": 211, "column": 6, "nodeType": "83", "endLine": 211, "endColumn": 48, "suggestions": "90"}, {"ruleId": "77", "severity": 1, "message": "91", "line": 312, "column": 11, "nodeType": "79", "messageId": "80", "endLine": 312, "endColumn": 15}, {"ruleId": "77", "severity": 1, "message": "92", "line": 321, "column": 11, "nodeType": "79", "messageId": "80", "endLine": 321, "endColumn": 21}, {"ruleId": "77", "severity": 1, "message": "93", "line": 3, "column": 23, "nodeType": "79", "messageId": "80", "endLine": 3, "endColumn": 31}, {"ruleId": "77", "severity": 1, "message": "94", "line": 95, "column": 9, "nodeType": "79", "messageId": "80", "endLine": 95, "endColumn": 47}, "@typescript-eslint/no-unused-vars", "'IconComponent' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadScores'. Either include it or remove the dependency array.", "ArrayExpression", ["95"], "'useRef' is defined but never used.", "'Score' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'loadScore'. Either include it or remove the dependency array.", ["96"], "React Hook React.useEffect has a missing dependency: 'handleSave'. Either include it or remove the dependency array.", ["97"], "'rect' is assigned a value but never used.", "'staffLines' is assigned a value but never used.", "'NoteName' is defined but never used.", "'minorChords' is assigned a value but never used.", {"desc": "98", "fix": "99"}, {"desc": "100", "fix": "101"}, {"desc": "102", "fix": "103"}, "Update the dependencies array to be: [currentUser, loadScores]", {"range": "104", "text": "105"}, "Update the dependencies array to be: [scoreId, currentUser, loadScore]", {"range": "106", "text": "107"}, "Update the dependencies array to be: [placedNotes, title, currentUser, scoreId, handleSave]", {"range": "108", "text": "109"}, [3676, 3689], "[currentUser, loadScores]", [4681, 4703], "[scoreId, currentUser, loadScore]", [5044, 5086], "[placedNotes, title, currentUser, scoreId, handleSave]"]