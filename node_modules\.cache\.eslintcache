[{"D:\\Dev\\partitura_digital\\src\\index.tsx": "1", "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts": "2", "D:\\Dev\\partitura_digital\\src\\App.tsx": "3", "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx": "4", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx": "5", "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx": "6", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx": "7", "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts": "8", "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx": "9"}, {"size": 554, "mtime": 1752362684897, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1752362684670, "results": "12", "hashOfConfig": "11"}, {"size": 2500, "mtime": 1752362961873, "results": "13", "hashOfConfig": "11"}, {"size": 2208, "mtime": 1752362838593, "results": "14", "hashOfConfig": "11"}, {"size": 7389, "mtime": 1752362940136, "results": "15", "hashOfConfig": "11"}, {"size": 3608, "mtime": 1752363212745, "results": "16", "hashOfConfig": "11"}, {"size": 5170, "mtime": 1752363223790, "results": "17", "hashOfConfig": "11"}, {"size": 693, "mtime": 1752362803354, "results": "18", "hashOfConfig": "11"}, {"size": 1095, "mtime": 1752363167379, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uw9qz0", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Dev\\partitura_digital\\src\\index.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts", [], [], "D:\\Dev\\partitura_digital\\src\\App.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx", ["47"], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx", ["48", "49", "50", "51", "52", "53"], [], "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts", [], [], "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx", [], [], {"ruleId": "54", "severity": 1, "message": "55", "line": 4, "column": 17, "nodeType": "56", "messageId": "57", "endLine": 4, "endColumn": 30}, {"ruleId": "54", "severity": 1, "message": "58", "line": 4, "column": 10, "nodeType": "56", "messageId": "57", "endLine": 4, "endColumn": 15}, {"ruleId": "59", "severity": 2, "message": "60", "line": 186, "column": 12, "nodeType": "61", "messageId": "62", "endLine": 186, "endColumn": 19}, {"ruleId": "59", "severity": 2, "message": "63", "line": 197, "column": 16, "nodeType": "61", "messageId": "62", "endLine": 197, "endColumn": 22}, {"ruleId": "59", "severity": 2, "message": "64", "line": 210, "column": 16, "nodeType": "61", "messageId": "62", "endLine": 210, "endColumn": 22}, {"ruleId": "59", "severity": 2, "message": "65", "line": 223, "column": 32, "nodeType": "61", "messageId": "62", "endLine": 223, "endColumn": 40}, {"ruleId": "59", "severity": 2, "message": "66", "line": 223, "column": 57, "nodeType": "61", "messageId": "62", "endLine": 223, "endColumn": 62}, "@typescript-eslint/no-unused-vars", "'IconComponent' is defined but never used.", "Identifier", "unusedVar", "'Icons' is defined but never used.", "react/jsx-no-undef", "'FiMusic' is not defined.", "JSXIdentifier", "undefined", "'FiMail' is not defined.", "'FiLock' is not defined.", "'FiEyeOff' is not defined.", "'FiEye' is not defined."]