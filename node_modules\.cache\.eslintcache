[{"D:\\Dev\\partitura_digital\\src\\index.tsx": "1", "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts": "2", "D:\\Dev\\partitura_digital\\src\\App.tsx": "3", "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx": "4", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx": "5", "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx": "6", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx": "7", "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts": "8", "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx": "9", "D:\\Dev\\partitura_digital\\src\\components\\FirebaseTest.tsx": "10", "D:\\Dev\\partitura_digital\\src\\pages\\ScoresList\\ScoresList.tsx": "11", "D:\\Dev\\partitura_digital\\src\\components\\ScoreEditor\\ScoreEditor.tsx": "12", "D:\\Dev\\partitura_digital\\src\\services\\scoreService.ts": "13"}, {"size": 554, "mtime": 1752362684897, "results": "14", "hashOfConfig": "15"}, {"size": 425, "mtime": 1752362684670, "results": "16", "hashOfConfig": "15"}, {"size": 3969, "mtime": 1752366159904, "results": "17", "hashOfConfig": "15"}, {"size": 2208, "mtime": 1752362838593, "results": "18", "hashOfConfig": "15"}, {"size": 8024, "mtime": 1752366228237, "results": "19", "hashOfConfig": "15"}, {"size": 5172, "mtime": 1752366031168, "results": "20", "hashOfConfig": "15"}, {"size": 5984, "mtime": 1752366201199, "results": "21", "hashOfConfig": "15"}, {"size": 1065, "mtime": 1752364240490, "results": "22", "hashOfConfig": "15"}, {"size": 1336, "mtime": 1752363322439, "results": "23", "hashOfConfig": "15"}, {"size": 1404, "mtime": 1752365762891, "results": "24", "hashOfConfig": "15"}, {"size": 7973, "mtime": 1752366126702, "results": "25", "hashOfConfig": "15"}, {"size": 10160, "mtime": 1752366083034, "results": "26", "hashOfConfig": "15"}, {"size": 3446, "mtime": 1752362859185, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uw9qz0", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Dev\\partitura_digital\\src\\index.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts", [], [], "D:\\Dev\\partitura_digital\\src\\App.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx", ["67"], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts", [], [], "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\FirebaseTest.tsx", ["68", "69"], [], "D:\\Dev\\partitura_digital\\src\\pages\\ScoresList\\ScoresList.tsx", ["70"], [], "D:\\Dev\\partitura_digital\\src\\components\\ScoreEditor\\ScoreEditor.tsx", ["71", "72", "73"], [], "D:\\Dev\\partitura_digital\\src\\services\\scoreService.ts", [], [], {"ruleId": "74", "severity": 1, "message": "75", "line": 4, "column": 17, "nodeType": "76", "messageId": "77", "endLine": 4, "endColumn": 30}, {"ruleId": "74", "severity": 1, "message": "78", "line": 3, "column": 10, "nodeType": "76", "messageId": "77", "endLine": 3, "endColumn": 29}, {"ruleId": "74", "severity": 1, "message": "79", "line": 4, "column": 10, "nodeType": "76", "messageId": "77", "endLine": 4, "endColumn": 34}, {"ruleId": "80", "severity": 1, "message": "81", "line": 172, "column": 6, "nodeType": "82", "endLine": 172, "endColumn": 19, "suggestions": "83"}, {"ruleId": "74", "severity": 1, "message": "84", "line": 1, "column": 27, "nodeType": "76", "messageId": "77", "endLine": 1, "endColumn": 33}, {"ruleId": "74", "severity": 1, "message": "85", "line": 4, "column": 10, "nodeType": "76", "messageId": "77", "endLine": 4, "endColumn": 15}, {"ruleId": "74", "severity": 1, "message": "86", "line": 4, "column": 17, "nodeType": "76", "messageId": "77", "endLine": 4, "endColumn": 28}, "@typescript-eslint/no-unused-vars", "'IconComponent' is defined but never used.", "Identifier", "unusedVar", "'connectAuthEmulator' is defined but never used.", "'connectFirestoreEmulator' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadScores'. Either include it or remove the dependency array.", "ArrayExpression", ["87"], "'useRef' is defined but never used.", "'Score' is defined but never used.", "'MusicalNote' is defined but never used.", {"desc": "88", "fix": "89"}, "Update the dependencies array to be: [currentUser, loadScores]", {"range": "90", "text": "91"}, [3676, 3689], "[currentUser, loadScores]"]