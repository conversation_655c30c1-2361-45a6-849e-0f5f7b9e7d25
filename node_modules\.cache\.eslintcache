[{"D:\\Dev\\partitura_digital\\src\\index.tsx": "1", "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts": "2", "D:\\Dev\\partitura_digital\\src\\App.tsx": "3", "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx": "4", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx": "5", "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx": "6", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx": "7", "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts": "8", "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx": "9", "D:\\Dev\\partitura_digital\\src\\pages\\ScoresList\\ScoresList.tsx": "10", "D:\\Dev\\partitura_digital\\src\\components\\ScoreEditor\\ScoreEditor.tsx": "11", "D:\\Dev\\partitura_digital\\src\\services\\scoreService.ts": "12", "D:\\Dev\\partitura_digital\\src\\components\\ChordView\\ChordView.tsx": "13", "D:\\Dev\\partitura_digital\\src\\components\\MusicalNote\\MusicalNote.tsx": "14", "D:\\Dev\\partitura_digital\\src\\components\\LyricsEditor\\LyricsEditor.tsx": "15", "D:\\Dev\\partitura_digital\\src\\utils\\instrumentTemplates.ts": "16", "D:\\Dev\\partitura_digital\\src\\components\\InstrumentSelector\\InstrumentSelector.tsx": "17", "D:\\Dev\\partitura_digital\\src\\pages\\NewScore\\NewScorePage.tsx": "18", "D:\\Dev\\partitura_digital\\src\\components\\StaffSystem\\StaffSystem.tsx": "19", "D:\\Dev\\partitura_digital\\src\\pages\\ProfessionalEditor\\ProfessionalEditorPage.tsx": "20", "D:\\Dev\\partitura_digital\\src\\components\\ProfessionalScoreEditor\\ProfessionalScoreEditor.tsx": "21", "D:\\Dev\\partitura_digital\\src\\components\\ProfessionalScore\\ProfessionalScore.tsx": "22", "D:\\Dev\\partitura_digital\\src\\components\\AdvancedLyricsEditor\\AdvancedLyricsEditor.tsx": "23", "D:\\Dev\\partitura_digital\\src\\components\\OrchestralSelector\\OrchestralSelector.tsx": "24", "D:\\Dev\\partitura_digital\\src\\utils\\orchestralInstruments.ts": "25"}, {"size": 554, "mtime": 1752362684897, "results": "26", "hashOfConfig": "27"}, {"size": 425, "mtime": 1752362684670, "results": "28", "hashOfConfig": "27"}, {"size": 4947, "mtime": 1752370581096, "results": "29", "hashOfConfig": "27"}, {"size": 2208, "mtime": 1752362838593, "results": "30", "hashOfConfig": "27"}, {"size": 8024, "mtime": 1752366228237, "results": "31", "hashOfConfig": "27"}, {"size": 5261, "mtime": 1752370603944, "results": "32", "hashOfConfig": "27"}, {"size": 5984, "mtime": 1752366201199, "results": "33", "hashOfConfig": "27"}, {"size": 1065, "mtime": 1752364240490, "results": "34", "hashOfConfig": "27"}, {"size": 1336, "mtime": 1752363322439, "results": "35", "hashOfConfig": "27"}, {"size": 7973, "mtime": 1752366126702, "results": "36", "hashOfConfig": "27"}, {"size": 19762, "mtime": 1752369449607, "results": "37", "hashOfConfig": "27"}, {"size": 3446, "mtime": 1752362859185, "results": "38", "hashOfConfig": "27"}, {"size": 8687, "mtime": 1752369461955, "results": "39", "hashOfConfig": "27"}, {"size": 5659, "mtime": 1752366965153, "results": "40", "hashOfConfig": "27"}, {"size": 8820, "mtime": 1752367223964, "results": "41", "hashOfConfig": "27"}, {"size": 6544, "mtime": 1752369338249, "results": "42", "hashOfConfig": "27"}, {"size": 7097, "mtime": 1752367431020, "results": "43", "hashOfConfig": "27"}, {"size": 9878, "mtime": 1752367564761, "results": "44", "hashOfConfig": "27"}, {"size": 6585, "mtime": 1752369302694, "results": "45", "hashOfConfig": "27"}, {"size": 324, "mtime": 1752370551057, "results": "46", "hashOfConfig": "27"}, {"size": 9744, "mtime": 1752370819249, "results": "47", "hashOfConfig": "27"}, {"size": 12684, "mtime": 1752370964790, "results": "48", "hashOfConfig": "27"}, {"size": 10131, "mtime": 1752370841788, "results": "49", "hashOfConfig": "27"}, {"size": 6020, "mtime": 1752370310118, "results": "50", "hashOfConfig": "27"}, {"size": 4652, "mtime": 1752370203972, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uw9qz0", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Dev\\partitura_digital\\src\\index.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts", [], [], "D:\\Dev\\partitura_digital\\src\\App.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx", ["127"], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts", [], [], "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\pages\\ScoresList\\ScoresList.tsx", ["128"], [], "D:\\Dev\\partitura_digital\\src\\components\\ScoreEditor\\ScoreEditor.tsx", ["129", "130", "131", "132", "133"], [], "D:\\Dev\\partitura_digital\\src\\services\\scoreService.ts", [], [], "D:\\Dev\\partitura_digital\\src\\components\\ChordView\\ChordView.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\MusicalNote\\MusicalNote.tsx", ["134"], [], "D:\\Dev\\partitura_digital\\src\\components\\LyricsEditor\\LyricsEditor.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\utils\\instrumentTemplates.ts", [], [], "D:\\Dev\\partitura_digital\\src\\components\\InstrumentSelector\\InstrumentSelector.tsx", ["135", "136"], [], "D:\\Dev\\partitura_digital\\src\\pages\\NewScore\\NewScorePage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\StaffSystem\\StaffSystem.tsx", ["137"], [], "D:\\Dev\\partitura_digital\\src\\pages\\ProfessionalEditor\\ProfessionalEditorPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\ProfessionalScoreEditor\\ProfessionalScoreEditor.tsx", ["138", "139", "140", "141", "142"], [], "D:\\Dev\\partitura_digital\\src\\components\\ProfessionalScore\\ProfessionalScore.tsx", ["143", "144", "145", "146"], [], "D:\\Dev\\partitura_digital\\src\\components\\AdvancedLyricsEditor\\AdvancedLyricsEditor.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\OrchestralSelector\\OrchestralSelector.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\utils\\orchestralInstruments.ts", ["147"], [], {"ruleId": "148", "severity": 1, "message": "149", "line": 4, "column": 17, "nodeType": "150", "messageId": "151", "endLine": 4, "endColumn": 30}, {"ruleId": "152", "severity": 1, "message": "153", "line": 172, "column": 6, "nodeType": "154", "endLine": 172, "endColumn": 19, "suggestions": "155"}, {"ruleId": "148", "severity": 1, "message": "156", "line": 164, "column": 7, "nodeType": "150", "messageId": "151", "endLine": 164, "endColumn": 12}, {"ruleId": "152", "severity": 1, "message": "157", "line": 214, "column": 6, "nodeType": "154", "endLine": 214, "endColumn": 28, "suggestions": "158"}, {"ruleId": "152", "severity": 1, "message": "159", "line": 225, "column": 6, "nodeType": "154", "endLine": 225, "endColumn": 56, "suggestions": "160"}, {"ruleId": "148", "severity": 1, "message": "161", "line": 334, "column": 11, "nodeType": "150", "messageId": "151", "endLine": 334, "endColumn": 15}, {"ruleId": "148", "severity": 1, "message": "162", "line": 343, "column": 11, "nodeType": "150", "messageId": "151", "endLine": 343, "endColumn": 21}, {"ruleId": "148", "severity": 1, "message": "163", "line": 2, "column": 35, "nodeType": "150", "messageId": "151", "endLine": 2, "endColumn": 47}, {"ruleId": "148", "severity": 1, "message": "164", "line": 4, "column": 55, "nodeType": "150", "messageId": "151", "endLine": 4, "endColumn": 78}, {"ruleId": "148", "severity": 1, "message": "165", "line": 143, "column": 23, "nodeType": "150", "messageId": "151", "endLine": 143, "endColumn": 37}, {"ruleId": "148", "severity": 1, "message": "166", "line": 2, "column": 39, "nodeType": "150", "messageId": "151", "endLine": 2, "endColumn": 47}, {"ruleId": "148", "severity": 1, "message": "167", "line": 3, "column": 31, "nodeType": "150", "messageId": "151", "endLine": 3, "endColumn": 45}, {"ruleId": "148", "severity": 1, "message": "168", "line": 7, "column": 10, "nodeType": "150", "messageId": "151", "endLine": 7, "endColumn": 33}, {"ruleId": "148", "severity": 1, "message": "169", "line": 188, "column": 17, "nodeType": "150", "messageId": "151", "endLine": 188, "endColumn": 25}, {"ruleId": "148", "severity": 1, "message": "170", "line": 189, "column": 20, "nodeType": "150", "messageId": "151", "endLine": 189, "endColumn": 31}, {"ruleId": "148", "severity": 1, "message": "171", "line": 225, "column": 11, "nodeType": "150", "messageId": "151", "endLine": 225, "endColumn": 22}, {"ruleId": "148", "severity": 1, "message": "172", "line": 117, "column": 7, "nodeType": "150", "messageId": "151", "endLine": 117, "endColumn": 19}, {"ruleId": "148", "severity": 1, "message": "173", "line": 148, "column": 10, "nodeType": "150", "messageId": "151", "endLine": 148, "endColumn": 26}, {"ruleId": "148", "severity": 1, "message": "174", "line": 150, "column": 20, "nodeType": "150", "messageId": "151", "endLine": 150, "endColumn": 31}, {"ruleId": "148", "severity": 1, "message": "175", "line": 277, "column": 9, "nodeType": "150", "messageId": "151", "endLine": 277, "endColumn": 29}, {"ruleId": "148", "severity": 1, "message": "167", "line": 1, "column": 10, "nodeType": "150", "messageId": "151", "endLine": 1, "endColumn": 24}, "@typescript-eslint/no-unused-vars", "'IconComponent' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadScores'. Either include it or remove the dependency array.", "ArrayExpression", ["176"], "'Staff' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'loadScore'. Either include it or remove the dependency array.", ["177"], "React Hook React.useEffect has a missing dependency: 'handleSave'. Either include it or remove the dependency array.", ["178"], "'rect' is assigned a value but never used.", "'staffLines' is assigned a value but never used.", "'NoteDuration' is defined but never used.", "'getInstrumentCategories' is defined but never used.", "'setShowDetails' is assigned a value but never used.", "'ClefType' is defined but never used.", "'InstrumentType' is defined but never used.", "'getOrchestralInstrument' is defined but never used.", "'setTitle' is assigned a value but never used.", "'setComposer' is assigned a value but never used.", "'staffHeight' is assigned a value but never used.", "'PlaybackLine' is assigned a value but never used.", "'playbackPosition' is assigned a value but never used.", "'setRenderer' is assigned a value but never used.", "'convertDurationToVex' is assigned a value but never used.", {"desc": "179", "fix": "180"}, {"desc": "181", "fix": "182"}, {"desc": "183", "fix": "184"}, "Update the dependencies array to be: [currentUser, loadScores]", {"range": "185", "text": "186"}, "Update the dependencies array to be: [scoreId, currentUser, loadScore]", {"range": "187", "text": "188"}, "Update the dependencies array to be: [placedNotes, lyrics, title, currentUser, scoreId, handleSave]", {"range": "189", "text": "190"}, [3676, 3689], "[currentUser, loadScores]", [5538, 5560], "[scoreId, currentUser, loadScore]", [5901, 5951], "[placedNotes, lyrics, title, currentUser, scoreId, handleSave]"]