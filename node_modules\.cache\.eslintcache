[{"D:\\Dev\\partitura_digital\\src\\index.tsx": "1", "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts": "2", "D:\\Dev\\partitura_digital\\src\\App.tsx": "3", "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx": "4", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx": "5", "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx": "6", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx": "7", "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts": "8", "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx": "9", "D:\\Dev\\partitura_digital\\src\\pages\\ScoresList\\ScoresList.tsx": "10", "D:\\Dev\\partitura_digital\\src\\components\\ScoreEditor\\ScoreEditor.tsx": "11", "D:\\Dev\\partitura_digital\\src\\services\\scoreService.ts": "12", "D:\\Dev\\partitura_digital\\src\\components\\ChordView\\ChordView.tsx": "13", "D:\\Dev\\partitura_digital\\src\\components\\MusicalNote\\MusicalNote.tsx": "14", "D:\\Dev\\partitura_digital\\src\\components\\LyricsEditor\\LyricsEditor.tsx": "15", "D:\\Dev\\partitura_digital\\src\\utils\\instrumentTemplates.ts": "16", "D:\\Dev\\partitura_digital\\src\\components\\InstrumentSelector\\InstrumentSelector.tsx": "17", "D:\\Dev\\partitura_digital\\src\\pages\\NewScore\\NewScorePage.tsx": "18"}, {"size": 554, "mtime": 1752362684897, "results": "19", "hashOfConfig": "20"}, {"size": 425, "mtime": 1752362684670, "results": "21", "hashOfConfig": "20"}, {"size": 4778, "mtime": 1752367608115, "results": "22", "hashOfConfig": "20"}, {"size": 2208, "mtime": 1752362838593, "results": "23", "hashOfConfig": "20"}, {"size": 8024, "mtime": 1752366228237, "results": "24", "hashOfConfig": "20"}, {"size": 5172, "mtime": 1752366031168, "results": "25", "hashOfConfig": "20"}, {"size": 5984, "mtime": 1752366201199, "results": "26", "hashOfConfig": "20"}, {"size": 1065, "mtime": 1752364240490, "results": "27", "hashOfConfig": "20"}, {"size": 1336, "mtime": 1752363322439, "results": "28", "hashOfConfig": "20"}, {"size": 7973, "mtime": 1752366126702, "results": "29", "hashOfConfig": "20"}, {"size": 22962, "mtime": 1752367678819, "results": "30", "hashOfConfig": "20"}, {"size": 3446, "mtime": 1752362859185, "results": "31", "hashOfConfig": "20"}, {"size": 8689, "mtime": 1752367161716, "results": "32", "hashOfConfig": "20"}, {"size": 5659, "mtime": 1752366965153, "results": "33", "hashOfConfig": "20"}, {"size": 8820, "mtime": 1752367223964, "results": "34", "hashOfConfig": "20"}, {"size": 6378, "mtime": 1752367395342, "results": "35", "hashOfConfig": "20"}, {"size": 7097, "mtime": 1752367431020, "results": "36", "hashOfConfig": "20"}, {"size": 9878, "mtime": 1752367564761, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uw9qz0", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Dev\\partitura_digital\\src\\index.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts", [], [], "D:\\Dev\\partitura_digital\\src\\App.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx", ["92"], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts", [], [], "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\pages\\ScoresList\\ScoresList.tsx", ["93"], [], "D:\\Dev\\partitura_digital\\src\\components\\ScoreEditor\\ScoreEditor.tsx", ["94", "95", "96", "97", "98", "99", "100"], [], "D:\\Dev\\partitura_digital\\src\\services\\scoreService.ts", [], [], "D:\\Dev\\partitura_digital\\src\\components\\ChordView\\ChordView.tsx", ["101"], [], "D:\\Dev\\partitura_digital\\src\\components\\MusicalNote\\MusicalNote.tsx", ["102"], [], "D:\\Dev\\partitura_digital\\src\\components\\LyricsEditor\\LyricsEditor.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\utils\\instrumentTemplates.ts", [], [], "D:\\Dev\\partitura_digital\\src\\components\\InstrumentSelector\\InstrumentSelector.tsx", ["103", "104"], [], "D:\\Dev\\partitura_digital\\src\\pages\\NewScore\\NewScorePage.tsx", [], [], {"ruleId": "105", "severity": 1, "message": "106", "line": 4, "column": 17, "nodeType": "107", "messageId": "108", "endLine": 4, "endColumn": 30}, {"ruleId": "109", "severity": 1, "message": "110", "line": 172, "column": 6, "nodeType": "111", "endLine": 172, "endColumn": 19, "suggestions": "112"}, {"ruleId": "105", "severity": 1, "message": "113", "line": 1, "column": 27, "nodeType": "107", "messageId": "108", "endLine": 1, "endColumn": 33}, {"ruleId": "105", "severity": 1, "message": "114", "line": 4, "column": 10, "nodeType": "107", "messageId": "108", "endLine": 4, "endColumn": 15}, {"ruleId": "105", "severity": 1, "message": "115", "line": 4, "column": 54, "nodeType": "107", "messageId": "108", "endLine": 4, "endColumn": 62}, {"ruleId": "109", "severity": 1, "message": "116", "line": 211, "column": 6, "nodeType": "111", "endLine": 211, "endColumn": 28, "suggestions": "117"}, {"ruleId": "109", "severity": 1, "message": "118", "line": 222, "column": 6, "nodeType": "111", "endLine": 222, "endColumn": 56, "suggestions": "119"}, {"ruleId": "105", "severity": 1, "message": "120", "line": 331, "column": 11, "nodeType": "107", "messageId": "108", "endLine": 331, "endColumn": 15}, {"ruleId": "105", "severity": 1, "message": "121", "line": 340, "column": 11, "nodeType": "107", "messageId": "108", "endLine": 340, "endColumn": 21}, {"ruleId": "105", "severity": 1, "message": "122", "line": 3, "column": 23, "nodeType": "107", "messageId": "108", "endLine": 3, "endColumn": 31}, {"ruleId": "105", "severity": 1, "message": "123", "line": 2, "column": 35, "nodeType": "107", "messageId": "108", "endLine": 2, "endColumn": 47}, {"ruleId": "105", "severity": 1, "message": "124", "line": 4, "column": 55, "nodeType": "107", "messageId": "108", "endLine": 4, "endColumn": 78}, {"ruleId": "105", "severity": 1, "message": "125", "line": 143, "column": 23, "nodeType": "107", "messageId": "108", "endLine": 143, "endColumn": 37}, "@typescript-eslint/no-unused-vars", "'IconComponent' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadScores'. Either include it or remove the dependency array.", "ArrayExpression", ["126"], "'useRef' is defined but never used.", "'Score' is defined but never used.", "'ClefType' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'loadScore'. Either include it or remove the dependency array.", ["127"], "React Hook React.useEffect has a missing dependency: 'handleSave'. Either include it or remove the dependency array.", ["128"], "'rect' is assigned a value but never used.", "'staffLines' is assigned a value but never used.", "'NoteName' is defined but never used.", "'NoteDuration' is defined but never used.", "'getInstrumentCategories' is defined but never used.", "'setShowDetails' is assigned a value but never used.", {"desc": "129", "fix": "130"}, {"desc": "131", "fix": "132"}, {"desc": "133", "fix": "134"}, "Update the dependencies array to be: [currentUser, loadScores]", {"range": "135", "text": "136"}, "Update the dependencies array to be: [scoreId, currentUser, loadScore]", {"range": "137", "text": "138"}, "Update the dependencies array to be: [placedNotes, lyrics, title, currentUser, scoreId, handleSave]", {"range": "139", "text": "140"}, [3676, 3689], "[currentUser, loadScores]", [5510, 5532], "[scoreId, currentUser, loadScore]", [5873, 5923], "[placedNotes, lyrics, title, currentUser, scoreId, handleSave]"]