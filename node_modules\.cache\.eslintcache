[{"D:\\Dev\\partitura_digital\\src\\index.tsx": "1", "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts": "2", "D:\\Dev\\partitura_digital\\src\\App.tsx": "3", "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx": "4", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx": "5", "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx": "6", "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx": "7", "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts": "8", "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx": "9", "D:\\Dev\\partitura_digital\\src\\components\\FirebaseTest.tsx": "10"}, {"size": 554, "mtime": 1752362684897, "results": "11", "hashOfConfig": "12"}, {"size": 425, "mtime": 1752362684670, "results": "13", "hashOfConfig": "12"}, {"size": 2618, "mtime": 1752365782869, "results": "14", "hashOfConfig": "12"}, {"size": 2208, "mtime": 1752362838593, "results": "15", "hashOfConfig": "12"}, {"size": 7377, "mtime": 1752363283775, "results": "16", "hashOfConfig": "12"}, {"size": 3608, "mtime": 1752363212745, "results": "17", "hashOfConfig": "12"}, {"size": 5182, "mtime": 1752363248316, "results": "18", "hashOfConfig": "12"}, {"size": 1065, "mtime": 1752364240490, "results": "19", "hashOfConfig": "12"}, {"size": 1336, "mtime": 1752363322439, "results": "20", "hashOfConfig": "12"}, {"size": 1404, "mtime": 1752365762891, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1uw9qz0", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Dev\\partitura_digital\\src\\index.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\reportWebVitals.ts", [], [], "D:\\Dev\\partitura_digital\\src\\App.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\RegisterPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\Layout\\Layout.tsx", ["52"], [], "D:\\Dev\\partitura_digital\\src\\pages\\Auth\\LoginPage.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\services\\firebase.ts", [], [], "D:\\Dev\\partitura_digital\\src\\utils\\icons.tsx", [], [], "D:\\Dev\\partitura_digital\\src\\components\\FirebaseTest.tsx", ["53", "54"], [], {"ruleId": "55", "severity": 1, "message": "56", "line": 4, "column": 17, "nodeType": "57", "messageId": "58", "endLine": 4, "endColumn": 30}, {"ruleId": "55", "severity": 1, "message": "59", "line": 3, "column": 10, "nodeType": "57", "messageId": "58", "endLine": 3, "endColumn": 29}, {"ruleId": "55", "severity": 1, "message": "60", "line": 4, "column": 10, "nodeType": "57", "messageId": "58", "endLine": 4, "endColumn": 34}, "@typescript-eslint/no-unused-vars", "'IconComponent' is defined but never used.", "Identifier", "unusedVar", "'connectAuthEmulator' is defined but never used.", "'connectFirestoreEmulator' is defined but never used."]