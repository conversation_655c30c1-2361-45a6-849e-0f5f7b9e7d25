import React, { useState } from 'react';
import styled from 'styled-components';
import { Lyrics } from '../../types/music';

const EditorContainer = styled.div`
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  margin: 1rem 0;
`;

const EditorHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 1rem;
`;

const Title = styled.h3`
  color: #2c3e50;
  margin: 0;
  font-size: 1.3rem;
`;

const ModeToggle = styled.div`
  display: flex;
  background: #f8f9fa;
  border-radius: 25px;
  padding: 0.25rem;
`;

const ModeButton = styled.button<{ active: boolean }>`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => props.active ? `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  ` : `
    background: transparent;
    color: #495057;
    &:hover {
      background: #e9ecef;
    }
  `}
`;

const LyricsInput = styled.div`
  margin-bottom: 1.5rem;
`;

const InputLabel = styled.label`
  display: block;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
  
  &::placeholder {
    color: #adb5bd;
  }
`;

const SyllableInput = styled.input`
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
`;

const PositionControls = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const ControlGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const NumberInput = styled.input`
  padding: 0.5rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
  
  &:focus {
    outline: none;
    border-color: #667eea;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => props.variant === 'primary' ? `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  ` : `
    background: #f8f9fa;
    color: #495057;
    border: 2px solid #e9ecef;
    &:hover {
      background: #e9ecef;
    }
  `}
`;

const LyricsList = styled.div`
  max-height: 300px;
  overflow-y: auto;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1rem;
  margin-top: 1rem;
`;

const LyricItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #f1f3f4;
  
  &:last-child {
    border-bottom: none;
  }
`;

const LyricText = styled.div`
  font-weight: 600;
  color: #2c3e50;
`;

const LyricPosition = styled.div`
  font-size: 0.9rem;
  color: #6c757d;
`;

const DeleteButton = styled.button`
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: #c82333;
  }
`;

interface AdvancedLyricsEditorProps {
  lyrics: Lyrics[];
  onLyricsAdd: (lyric: Lyrics) => void;
  onLyricsRemove: (index: number) => void;
  onLyricsClear: () => void;
}

export const AdvancedLyricsEditor: React.FC<AdvancedLyricsEditorProps> = ({
  lyrics,
  onLyricsAdd,
  onLyricsRemove,
  onLyricsClear
}) => {
  const [mode, setMode] = useState<'bulk' | 'individual'>('bulk');
  const [bulkText, setBulkText] = useState('');
  const [currentSyllable, setCurrentSyllable] = useState('');
  const [currentPosition, setCurrentPosition] = useState({
    measure: 1,
    beat: 1,
    staff: 0
  });

  const handleBulkProcess = () => {
    if (!bulkText.trim()) return;

    const words = bulkText.trim().split(/\s+/);
    let measure = 1;
    let beat = 1;

    words.forEach((word) => {
      const syllables = word.split('-');
      
      syllables.forEach((syllable) => {
        const lyric: Lyrics = {
          text: syllable,
          position: {
            measure,
            beat,
            staff: 0
          }
        };
        
        onLyricsAdd(lyric);
        
        // Avançar para próxima posição
        beat++;
        if (beat > 4) {
          beat = 1;
          measure++;
        }
      });
    });

    setBulkText('');
  };

  const handleIndividualAdd = () => {
    if (!currentSyllable.trim()) return;

    const lyric: Lyrics = {
      text: currentSyllable.trim(),
      position: {
        measure: currentPosition.measure,
        beat: currentPosition.beat,
        staff: currentPosition.staff
      }
    };

    onLyricsAdd(lyric);
    setCurrentSyllable('');
    
    // Auto-avançar posição
    setCurrentPosition(prev => ({
      ...prev,
      beat: prev.beat < 4 ? prev.beat + 1 : 1,
      measure: prev.beat < 4 ? prev.measure : prev.measure + 1
    }));
  };

  return (
    <EditorContainer>
      <EditorHeader>
        <Title>Editor de Letras</Title>
        <ModeToggle>
          <ModeButton 
            active={mode === 'bulk'} 
            onClick={() => setMode('bulk')}
          >
            Texto Completo
          </ModeButton>
          <ModeButton 
            active={mode === 'individual'} 
            onClick={() => setMode('individual')}
          >
            Sílaba por Sílaba
          </ModeButton>
        </ModeToggle>
      </EditorHeader>

      {mode === 'bulk' ? (
        <LyricsInput>
          <InputLabel>Cole ou digite a letra completa:</InputLabel>
          <TextArea
            value={bulkText}
            onChange={(e) => setBulkText(e.target.value)}
            placeholder="Digite a letra da música aqui. Use hífens para separar sílabas (ex: can-ta-rei) e espaços para separar palavras."
          />
          <ActionButtons>
            <ActionButton onClick={() => setBulkText('')}>
              Limpar
            </ActionButton>
            <ActionButton variant="primary" onClick={handleBulkProcess}>
              Processar Letra
            </ActionButton>
          </ActionButtons>
        </LyricsInput>
      ) : (
        <>
          <LyricsInput>
            <InputLabel>Sílaba atual:</InputLabel>
            <SyllableInput
              value={currentSyllable}
              onChange={(e) => setCurrentSyllable(e.target.value)}
              placeholder="Digite uma sílaba..."
              onKeyPress={(e) => e.key === 'Enter' && handleIndividualAdd()}
            />
          </LyricsInput>

          <PositionControls>
            <ControlGroup>
              <InputLabel>Compasso</InputLabel>
              <NumberInput
                type="number"
                min="1"
                value={currentPosition.measure}
                onChange={(e) => setCurrentPosition(prev => ({
                  ...prev,
                  measure: parseInt(e.target.value) || 1
                }))}
              />
            </ControlGroup>
            <ControlGroup>
              <InputLabel>Tempo</InputLabel>
              <NumberInput
                type="number"
                min="1"
                max="4"
                value={currentPosition.beat}
                onChange={(e) => setCurrentPosition(prev => ({
                  ...prev,
                  beat: parseInt(e.target.value) || 1
                }))}
              />
            </ControlGroup>
            <ControlGroup>
              <InputLabel>Pauta</InputLabel>
              <NumberInput
                type="number"
                min="0"
                value={currentPosition.staff}
                onChange={(e) => setCurrentPosition(prev => ({
                  ...prev,
                  staff: parseInt(e.target.value) || 0
                }))}
              />
            </ControlGroup>
          </PositionControls>

          <ActionButtons>
            <ActionButton onClick={() => setCurrentSyllable('')}>
              Limpar
            </ActionButton>
            <ActionButton variant="primary" onClick={handleIndividualAdd}>
              Adicionar Sílaba
            </ActionButton>
          </ActionButtons>
        </>
      )}

      {lyrics.length > 0 && (
        <>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '1.5rem' }}>
            <h4 style={{ margin: 0, color: '#2c3e50' }}>
              Letras Adicionadas ({lyrics.length})
            </h4>
            <ActionButton onClick={onLyricsClear}>
              Limpar Todas
            </ActionButton>
          </div>
          
          <LyricsList>
            {lyrics.map((lyric, index) => (
              <LyricItem key={index}>
                <div>
                  <LyricText>{lyric.text}</LyricText>
                  <LyricPosition>
                    Compasso {lyric.position.measure}, Tempo {lyric.position.beat}
                    {lyric.position.staff && lyric.position.staff > 0 && `, Pauta ${lyric.position.staff + 1}`}
                  </LyricPosition>
                </div>
                <DeleteButton onClick={() => onLyricsRemove(index)}>
                  ×
                </DeleteButton>
              </LyricItem>
            ))}
          </LyricsList>
        </>
      )}
    </EditorContainer>
  );
};
