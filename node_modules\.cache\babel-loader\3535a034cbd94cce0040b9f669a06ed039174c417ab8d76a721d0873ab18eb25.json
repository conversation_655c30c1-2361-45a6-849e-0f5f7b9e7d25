{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\pages\\\\Auth\\\\RegisterPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Icons } from '../../utils/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 1rem;\n`;\n_c = AuthContainer;\nconst AuthCard = styled.div`\n  background: white;\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  width: 100%;\n  max-width: 400px;\n`;\n_c2 = AuthCard;\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  font-size: 2rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n`;\n_c3 = Logo;\nconst Title = styled.h2`\n  text-align: center;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n  font-weight: 600;\n`;\n_c4 = Title;\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\n_c5 = Form;\nconst InputGroup = styled.div`\n  position: relative;\n`;\n_c6 = InputGroup;\nconst InputIcon = styled.div`\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #6b7280;\n`;\n_c7 = InputIcon;\nconst Input = styled.input`\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 3rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n  box-sizing: border-box;\n\n  &:focus {\n    outline: none;\n    border-color: #3b82f6;\n  }\n\n  &::placeholder {\n    color: #9ca3af;\n  }\n`;\n_c8 = Input;\nconst PasswordToggle = styled.button`\n  position: absolute;\n  right: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 0.25rem;\n\n  &:hover {\n    color: #374151;\n  }\n`;\n_c9 = PasswordToggle;\nconst SubmitButton = styled.button`\n  width: 100%;\n  padding: 0.75rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: transform 0.2s;\n  margin-top: 1rem;\n\n  &:hover {\n    transform: translateY(-1px);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c0 = SubmitButton;\nconst SwitchAuth = styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  color: #6b7280;\n`;\n_c1 = SwitchAuth;\nconst SwitchLink = styled.button`\n  background: none;\n  border: none;\n  color: #3b82f6;\n  cursor: pointer;\n  font-weight: 600;\n  text-decoration: underline;\n\n  &:hover {\n    color: #2563eb;\n  }\n`;\n_c10 = SwitchLink;\nconst ErrorMessage = styled.div`\n  background-color: #fee2e2;\n  color: #dc2626;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;\n_c11 = ErrorMessage;\nconst SuccessMessage = styled.div`\n  background-color: #d1fae5;\n  color: #065f46;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;\n_c12 = SuccessMessage;\nexport const RegisterPage = ({\n  onSwitchToLogin\n}) => {\n  _s();\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const {\n    register\n  } = useAuth();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!name || !email || !password || !confirmPassword) {\n      setError('Por favor, preencha todos os campos');\n      return;\n    }\n    if (password !== confirmPassword) {\n      setError('As senhas não coincidem');\n      return;\n    }\n    if (password.length < 6) {\n      setError('A senha deve ter pelo menos 6 caracteres');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      await register(email, password, name);\n      setSuccess('Conta criada com sucesso! Você será redirecionado...');\n    } catch (error) {\n      console.error('Erro no registro:', error);\n      if (error.code === 'auth/email-already-in-use') {\n        setError('Este email já está em uso');\n      } else if (error.code === 'auth/weak-password') {\n        setError('A senha é muito fraca');\n      } else if (error.code === 'auth/invalid-email') {\n        setError('Email inválido');\n      } else {\n        setError('Erro ao criar conta. Tente novamente.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContainer, {\n    children: /*#__PURE__*/_jsxDEV(AuthCard, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: [/*#__PURE__*/_jsxDEV(Icons.Music, {\n          size: 32\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), \"Partitura Digital\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Title, {\n        children: \"Criar nova conta\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 19\n      }, this), success && /*#__PURE__*/_jsxDEV(SuccessMessage, {\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputIcon, {\n            children: /*#__PURE__*/_jsxDEV(Icons.User, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"text\",\n            placeholder: \"Seu nome\",\n            value: name,\n            onChange: e => setName(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputIcon, {\n            children: /*#__PURE__*/_jsxDEV(Icons.Mail, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"email\",\n            placeholder: \"Seu email\",\n            value: email,\n            onChange: e => setEmail(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputIcon, {\n            children: /*#__PURE__*/_jsxDEV(Icons.Lock, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: showPassword ? 'text' : 'password',\n            placeholder: \"Sua senha\",\n            value: password,\n            onChange: e => setPassword(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PasswordToggle, {\n            type: \"button\",\n            onClick: () => setShowPassword(!showPassword),\n            children: showPassword ? /*#__PURE__*/_jsxDEV(Icons.EyeOff, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 31\n            }, this) : /*#__PURE__*/_jsxDEV(Icons.Eye, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 60\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputIcon, {\n            children: /*#__PURE__*/_jsxDEV(Icons.Lock, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: showConfirmPassword ? 'text' : 'password',\n            placeholder: \"Confirme sua senha\",\n            value: confirmPassword,\n            onChange: e => setConfirmPassword(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PasswordToggle, {\n            type: \"button\",\n            onClick: () => setShowConfirmPassword(!showConfirmPassword),\n            children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(Icons.EyeOff, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(Icons.Eye, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 67\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SubmitButton, {\n          type: \"submit\",\n          disabled: loading,\n          children: loading ? 'Criando conta...' : 'Criar conta'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SwitchAuth, {\n        children: [\"J\\xE1 tem uma conta?\", ' ', /*#__PURE__*/_jsxDEV(SwitchLink, {\n          onClick: onSwitchToLogin,\n          children: \"Fazer login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 5\n  }, this);\n};\n_s(RegisterPage, \"Ekg6Qhg0CXUkt1wdqFrZGK5Z6fs=\", false, function () {\n  return [useAuth];\n});\n_c13 = RegisterPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"AuthContainer\");\n$RefreshReg$(_c2, \"AuthCard\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"Form\");\n$RefreshReg$(_c6, \"InputGroup\");\n$RefreshReg$(_c7, \"InputIcon\");\n$RefreshReg$(_c8, \"Input\");\n$RefreshReg$(_c9, \"PasswordToggle\");\n$RefreshReg$(_c0, \"SubmitButton\");\n$RefreshReg$(_c1, \"SwitchAuth\");\n$RefreshReg$(_c10, \"SwitchLink\");\n$RefreshReg$(_c11, \"ErrorMessage\");\n$RefreshReg$(_c12, \"SuccessMessage\");\n$RefreshReg$(_c13, \"RegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "Icons", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "AuthCard", "_c2", "Logo", "_c3", "Title", "h2", "_c4", "Form", "form", "_c5", "InputGroup", "_c6", "InputIcon", "_c7", "Input", "input", "_c8", "PasswordToggle", "button", "_c9", "SubmitButton", "_c0", "SwitchAuth", "_c1", "SwitchLink", "_c10", "ErrorMessage", "_c11", "SuccessMessage", "_c12", "RegisterPage", "onSwitchToLogin", "_s", "name", "setName", "email", "setEmail", "password", "setPassword", "confirmPassword", "setConfirmPassword", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "loading", "setLoading", "error", "setError", "success", "setSuccess", "register", "handleSubmit", "e", "preventDefault", "length", "console", "code", "children", "Music", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "User", "type", "placeholder", "value", "onChange", "target", "required", "Mail", "Lock", "onClick", "Eye<PERSON>ff", "Eye", "disabled", "_c13", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/pages/Auth/RegisterPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Icons } from '../../utils/icons';\n\nconst AuthContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 1rem;\n`;\n\nconst AuthCard = styled.div`\n  background: white;\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  width: 100%;\n  max-width: 400px;\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  font-size: 2rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n`;\n\nconst Title = styled.h2`\n  text-align: center;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n  font-weight: 600;\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\n\nconst InputGroup = styled.div`\n  position: relative;\n`;\n\nconst InputIcon = styled.div`\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #6b7280;\n`;\n\nconst Input = styled.input`\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 3rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n  box-sizing: border-box;\n\n  &:focus {\n    outline: none;\n    border-color: #3b82f6;\n  }\n\n  &::placeholder {\n    color: #9ca3af;\n  }\n`;\n\nconst PasswordToggle = styled.button`\n  position: absolute;\n  right: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 0.25rem;\n\n  &:hover {\n    color: #374151;\n  }\n`;\n\nconst SubmitButton = styled.button`\n  width: 100%;\n  padding: 0.75rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: transform 0.2s;\n  margin-top: 1rem;\n\n  &:hover {\n    transform: translateY(-1px);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst SwitchAuth = styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  color: #6b7280;\n`;\n\nconst SwitchLink = styled.button`\n  background: none;\n  border: none;\n  color: #3b82f6;\n  cursor: pointer;\n  font-weight: 600;\n  text-decoration: underline;\n\n  &:hover {\n    color: #2563eb;\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background-color: #fee2e2;\n  color: #dc2626;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;\n\nconst SuccessMessage = styled.div`\n  background-color: #d1fae5;\n  color: #065f46;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;\n\ninterface RegisterPageProps {\n  onSwitchToLogin: () => void;\n}\n\nexport const RegisterPage: React.FC<RegisterPageProps> = ({ onSwitchToLogin }) => {\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const { register } = useAuth();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!name || !email || !password || !confirmPassword) {\n      setError('Por favor, preencha todos os campos');\n      return;\n    }\n\n    if (password !== confirmPassword) {\n      setError('As senhas não coincidem');\n      return;\n    }\n\n    if (password.length < 6) {\n      setError('A senha deve ter pelo menos 6 caracteres');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      await register(email, password, name);\n      setSuccess('Conta criada com sucesso! Você será redirecionado...');\n    } catch (error: any) {\n      console.error('Erro no registro:', error);\n      if (error.code === 'auth/email-already-in-use') {\n        setError('Este email já está em uso');\n      } else if (error.code === 'auth/weak-password') {\n        setError('A senha é muito fraca');\n      } else if (error.code === 'auth/invalid-email') {\n        setError('Email inválido');\n      } else {\n        setError('Erro ao criar conta. Tente novamente.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <AuthContainer>\n      <AuthCard>\n        <Logo>\n          <Icons.Music size={32} />\n          Partitura Digital\n        </Logo>\n\n        <Title>Criar nova conta</Title>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n        {success && <SuccessMessage>{success}</SuccessMessage>}\n\n        <Form onSubmit={handleSubmit}>\n          <InputGroup>\n            <InputIcon>\n              <Icons.User size={20} />\n            </InputIcon>\n            <Input\n              type=\"text\"\n              placeholder=\"Seu nome\"\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              required\n            />\n          </InputGroup>\n\n          <InputGroup>\n            <InputIcon>\n              <Icons.Mail size={20} />\n            </InputIcon>\n            <Input\n              type=\"email\"\n              placeholder=\"Seu email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n            />\n          </InputGroup>\n\n          <InputGroup>\n            <InputIcon>\n              <Icons.Lock size={20} />\n            </InputIcon>\n            <Input\n              type={showPassword ? 'text' : 'password'}\n              placeholder=\"Sua senha\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n            />\n            <PasswordToggle\n              type=\"button\"\n              onClick={() => setShowPassword(!showPassword)}\n            >\n              {showPassword ? <Icons.EyeOff size={20} /> : <Icons.Eye size={20} />}\n            </PasswordToggle>\n          </InputGroup>\n\n          <InputGroup>\n            <InputIcon>\n              <Icons.Lock size={20} />\n            </InputIcon>\n            <Input\n              type={showConfirmPassword ? 'text' : 'password'}\n              placeholder=\"Confirme sua senha\"\n              value={confirmPassword}\n              onChange={(e) => setConfirmPassword(e.target.value)}\n              required\n            />\n            <PasswordToggle\n              type=\"button\"\n              onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n            >\n              {showConfirmPassword ? <Icons.EyeOff size={20} /> : <Icons.Eye size={20} />}\n            </PasswordToggle>\n          </InputGroup>\n\n          <SubmitButton type=\"submit\" disabled={loading}>\n            {loading ? 'Criando conta...' : 'Criar conta'}\n          </SubmitButton>\n        </Form>\n\n        <SwitchAuth>\n          Já tem uma conta?{' '}\n          <SwitchLink onClick={onSwitchToLogin}>\n            Fazer login\n          </SwitchLink>\n        </SwitchAuth>\n      </AuthCard>\n    </AuthContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,KAAK,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,aAAa,GAAGL,MAAM,CAACM,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,aAAa;AASnB,MAAMG,QAAQ,GAAGR,MAAM,CAACM,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,QAAQ;AASd,MAAME,IAAI,GAAGV,MAAM,CAACM,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GATID,IAAI;AAWV,MAAME,KAAK,GAAGZ,MAAM,CAACa,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,IAAI,GAAGf,MAAM,CAACgB,IAAI;AACxB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,IAAI;AAMV,MAAMG,UAAU,GAAGlB,MAAM,CAACM,GAAG;AAC7B;AACA,CAAC;AAACa,GAAA,GAFID,UAAU;AAIhB,MAAME,SAAS,GAAGpB,MAAM,CAACM,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GANID,SAAS;AAQf,MAAME,KAAK,GAAGtB,MAAM,CAACuB,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAjBIF,KAAK;AAmBX,MAAMG,cAAc,GAAGzB,MAAM,CAAC0B,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,cAAc;AAgBpB,MAAMG,YAAY,GAAG5B,MAAM,CAAC0B,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAtBID,YAAY;AAwBlB,MAAME,UAAU,GAAG9B,MAAM,CAACM,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GAJID,UAAU;AAMhB,MAAME,UAAU,GAAGhC,MAAM,CAAC0B,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,IAAA,GAXID,UAAU;AAahB,MAAME,YAAY,GAAGlC,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6B,IAAA,GARID,YAAY;AAUlB,MAAME,cAAc,GAAGpC,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GARID,cAAc;AAcpB,OAAO,MAAME,YAAyC,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwD,KAAK,EAAEC,QAAQ,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0D,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM;IAAE4D;EAAS,CAAC,GAAG1D,OAAO,CAAC,CAAC;EAE9B,MAAM2D,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACrB,IAAI,IAAI,CAACE,KAAK,IAAI,CAACE,QAAQ,IAAI,CAACE,eAAe,EAAE;MACpDS,QAAQ,CAAC,qCAAqC,CAAC;MAC/C;IACF;IAEA,IAAIX,QAAQ,KAAKE,eAAe,EAAE;MAChCS,QAAQ,CAAC,yBAAyB,CAAC;MACnC;IACF;IAEA,IAAIX,QAAQ,CAACkB,MAAM,GAAG,CAAC,EAAE;MACvBP,QAAQ,CAAC,0CAA0C,CAAC;MACpD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMC,QAAQ,CAAChB,KAAK,EAAEE,QAAQ,EAAEJ,IAAI,CAAC;MACrCiB,UAAU,CAAC,sDAAsD,CAAC;IACpE,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnBS,OAAO,CAACT,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,IAAIA,KAAK,CAACU,IAAI,KAAK,2BAA2B,EAAE;QAC9CT,QAAQ,CAAC,2BAA2B,CAAC;MACvC,CAAC,MAAM,IAAID,KAAK,CAACU,IAAI,KAAK,oBAAoB,EAAE;QAC9CT,QAAQ,CAAC,uBAAuB,CAAC;MACnC,CAAC,MAAM,IAAID,KAAK,CAACU,IAAI,KAAK,oBAAoB,EAAE;QAC9CT,QAAQ,CAAC,gBAAgB,CAAC;MAC5B,CAAC,MAAM;QACLA,QAAQ,CAAC,uCAAuC,CAAC;MACnD;IACF,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACElD,OAAA,CAACC,aAAa;IAAA6D,QAAA,eACZ9D,OAAA,CAACI,QAAQ;MAAA0D,QAAA,gBACP9D,OAAA,CAACM,IAAI;QAAAwD,QAAA,gBACH9D,OAAA,CAACF,KAAK,CAACiE,KAAK;UAACC,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAE3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEPpE,OAAA,CAACQ,KAAK;QAAAsD,QAAA,EAAC;MAAgB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAE9BjB,KAAK,iBAAInD,OAAA,CAAC8B,YAAY;QAAAgC,QAAA,EAAEX;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,EAC7Cf,OAAO,iBAAIrD,OAAA,CAACgC,cAAc;QAAA8B,QAAA,EAAET;MAAO;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC,eAEtDpE,OAAA,CAACW,IAAI;QAAC0D,QAAQ,EAAEb,YAAa;QAAAM,QAAA,gBAC3B9D,OAAA,CAACc,UAAU;UAAAgD,QAAA,gBACT9D,OAAA,CAACgB,SAAS;YAAA8C,QAAA,eACR9D,OAAA,CAACF,KAAK,CAACwE,IAAI;cAACN,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACZpE,OAAA,CAACkB,KAAK;YACJqD,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,UAAU;YACtBC,KAAK,EAAEpC,IAAK;YACZqC,QAAQ,EAAGjB,CAAC,IAAKnB,OAAO,CAACmB,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAE;YACzCG,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAEbpE,OAAA,CAACc,UAAU;UAAAgD,QAAA,gBACT9D,OAAA,CAACgB,SAAS;YAAA8C,QAAA,eACR9D,OAAA,CAACF,KAAK,CAAC+E,IAAI;cAACb,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACZpE,OAAA,CAACkB,KAAK;YACJqD,IAAI,EAAC,OAAO;YACZC,WAAW,EAAC,WAAW;YACvBC,KAAK,EAAElC,KAAM;YACbmC,QAAQ,EAAGjB,CAAC,IAAKjB,QAAQ,CAACiB,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAE;YAC1CG,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAEbpE,OAAA,CAACc,UAAU;UAAAgD,QAAA,gBACT9D,OAAA,CAACgB,SAAS;YAAA8C,QAAA,eACR9D,OAAA,CAACF,KAAK,CAACgF,IAAI;cAACd,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACZpE,OAAA,CAACkB,KAAK;YACJqD,IAAI,EAAE1B,YAAY,GAAG,MAAM,GAAG,UAAW;YACzC2B,WAAW,EAAC,WAAW;YACvBC,KAAK,EAAEhC,QAAS;YAChBiC,QAAQ,EAAGjB,CAAC,IAAKf,WAAW,CAACe,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAE;YAC7CG,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFpE,OAAA,CAACqB,cAAc;YACbkD,IAAI,EAAC,QAAQ;YACbQ,OAAO,EAAEA,CAAA,KAAMjC,eAAe,CAAC,CAACD,YAAY,CAAE;YAAAiB,QAAA,EAE7CjB,YAAY,gBAAG7C,OAAA,CAACF,KAAK,CAACkF,MAAM;cAAChB,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpE,OAAA,CAACF,KAAK,CAACmF,GAAG;cAACjB,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEbpE,OAAA,CAACc,UAAU;UAAAgD,QAAA,gBACT9D,OAAA,CAACgB,SAAS;YAAA8C,QAAA,eACR9D,OAAA,CAACF,KAAK,CAACgF,IAAI;cAACd,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACZpE,OAAA,CAACkB,KAAK;YACJqD,IAAI,EAAExB,mBAAmB,GAAG,MAAM,GAAG,UAAW;YAChDyB,WAAW,EAAC,oBAAoB;YAChCC,KAAK,EAAE9B,eAAgB;YACvB+B,QAAQ,EAAGjB,CAAC,IAAKb,kBAAkB,CAACa,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAE;YACpDG,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFpE,OAAA,CAACqB,cAAc;YACbkD,IAAI,EAAC,QAAQ;YACbQ,OAAO,EAAEA,CAAA,KAAM/B,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;YAAAe,QAAA,EAE3Df,mBAAmB,gBAAG/C,OAAA,CAACF,KAAK,CAACkF,MAAM;cAAChB,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpE,OAAA,CAACF,KAAK,CAACmF,GAAG;cAACjB,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEbpE,OAAA,CAACwB,YAAY;UAAC+C,IAAI,EAAC,QAAQ;UAACW,QAAQ,EAAEjC,OAAQ;UAAAa,QAAA,EAC3Cb,OAAO,GAAG,kBAAkB,GAAG;QAAa;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAEPpE,OAAA,CAAC0B,UAAU;QAAAoC,QAAA,GAAC,sBACO,EAAC,GAAG,eACrB9D,OAAA,CAAC4B,UAAU;UAACmD,OAAO,EAAE5C,eAAgB;UAAA2B,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEpB,CAAC;AAAChC,EAAA,CAlJWF,YAAyC;EAAA,QAW/BrC,OAAO;AAAA;AAAAsF,IAAA,GAXjBjD,YAAyC;AAAA,IAAA/B,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAkD,IAAA;AAAAC,YAAA,CAAAjF,EAAA;AAAAiF,YAAA,CAAA/E,GAAA;AAAA+E,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAA3D,GAAA;AAAA2D,YAAA,CAAAzD,GAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}