{"ast": null, "code": "import React,{useState}from'react';import styled from'styled-components';import{INSTRUMENT_TEMPLATES,getInstrumentTemplate}from'../../utils/instrumentTemplates';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SelectorContainer=styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 1.5rem;\n  margin-bottom: 1rem;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n`;const SelectorHeader=styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n`;const SelectorTitle=styled.h4`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.1rem;\n`;const CurrentInstrument=styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1rem;\n  color: #495057;\n  font-weight: 600;\n`;const InstrumentGrid=styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1rem;\n`;const InstrumentCard=styled.button`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 1rem;\n  border: 2px solid ${props=>props.active?'#667eea':'#e9ecef'};\n  border-radius: 12px;\n  background: ${props=>props.active?'rgba(102, 126, 234, 0.1)':'white'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    border-color: #667eea;\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\n  }\n`;const InstrumentEmoji=styled.div`\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n`;const InstrumentName=styled.div`\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 0.25rem;\n`;const InstrumentDescription=styled.div`\n  font-size: 0.8rem;\n  color: #666;\n  text-align: center;\n  line-height: 1.3;\n`;const InstrumentDetails=styled.div`\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-top: 1rem;\n  border: 1px solid #e9ecef;\n`;const DetailRow=styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;const DetailLabel=styled.span`\n  font-weight: 600;\n  color: #495057;\n`;const DetailValue=styled.span`\n  color: #666;\n`;const TuningInfo=styled.div`\n  background: #e3f2fd;\n  border-radius: 6px;\n  padding: 0.5rem;\n  margin-top: 0.5rem;\n  font-size: 0.8rem;\n  color: #1565c0;\n`;const KeySuggestions=styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n  margin-top: 0.5rem;\n`;const KeyChip=styled.span`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0.2rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: 600;\n`;export const InstrumentSelector=_ref=>{let{selectedInstrument,onInstrumentChange,compact=false}=_ref;const[showDetails,setShowDetails]=useState(!compact);const currentTemplate=getInstrumentTemplate(selectedInstrument);const formatRange=template=>{return`${template.range.lowest.note}${template.range.lowest.octave} - ${template.range.highest.note}${template.range.highest.octave}`;};const getClefName=clef=>{const clefNames={treble:'Sol',bass:'Fá',alto:'Dó (3ª linha)',tenor:'Dó (4ª linha)'};return clefNames[clef]||clef;};if(compact){return/*#__PURE__*/_jsxs(SelectorContainer,{children:[/*#__PURE__*/_jsxs(SelectorHeader,{children:[/*#__PURE__*/_jsx(SelectorTitle,{children:\"\\uD83C\\uDFBC Instrumento\"}),/*#__PURE__*/_jsxs(CurrentInstrument,{children:[/*#__PURE__*/_jsx(\"span\",{children:currentTemplate.emoji}),/*#__PURE__*/_jsx(\"span\",{children:currentTemplate.name})]})]}),/*#__PURE__*/_jsx(\"select\",{value:selectedInstrument,onChange:e=>onInstrumentChange(e.target.value),style:{width:'100%',padding:'0.5rem',border:'1px solid #dee2e6',borderRadius:'6px',fontSize:'0.9rem'},children:INSTRUMENT_TEMPLATES.map(template=>/*#__PURE__*/_jsxs(\"option\",{value:template.id,children:[template.emoji,\" \",template.name]},template.id))})]});}return/*#__PURE__*/_jsxs(SelectorContainer,{children:[/*#__PURE__*/_jsxs(SelectorHeader,{children:[/*#__PURE__*/_jsx(SelectorTitle,{children:\"\\uD83C\\uDFBC Selecionar Instrumento\"}),/*#__PURE__*/_jsxs(CurrentInstrument,{children:[/*#__PURE__*/_jsx(\"span\",{children:currentTemplate.emoji}),/*#__PURE__*/_jsx(\"span\",{children:currentTemplate.name})]})]}),/*#__PURE__*/_jsx(InstrumentGrid,{children:INSTRUMENT_TEMPLATES.map(template=>/*#__PURE__*/_jsxs(InstrumentCard,{active:selectedInstrument===template.id,onClick:()=>onInstrumentChange(template.id),children:[/*#__PURE__*/_jsx(InstrumentEmoji,{children:template.emoji}),/*#__PURE__*/_jsx(InstrumentName,{children:template.name}),/*#__PURE__*/_jsx(InstrumentDescription,{children:template.description})]},template.id))}),showDetails&&/*#__PURE__*/_jsxs(InstrumentDetails,{children:[/*#__PURE__*/_jsxs(DetailRow,{children:[/*#__PURE__*/_jsx(DetailLabel,{children:\"Clave:\"}),/*#__PURE__*/_jsx(DetailValue,{children:getClefName(currentTemplate.clef)})]}),/*#__PURE__*/_jsxs(DetailRow,{children:[/*#__PURE__*/_jsx(DetailLabel,{children:\"Extens\\xE3o:\"}),/*#__PURE__*/_jsx(DetailValue,{children:formatRange(currentTemplate)})]}),/*#__PURE__*/_jsxs(DetailRow,{children:[/*#__PURE__*/_jsx(DetailLabel,{children:\"Pautas:\"}),/*#__PURE__*/_jsx(DetailValue,{children:currentTemplate.staffCount===1?'Simples':'Dupla (Sol + Fá)'})]}),currentTemplate.transposition&&/*#__PURE__*/_jsxs(DetailRow,{children:[/*#__PURE__*/_jsx(DetailLabel,{children:\"Transposi\\xE7\\xE3o:\"}),/*#__PURE__*/_jsxs(DetailValue,{children:[currentTemplate.transposition>0?'+':'',currentTemplate.transposition,\" semitons\"]})]}),currentTemplate.tuning&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(DetailRow,{children:[/*#__PURE__*/_jsx(DetailLabel,{children:\"Afina\\xE7\\xE3o:\"}),/*#__PURE__*/_jsx(DetailValue,{children:\"Cordas\"})]}),/*#__PURE__*/_jsxs(TuningInfo,{children:[\"\\uD83C\\uDFB8 \",currentTemplate.tuning.join(' - ')]})]}),/*#__PURE__*/_jsxs(DetailRow,{children:[/*#__PURE__*/_jsx(DetailLabel,{children:\"Tonalidades comuns:\"}),/*#__PURE__*/_jsx(DetailValue,{children:/*#__PURE__*/_jsx(KeySuggestions,{children:currentTemplate.commonKeys.slice(0,6).map(key=>/*#__PURE__*/_jsx(KeyChip,{children:key},key))})})]})]})]});};", "map": {"version": 3, "names": ["React", "useState", "styled", "INSTRUMENT_TEMPLATES", "getInstrumentTemplate", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SelectorContainer", "div", "SelectorHeader", "SelectorTitle", "h4", "CurrentInstrument", "InstrumentGrid", "InstrumentCard", "button", "props", "active", "InstrumentEmoji", "InstrumentName", "InstrumentDescription", "InstrumentDetails", "DetailRow", "DetailLabel", "span", "DetailValue", "TuningInfo", "KeySuggestions", "KeyChip", "InstrumentSelector", "_ref", "selectedInstrument", "onInstrumentChange", "compact", "showDetails", "setShowDetails", "currentTemplate", "formatRange", "template", "range", "lowest", "note", "octave", "highest", "getClefName", "clef", "clefNames", "treble", "bass", "alto", "tenor", "children", "emoji", "name", "value", "onChange", "e", "target", "style", "width", "padding", "border", "borderRadius", "fontSize", "map", "id", "onClick", "description", "staffCount", "transposition", "tuning", "join", "commonKeys", "slice", "key"], "sources": ["D:/Dev/partitura_digital/src/components/InstrumentSelector/InstrumentSelector.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { InstrumentType } from '../../types/music';\nimport { INSTRUMENT_TEMPLATES, getInstrumentTemplate, getInstrumentCategories } from '../../utils/instrumentTemplates';\n\nconst SelectorContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 1.5rem;\n  margin-bottom: 1rem;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n`;\n\nconst SelectorHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n`;\n\nconst SelectorTitle = styled.h4`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.1rem;\n`;\n\nconst CurrentInstrument = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1rem;\n  color: #495057;\n  font-weight: 600;\n`;\n\nconst InstrumentGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1rem;\n`;\n\nconst InstrumentCard = styled.button<{ active?: boolean }>`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 1rem;\n  border: 2px solid ${props => props.active ? '#667eea' : '#e9ecef'};\n  border-radius: 12px;\n  background: ${props => props.active ? 'rgba(102, 126, 234, 0.1)' : 'white'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    border-color: #667eea;\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\n  }\n`;\n\nconst InstrumentEmoji = styled.div`\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n`;\n\nconst InstrumentName = styled.div`\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 0.25rem;\n`;\n\nconst InstrumentDescription = styled.div`\n  font-size: 0.8rem;\n  color: #666;\n  text-align: center;\n  line-height: 1.3;\n`;\n\nconst InstrumentDetails = styled.div`\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-top: 1rem;\n  border: 1px solid #e9ecef;\n`;\n\nconst DetailRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\nconst DetailLabel = styled.span`\n  font-weight: 600;\n  color: #495057;\n`;\n\nconst DetailValue = styled.span`\n  color: #666;\n`;\n\nconst TuningInfo = styled.div`\n  background: #e3f2fd;\n  border-radius: 6px;\n  padding: 0.5rem;\n  margin-top: 0.5rem;\n  font-size: 0.8rem;\n  color: #1565c0;\n`;\n\nconst KeySuggestions = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n  margin-top: 0.5rem;\n`;\n\nconst KeyChip = styled.span`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0.2rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: 600;\n`;\n\ninterface InstrumentSelectorProps {\n  selectedInstrument: InstrumentType;\n  onInstrumentChange: (instrument: InstrumentType) => void;\n  compact?: boolean;\n}\n\nexport const InstrumentSelector: React.FC<InstrumentSelectorProps> = ({\n  selectedInstrument,\n  onInstrumentChange,\n  compact = false\n}) => {\n  const [showDetails, setShowDetails] = useState(!compact);\n  const currentTemplate = getInstrumentTemplate(selectedInstrument);\n\n  const formatRange = (template: typeof currentTemplate) => {\n    return `${template.range.lowest.note}${template.range.lowest.octave} - ${template.range.highest.note}${template.range.highest.octave}`;\n  };\n\n  const getClefName = (clef: string) => {\n    const clefNames = {\n      treble: 'Sol',\n      bass: 'Fá',\n      alto: 'Dó (3ª linha)',\n      tenor: 'Dó (4ª linha)'\n    };\n    return clefNames[clef as keyof typeof clefNames] || clef;\n  };\n\n  if (compact) {\n    return (\n      <SelectorContainer>\n        <SelectorHeader>\n          <SelectorTitle>🎼 Instrumento</SelectorTitle>\n          <CurrentInstrument>\n            <span>{currentTemplate.emoji}</span>\n            <span>{currentTemplate.name}</span>\n          </CurrentInstrument>\n        </SelectorHeader>\n        \n        <select\n          value={selectedInstrument}\n          onChange={(e) => onInstrumentChange(e.target.value as InstrumentType)}\n          style={{\n            width: '100%',\n            padding: '0.5rem',\n            border: '1px solid #dee2e6',\n            borderRadius: '6px',\n            fontSize: '0.9rem'\n          }}\n        >\n          {INSTRUMENT_TEMPLATES.map(template => (\n            <option key={template.id} value={template.id}>\n              {template.emoji} {template.name}\n            </option>\n          ))}\n        </select>\n      </SelectorContainer>\n    );\n  }\n\n  return (\n    <SelectorContainer>\n      <SelectorHeader>\n        <SelectorTitle>🎼 Selecionar Instrumento</SelectorTitle>\n        <CurrentInstrument>\n          <span>{currentTemplate.emoji}</span>\n          <span>{currentTemplate.name}</span>\n        </CurrentInstrument>\n      </SelectorHeader>\n\n      <InstrumentGrid>\n        {INSTRUMENT_TEMPLATES.map(template => (\n          <InstrumentCard\n            key={template.id}\n            active={selectedInstrument === template.id}\n            onClick={() => onInstrumentChange(template.id)}\n          >\n            <InstrumentEmoji>{template.emoji}</InstrumentEmoji>\n            <InstrumentName>{template.name}</InstrumentName>\n            <InstrumentDescription>{template.description}</InstrumentDescription>\n          </InstrumentCard>\n        ))}\n      </InstrumentGrid>\n\n      {showDetails && (\n        <InstrumentDetails>\n          <DetailRow>\n            <DetailLabel>Clave:</DetailLabel>\n            <DetailValue>{getClefName(currentTemplate.clef)}</DetailValue>\n          </DetailRow>\n          \n          <DetailRow>\n            <DetailLabel>Extensão:</DetailLabel>\n            <DetailValue>{formatRange(currentTemplate)}</DetailValue>\n          </DetailRow>\n          \n          <DetailRow>\n            <DetailLabel>Pautas:</DetailLabel>\n            <DetailValue>{currentTemplate.staffCount === 1 ? 'Simples' : 'Dupla (Sol + Fá)'}</DetailValue>\n          </DetailRow>\n          \n          {currentTemplate.transposition && (\n            <DetailRow>\n              <DetailLabel>Transposição:</DetailLabel>\n              <DetailValue>\n                {currentTemplate.transposition > 0 ? '+' : ''}{currentTemplate.transposition} semitons\n              </DetailValue>\n            </DetailRow>\n          )}\n          \n          {currentTemplate.tuning && (\n            <>\n              <DetailRow>\n                <DetailLabel>Afinação:</DetailLabel>\n                <DetailValue>Cordas</DetailValue>\n              </DetailRow>\n              <TuningInfo>\n                🎸 {currentTemplate.tuning.join(' - ')}\n              </TuningInfo>\n            </>\n          )}\n          \n          <DetailRow>\n            <DetailLabel>Tonalidades comuns:</DetailLabel>\n            <DetailValue>\n              <KeySuggestions>\n                {currentTemplate.commonKeys.slice(0, 6).map(key => (\n                  <KeyChip key={key}>{key}</KeyChip>\n                ))}\n              </KeySuggestions>\n            </DetailValue>\n          </DetailRow>\n        </InstrumentDetails>\n      )}\n    </SelectorContainer>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CAEtC,OAASC,oBAAoB,CAAEC,qBAAqB,KAAiC,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvH,KAAM,CAAAC,iBAAiB,CAAGT,MAAM,CAACU,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGX,MAAM,CAACU,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAE,aAAa,CAAGZ,MAAM,CAACa,EAAE;AAC/B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAGd,MAAM,CAACU,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAK,cAAc,CAAGf,MAAM,CAACU,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAM,cAAc,CAAGhB,MAAM,CAACiB,MAA4B;AAC1D;AACA;AACA;AACA;AACA,sBAAsBC,KAAK,EAAIA,KAAK,CAACC,MAAM,CAAG,SAAS,CAAG,SAAS;AACnE;AACA,gBAAgBD,KAAK,EAAIA,KAAK,CAACC,MAAM,CAAG,0BAA0B,CAAG,OAAO;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGpB,MAAM,CAACU,GAAG;AAClC;AACA;AACA,CAAC,CAED,KAAM,CAAAW,cAAc,CAAGrB,MAAM,CAACU,GAAG;AACjC;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAY,qBAAqB,CAAGtB,MAAM,CAACU,GAAG;AACxC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAa,iBAAiB,CAAGvB,MAAM,CAACU,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAc,SAAS,CAAGxB,MAAM,CAACU,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAe,WAAW,CAAGzB,MAAM,CAAC0B,IAAI;AAC/B;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG3B,MAAM,CAAC0B,IAAI;AAC/B;AACA,CAAC,CAED,KAAM,CAAAE,UAAU,CAAG5B,MAAM,CAACU,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAmB,cAAc,CAAG7B,MAAM,CAACU,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAoB,OAAO,CAAG9B,MAAM,CAAC0B,IAAI;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAQD,MAAO,MAAM,CAAAK,kBAAqD,CAAGC,IAAA,EAI/D,IAJgE,CACpEC,kBAAkB,CAClBC,kBAAkB,CAClBC,OAAO,CAAG,KACZ,CAAC,CAAAH,IAAA,CACC,KAAM,CAACI,WAAW,CAAEC,cAAc,CAAC,CAAGtC,QAAQ,CAAC,CAACoC,OAAO,CAAC,CACxD,KAAM,CAAAG,eAAe,CAAGpC,qBAAqB,CAAC+B,kBAAkB,CAAC,CAEjE,KAAM,CAAAM,WAAW,CAAIC,QAAgC,EAAK,CACxD,MAAO,GAAGA,QAAQ,CAACC,KAAK,CAACC,MAAM,CAACC,IAAI,GAAGH,QAAQ,CAACC,KAAK,CAACC,MAAM,CAACE,MAAM,MAAMJ,QAAQ,CAACC,KAAK,CAACI,OAAO,CAACF,IAAI,GAAGH,QAAQ,CAACC,KAAK,CAACI,OAAO,CAACD,MAAM,EAAE,CACxI,CAAC,CAED,KAAM,CAAAE,WAAW,CAAIC,IAAY,EAAK,CACpC,KAAM,CAAAC,SAAS,CAAG,CAChBC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,eACT,CAAC,CACD,MAAO,CAAAJ,SAAS,CAACD,IAAI,CAA2B,EAAIA,IAAI,CAC1D,CAAC,CAED,GAAIZ,OAAO,CAAE,CACX,mBACE7B,KAAA,CAACG,iBAAiB,EAAA4C,QAAA,eAChB/C,KAAA,CAACK,cAAc,EAAA0C,QAAA,eACbjD,IAAA,CAACQ,aAAa,EAAAyC,QAAA,CAAC,0BAAc,CAAe,CAAC,cAC7C/C,KAAA,CAACQ,iBAAiB,EAAAuC,QAAA,eAChBjD,IAAA,SAAAiD,QAAA,CAAOf,eAAe,CAACgB,KAAK,CAAO,CAAC,cACpClD,IAAA,SAAAiD,QAAA,CAAOf,eAAe,CAACiB,IAAI,CAAO,CAAC,EAClB,CAAC,EACN,CAAC,cAEjBnD,IAAA,WACEoD,KAAK,CAAEvB,kBAAmB,CAC1BwB,QAAQ,CAAGC,CAAC,EAAKxB,kBAAkB,CAACwB,CAAC,CAACC,MAAM,CAACH,KAAuB,CAAE,CACtEI,KAAK,CAAE,CACLC,KAAK,CAAE,MAAM,CACbC,OAAO,CAAE,QAAQ,CACjBC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBC,QAAQ,CAAE,QACZ,CAAE,CAAAZ,QAAA,CAEDpD,oBAAoB,CAACiE,GAAG,CAAC1B,QAAQ,eAChClC,KAAA,WAA0BkD,KAAK,CAAEhB,QAAQ,CAAC2B,EAAG,CAAAd,QAAA,EAC1Cb,QAAQ,CAACc,KAAK,CAAC,GAAC,CAACd,QAAQ,CAACe,IAAI,GADpBf,QAAQ,CAAC2B,EAEd,CACT,CAAC,CACI,CAAC,EACQ,CAAC,CAExB,CAEA,mBACE7D,KAAA,CAACG,iBAAiB,EAAA4C,QAAA,eAChB/C,KAAA,CAACK,cAAc,EAAA0C,QAAA,eACbjD,IAAA,CAACQ,aAAa,EAAAyC,QAAA,CAAC,qCAAyB,CAAe,CAAC,cACxD/C,KAAA,CAACQ,iBAAiB,EAAAuC,QAAA,eAChBjD,IAAA,SAAAiD,QAAA,CAAOf,eAAe,CAACgB,KAAK,CAAO,CAAC,cACpClD,IAAA,SAAAiD,QAAA,CAAOf,eAAe,CAACiB,IAAI,CAAO,CAAC,EAClB,CAAC,EACN,CAAC,cAEjBnD,IAAA,CAACW,cAAc,EAAAsC,QAAA,CACZpD,oBAAoB,CAACiE,GAAG,CAAC1B,QAAQ,eAChClC,KAAA,CAACU,cAAc,EAEbG,MAAM,CAAEc,kBAAkB,GAAKO,QAAQ,CAAC2B,EAAG,CAC3CC,OAAO,CAAEA,CAAA,GAAMlC,kBAAkB,CAACM,QAAQ,CAAC2B,EAAE,CAAE,CAAAd,QAAA,eAE/CjD,IAAA,CAACgB,eAAe,EAAAiC,QAAA,CAAEb,QAAQ,CAACc,KAAK,CAAkB,CAAC,cACnDlD,IAAA,CAACiB,cAAc,EAAAgC,QAAA,CAAEb,QAAQ,CAACe,IAAI,CAAiB,CAAC,cAChDnD,IAAA,CAACkB,qBAAqB,EAAA+B,QAAA,CAAEb,QAAQ,CAAC6B,WAAW,CAAwB,CAAC,GANhE7B,QAAQ,CAAC2B,EAOA,CACjB,CAAC,CACY,CAAC,CAEhB/B,WAAW,eACV9B,KAAA,CAACiB,iBAAiB,EAAA8B,QAAA,eAChB/C,KAAA,CAACkB,SAAS,EAAA6B,QAAA,eACRjD,IAAA,CAACqB,WAAW,EAAA4B,QAAA,CAAC,QAAM,CAAa,CAAC,cACjCjD,IAAA,CAACuB,WAAW,EAAA0B,QAAA,CAAEP,WAAW,CAACR,eAAe,CAACS,IAAI,CAAC,CAAc,CAAC,EACrD,CAAC,cAEZzC,KAAA,CAACkB,SAAS,EAAA6B,QAAA,eACRjD,IAAA,CAACqB,WAAW,EAAA4B,QAAA,CAAC,cAAS,CAAa,CAAC,cACpCjD,IAAA,CAACuB,WAAW,EAAA0B,QAAA,CAAEd,WAAW,CAACD,eAAe,CAAC,CAAc,CAAC,EAChD,CAAC,cAEZhC,KAAA,CAACkB,SAAS,EAAA6B,QAAA,eACRjD,IAAA,CAACqB,WAAW,EAAA4B,QAAA,CAAC,SAAO,CAAa,CAAC,cAClCjD,IAAA,CAACuB,WAAW,EAAA0B,QAAA,CAAEf,eAAe,CAACgC,UAAU,GAAK,CAAC,CAAG,SAAS,CAAG,kBAAkB,CAAc,CAAC,EACrF,CAAC,CAEXhC,eAAe,CAACiC,aAAa,eAC5BjE,KAAA,CAACkB,SAAS,EAAA6B,QAAA,eACRjD,IAAA,CAACqB,WAAW,EAAA4B,QAAA,CAAC,qBAAa,CAAa,CAAC,cACxC/C,KAAA,CAACqB,WAAW,EAAA0B,QAAA,EACTf,eAAe,CAACiC,aAAa,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,CAAEjC,eAAe,CAACiC,aAAa,CAAC,WAC/E,EAAa,CAAC,EACL,CACZ,CAEAjC,eAAe,CAACkC,MAAM,eACrBlE,KAAA,CAAAE,SAAA,EAAA6C,QAAA,eACE/C,KAAA,CAACkB,SAAS,EAAA6B,QAAA,eACRjD,IAAA,CAACqB,WAAW,EAAA4B,QAAA,CAAC,iBAAS,CAAa,CAAC,cACpCjD,IAAA,CAACuB,WAAW,EAAA0B,QAAA,CAAC,QAAM,CAAa,CAAC,EACxB,CAAC,cACZ/C,KAAA,CAACsB,UAAU,EAAAyB,QAAA,EAAC,eACP,CAACf,eAAe,CAACkC,MAAM,CAACC,IAAI,CAAC,KAAK,CAAC,EAC5B,CAAC,EACb,CACH,cAEDnE,KAAA,CAACkB,SAAS,EAAA6B,QAAA,eACRjD,IAAA,CAACqB,WAAW,EAAA4B,QAAA,CAAC,qBAAmB,CAAa,CAAC,cAC9CjD,IAAA,CAACuB,WAAW,EAAA0B,QAAA,cACVjD,IAAA,CAACyB,cAAc,EAAAwB,QAAA,CACZf,eAAe,CAACoC,UAAU,CAACC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACT,GAAG,CAACU,GAAG,eAC7CxE,IAAA,CAAC0B,OAAO,EAAAuB,QAAA,CAAYuB,GAAG,EAATA,GAAmB,CAClC,CAAC,CACY,CAAC,CACN,CAAC,EACL,CAAC,EACK,CACpB,EACgB,CAAC,CAExB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}