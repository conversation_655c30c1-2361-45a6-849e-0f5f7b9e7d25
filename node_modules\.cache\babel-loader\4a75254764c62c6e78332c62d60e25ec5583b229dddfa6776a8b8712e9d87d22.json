{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\pages\\\\ScoresList\\\\ScoresList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { ScoreService } from '../../services/scoreService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ScoresContainer = styled.div`\n  padding: 2rem;\n`;\n_c = ScoresContainer;\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n`;\n_c2 = Header;\nconst Title = styled.h1`\n  color: white;\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 600;\n`;\n_c3 = Title;\nconst SearchBar = styled.input`\n  padding: 0.75rem 1rem;\n  border: 1px solid rgba(255,255,255,0.3);\n  border-radius: 12px;\n  background: rgba(255,255,255,0.1);\n  color: white;\n  font-size: 1rem;\n  width: 300px;\n  backdrop-filter: blur(10px);\n  \n  &::placeholder {\n    color: rgba(255,255,255,0.7);\n  }\n  \n  &:focus {\n    outline: none;\n    border-color: rgba(255,255,255,0.6);\n    background: rgba(255,255,255,0.2);\n  }\n`;\n_c4 = SearchBar;\nconst ScoresGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1.5rem;\n`;\n_c5 = ScoresGrid;\nconst ScoreCard = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 1.5rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  \n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: 0 12px 40px rgba(0,0,0,0.15);\n  }\n`;\n_c6 = ScoreCard;\nconst ScoreTitle = styled.h3`\n  margin: 0 0 0.5rem 0;\n  color: #2c3e50;\n  font-size: 1.2rem;\n  font-weight: 600;\n`;\n_c7 = ScoreTitle;\nconst ScoreInfo = styled.div`\n  color: #666;\n  font-size: 0.9rem;\n  margin-bottom: 1rem;\n`;\n_c8 = ScoreInfo;\nconst ScoreActions = styled.div`\n  display: flex;\n  gap: 0.5rem;\n  margin-top: 1rem;\n`;\n_c9 = ScoreActions;\nconst ActionButton = styled.button`\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => {\n  switch (props.variant) {\n    case 'primary':\n      return `\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          &:hover { transform: translateY(-1px); }\n        `;\n    case 'danger':\n      return `\n          background: #e74c3c;\n          color: white;\n          &:hover { background: #c0392b; }\n        `;\n    default:\n      return `\n          background: #f8f9fa;\n          color: #495057;\n          border: 1px solid #dee2e6;\n          &:hover { background: #e9ecef; }\n        `;\n  }\n}}\n`;\n_c0 = ActionButton;\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 4rem 2rem;\n  color: rgba(255,255,255,0.8);\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    font-size: 1.5rem;\n  }\n  \n  p {\n    margin: 0 0 2rem 0;\n    font-size: 1.1rem;\n  }\n`;\n_c1 = EmptyState;\nconst CreateButton = styled.button`\n  padding: 1rem 2rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n  }\n`;\n_c10 = CreateButton;\nconst LoadingState = styled.div`\n  text-align: center;\n  padding: 4rem 2rem;\n  color: rgba(255,255,255,0.8);\n  font-size: 1.2rem;\n`;\n_c11 = LoadingState;\nexport const ScoresList = ({\n  onCreateNew,\n  onEditScore\n}) => {\n  _s();\n  const {\n    currentUser\n  } = useAuth();\n  const [scores, setScores] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  useEffect(() => {\n    loadScores();\n  }, [currentUser]);\n  const loadScores = async () => {\n    if (!currentUser) {\n      setLoading(false);\n      return;\n    }\n    try {\n      const userScores = await ScoreService.getUserScores(currentUser.uid);\n      setScores(userScores);\n    } catch (error) {\n      console.error('Erro ao carregar partituras:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteScore = async scoreId => {\n    if (!window.confirm('Tem certeza que deseja excluir esta partitura?')) {\n      return;\n    }\n    try {\n      await ScoreService.deleteScore(scoreId);\n      setScores(scores.filter(score => score.id !== scoreId));\n    } catch (error) {\n      console.error('Erro ao excluir partitura:', error);\n      alert('Erro ao excluir partitura');\n    }\n  };\n  const handleDuplicateScore = async score => {\n    try {\n      const newTitle = `${score.title} (Cópia)`;\n      await ScoreService.duplicateScore(score.id, newTitle);\n      loadScores(); // Recarregar lista\n    } catch (error) {\n      console.error('Erro ao duplicar partitura:', error);\n      alert('Erro ao duplicar partitura');\n    }\n  };\n  const filteredScores = scores.filter(score => score.title.toLowerCase().includes(searchTerm.toLowerCase()) || score.composer && score.composer.toLowerCase().includes(searchTerm.toLowerCase()));\n  const formatDate = date => {\n    return new Intl.DateTimeFormat('pt-BR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    }).format(date);\n  };\n  if (!currentUser) {\n    return /*#__PURE__*/_jsxDEV(ScoresContainer, {\n      children: /*#__PURE__*/_jsxDEV(EmptyState, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83C\\uDFB5 Suas Partituras\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Fa\\xE7a login para ver e gerenciar suas partituras salvas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(ScoresContainer, {\n      children: /*#__PURE__*/_jsxDEV(LoadingState, {\n        children: \"\\uD83C\\uDFBC Carregando suas partituras...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(ScoresContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"\\uD83C\\uDFB5 Minhas Partituras\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SearchBar, {\n        type: \"text\",\n        placeholder: \"Buscar partituras...\",\n        value: searchTerm,\n        onChange: e => setSearchTerm(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), filteredScores.length === 0 ? /*#__PURE__*/_jsxDEV(EmptyState, {\n      children: scores.length === 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83C\\uDFBC Nenhuma partitura ainda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Comece criando sua primeira partitura musical!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CreateButton, {\n          onClick: onCreateNew,\n          children: \"\\u2795 Criar Nova Partitura\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83D\\uDD0D Nenhuma partitura encontrada\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Tente buscar com outros termos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(ScoresGrid, {\n      children: filteredScores.map(score => /*#__PURE__*/_jsxDEV(ScoreCard, {\n        onClick: () => onEditScore(score.id),\n        children: [/*#__PURE__*/_jsxDEV(ScoreTitle, {\n          children: score.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ScoreInfo, {\n          children: [score.composer && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\uD83C\\uDFAD Compositor: \", score.composer]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 36\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\uD83C\\uDFB9 Tom: \", score.key.note, \" \", score.key.mode]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u23F1\\uFE0F Compasso: \", score.timeSignature.numerator, \"/\", score.timeSignature.denominator]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\uD83D\\uDCC5 Modificado: \", formatDate(score.updatedAt)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ScoreActions, {\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n            variant: \"primary\",\n            onClick: () => onEditScore(score.id),\n            children: \"\\u270F\\uFE0F Editar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n            variant: \"secondary\",\n            onClick: () => handleDuplicateScore(score),\n            children: \"\\uD83D\\uDCCB Duplicar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n            variant: \"danger\",\n            onClick: () => handleDeleteScore(score.id),\n            children: \"\\uD83D\\uDDD1\\uFE0F Excluir\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 15\n        }, this)]\n      }, score.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n_s(ScoresList, \"8AWuS3ZQLXKyB52se0hNUCS018k=\", false, function () {\n  return [useAuth];\n});\n_c12 = ScoresList;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"ScoresContainer\");\n$RefreshReg$(_c2, \"Header\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"SearchBar\");\n$RefreshReg$(_c5, \"ScoresGrid\");\n$RefreshReg$(_c6, \"ScoreCard\");\n$RefreshReg$(_c7, \"ScoreTitle\");\n$RefreshReg$(_c8, \"ScoreInfo\");\n$RefreshReg$(_c9, \"ScoreActions\");\n$RefreshReg$(_c0, \"ActionButton\");\n$RefreshReg$(_c1, \"EmptyState\");\n$RefreshReg$(_c10, \"CreateButton\");\n$RefreshReg$(_c11, \"LoadingState\");\n$RefreshReg$(_c12, \"ScoresList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "useAuth", "ScoreService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ScoresContainer", "div", "_c", "Header", "_c2", "Title", "h1", "_c3", "SearchBar", "input", "_c4", "ScoresGrid", "_c5", "ScoreCard", "_c6", "ScoreTitle", "h3", "_c7", "ScoreInfo", "_c8", "ScoreActions", "_c9", "ActionButton", "button", "props", "variant", "_c0", "EmptyState", "_c1", "CreateButton", "_c10", "LoadingState", "_c11", "ScoresList", "onCreateNew", "onEditScore", "_s", "currentUser", "scores", "setScores", "loading", "setLoading", "searchTerm", "setSearchTerm", "loadScores", "userScores", "getUserScores", "uid", "error", "console", "handleDeleteScore", "scoreId", "window", "confirm", "deleteScore", "filter", "score", "id", "alert", "handleDuplicateScore", "newTitle", "title", "duplicateScore", "filteredScores", "toLowerCase", "includes", "composer", "formatDate", "date", "Intl", "DateTimeFormat", "day", "month", "year", "hour", "minute", "format", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "e", "target", "length", "onClick", "map", "key", "note", "mode", "timeSignature", "numerator", "denominator", "updatedAt", "stopPropagation", "_c12", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/pages/ScoresList/ScoresList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Score } from '../../types/music';\nimport { ScoreService } from '../../services/scoreService';\n\nconst ScoresContainer = styled.div`\n  padding: 2rem;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n`;\n\nconst Title = styled.h1`\n  color: white;\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 600;\n`;\n\nconst SearchBar = styled.input`\n  padding: 0.75rem 1rem;\n  border: 1px solid rgba(255,255,255,0.3);\n  border-radius: 12px;\n  background: rgba(255,255,255,0.1);\n  color: white;\n  font-size: 1rem;\n  width: 300px;\n  backdrop-filter: blur(10px);\n  \n  &::placeholder {\n    color: rgba(255,255,255,0.7);\n  }\n  \n  &:focus {\n    outline: none;\n    border-color: rgba(255,255,255,0.6);\n    background: rgba(255,255,255,0.2);\n  }\n`;\n\nconst ScoresGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1.5rem;\n`;\n\nconst ScoreCard = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 1.5rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  \n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: 0 12px 40px rgba(0,0,0,0.15);\n  }\n`;\n\nconst ScoreTitle = styled.h3`\n  margin: 0 0 0.5rem 0;\n  color: #2c3e50;\n  font-size: 1.2rem;\n  font-weight: 600;\n`;\n\nconst ScoreInfo = styled.div`\n  color: #666;\n  font-size: 0.9rem;\n  margin-bottom: 1rem;\n`;\n\nconst ScoreActions = styled.div`\n  display: flex;\n  gap: 0.5rem;\n  margin-top: 1rem;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => {\n    switch (props.variant) {\n      case 'primary':\n        return `\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          &:hover { transform: translateY(-1px); }\n        `;\n      case 'danger':\n        return `\n          background: #e74c3c;\n          color: white;\n          &:hover { background: #c0392b; }\n        `;\n      default:\n        return `\n          background: #f8f9fa;\n          color: #495057;\n          border: 1px solid #dee2e6;\n          &:hover { background: #e9ecef; }\n        `;\n    }\n  }}\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 4rem 2rem;\n  color: rgba(255,255,255,0.8);\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    font-size: 1.5rem;\n  }\n  \n  p {\n    margin: 0 0 2rem 0;\n    font-size: 1.1rem;\n  }\n`;\n\nconst CreateButton = styled.button`\n  padding: 1rem 2rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n  }\n`;\n\nconst LoadingState = styled.div`\n  text-align: center;\n  padding: 4rem 2rem;\n  color: rgba(255,255,255,0.8);\n  font-size: 1.2rem;\n`;\n\ninterface ScoresListProps {\n  onCreateNew: () => void;\n  onEditScore: (scoreId: string) => void;\n}\n\nexport const ScoresList: React.FC<ScoresListProps> = ({ onCreateNew, onEditScore }) => {\n  const { currentUser } = useAuth();\n  const [scores, setScores] = useState<Score[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  useEffect(() => {\n    loadScores();\n  }, [currentUser]);\n\n  const loadScores = async () => {\n    if (!currentUser) {\n      setLoading(false);\n      return;\n    }\n\n    try {\n      const userScores = await ScoreService.getUserScores(currentUser.uid);\n      setScores(userScores);\n    } catch (error) {\n      console.error('Erro ao carregar partituras:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteScore = async (scoreId: string) => {\n    if (!window.confirm('Tem certeza que deseja excluir esta partitura?')) {\n      return;\n    }\n\n    try {\n      await ScoreService.deleteScore(scoreId);\n      setScores(scores.filter(score => score.id !== scoreId));\n    } catch (error) {\n      console.error('Erro ao excluir partitura:', error);\n      alert('Erro ao excluir partitura');\n    }\n  };\n\n  const handleDuplicateScore = async (score: Score) => {\n    try {\n      const newTitle = `${score.title} (Cópia)`;\n      await ScoreService.duplicateScore(score.id, newTitle);\n      loadScores(); // Recarregar lista\n    } catch (error) {\n      console.error('Erro ao duplicar partitura:', error);\n      alert('Erro ao duplicar partitura');\n    }\n  };\n\n  const filteredScores = scores.filter(score =>\n    score.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (score.composer && score.composer.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  const formatDate = (date: Date) => {\n    return new Intl.DateTimeFormat('pt-BR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    }).format(date);\n  };\n\n  if (!currentUser) {\n    return (\n      <ScoresContainer>\n        <EmptyState>\n          <h3>🎵 Suas Partituras</h3>\n          <p>Faça login para ver e gerenciar suas partituras salvas</p>\n        </EmptyState>\n      </ScoresContainer>\n    );\n  }\n\n  if (loading) {\n    return (\n      <ScoresContainer>\n        <LoadingState>\n          🎼 Carregando suas partituras...\n        </LoadingState>\n      </ScoresContainer>\n    );\n  }\n\n  return (\n    <ScoresContainer>\n      <Header>\n        <Title>🎵 Minhas Partituras</Title>\n        <SearchBar\n          type=\"text\"\n          placeholder=\"Buscar partituras...\"\n          value={searchTerm}\n          onChange={(e) => setSearchTerm(e.target.value)}\n        />\n      </Header>\n\n      {filteredScores.length === 0 ? (\n        <EmptyState>\n          {scores.length === 0 ? (\n            <>\n              <h3>🎼 Nenhuma partitura ainda</h3>\n              <p>Comece criando sua primeira partitura musical!</p>\n              <CreateButton onClick={onCreateNew}>\n                ➕ Criar Nova Partitura\n              </CreateButton>\n            </>\n          ) : (\n            <>\n              <h3>🔍 Nenhuma partitura encontrada</h3>\n              <p>Tente buscar com outros termos</p>\n            </>\n          )}\n        </EmptyState>\n      ) : (\n        <ScoresGrid>\n          {filteredScores.map(score => (\n            <ScoreCard key={score.id} onClick={() => onEditScore(score.id)}>\n              <ScoreTitle>{score.title}</ScoreTitle>\n              <ScoreInfo>\n                {score.composer && <div>🎭 Compositor: {score.composer}</div>}\n                <div>🎹 Tom: {score.key.note} {score.key.mode}</div>\n                <div>⏱️ Compasso: {score.timeSignature.numerator}/{score.timeSignature.denominator}</div>\n                <div>📅 Modificado: {formatDate(score.updatedAt)}</div>\n              </ScoreInfo>\n              \n              <ScoreActions onClick={(e) => e.stopPropagation()}>\n                <ActionButton \n                  variant=\"primary\"\n                  onClick={() => onEditScore(score.id)}\n                >\n                  ✏️ Editar\n                </ActionButton>\n                <ActionButton \n                  variant=\"secondary\"\n                  onClick={() => handleDuplicateScore(score)}\n                >\n                  📋 Duplicar\n                </ActionButton>\n                <ActionButton \n                  variant=\"danger\"\n                  onClick={() => handleDeleteScore(score.id)}\n                >\n                  🗑️ Excluir\n                </ActionButton>\n              </ScoreActions>\n            </ScoreCard>\n          ))}\n        </ScoresGrid>\n      )}\n    </ScoresContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AAEpD,SAASC,YAAY,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,eAAe,GAAGP,MAAM,CAACQ,GAAG;AAClC;AACA,CAAC;AAACC,EAAA,GAFIF,eAAe;AAIrB,MAAMG,MAAM,GAAGV,MAAM,CAACQ,GAAG;AACzB;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,MAAM;AAOZ,MAAME,KAAK,GAAGZ,MAAM,CAACa,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,SAAS,GAAGf,MAAM,CAACgB,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIF,SAAS;AAqBf,MAAMG,UAAU,GAAGlB,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAJID,UAAU;AAMhB,MAAME,SAAS,GAAGpB,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GAZID,SAAS;AAcf,MAAME,UAAU,GAAGtB,MAAM,CAACuB,EAAE;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,UAAU;AAOhB,MAAMG,SAAS,GAAGzB,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GAJID,SAAS;AAMf,MAAME,YAAY,GAAG3B,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,YAAY;AAMlB,MAAME,YAAY,GAAG7B,MAAM,CAAC8B,MAAwD;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAI;EACT,QAAQA,KAAK,CAACC,OAAO;IACnB,KAAK,SAAS;MACZ,OAAO;AACf;AACA;AACA;AACA,SAAS;IACH,KAAK,QAAQ;MACX,OAAO;AACf;AACA;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GAhCIJ,YAAY;AAkClB,MAAMK,UAAU,GAAGlC,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC2B,GAAA,GAdID,UAAU;AAgBhB,MAAME,YAAY,GAAGpC,MAAM,CAAC8B,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,IAAA,GAfID,YAAY;AAiBlB,MAAME,YAAY,GAAGtC,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GALID,YAAY;AAYlB,OAAO,MAAME,UAAqC,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM;IAAEC;EAAY,CAAC,GAAG3C,OAAO,CAAC,CAAC;EACjC,MAAM,CAAC4C,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACdoD,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACP,WAAW,CAAC,CAAC;EAEjB,MAAMO,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACP,WAAW,EAAE;MAChBI,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACF,MAAMI,UAAU,GAAG,MAAMlD,YAAY,CAACmD,aAAa,CAACT,WAAW,CAACU,GAAG,CAAC;MACpER,SAAS,CAACM,UAAU,CAAC;IACvB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMS,iBAAiB,GAAG,MAAOC,OAAe,IAAK;IACnD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MACrE;IACF;IAEA,IAAI;MACF,MAAM1D,YAAY,CAAC2D,WAAW,CAACH,OAAO,CAAC;MACvCZ,SAAS,CAACD,MAAM,CAACiB,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,EAAE,KAAKN,OAAO,CAAC,CAAC;IACzD,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDU,KAAK,CAAC,2BAA2B,CAAC;IACpC;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAG,MAAOH,KAAY,IAAK;IACnD,IAAI;MACF,MAAMI,QAAQ,GAAG,GAAGJ,KAAK,CAACK,KAAK,UAAU;MACzC,MAAMlE,YAAY,CAACmE,cAAc,CAACN,KAAK,CAACC,EAAE,EAAEG,QAAQ,CAAC;MACrDhB,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDU,KAAK,CAAC,4BAA4B,CAAC;IACrC;EACF,CAAC;EAED,MAAMK,cAAc,GAAGzB,MAAM,CAACiB,MAAM,CAACC,KAAK,IACxCA,KAAK,CAACK,KAAK,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,IAC3DR,KAAK,CAACU,QAAQ,IAAIV,KAAK,CAACU,QAAQ,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CACnF,CAAC;EAED,MAAMG,UAAU,GAAIC,IAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;MACtCC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC,CAACC,MAAM,CAACR,IAAI,CAAC;EACjB,CAAC;EAED,IAAI,CAAC/B,WAAW,EAAE;IAChB,oBACExC,OAAA,CAACG,eAAe;MAAA6E,QAAA,eACdhF,OAAA,CAAC8B,UAAU;QAAAkD,QAAA,gBACThF,OAAA;UAAAgF,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BpF,OAAA;UAAAgF,QAAA,EAAG;QAAsD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEtB;EAEA,IAAIzC,OAAO,EAAE;IACX,oBACE3C,OAAA,CAACG,eAAe;MAAA6E,QAAA,eACdhF,OAAA,CAACkC,YAAY;QAAA8C,QAAA,EAAC;MAEd;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEtB;EAEA,oBACEpF,OAAA,CAACG,eAAe;IAAA6E,QAAA,gBACdhF,OAAA,CAACM,MAAM;MAAA0E,QAAA,gBACLhF,OAAA,CAACQ,KAAK;QAAAwE,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnCpF,OAAA,CAACW,SAAS;QACR0E,IAAI,EAAC,MAAM;QACXC,WAAW,EAAC,sBAAsB;QAClCC,KAAK,EAAE1C,UAAW;QAClB2C,QAAQ,EAAGC,CAAC,IAAK3C,aAAa,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAK;MAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAERlB,cAAc,CAACyB,MAAM,KAAK,CAAC,gBAC1B3F,OAAA,CAAC8B,UAAU;MAAAkD,QAAA,EACRvC,MAAM,CAACkD,MAAM,KAAK,CAAC,gBAClB3F,OAAA,CAAAE,SAAA;QAAA8E,QAAA,gBACEhF,OAAA;UAAAgF,QAAA,EAAI;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnCpF,OAAA;UAAAgF,QAAA,EAAG;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrDpF,OAAA,CAACgC,YAAY;UAAC4D,OAAO,EAAEvD,WAAY;UAAA2C,QAAA,EAAC;QAEpC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA,eACf,CAAC,gBAEHpF,OAAA,CAAAE,SAAA;QAAA8E,QAAA,gBACEhF,OAAA;UAAAgF,QAAA,EAAI;QAA+B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxCpF,OAAA;UAAAgF,QAAA,EAAG;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA,eACrC;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,gBAEbpF,OAAA,CAACc,UAAU;MAAAkE,QAAA,EACRd,cAAc,CAAC2B,GAAG,CAAClC,KAAK,iBACvB3D,OAAA,CAACgB,SAAS;QAAgB4E,OAAO,EAAEA,CAAA,KAAMtD,WAAW,CAACqB,KAAK,CAACC,EAAE,CAAE;QAAAoB,QAAA,gBAC7DhF,OAAA,CAACkB,UAAU;UAAA8D,QAAA,EAAErB,KAAK,CAACK;QAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACtCpF,OAAA,CAACqB,SAAS;UAAA2D,QAAA,GACPrB,KAAK,CAACU,QAAQ,iBAAIrE,OAAA;YAAAgF,QAAA,GAAK,2BAAe,EAACrB,KAAK,CAACU,QAAQ;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7DpF,OAAA;YAAAgF,QAAA,GAAK,oBAAQ,EAACrB,KAAK,CAACmC,GAAG,CAACC,IAAI,EAAC,GAAC,EAACpC,KAAK,CAACmC,GAAG,CAACE,IAAI;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDpF,OAAA;YAAAgF,QAAA,GAAK,yBAAa,EAACrB,KAAK,CAACsC,aAAa,CAACC,SAAS,EAAC,GAAC,EAACvC,KAAK,CAACsC,aAAa,CAACE,WAAW;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzFpF,OAAA;YAAAgF,QAAA,GAAK,2BAAe,EAACV,UAAU,CAACX,KAAK,CAACyC,SAAS,CAAC;UAAA;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eAEZpF,OAAA,CAACuB,YAAY;UAACqE,OAAO,EAAGH,CAAC,IAAKA,CAAC,CAACY,eAAe,CAAC,CAAE;UAAArB,QAAA,gBAChDhF,OAAA,CAACyB,YAAY;YACXG,OAAO,EAAC,SAAS;YACjBgE,OAAO,EAAEA,CAAA,KAAMtD,WAAW,CAACqB,KAAK,CAACC,EAAE,CAAE;YAAAoB,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfpF,OAAA,CAACyB,YAAY;YACXG,OAAO,EAAC,WAAW;YACnBgE,OAAO,EAAEA,CAAA,KAAM9B,oBAAoB,CAACH,KAAK,CAAE;YAAAqB,QAAA,EAC5C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfpF,OAAA,CAACyB,YAAY;YACXG,OAAO,EAAC,QAAQ;YAChBgE,OAAO,EAAEA,CAAA,KAAMvC,iBAAiB,CAACM,KAAK,CAACC,EAAE,CAAE;YAAAoB,QAAA,EAC5C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GA5BDzB,KAAK,CAACC,EAAE;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6Bb,CACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAEtB,CAAC;AAAC7C,EAAA,CA1JWH,UAAqC;EAAA,QACxBvC,OAAO;AAAA;AAAAyG,IAAA,GADpBlE,UAAqC;AAAA,IAAA/B,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAmE,IAAA;AAAAC,YAAA,CAAAlG,EAAA;AAAAkG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA7F,GAAA;AAAA6F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA/E,GAAA;AAAA+E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}