{"ast": null, "code": "import { Deferred } from '@firebase/util';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nclass Component {\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(name, instanceFactory, type) {\n    this.name = name;\n    this.instanceFactory = instanceFactory;\n    this.type = type;\n    this.multipleInstances = false;\n    /**\n     * Properties to be added to the service namespace\n     */\n    this.serviceProps = {};\n    this.instantiationMode = \"LAZY\" /* InstantiationMode.LAZY */;\n    this.onInstanceCreated = null;\n  }\n  setInstantiationMode(mode) {\n    this.instantiationMode = mode;\n    return this;\n  }\n  setMultipleInstances(multipleInstances) {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n  setServiceProps(props) {\n    this.serviceProps = props;\n    return this;\n  }\n  setInstanceCreatedCallback(callback) {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nclass Provider {\n  constructor(name, container) {\n    this.name = name;\n    this.container = container;\n    this.component = null;\n    this.instances = new Map();\n    this.instancesDeferred = new Map();\n    this.instancesOptions = new Map();\n    this.onInitCallbacks = new Map();\n  }\n  /**\n   * @param identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   */\n  get(identifier) {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n      if (this.isInitialized(normalizedIdentifier) || this.shouldAutoInitialize()) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n    return this.instancesDeferred.get(normalizedIdentifier).promise;\n  }\n  getImmediate(options) {\n    var _a;\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(options === null || options === void 0 ? void 0 : options.identifier);\n    const optional = (_a = options === null || options === void 0 ? void 0 : options.optional) !== null && _a !== void 0 ? _a : false;\n    if (this.isInitialized(normalizedIdentifier) || this.shouldAutoInitialize()) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/cannot be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n  getComponent() {\n    return this.component;\n  }\n  setComponent(component) {\n    if (component.name !== this.name) {\n      throw Error(`Mismatching Component ${component.name} for Provider ${this.name}.`);\n    }\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n    this.component = component;\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({\n          instanceIdentifier: DEFAULT_ENTRY_NAME\n        });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n  clearInstance() {\n    let identifier = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : DEFAULT_ENTRY_NAME;\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  }\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  async delete() {\n    const services = Array.from(this.instances.values());\n    await Promise.all([...services.filter(service => 'INTERNAL' in service) // legacy services\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    .map(service => service.INTERNAL.delete()), ...services.filter(service => '_delete' in service) // modularized services\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    .map(service => service._delete())]);\n  }\n  isComponentSet() {\n    return this.component != null;\n  }\n  isInitialized() {\n    let identifier = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : DEFAULT_ENTRY_NAME;\n    return this.instances.has(identifier);\n  }\n  getOptions() {\n    let identifier = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : DEFAULT_ENTRY_NAME;\n    return this.instancesOptions.get(identifier) || {};\n  }\n  initialize() {\n    let opts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const {\n      options = {}\n    } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(opts.instanceIdentifier);\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(`${this.name}(${normalizedIdentifier}) has already been initialized`);\n    }\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    });\n    // resolve any pending promise waiting for the service instance\n    for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n    return instance;\n  }\n  /**\n   *\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n   *\n   * @param identifier An optional instance identifier\n   * @returns a function to unregister the callback\n   */\n  onInit(callback, identifier) {\n    var _a;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks = (_a = this.onInitCallbacks.get(normalizedIdentifier)) !== null && _a !== void 0 ? _a : new Set();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n  /**\n   * Invoke onInit callbacks synchronously\n   * @param instance the service instance`\n   */\n  invokeOnInitCallbacks(instance, identifier) {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch (_a) {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n  getOrInitializeService(_ref) {\n    let {\n      instanceIdentifier,\n      options = {}\n    } = _ref;\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance);\n      this.instancesOptions.set(instanceIdentifier, options);\n      /**\n       * Invoke onInit listeners.\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\n       * while onInit listeners are registered by consumers of the provider.\n       */\n      this.invokeOnInitCallbacks(instance, instanceIdentifier);\n      /**\n       * Order is important\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n       * makes `isInitialized()` return true.\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(this.container, instanceIdentifier, instance);\n        } catch (_a) {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n    return instance || null;\n  }\n  normalizeInstanceIdentifier() {\n    let identifier = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : DEFAULT_ENTRY_NAME;\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n  shouldAutoInitialize() {\n    return !!this.component && this.component.instantiationMode !== \"EXPLICIT\" /* InstantiationMode.EXPLICIT */;\n  }\n}\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier) {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\nfunction isComponentEager(component) {\n  return component.instantiationMode === \"EAGER\" /* InstantiationMode.EAGER */;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nclass ComponentContainer {\n  constructor(name) {\n    this.name = name;\n    this.providers = new Map();\n  }\n  /**\n   *\n   * @param component Component being added\n   * @param overwrite When a component with the same name has already been registered,\n   * if overwrite is true: overwrite the existing component with the new component and create a new\n   * provider with the new component. It can be useful in tests where you want to use different mocks\n   * for different tests.\n   * if overwrite is false: throw an exception\n   */\n  addComponent(component) {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(`Component ${component.name} has already been registered with ${this.name}`);\n    }\n    provider.setComponent(component);\n  }\n  addOrOverwriteComponent(component) {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n    this.addComponent(component);\n  }\n  /**\n   * getProvider provides a type safe interface where it can only be called with a field name\n   * present in NameServiceMapping interface.\n   *\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\n   * themselves.\n   */\n  getProvider(name) {\n    if (this.providers.has(name)) {\n      return this.providers.get(name);\n    }\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider(name, this);\n    this.providers.set(name, provider);\n    return provider;\n  }\n  getProviders() {\n    return Array.from(this.providers.values());\n  }\n}\nexport { Component, ComponentContainer, Provider };", "map": {"version": 3, "names": ["Component", "constructor", "name", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "DEFAULT_ENTRY_NAME", "Provider", "container", "component", "instances", "Map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instancesOptions", "onInitCallbacks", "get", "identifier", "normalizedIdentifier", "normalizeInstanceIdentifier", "has", "deferred", "Deferred", "set", "isInitialized", "shouldAutoInitialize", "instance", "getOrInitializeService", "instanceIdentifier", "resolve", "e", "promise", "getImmediate", "options", "optional", "_a", "Error", "getComponent", "setComponent", "isComponentEager", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entries", "clearInstance", "arguments", "length", "undefined", "delete", "services", "Array", "from", "values", "Promise", "all", "filter", "service", "map", "INTERNAL", "_delete", "isComponentSet", "getOptions", "initialize", "opts", "normalizedDeferredIdentifier", "onInit", "existingCallbacks", "Set", "add", "existingInstance", "invokeOnInitCallbacks", "callbacks", "_ref", "normalizeIdentifierForFactory", "ComponentContainer", "providers", "addComponent", "provider", "get<PERSON><PERSON><PERSON>", "addOrOverwriteComponent", "getProviders"], "sources": ["D:\\Dev\\partitura_digital\\node_modules\\@firebase\\component\\src\\component.ts", "D:\\Dev\\partitura_digital\\node_modules\\@firebase\\component\\src\\constants.ts", "D:\\Dev\\partitura_digital\\node_modules\\@firebase\\component\\src\\provider.ts", "D:\\Dev\\partitura_digital\\node_modules\\@firebase\\component\\src\\component_container.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from '@firebase/util';\nimport { ComponentContainer } from './component_container';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport {\n  InitializeOptions,\n  InstantiationMode,\n  Name,\n  NameServiceMapping,\n  OnInitCallBack\n} from './types';\nimport { Component } from './component';\n\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nexport class Provider<T extends Name> {\n  private component: Component<T> | null = null;\n  private readonly instances: Map<string, NameServiceMapping[T]> = new Map();\n  private readonly instancesDeferred: Map<\n    string,\n    Deferred<NameServiceMapping[T]>\n  > = new Map();\n  private readonly instancesOptions: Map<string, Record<string, unknown>> =\n    new Map();\n  private onInitCallbacks: Map<string, Set<OnInitCallBack<T>>> = new Map();\n\n  constructor(\n    private readonly name: T,\n    private readonly container: ComponentContainer\n  ) {}\n\n  /**\n   * @param identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   */\n  get(identifier?: string): Promise<NameServiceMapping[T]> {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred<NameServiceMapping[T]>();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n\n      if (\n        this.isInitialized(normalizedIdentifier) ||\n        this.shouldAutoInitialize()\n      ) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n\n    return this.instancesDeferred.get(normalizedIdentifier)!.promise;\n  }\n\n  /**\n   *\n   * @param options.identifier A provider can provide multiple instances of a service\n   * if this.component.multipleInstances is true.\n   * @param options.optional If optional is false or not provided, the method throws an error when\n   * the service is not immediately available.\n   * If optional is true, the method returns null if the service is not immediately available.\n   */\n  getImmediate(options: {\n    identifier?: string;\n    optional: true;\n  }): NameServiceMapping[T] | null;\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: false;\n  }): NameServiceMapping[T];\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: boolean;\n  }): NameServiceMapping[T] | null {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      options?.identifier\n    );\n    const optional = options?.optional ?? false;\n\n    if (\n      this.isInitialized(normalizedIdentifier) ||\n      this.shouldAutoInitialize()\n    ) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/cannot be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n\n  getComponent(): Component<T> | null {\n    return this.component;\n  }\n\n  setComponent(component: Component<T>): void {\n    if (component.name !== this.name) {\n      throw Error(\n        `Mismatching Component ${component.name} for Provider ${this.name}.`\n      );\n    }\n\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n\n    this.component = component;\n\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        })!;\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n\n  clearInstance(identifier: string = DEFAULT_ENTRY_NAME): void {\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  }\n\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  async delete(): Promise<void> {\n    const services = Array.from(this.instances.values());\n\n    await Promise.all([\n      ...services\n        .filter(service => 'INTERNAL' in service) // legacy services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any).INTERNAL!.delete()),\n      ...services\n        .filter(service => '_delete' in service) // modularized services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any)._delete())\n    ]);\n  }\n\n  isComponentSet(): boolean {\n    return this.component != null;\n  }\n\n  isInitialized(identifier: string = DEFAULT_ENTRY_NAME): boolean {\n    return this.instances.has(identifier);\n  }\n\n  getOptions(identifier: string = DEFAULT_ENTRY_NAME): Record<string, unknown> {\n    return this.instancesOptions.get(identifier) || {};\n  }\n\n  initialize(opts: InitializeOptions = {}): NameServiceMapping[T] {\n    const { options = {} } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      opts.instanceIdentifier\n    );\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(\n        `${this.name}(${normalizedIdentifier}) has already been initialized`\n      );\n    }\n\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    })!;\n\n    // resolve any pending promise waiting for the service instance\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n\n    return instance;\n  }\n\n  /**\n   *\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n   *\n   * @param identifier An optional instance identifier\n   * @returns a function to unregister the callback\n   */\n  onInit(callback: OnInitCallBack<T>, identifier?: string): () => void {\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks =\n      this.onInitCallbacks.get(normalizedIdentifier) ??\n      new Set<OnInitCallBack<T>>();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n\n  /**\n   * Invoke onInit callbacks synchronously\n   * @param instance the service instance`\n   */\n  private invokeOnInitCallbacks(\n    instance: NameServiceMapping[T],\n    identifier: string\n  ): void {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n\n  private getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }: {\n    instanceIdentifier: string;\n    options?: Record<string, unknown>;\n  }): NameServiceMapping[T] | null {\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance!);\n      this.instancesOptions.set(instanceIdentifier, options);\n\n      /**\n       * Invoke onInit listeners.\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\n       * while onInit listeners are registered by consumers of the provider.\n       */\n      this.invokeOnInitCallbacks(instance!, instanceIdentifier);\n\n      /**\n       * Order is important\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n       * makes `isInitialized()` return true.\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(\n            this.container,\n            instanceIdentifier,\n            instance!\n          );\n        } catch {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n\n    return instance || null;\n  }\n\n  private normalizeInstanceIdentifier(\n    identifier: string = DEFAULT_ENTRY_NAME\n  ): string {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n\n  private shouldAutoInitialize(): boolean {\n    return (\n      !!this.component &&\n      this.component.instantiationMode !== InstantiationMode.EXPLICIT\n    );\n  }\n}\n\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier: string): string | undefined {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\n\nfunction isComponentEager<T extends Name>(component: Component<T>): boolean {\n  return component.instantiationMode === InstantiationMode.EAGER;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from './provider';\nimport { Component } from './component';\nimport { Name } from './types';\n\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nexport class ComponentContainer {\n  private readonly providers = new Map<string, Provider<Name>>();\n\n  constructor(private readonly name: string) {}\n\n  /**\n   *\n   * @param component Component being added\n   * @param overwrite When a component with the same name has already been registered,\n   * if overwrite is true: overwrite the existing component with the new component and create a new\n   * provider with the new component. It can be useful in tests where you want to use different mocks\n   * for different tests.\n   * if overwrite is false: throw an exception\n   */\n  addComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(\n        `Component ${component.name} has already been registered with ${this.name}`\n      );\n    }\n\n    provider.setComponent(component);\n  }\n\n  addOrOverwriteComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n\n    this.addComponent(component);\n  }\n\n  /**\n   * getProvider provides a type safe interface where it can only be called with a field name\n   * present in NameServiceMapping interface.\n   *\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\n   * themselves.\n   */\n  getProvider<T extends Name>(name: T): Provider<T> {\n    if (this.providers.has(name)) {\n      return this.providers.get(name) as unknown as Provider<T>;\n    }\n\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider<T>(name, this);\n    this.providers.set(name, provider as unknown as Provider<Name>);\n\n    return provider as Provider<T>;\n  }\n\n  getProviders(): Array<Provider<Name>> {\n    return Array.from(this.providers.values());\n  }\n}\n"], "mappings": ";;AAyBA;;AAEG;MACUA,SAAS;EAWpB;;;;;AAKG;EACHC,YACWC,IAAO,EACPC,eAAmC,EACnCC,IAAmB;IAFnB,IAAI,CAAAF,IAAA,GAAJA,IAAI;IACJ,IAAe,CAAAC,eAAA,GAAfA,eAAe;IACf,IAAI,CAAAC,IAAA,GAAJA,IAAI;IAnBf,IAAiB,CAAAC,iBAAA,GAAG,KAAK;IACzB;;AAEG;IACH,IAAY,CAAAC,YAAA,GAAe,EAAE;IAE7B,KAAAC,iBAAiB,GAA0B;IAE3C,IAAiB,CAAAC,iBAAA,GAAwC,IAAI;;EAc7DC,oBAAoBA,CAACC,IAAuB;IAC1C,IAAI,CAACH,iBAAiB,GAAGG,IAAI;IAC7B,OAAO,IAAI;;EAGbC,oBAAoBA,CAACN,iBAA0B;IAC7C,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;IAC1C,OAAO,IAAI;;EAGbO,eAAeA,CAACC,KAAiB;IAC/B,IAAI,CAACP,YAAY,GAAGO,KAAK;IACzB,OAAO,IAAI;;EAGbC,0BAA0BA,CAACC,QAAsC;IAC/D,IAAI,CAACP,iBAAiB,GAAGO,QAAQ;IACjC,OAAO,IAAI;;AAEd;;ACtED;;;;;;;;;;;;;;;AAeG;AAEI,MAAMC,kBAAkB,GAAG,WAAW;;ACjB7C;;;;;;;;;;;;;;;AAeG;AAcH;;;AAGG;MACUC,QAAQ;EAWnBhB,WACmBA,CAAAC,IAAO,EACPgB,SAA6B;IAD7B,IAAI,CAAAhB,IAAA,GAAJA,IAAI;IACJ,IAAS,CAAAgB,SAAA,GAATA,SAAS;IAZpB,IAAS,CAAAC,SAAA,GAAwB,IAAI;IAC5B,KAAAC,SAAS,GAAuC,IAAIC,GAAG,EAAE;IACzD,KAAAC,iBAAiB,GAG9B,IAAID,GAAG,EAAE;IACI,KAAAE,gBAAgB,GAC/B,IAAIF,GAAG,EAAE;IACH,KAAAG,eAAe,GAAwC,IAAIH,GAAG,EAAE;;EAOxE;;;AAGG;EACHI,GAAGA,CAACC,UAAmB;;IAErB,MAAMC,oBAAoB,GAAG,IAAI,CAACC,2BAA2B,CAACF,UAAU,CAAC;IAEzE,IAAI,CAAC,IAAI,CAACJ,iBAAiB,CAACO,GAAG,CAACF,oBAAoB,CAAC,EAAE;MACrD,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,EAAyB;MACtD,IAAI,CAACT,iBAAiB,CAACU,GAAG,CAACL,oBAAoB,EAAEG,QAAQ,CAAC;MAE1D,IACE,IAAI,CAACG,aAAa,CAACN,oBAAoB,CAAC,IACxC,IAAI,CAACO,oBAAoB,EAAE,EAC3B;;QAEA,IAAI;UACF,MAAMC,QAAQ,GAAG,IAAI,CAACC,sBAAsB,CAAC;YAC3CC,kBAAkB,EAAEV;UACrB,EAAC;UACF,IAAIQ,QAAQ,EAAE;YACZL,QAAQ,CAACQ,OAAO,CAACH,QAAQ,CAAC;;SAE7B,CAAC,OAAOI,CAAC,EAAE;;;;;;IAOhB,OAAO,IAAI,CAACjB,iBAAiB,CAACG,GAAG,CAACE,oBAAoB,CAAE,CAACa,OAAO;;EAmBlEC,YAAYA,CAACC,OAGZ;;;IAEC,MAAMf,oBAAoB,GAAG,IAAI,CAACC,2BAA2B,CAC3Dc,OAAO,KAAP,QAAAA,OAAO,KAAP,kBAAAA,OAAO,CAAEhB,UAAU,CACpB;IACD,MAAMiB,QAAQ,GAAG,CAAAC,EAAA,GAAAF,OAAO,KAAP,QAAAA,OAAO,KAAP,kBAAAA,OAAO,CAAEC,QAAQ,MAAI,QAAAC,EAAA,cAAAA,EAAA,QAAK;IAE3C,IACE,IAAI,CAACX,aAAa,CAACN,oBAAoB,CAAC,IACxC,IAAI,CAACO,oBAAoB,EAAE,EAC3B;MACA,IAAI;QACF,OAAO,IAAI,CAACE,sBAAsB,CAAC;UACjCC,kBAAkB,EAAEV;QACrB,EAAC;OACH,CAAC,OAAOY,CAAC,EAAE;QACV,IAAII,QAAQ,EAAE;UACZ,OAAO,IAAI;SACZ,MAAM;UACL,MAAMJ,CAAC;;;KAGZ,MAAM;;MAEL,IAAII,QAAQ,EAAE;QACZ,OAAO,IAAI;OACZ,MAAM;QACL,MAAME,KAAK,CAAC,WAAW,IAAI,CAAC3C,IAAI,mBAAmB,CAAC;;;;EAK1D4C,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC3B,SAAS;;EAGvB4B,YAAYA,CAAC5B,SAAuB;IAClC,IAAIA,SAAS,CAACjB,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;MAChC,MAAM2C,KAAK,CACT,yBAAyB1B,SAAS,CAACjB,IAAI,iBAAiB,IAAI,CAACA,IAAI,GAAG,CACrE;;IAGH,IAAI,IAAI,CAACiB,SAAS,EAAE;MAClB,MAAM0B,KAAK,CAAC,iBAAiB,IAAI,CAAC3C,IAAI,4BAA4B,CAAC;;IAGrE,IAAI,CAACiB,SAAS,GAAGA,SAAS;;IAG1B,IAAI,CAAC,IAAI,CAACe,oBAAoB,EAAE,EAAE;MAChC;;;IAIF,IAAIc,gBAAgB,CAAC7B,SAAS,CAAC,EAAE;MAC/B,IAAI;QACF,IAAI,CAACiB,sBAAsB,CAAC;UAAEC,kBAAkB,EAAErB;QAAkB,CAAE,CAAC;OACxE,CAAC,OAAOuB,CAAC,EAAE;;;;;;;;;;IAWd,KAAK,MAAM,CACTF,kBAAkB,EAClBY,gBAAgB,CACjB,IAAI,IAAI,CAAC3B,iBAAiB,CAAC4B,OAAO,EAAE,EAAE;MACrC,MAAMvB,oBAAoB,GACxB,IAAI,CAACC,2BAA2B,CAACS,kBAAkB,CAAC;MAEtD,IAAI;;QAEF,MAAMF,QAAQ,GAAG,IAAI,CAACC,sBAAsB,CAAC;UAC3CC,kBAAkB,EAAEV;QACrB,EAAE;QACHsB,gBAAgB,CAACX,OAAO,CAACH,QAAQ,CAAC;OACnC,CAAC,OAAOI,CAAC,EAAE;;;;;;EAOhBY,aAAaA,CAAA,EAAwC;IAAA,IAAvCzB,UAAA,GAAA0B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAqBpC,kBAAkB;IACnD,IAAI,CAACM,iBAAiB,CAACiC,MAAM,CAAC7B,UAAU,CAAC;IACzC,IAAI,CAACH,gBAAgB,CAACgC,MAAM,CAAC7B,UAAU,CAAC;IACxC,IAAI,CAACN,SAAS,CAACmC,MAAM,CAAC7B,UAAU,CAAC;;;;EAKnC,MAAM6B,MAAMA,CAAA;IACV,MAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtC,SAAS,CAACuC,MAAM,EAAE,CAAC;IAEpD,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChB,GAAGL,QAAQ,CACRM,MAAM,CAACC,OAAO,IAAI,UAAU,IAAIA,OAAO,CAAC;;KAExCC,GAAG,CAACD,OAAO,IAAKA,OAAe,CAACE,QAAS,CAACV,MAAM,EAAE,CAAC,EACtD,GAAGC,QAAQ,CACRM,MAAM,CAACC,OAAO,IAAI,SAAS,IAAIA,OAAO,CAAC;;KAEvCC,GAAG,CAACD,OAAO,IAAKA,OAAe,CAACG,OAAO,EAAE,CAAC,CAC9C,CAAC;;EAGJC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAChD,SAAS,IAAI,IAAI;;EAG/Bc,aAAaA,CAAA,EAAwC;IAAA,IAAvCP,UAAA,GAAA0B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAqBpC,kBAAkB;IACnD,OAAO,IAAI,CAACI,SAAS,CAACS,GAAG,CAACH,UAAU,CAAC;;EAGvC0C,UAAUA,CAAA,EAAwC;IAAA,IAAvC1C,UAAA,GAAA0B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAqBpC,kBAAkB;IAChD,OAAO,IAAI,CAACO,gBAAgB,CAACE,GAAG,CAACC,UAAU,CAAC,IAAI,EAAE;;EAGpD2C,UAAUA,CAAA,EAA6B;IAAA,IAA5BC,IAAA,GAAAlB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA0B,EAAE;IACrC,MAAM;MAAEV,OAAO,GAAG;IAAE,CAAE,GAAG4B,IAAI;IAC7B,MAAM3C,oBAAoB,GAAG,IAAI,CAACC,2BAA2B,CAC3D0C,IAAI,CAACjC,kBAAkB,CACxB;IACD,IAAI,IAAI,CAACJ,aAAa,CAACN,oBAAoB,CAAC,EAAE;MAC5C,MAAMkB,KAAK,CACT,GAAG,IAAI,CAAC3C,IAAI,IAAIyB,oBAAoB,gCAAgC,CACrE;;IAGH,IAAI,CAAC,IAAI,CAACwC,cAAc,EAAE,EAAE;MAC1B,MAAMtB,KAAK,CAAC,aAAa,IAAI,CAAC3C,IAAI,8BAA8B,CAAC;;IAGnE,MAAMiC,QAAQ,GAAG,IAAI,CAACC,sBAAsB,CAAC;MAC3CC,kBAAkB,EAAEV,oBAAoB;MACxCe;IACD,EAAE;;IAGH,KAAK,MAAM,CACTL,kBAAkB,EAClBY,gBAAgB,CACjB,IAAI,IAAI,CAAC3B,iBAAiB,CAAC4B,OAAO,EAAE,EAAE;MACrC,MAAMqB,4BAA4B,GAChC,IAAI,CAAC3C,2BAA2B,CAACS,kBAAkB,CAAC;MACtD,IAAIV,oBAAoB,KAAK4C,4BAA4B,EAAE;QACzDtB,gBAAgB,CAACX,OAAO,CAACH,QAAQ,CAAC;;;IAItC,OAAOA,QAAQ;;EAGjB;;;;;;;AAOG;EACHqC,MAAMA,CAACzD,QAA2B,EAAEW,UAAmB;;IACrD,MAAMC,oBAAoB,GAAG,IAAI,CAACC,2BAA2B,CAACF,UAAU,CAAC;IACzE,MAAM+C,iBAAiB,GACrB,CAAA7B,EAAA,OAAI,CAACpB,eAAe,CAACC,GAAG,CAACE,oBAAoB,CAAC,cAAAiB,EAAA,cAAAA,EAAA,GAC9C,IAAI8B,GAAG,EAAqB;IAC9BD,iBAAiB,CAACE,GAAG,CAAC5D,QAAQ,CAAC;IAC/B,IAAI,CAACS,eAAe,CAACQ,GAAG,CAACL,oBAAoB,EAAE8C,iBAAiB,CAAC;IAEjE,MAAMG,gBAAgB,GAAG,IAAI,CAACxD,SAAS,CAACK,GAAG,CAACE,oBAAoB,CAAC;IACjE,IAAIiD,gBAAgB,EAAE;MACpB7D,QAAQ,CAAC6D,gBAAgB,EAAEjD,oBAAoB,CAAC;;IAGlD,OAAO,MAAK;MACV8C,iBAAiB,CAAClB,MAAM,CAACxC,QAAQ,CAAC;IACpC,CAAC;;EAGH;;;AAGG;EACK8D,qBAAqBA,CAC3B1C,QAA+B,EAC/BT,UAAkB;IAElB,MAAMoD,SAAS,GAAG,IAAI,CAACtD,eAAe,CAACC,GAAG,CAACC,UAAU,CAAC;IACtD,IAAI,CAACoD,SAAS,EAAE;MACd;;IAEF,KAAK,MAAM/D,QAAQ,IAAI+D,SAAS,EAAE;MAChC,IAAI;QACF/D,QAAQ,CAACoB,QAAQ,EAAET,UAAU,CAAC;OAC/B,CAAC,OAAAkB,EAAA,EAAM;;;;;EAMJR,sBAAsBA,CAAA2C,IAAA,EAM7B;IAAA,IAN8B;MAC7B1C,kBAAkB;MAClBK,OAAO,GAAG;IAAE,CAIb,GAAAqC,IAAA;IACC,IAAI5C,QAAQ,GAAG,IAAI,CAACf,SAAS,CAACK,GAAG,CAACY,kBAAkB,CAAC;IACrD,IAAI,CAACF,QAAQ,IAAI,IAAI,CAAChB,SAAS,EAAE;MAC/BgB,QAAQ,GAAG,IAAI,CAAChB,SAAS,CAAChB,eAAe,CAAC,IAAI,CAACe,SAAS,EAAE;QACxDmB,kBAAkB,EAAE2C,6BAA6B,CAAC3C,kBAAkB,CAAC;QACrEK;MACD,EAAC;MACF,IAAI,CAACtB,SAAS,CAACY,GAAG,CAACK,kBAAkB,EAAEF,QAAS,CAAC;MACjD,IAAI,CAACZ,gBAAgB,CAACS,GAAG,CAACK,kBAAkB,EAAEK,OAAO,CAAC;MAEtD;;;;AAIG;MACH,IAAI,CAACmC,qBAAqB,CAAC1C,QAAS,EAAEE,kBAAkB,CAAC;MAEzD;;;;AAIG;MACH,IAAI,IAAI,CAAClB,SAAS,CAACX,iBAAiB,EAAE;QACpC,IAAI;UACF,IAAI,CAACW,SAAS,CAACX,iBAAiB,CAC9B,IAAI,CAACU,SAAS,EACdmB,kBAAkB,EAClBF,QAAS,CACV;SACF,CAAC,OAAAS,EAAA,EAAM;;;;;IAMZ,OAAOT,QAAQ,IAAI,IAAI;;EAGjBP,2BAA2BA,CAAA,EACM;IAAA,IAAvCF,UAAA,GAAA0B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAqBpC,kBAAkB;IAEvC,IAAI,IAAI,CAACG,SAAS,EAAE;MAClB,OAAO,IAAI,CAACA,SAAS,CAACd,iBAAiB,GAAGqB,UAAU,GAAGV,kBAAkB;KAC1E,MAAM;MACL,OAAOU,UAAU,CAAC;;;EAIdQ,oBAAoBA,CAAA;IAC1B,OACE,CAAC,CAAC,IAAI,CAACf,SAAS,IAChB,IAAI,CAACA,SAAS,CAACZ,iBAAiB;;AAGrC;AAED;AACA,SAASyE,6BAA6BA,CAACtD,UAAkB;EACvD,OAAOA,UAAU,KAAKV,kBAAkB,GAAGsC,SAAS,GAAG5B,UAAU;AACnE;AAEA,SAASsB,gBAAgBA,CAAiB7B,SAAuB;EAC/D,OAAOA,SAAS,CAACZ,iBAAiB;AACpC;;ACzXA;;;;;;;;;;;;;;;AAeG;AAMH;;AAEG;MACU0E,kBAAkB;EAG7BhF,YAA6BC,IAAY;IAAZ,IAAI,CAAAA,IAAA,GAAJA,IAAI;IAFhB,KAAAgF,SAAS,GAAG,IAAI7D,GAAG,EAA0B;;EAI9D;;;;;;;;AAQG;EACH8D,YAAYA,CAAiBhE,SAAuB;IAClD,MAAMiE,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAClE,SAAS,CAACjB,IAAI,CAAC;IACjD,IAAIkF,QAAQ,CAACjB,cAAc,EAAE,EAAE;MAC7B,MAAM,IAAItB,KAAK,CACb,aAAa1B,SAAS,CAACjB,IAAI,qCAAqC,IAAI,CAACA,IAAI,EAAE,CAC5E;;IAGHkF,QAAQ,CAACrC,YAAY,CAAC5B,SAAS,CAAC;;EAGlCmE,uBAAuBA,CAAiBnE,SAAuB;IAC7D,MAAMiE,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAClE,SAAS,CAACjB,IAAI,CAAC;IACjD,IAAIkF,QAAQ,CAACjB,cAAc,EAAE,EAAE;;MAE7B,IAAI,CAACe,SAAS,CAAC3B,MAAM,CAACpC,SAAS,CAACjB,IAAI,CAAC;;IAGvC,IAAI,CAACiF,YAAY,CAAChE,SAAS,CAAC;;EAG9B;;;;;;AAMG;EACHkE,WAAWA,CAAiBnF,IAAO;IACjC,IAAI,IAAI,CAACgF,SAAS,CAACrD,GAAG,CAAC3B,IAAI,CAAC,EAAE;MAC5B,OAAO,IAAI,CAACgF,SAAS,CAACzD,GAAG,CAACvB,IAAI,CAA2B;;;IAI3D,MAAMkF,QAAQ,GAAG,IAAInE,QAAQ,CAAIf,IAAI,EAAE,IAAI,CAAC;IAC5C,IAAI,CAACgF,SAAS,CAAClD,GAAG,CAAC9B,IAAI,EAAEkF,QAAqC,CAAC;IAE/D,OAAOA,QAAuB;;EAGhCG,YAAYA,CAAA;IACV,OAAO9B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACwB,SAAS,CAACvB,MAAM,EAAE,CAAC;;AAE7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}