import React, { useState } from 'react';
import styled from 'styled-components';
import { InstrumentType } from '../../types/music';
import { INSTRUMENT_TEMPLATES, getInstrumentTemplate, getInstrumentCategories } from '../../utils/instrumentTemplates';

const SelectorContainer = styled.div`
  background: rgba(255,255,255,0.95);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
`;

const SelectorHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const SelectorTitle = styled.h4`
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
`;

const CurrentInstrument = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  color: #495057;
  font-weight: 600;
`;

const InstrumentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
`;

const InstrumentCard = styled.button<{ active?: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 2px solid ${props => props.active ? '#667eea' : '#e9ecef'};
  border-radius: 12px;
  background: ${props => props.active ? 'rgba(102, 126, 234, 0.1)' : 'white'};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
  }
`;

const InstrumentEmoji = styled.div`
  font-size: 2rem;
  margin-bottom: 0.5rem;
`;

const InstrumentName = styled.div`
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
`;

const InstrumentDescription = styled.div`
  font-size: 0.8rem;
  color: #666;
  text-align: center;
  line-height: 1.3;
`;

const InstrumentDetails = styled.div`
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  border: 1px solid #e9ecef;
`;

const DetailRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const DetailLabel = styled.span`
  font-weight: 600;
  color: #495057;
`;

const DetailValue = styled.span`
  color: #666;
`;

const TuningInfo = styled.div`
  background: #e3f2fd;
  border-radius: 6px;
  padding: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: #1565c0;
`;

const KeySuggestions = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.5rem;
`;

const KeyChip = styled.span`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
`;

interface InstrumentSelectorProps {
  selectedInstrument: InstrumentType;
  onInstrumentChange: (instrument: InstrumentType) => void;
  compact?: boolean;
}

export const InstrumentSelector: React.FC<InstrumentSelectorProps> = ({
  selectedInstrument,
  onInstrumentChange,
  compact = false
}) => {
  const [showDetails, setShowDetails] = useState(!compact);
  const currentTemplate = getInstrumentTemplate(selectedInstrument);

  const formatRange = (template: typeof currentTemplate) => {
    return `${template.range.lowest.note}${template.range.lowest.octave} - ${template.range.highest.note}${template.range.highest.octave}`;
  };

  const getClefName = (clef: string) => {
    const clefNames = {
      treble: 'Sol',
      bass: 'Fá',
      alto: 'Dó (3ª linha)',
      tenor: 'Dó (4ª linha)'
    };
    return clefNames[clef as keyof typeof clefNames] || clef;
  };

  if (compact) {
    return (
      <SelectorContainer>
        <SelectorHeader>
          <SelectorTitle>🎼 Instrumento</SelectorTitle>
          <CurrentInstrument>
            <span>{currentTemplate.emoji}</span>
            <span>{currentTemplate.name}</span>
          </CurrentInstrument>
        </SelectorHeader>
        
        <select
          value={selectedInstrument}
          onChange={(e) => onInstrumentChange(e.target.value as InstrumentType)}
          style={{
            width: '100%',
            padding: '0.5rem',
            border: '1px solid #dee2e6',
            borderRadius: '6px',
            fontSize: '0.9rem'
          }}
        >
          {INSTRUMENT_TEMPLATES.map(template => (
            <option key={template.id} value={template.id}>
              {template.emoji} {template.name}
            </option>
          ))}
        </select>
      </SelectorContainer>
    );
  }

  return (
    <SelectorContainer>
      <SelectorHeader>
        <SelectorTitle>🎼 Selecionar Instrumento</SelectorTitle>
        <CurrentInstrument>
          <span>{currentTemplate.emoji}</span>
          <span>{currentTemplate.name}</span>
        </CurrentInstrument>
      </SelectorHeader>

      <InstrumentGrid>
        {INSTRUMENT_TEMPLATES.map(template => (
          <InstrumentCard
            key={template.id}
            active={selectedInstrument === template.id}
            onClick={() => onInstrumentChange(template.id)}
          >
            <InstrumentEmoji>{template.emoji}</InstrumentEmoji>
            <InstrumentName>{template.name}</InstrumentName>
            <InstrumentDescription>{template.description}</InstrumentDescription>
          </InstrumentCard>
        ))}
      </InstrumentGrid>

      {showDetails && (
        <InstrumentDetails>
          <DetailRow>
            <DetailLabel>Clave:</DetailLabel>
            <DetailValue>{getClefName(currentTemplate.clef)}</DetailValue>
          </DetailRow>
          
          <DetailRow>
            <DetailLabel>Extensão:</DetailLabel>
            <DetailValue>{formatRange(currentTemplate)}</DetailValue>
          </DetailRow>
          
          <DetailRow>
            <DetailLabel>Pautas:</DetailLabel>
            <DetailValue>{currentTemplate.staffCount === 1 ? 'Simples' : 'Dupla (Sol + Fá)'}</DetailValue>
          </DetailRow>
          
          {currentTemplate.transposition && (
            <DetailRow>
              <DetailLabel>Transposição:</DetailLabel>
              <DetailValue>
                {currentTemplate.transposition > 0 ? '+' : ''}{currentTemplate.transposition} semitons
              </DetailValue>
            </DetailRow>
          )}
          
          {currentTemplate.tuning && (
            <>
              <DetailRow>
                <DetailLabel>Afinação:</DetailLabel>
                <DetailValue>Cordas</DetailValue>
              </DetailRow>
              <TuningInfo>
                🎸 {currentTemplate.tuning.join(' - ')}
              </TuningInfo>
            </>
          )}
          
          <DetailRow>
            <DetailLabel>Tonalidades comuns:</DetailLabel>
            <DetailValue>
              <KeySuggestions>
                {currentTemplate.commonKeys.slice(0, 6).map(key => (
                  <KeyChip key={key}>{key}</KeyChip>
                ))}
              </KeySuggestions>
            </DetailValue>
          </DetailRow>
        </InstrumentDetails>
      )}
    </SelectorContainer>
  );
};
