{"ast": null, "code": "/* global __react_refresh_library__ */\n\nconst safeThis = require('core-js-pure/features/global-this');\nconst RefreshRuntime = require('react-refresh/runtime');\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof safeThis !== 'undefined') {\n    var $RefreshInjected$ = '__reactRefreshInjected';\n    // Namespace the injected flag (if necessary) for monorepo compatibility\n    if (typeof __react_refresh_library__ !== 'undefined' && __react_refresh_library__) {\n      $RefreshInjected$ += '_' + __react_refresh_library__;\n    }\n\n    // Only inject the runtime if it hasn't been injected\n    if (!safeThis[$RefreshInjected$]) {\n      // Inject refresh runtime into global scope\n      RefreshRuntime.injectIntoGlobalHook(safeThis);\n\n      // Mark the runtime as injected to prevent double-injection\n      safeThis[$RefreshInjected$] = true;\n    }\n  }\n}", "map": {"version": 3, "names": ["safeThis", "require", "RefreshRuntime", "process", "env", "NODE_ENV", "$RefreshInjected$", "__react_refresh_library__", "injectIntoGlobalHook"], "sources": ["D:/Dev/partitura_digital/node_modules/@pmmmwh/react-refresh-webpack-plugin/client/ReactRefreshEntry.js"], "sourcesContent": ["/* global __react_refresh_library__ */\n\nconst safeThis = require('core-js-pure/features/global-this');\nconst RefreshRuntime = require('react-refresh/runtime');\n\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof safeThis !== 'undefined') {\n    var $RefreshInjected$ = '__reactRefreshInjected';\n    // Namespace the injected flag (if necessary) for monorepo compatibility\n    if (typeof __react_refresh_library__ !== 'undefined' && __react_refresh_library__) {\n      $RefreshInjected$ += '_' + __react_refresh_library__;\n    }\n\n    // Only inject the runtime if it hasn't been injected\n    if (!safeThis[$RefreshInjected$]) {\n      // Inject refresh runtime into global scope\n      RefreshRuntime.injectIntoGlobalHook(safeThis);\n\n      // Mark the runtime as injected to prevent double-injection\n      safeThis[$RefreshInjected$] = true;\n    }\n  }\n}\n"], "mappings": "AAAA;;AAEA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,mCAAmC,CAAC;AAC7D,MAAMC,cAAc,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAEvD,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,IAAI,OAAOL,QAAQ,KAAK,WAAW,EAAE;IACnC,IAAIM,iBAAiB,GAAG,wBAAwB;IAChD;IACA,IAAI,OAAOC,yBAAyB,KAAK,WAAW,IAAIA,yBAAyB,EAAE;MACjFD,iBAAiB,IAAI,GAAG,GAAGC,yBAAyB;IACtD;;IAEA;IACA,IAAI,CAACP,QAAQ,CAACM,iBAAiB,CAAC,EAAE;MAChC;MACAJ,cAAc,CAACM,oBAAoB,CAACR,QAAQ,CAAC;;MAE7C;MACAA,QAAQ,CAACM,iBAAiB,CAAC,GAAG,IAAI;IACpC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}