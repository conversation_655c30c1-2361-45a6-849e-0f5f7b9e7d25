{"ast": null, "code": "import React,{useState}from'react';import{AuthProvider,useAuth}from'./contexts/AuthContext';import{LoginPage}from'./pages/Auth/LoginPage';import{RegisterPage}from'./pages/Auth/RegisterPage';import{Layout}from'./components/Layout/Layout';import{ScoreEditor}from'./components/ScoreEditor/ScoreEditor';import{ScoresList}from'./pages/ScoresList/ScoresList';import{NewScorePage}from'./pages/NewScore/NewScorePage';import styled from'styled-components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoadingContainer=styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  font-size: 1.2rem;\n  color: #666;\n`;const HomePage=styled.div`\n  text-align: center;\n  padding: 2rem;\n`;const WelcomeTitle=styled.h1`\n  color: #2c3e50;\n  margin-bottom: 1rem;\n`;const WelcomeText=styled.p`\n  color: #666;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  max-width: 600px;\n  margin: 0 auto 2rem auto;\n`;const GuestNotice=styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin: 2rem auto;\n  max-width: 500px;\n  text-align: center;\n  box-shadow: 0 4px 6px rgba(0,0,0,0.1);\n\n  p {\n    margin: 0.5rem 0;\n    color: #856404;\n  }\n\n  strong {\n    color: #533f03;\n  }\n`;// Componente principal da aplicação\nconst MainApp=()=>{const[currentPage,setCurrentPage]=useState('home');const[authMode,setAuthMode]=useState('login');const[editingScoreId,setEditingScoreId]=useState(null);const[scoreConfig,setScoreConfig]=useState(null);const{currentUser}=useAuth();const renderPage=()=>{switch(currentPage){case'home':return/*#__PURE__*/_jsxs(HomePage,{children:[/*#__PURE__*/_jsx(WelcomeTitle,{children:\"\\uD83C\\uDFB5 Bem-vindo ao Partitura Digital!\"}),/*#__PURE__*/_jsx(WelcomeText,{children:\"Crie e edite suas partituras musicais de forma intuitiva. Adicione notas, acordes, letras e muito mais. Visualize suas composi\\xE7\\xF5es tanto em nota\\xE7\\xE3o tradicional quanto em cifras.\"}),!currentUser&&/*#__PURE__*/_jsxs(GuestNotice,{children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"\\uD83D\\uDCA1 \",/*#__PURE__*/_jsx(\"strong\",{children:\"Modo Visitante:\"}),\" Voc\\xEA pode usar o editor, mas suas partituras n\\xE3o ser\\xE3o salvas.\"]}),/*#__PURE__*/_jsx(\"p\",{children:\"Fa\\xE7a login para salvar suas cria\\xE7\\xF5es!\"})]})]});case'scores':return/*#__PURE__*/_jsx(ScoresList,{onCreateNew:()=>{setEditingScoreId(null);setScoreConfig(null);setCurrentPage('new-score-setup');},onEditScore:scoreId=>{setEditingScoreId(scoreId);setScoreConfig(null);setCurrentPage('score-editor');}});case'new-score':// Redirecionar para setup se não há configuração\nif(!scoreConfig){setCurrentPage('new-score-setup');return null;}return/*#__PURE__*/_jsx(ScoreEditor,{scoreId:editingScoreId||undefined,initialConfig:scoreConfig});case'new-score-setup':return/*#__PURE__*/_jsx(NewScorePage,{onCreateScore:config=>{setScoreConfig(config);setCurrentPage('new-score');},onCancel:()=>setCurrentPage('home')});case'score-editor':return/*#__PURE__*/_jsx(ScoreEditor,{scoreId:editingScoreId||undefined});case'auth':return authMode==='login'?/*#__PURE__*/_jsx(LoginPage,{onSwitchToRegister:()=>setAuthMode('register')}):/*#__PURE__*/_jsx(RegisterPage,{onSwitchToLogin:()=>setAuthMode('login')});default:return/*#__PURE__*/_jsx(\"div\",{children:\"P\\xE1gina n\\xE3o encontrada\"});}};const handleNavigate=page=>{if(page==='login'){setAuthMode('login');setCurrentPage('auth');}else if(page==='register'){setAuthMode('register');setCurrentPage('auth');}else{setCurrentPage(page);}};return/*#__PURE__*/_jsx(Layout,{currentPage:currentPage,onNavigate:handleNavigate,showAuthInSidebar:!currentUser,children:renderPage()});};// Componente principal que gerencia o estado de loading\nconst AppContent=()=>{const{loading}=useAuth();if(loading){return/*#__PURE__*/_jsx(LoadingContainer,{children:\"Carregando...\"});}return/*#__PURE__*/_jsx(MainApp,{});};// App principal com provider\nfunction App(){return/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(AppContent,{})});}export default App;", "map": {"version": 3, "names": ["React", "useState", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "LoginPage", "RegisterPage", "Layout", "ScoreEditor", "ScoresList", "NewScorePage", "styled", "jsx", "_jsx", "jsxs", "_jsxs", "LoadingContainer", "div", "HomePage", "WelcomeTitle", "h1", "WelcomeText", "p", "GuestNotice", "MainApp", "currentPage", "setCurrentPage", "authMode", "setAuthMode", "editingScoreId", "setEditingScoreId", "scoreConfig", "setScoreConfig", "currentUser", "renderPage", "children", "onCreateNew", "onEditScore", "scoreId", "undefined", "initialConfig", "onCreateScore", "config", "onCancel", "onSwitchToRegister", "onSwitchToLogin", "handleNavigate", "page", "onNavigate", "showAuthInSidebar", "A<PERSON><PERSON><PERSON>nt", "loading", "App"], "sources": ["D:/Dev/partitura_digital/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { LoginPage } from './pages/Auth/LoginPage';\nimport { RegisterPage } from './pages/Auth/RegisterPage';\nimport { Layout } from './components/Layout/Layout';\nimport { ScoreEditor } from './components/ScoreEditor/ScoreEditor';\nimport { ScoresList } from './pages/ScoresList/ScoresList';\nimport { NewScorePage, ScoreConfig } from './pages/NewScore/NewScorePage';\nimport styled from 'styled-components';\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  font-size: 1.2rem;\n  color: #666;\n`;\n\nconst HomePage = styled.div`\n  text-align: center;\n  padding: 2rem;\n`;\n\nconst WelcomeTitle = styled.h1`\n  color: #2c3e50;\n  margin-bottom: 1rem;\n`;\n\nconst WelcomeText = styled.p`\n  color: #666;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  max-width: 600px;\n  margin: 0 auto 2rem auto;\n`;\n\nconst GuestNotice = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin: 2rem auto;\n  max-width: 500px;\n  text-align: center;\n  box-shadow: 0 4px 6px rgba(0,0,0,0.1);\n\n  p {\n    margin: 0.5rem 0;\n    color: #856404;\n  }\n\n  strong {\n    color: #533f03;\n  }\n`;\n\n// Componente principal da aplicação\nconst MainApp: React.FC = () => {\n  const [currentPage, setCurrentPage] = useState('home');\n  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');\n  const [editingScoreId, setEditingScoreId] = useState<string | null>(null);\n  const [scoreConfig, setScoreConfig] = useState<ScoreConfig | null>(null);\n  const { currentUser } = useAuth();\n\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return (\n          <HomePage>\n            <WelcomeTitle>🎵 Bem-vindo ao Partitura Digital!</WelcomeTitle>\n            <WelcomeText>\n              Crie e edite suas partituras musicais de forma intuitiva.\n              Adicione notas, acordes, letras e muito mais.\n              Visualize suas composições tanto em notação tradicional quanto em cifras.\n            </WelcomeText>\n            {!currentUser && (\n              <GuestNotice>\n                <p>💡 <strong>Modo Visitante:</strong> Você pode usar o editor, mas suas partituras não serão salvas.</p>\n                <p>Faça login para salvar suas criações!</p>\n              </GuestNotice>\n            )}\n          </HomePage>\n        );\n      case 'scores':\n        return (\n          <ScoresList\n            onCreateNew={() => {\n              setEditingScoreId(null);\n              setScoreConfig(null);\n              setCurrentPage('new-score-setup');\n            }}\n            onEditScore={(scoreId) => {\n              setEditingScoreId(scoreId);\n              setScoreConfig(null);\n              setCurrentPage('score-editor');\n            }}\n          />\n        );\n      case 'new-score':\n        // Redirecionar para setup se não há configuração\n        if (!scoreConfig) {\n          setCurrentPage('new-score-setup');\n          return null;\n        }\n        return <ScoreEditor scoreId={editingScoreId || undefined} initialConfig={scoreConfig} />;\n      case 'new-score-setup':\n        return (\n          <NewScorePage\n            onCreateScore={(config) => {\n              setScoreConfig(config);\n              setCurrentPage('new-score');\n            }}\n            onCancel={() => setCurrentPage('home')}\n          />\n        );\n      case 'score-editor':\n        return <ScoreEditor scoreId={editingScoreId || undefined} />;\n      case 'auth':\n        return authMode === 'login' ? (\n          <LoginPage onSwitchToRegister={() => setAuthMode('register')} />\n        ) : (\n          <RegisterPage onSwitchToLogin={() => setAuthMode('login')} />\n        );\n      default:\n        return <div>Página não encontrada</div>;\n    }\n  };\n\n  const handleNavigate = (page: string) => {\n    if (page === 'login') {\n      setAuthMode('login');\n      setCurrentPage('auth');\n    } else if (page === 'register') {\n      setAuthMode('register');\n      setCurrentPage('auth');\n    } else {\n      setCurrentPage(page);\n    }\n  };\n\n  return (\n    <Layout\n      currentPage={currentPage}\n      onNavigate={handleNavigate}\n      showAuthInSidebar={!currentUser}\n    >\n      {renderPage()}\n    </Layout>\n  );\n};\n\n// Componente principal que gerencia o estado de loading\nconst AppContent: React.FC = () => {\n  const { loading } = useAuth();\n\n  if (loading) {\n    return <LoadingContainer>Carregando...</LoadingContainer>;\n  }\n\n  return <MainApp />;\n};\n\n// App principal com provider\nfunction App() {\n  return (\n    <AuthProvider>\n      <AppContent />\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,YAAY,CAAEC,OAAO,KAAQ,wBAAwB,CAC9D,OAASC,SAAS,KAAQ,wBAAwB,CAClD,OAASC,YAAY,KAAQ,2BAA2B,CACxD,OAASC,MAAM,KAAQ,4BAA4B,CACnD,OAASC,WAAW,KAAQ,sCAAsC,CAClE,OAASC,UAAU,KAAQ,+BAA+B,CAC1D,OAASC,YAAY,KAAqB,+BAA+B,CACzE,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvC,KAAM,CAAAC,gBAAgB,CAAGL,MAAM,CAACM,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGP,MAAM,CAACM,GAAG;AAC3B;AACA;AACA,CAAC,CAED,KAAM,CAAAE,YAAY,CAAGR,MAAM,CAACS,EAAE;AAC9B;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGV,MAAM,CAACW,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGZ,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAO,OAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGxB,QAAQ,CAAC,MAAM,CAAC,CACtD,KAAM,CAACyB,QAAQ,CAAEC,WAAW,CAAC,CAAG1B,QAAQ,CAAuB,OAAO,CAAC,CACvE,KAAM,CAAC2B,cAAc,CAAEC,iBAAiB,CAAC,CAAG5B,QAAQ,CAAgB,IAAI,CAAC,CACzE,KAAM,CAAC6B,WAAW,CAAEC,cAAc,CAAC,CAAG9B,QAAQ,CAAqB,IAAI,CAAC,CACxE,KAAM,CAAE+B,WAAY,CAAC,CAAG7B,OAAO,CAAC,CAAC,CAEjC,KAAM,CAAA8B,UAAU,CAAGA,CAAA,GAAM,CACvB,OAAQT,WAAW,EACjB,IAAK,MAAM,CACT,mBACEV,KAAA,CAACG,QAAQ,EAAAiB,QAAA,eACPtB,IAAA,CAACM,YAAY,EAAAgB,QAAA,CAAC,8CAAkC,CAAc,CAAC,cAC/DtB,IAAA,CAACQ,WAAW,EAAAc,QAAA,CAAC,+LAIb,CAAa,CAAC,CACb,CAACF,WAAW,eACXlB,KAAA,CAACQ,WAAW,EAAAY,QAAA,eACVpB,KAAA,MAAAoB,QAAA,EAAG,eAAG,cAAAtB,IAAA,WAAAsB,QAAA,CAAQ,iBAAe,CAAQ,CAAC,2EAA+D,EAAG,CAAC,cACzGtB,IAAA,MAAAsB,QAAA,CAAG,gDAAqC,CAAG,CAAC,EACjC,CACd,EACO,CAAC,CAEf,IAAK,QAAQ,CACX,mBACEtB,IAAA,CAACJ,UAAU,EACT2B,WAAW,CAAEA,CAAA,GAAM,CACjBN,iBAAiB,CAAC,IAAI,CAAC,CACvBE,cAAc,CAAC,IAAI,CAAC,CACpBN,cAAc,CAAC,iBAAiB,CAAC,CACnC,CAAE,CACFW,WAAW,CAAGC,OAAO,EAAK,CACxBR,iBAAiB,CAACQ,OAAO,CAAC,CAC1BN,cAAc,CAAC,IAAI,CAAC,CACpBN,cAAc,CAAC,cAAc,CAAC,CAChC,CAAE,CACH,CAAC,CAEN,IAAK,WAAW,CACd;AACA,GAAI,CAACK,WAAW,CAAE,CAChBL,cAAc,CAAC,iBAAiB,CAAC,CACjC,MAAO,KAAI,CACb,CACA,mBAAOb,IAAA,CAACL,WAAW,EAAC8B,OAAO,CAAET,cAAc,EAAIU,SAAU,CAACC,aAAa,CAAET,WAAY,CAAE,CAAC,CAC1F,IAAK,iBAAiB,CACpB,mBACElB,IAAA,CAACH,YAAY,EACX+B,aAAa,CAAGC,MAAM,EAAK,CACzBV,cAAc,CAACU,MAAM,CAAC,CACtBhB,cAAc,CAAC,WAAW,CAAC,CAC7B,CAAE,CACFiB,QAAQ,CAAEA,CAAA,GAAMjB,cAAc,CAAC,MAAM,CAAE,CACxC,CAAC,CAEN,IAAK,cAAc,CACjB,mBAAOb,IAAA,CAACL,WAAW,EAAC8B,OAAO,CAAET,cAAc,EAAIU,SAAU,CAAE,CAAC,CAC9D,IAAK,MAAM,CACT,MAAO,CAAAZ,QAAQ,GAAK,OAAO,cACzBd,IAAA,CAACR,SAAS,EAACuC,kBAAkB,CAAEA,CAAA,GAAMhB,WAAW,CAAC,UAAU,CAAE,CAAE,CAAC,cAEhEf,IAAA,CAACP,YAAY,EAACuC,eAAe,CAAEA,CAAA,GAAMjB,WAAW,CAAC,OAAO,CAAE,CAAE,CAC7D,CACH,QACE,mBAAOf,IAAA,QAAAsB,QAAA,CAAK,6BAAqB,CAAK,CAAC,CAC3C,CACF,CAAC,CAED,KAAM,CAAAW,cAAc,CAAIC,IAAY,EAAK,CACvC,GAAIA,IAAI,GAAK,OAAO,CAAE,CACpBnB,WAAW,CAAC,OAAO,CAAC,CACpBF,cAAc,CAAC,MAAM,CAAC,CACxB,CAAC,IAAM,IAAIqB,IAAI,GAAK,UAAU,CAAE,CAC9BnB,WAAW,CAAC,UAAU,CAAC,CACvBF,cAAc,CAAC,MAAM,CAAC,CACxB,CAAC,IAAM,CACLA,cAAc,CAACqB,IAAI,CAAC,CACtB,CACF,CAAC,CAED,mBACElC,IAAA,CAACN,MAAM,EACLkB,WAAW,CAAEA,WAAY,CACzBuB,UAAU,CAAEF,cAAe,CAC3BG,iBAAiB,CAAE,CAAChB,WAAY,CAAAE,QAAA,CAE/BD,UAAU,CAAC,CAAC,CACP,CAAC,CAEb,CAAC,CAED;AACA,KAAM,CAAAgB,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAEC,OAAQ,CAAC,CAAG/C,OAAO,CAAC,CAAC,CAE7B,GAAI+C,OAAO,CAAE,CACX,mBAAOtC,IAAA,CAACG,gBAAgB,EAAAmB,QAAA,CAAC,eAAa,CAAkB,CAAC,CAC3D,CAEA,mBAAOtB,IAAA,CAACW,OAAO,GAAE,CAAC,CACpB,CAAC,CAED;AACA,QAAS,CAAA4B,GAAGA,CAAA,CAAG,CACb,mBACEvC,IAAA,CAACV,YAAY,EAAAgC,QAAA,cACXtB,IAAA,CAACqC,UAAU,GAAE,CAAC,CACF,CAAC,CAEnB,CAEA,cAAe,CAAAE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}