{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ScoreEditor\\\\ScoreEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { ScoreService } from '../../services/scoreService';\nimport { ChordView } from '../ChordView/ChordView';\nimport { v4 as uuidv4 } from 'uuid';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n_c = EditorContainer;\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c2 = EditorHeader;\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n_c3 = EditorTitle;\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n_c4 = EditorActions;\nconst ActionButton = styled.button`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c5 = ActionButton;\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n_c6 = EditorContent;\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n_c7 = ToolPanel;\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n_c8 = ToolSection;\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n_c9 = ToolGrid;\nconst ToolButton = styled.button`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n_c0 = ToolButton;\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n_c1 = ScoreCanvas;\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n_c10 = StaffContainer;\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n_c11 = Staff;\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n_c12 = GuestWarning;\nexport const ScoreEditor = ({\n  scoreId\n}) => {\n  _s();\n  const {\n    currentUser\n  } = useAuth();\n  const [title, setTitle] = useState('Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState('note');\n  const [selectedDuration, setSelectedDuration] = useState('quarter');\n  const [selectedNote, setSelectedNote] = useState('C');\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [placedNotes, setPlacedNotes] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showChords, setShowChords] = useState(false);\n  const [lastSaved, setLastSaved] = useState(null);\n\n  // Carregar partitura existente\n  React.useEffect(() => {\n    if (scoreId && currentUser) {\n      loadScore();\n    }\n  }, [scoreId, currentUser]);\n\n  // Salvamento automático\n  React.useEffect(() => {\n    if (!currentUser || !scoreId || placedNotes.length === 0) return;\n    const autoSaveTimer = setTimeout(() => {\n      handleSave(true); // true indica salvamento automático\n    }, 2000); // Salvar após 2 segundos de inatividade\n\n    return () => clearTimeout(autoSaveTimer);\n  }, [placedNotes, title, currentUser, scoreId]);\n  const loadScore = async () => {\n    if (!scoreId || !currentUser) return;\n    setIsLoading(true);\n    try {\n      const score = await ScoreService.getScore(scoreId);\n      if (score && score.userId === currentUser.uid) {\n        setTitle(score.title);\n\n        // Extrair notas de todos os compassos\n        const allNotes = [];\n        score.staffs.forEach(staff => {\n          staff.measures.forEach(measure => {\n            allNotes.push(...measure.notes);\n          });\n        });\n        setPlacedNotes(allNotes);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar partitura:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSave = async (isAutoSave = false) => {\n    if (!currentUser) {\n      if (!isAutoSave) {\n        alert('Você precisa estar logado para salvar partituras!');\n      }\n      return;\n    }\n    setIsSaving(true);\n    try {\n      // Organizar notas por compasso\n      const measureMap = new Map();\n      placedNotes.forEach(note => {\n        const measure = note.position.measure;\n        if (!measureMap.has(measure)) {\n          measureMap.set(measure, []);\n        }\n        measureMap.get(measure).push(note);\n      });\n\n      // Criar compassos com as notas\n      const measures = Array.from(measureMap.entries()).map(([measureNumber, notes]) => ({\n        id: uuidv4(),\n        number: measureNumber,\n        timeSignature: {\n          numerator: 4,\n          denominator: 4\n        },\n        notes: notes.sort((a, b) => a.position.beat - b.position.beat),\n        chords: []\n      }));\n\n      // Criar dados da partitura\n      const scoreData = {\n        title,\n        key: {\n          note: 'C',\n          mode: 'major'\n        },\n        timeSignature: {\n          numerator: 4,\n          denominator: 4\n        },\n        tempo: 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: 'treble',\n          instrument: 'piano',\n          measures\n        }],\n        lyrics: [],\n        userId: currentUser.uid\n      };\n      if (scoreId) {\n        // Para update, removemos o userId pois não deve ser alterado\n        const {\n          userId,\n          ...updateData\n        } = scoreData;\n        await ScoreService.updateScore(scoreId, updateData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n      setLastSaved(new Date());\n      if (!isAutoSave) {\n        alert('Partitura salva com sucesso!');\n      }\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      if (!isAutoSave) {\n        alert('Erro ao salvar partitura');\n      }\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n  const handleStaffClick = event => {\n    var _event$currentTarget$;\n    const rect = event.currentTarget.getBoundingClientRect();\n    const svgRect = (_event$currentTarget$ = event.currentTarget.closest('svg')) === null || _event$currentTarget$ === void 0 ? void 0 : _event$currentTarget$.getBoundingClientRect();\n    if (!svgRect) return;\n    const x = event.clientX - svgRect.left;\n    const y = event.clientY - svgRect.top;\n\n    // Calcular a linha da pauta baseada na posição Y\n    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta\n    const notePositions = [{\n      y: 40,\n      note: 'A',\n      octave: 5\n    },\n    // Acima da pauta\n    {\n      y: 50,\n      note: 'G',\n      octave: 5\n    },\n    // 5ª linha\n    {\n      y: 60,\n      note: 'F',\n      octave: 5\n    },\n    // Entre 4ª e 5ª\n    {\n      y: 70,\n      note: 'E',\n      octave: 5\n    },\n    // 4ª linha\n    {\n      y: 80,\n      note: 'D',\n      octave: 5\n    },\n    // Entre 3ª e 4ª\n    {\n      y: 90,\n      note: 'C',\n      octave: 5\n    },\n    // 3ª linha (Dó central)\n    {\n      y: 100,\n      note: 'B',\n      octave: 4\n    },\n    // Entre 2ª e 3ª\n    {\n      y: 110,\n      note: 'A',\n      octave: 4\n    },\n    // 2ª linha\n    {\n      y: 120,\n      note: 'G',\n      octave: 4\n    },\n    // Entre 1ª e 2ª\n    {\n      y: 130,\n      note: 'F',\n      octave: 4\n    },\n    // 1ª linha\n    {\n      y: 140,\n      note: 'E',\n      octave: 4\n    } // Abaixo da pauta\n    ];\n\n    // Encontrar a posição mais próxima\n    const closestPosition = notePositions.reduce((closest, current) => {\n      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;\n    });\n\n    // Calcular a posição horizontal (compasso e tempo)\n    const measureWidth = 150; // Largura aproximada de um compasso\n    const startX = 120; // Início da área de notas\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = relativeX % measureWidth / measureWidth * 4; // 4 tempos por compasso\n\n    // Criar nova nota\n    const newNote = {\n      id: uuidv4(),\n      name: selectedNote,\n      // Usar a nota selecionada no painel\n      octave: closestPosition.octave,\n      duration: selectedDuration,\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4,\n        // Arredondar para 1/4 de tempo\n        staff: 0\n      },\n      isRest: selectedTool === 'rest'\n    };\n    setPlacedNotes(prev => [...prev, newNote]);\n  };\n  const durations = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(EditorContainer, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '400px',\n          fontSize: '1.2rem',\n          color: '#666'\n        },\n        children: \"\\uD83C\\uDFBC Carregando partitura...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(EditorContainer, {\n    children: [/*#__PURE__*/_jsxDEV(EditorHeader, {\n      children: [/*#__PURE__*/_jsxDEV(EditorTitle, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Editor de Partituras\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: title,\n            onChange: e => setTitle(e.target.value),\n            placeholder: \"Nome da partitura...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this), currentUser && lastSaved && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              color: 'rgba(255,255,255,0.8)'\n            },\n            children: [\"\\uD83D\\uDCBE Salvo \\xE0s \", lastSaved.toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorActions, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setShowChords(!showChords),\n          variant: \"primary\",\n          children: showChords ? '🎼 Partitura' : '🎸 Cifras'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setPlacedNotes([]),\n          variant: \"primary\",\n          disabled: placedNotes.length === 0,\n          children: \"\\uD83D\\uDDD1\\uFE0F Limpar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handlePlay,\n          variant: \"primary\",\n          children: isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handleSave,\n          variant: \"secondary\",\n          disabled: !currentUser || isSaving,\n          children: isSaving ? '💾 Salvando...' : '💾 Salvar'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditorContent, {\n      children: [/*#__PURE__*/_jsxDEV(ToolPanel, {\n        children: [!currentUser && /*#__PURE__*/_jsxDEV(GuestWarning, {\n          children: \"\\u26A0\\uFE0F Modo visitante: suas altera\\xE7\\xF5es n\\xE3o ser\\xE3o salvas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFB5 Ferramentas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: [/*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'note',\n              onClick: () => setSelectedTool('note'),\n              children: \"\\uD83C\\uDFB5 Nota\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'rest',\n              onClick: () => setSelectedTool('rest'),\n              children: \"\\uD83C\\uDFBC Pausa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'chord',\n              onClick: () => setSelectedTool('chord'),\n              children: \"\\uD83C\\uDFB9 Acorde\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u23F1\\uFE0F Dura\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: durations.map(duration => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedDuration === duration,\n              onClick: () => setSelectedDuration(duration),\n              children: duration === 'whole' ? '𝅝' : duration === 'half' ? '𝅗𝅥' : duration === 'quarter' ? '♩' : duration === 'eighth' ? '♫' : '♬'\n            }, duration, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFBC Notas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: notes.map(note => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedNote === note,\n              onClick: () => setSelectedNote(note),\n              children: note\n            }, note, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCCA Estat\\xEDsticas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.9rem',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83D\\uDCDD Notas: \", placedNotes.filter(n => !n.isRest).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u23F8\\uFE0F Pausas: \", placedNotes.filter(n => n.isRest).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83C\\uDFB5 Total: \", placedNotes.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScoreCanvas, {\n        children: showChords ? /*#__PURE__*/_jsxDEV(ChordView, {\n          notes: placedNotes,\n          title: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(StaffContainer, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 1rem 0',\n              color: '#495057'\n            },\n            children: [\"Partitura: \", title]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Staff, {\n            viewBox: \"0 0 800 200\",\n            children: [[0, 1, 2, 3, 4].map(line => /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"50\",\n              y1: 50 + line * 20,\n              x2: \"750\",\n              y2: 50 + line * 20,\n              stroke: \"#333\",\n              strokeWidth: \"1\"\n            }, line, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"20\",\n              y: \"90\",\n              fontSize: \"40\",\n              fill: \"#333\",\n              children: \"\\uD834\\uDD1E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"75\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"95\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"110\",\n              y1: \"50\",\n              x2: \"110\",\n              y2: \"130\",\n              stroke: \"#333\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this), [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: 120 + measure * 150,\n              y1: \"50\",\n              x2: 120 + measure * 150,\n              y2: \"130\",\n              stroke: \"#999\",\n              strokeWidth: \"1\"\n            }, measure, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 17\n            }, this)), [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"text\", {\n              x: 120 + (measure - 1) * 150 + 75,\n              y: \"35\",\n              fontSize: \"12\",\n              fill: \"#666\",\n              textAnchor: \"middle\",\n              children: measure\n            }, measure, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"120\",\n              y: \"40\",\n              width: \"600\",\n              height: \"100\",\n              fill: \"transparent\",\n              style: {\n                cursor: 'crosshair'\n              },\n              onClick: handleStaffClick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this), placedNotes.map((note, index) => {\n              const x = 120 + (note.position.measure - 1) * 150 + note.position.beat * 30;\n              const notePositions = {\n                'A5': 40,\n                'G5': 50,\n                'F5': 60,\n                'E5': 70,\n                'D5': 80,\n                'C5': 90,\n                'B4': 100,\n                'A4': 110,\n                'G4': 120,\n                'F4': 130,\n                'E4': 140\n              };\n              const y = notePositions[`${note.name}${note.octave}`] || 90;\n              return /*#__PURE__*/_jsxDEV(\"g\", {\n                children: [(y < 50 || y > 130) && /*#__PURE__*/_jsxDEV(\"line\", {\n                  x1: x - 10,\n                  y1: y,\n                  x2: x + 10,\n                  y2: y,\n                  stroke: \"#666\",\n                  strokeWidth: \"1\",\n                  strokeDasharray: \"2,2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 23\n                }, this), note.isRest ?\n                /*#__PURE__*/\n                // Símbolo de pausa\n                _jsxDEV(\"g\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                    x: x - 4,\n                    y: y - 8,\n                    width: \"8\",\n                    height: \"4\",\n                    fill: \"#333\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                    x: x - 4,\n                    y: y + 4,\n                    width: \"8\",\n                    height: \"4\",\n                    fill: \"#333\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 23\n                }, this) :\n                /*#__PURE__*/\n                // Nota musical\n                _jsxDEV(\"ellipse\", {\n                  cx: x,\n                  cy: y,\n                  rx: \"7\",\n                  ry: \"5\",\n                  fill: note.duration === 'whole' || note.duration === 'half' ? 'white' : '#333',\n                  stroke: \"#333\",\n                  strokeWidth: \"2\",\n                  transform: `rotate(-20 ${x} ${y})`,\n                  style: {\n                    cursor: 'pointer'\n                  },\n                  onClick: e => {\n                    e.stopPropagation();\n                    setPlacedNotes(prev => prev.filter(n => n.id !== note.id));\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 23\n                }, this), !note.isRest && note.duration !== 'whole' && note.duration !== 'half' && /*#__PURE__*/_jsxDEV(\"line\", {\n                  x1: x + (y > 90 ? -7 : 7),\n                  y1: y,\n                  x2: x + (y > 90 ? -7 : 7),\n                  y2: y + (y > 90 ? 25 : -25),\n                  stroke: \"#333\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 23\n                }, this), !note.isRest && (note.duration === 'eighth' || note.duration === 'sixteenth') && /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: y > 90 ? `M ${x - 7} ${y + 25} Q ${x - 15} ${y + 20} ${x - 12} ${y + 15}` : `M ${x + 7} ${y - 25} Q ${x + 15} ${y - 20} ${x + 12} ${y - 15}`,\n                  fill: \"#333\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                  x: x,\n                  y: y - 15,\n                  fontSize: \"10\",\n                  fill: \"#666\",\n                  textAnchor: \"middle\",\n                  style: {\n                    pointerEvents: 'none'\n                  },\n                  children: [note.name, note.octave]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 21\n                }, this)]\n              }, note.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this);\n            }), placedNotes.length === 0 && /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"100\",\n              fontSize: \"14\",\n              fill: \"#999\",\n              textAnchor: \"middle\",\n              children: \"Clique na pauta para adicionar notas \\u2022 Clique em uma nota para remov\\xEA-la\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 386,\n    columnNumber: 5\n  }, this);\n};\n_s(ScoreEditor, \"m+Lnr2LUjijeI+aSvH98kz3C8Yc=\", false, function () {\n  return [useAuth];\n});\n_c13 = ScoreEditor;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"EditorContainer\");\n$RefreshReg$(_c2, \"EditorHeader\");\n$RefreshReg$(_c3, \"EditorTitle\");\n$RefreshReg$(_c4, \"EditorActions\");\n$RefreshReg$(_c5, \"ActionButton\");\n$RefreshReg$(_c6, \"EditorContent\");\n$RefreshReg$(_c7, \"ToolPanel\");\n$RefreshReg$(_c8, \"ToolSection\");\n$RefreshReg$(_c9, \"ToolGrid\");\n$RefreshReg$(_c0, \"ToolButton\");\n$RefreshReg$(_c1, \"ScoreCanvas\");\n$RefreshReg$(_c10, \"StaffContainer\");\n$RefreshReg$(_c11, \"Staff\");\n$RefreshReg$(_c12, \"GuestWarning\");\n$RefreshReg$(_c13, \"ScoreEditor\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "ScoreService", "ChordView", "v4", "uuidv4", "jsxDEV", "_jsxDEV", "EditorC<PERSON><PERSON>", "div", "_c", "Editor<PERSON><PERSON>er", "_c2", "EditorT<PERSON>le", "_c3", "EditorActions", "_c4", "ActionButton", "button", "props", "variant", "_c5", "Editor<PERSON><PERSON><PERSON>", "_c6", "ToolPanel", "_c7", "ToolSection", "_c8", "ToolGrid", "_c9", "<PERSON><PERSON><PERSON><PERSON>on", "active", "_c0", "ScoreCanvas", "_c1", "StaffC<PERSON>r", "_c10", "Staff", "svg", "_c11", "Guest<PERSON><PERSON>ning", "_c12", "ScoreEditor", "scoreId", "_s", "currentUser", "title", "setTitle", "selectedTool", "setSelectedTool", "selectedDuration", "setSelectedDuration", "selected<PERSON><PERSON>", "setSelectedNote", "isPlaying", "setIsPlaying", "isSaving", "setIsSaving", "placedNotes", "setPlacedNotes", "isLoading", "setIsLoading", "showChords", "setShowChords", "lastSaved", "setLastSaved", "useEffect", "loadScore", "length", "autoSaveTimer", "setTimeout", "handleSave", "clearTimeout", "score", "getScore", "userId", "uid", "allNotes", "staffs", "for<PERSON>ach", "staff", "measures", "measure", "push", "notes", "error", "console", "isAutoSave", "alert", "measureMap", "Map", "note", "position", "has", "set", "get", "Array", "from", "entries", "map", "measureNumber", "id", "number", "timeSignature", "numerator", "denominator", "sort", "a", "b", "beat", "chords", "scoreData", "key", "mode", "tempo", "clef", "instrument", "lyrics", "updateData", "updateScore", "createScore", "Date", "handlePlay", "handleStaffClick", "event", "_event$currentTarget$", "rect", "currentTarget", "getBoundingClientRect", "svgRect", "closest", "x", "clientX", "left", "y", "clientY", "top", "staffLines", "notePositions", "octave", "closestPosition", "reduce", "current", "Math", "abs", "measureWidth", "startX", "relativeX", "max", "floor", "beatPosition", "newNote", "name", "duration", "round", "isRest", "prev", "durations", "children", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "type", "value", "onChange", "e", "target", "placeholder", "toLocaleTimeString", "onClick", "disabled", "filter", "n", "margin", "viewBox", "line", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "fill", "textAnchor", "width", "cursor", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cx", "cy", "rx", "ry", "transform", "stopPropagation", "d", "pointerEvents", "_c13", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ScoreEditor/ScoreEditor.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Score, MusicalNote, NoteName, NoteDuration, ClefType, InstrumentType, Octave } from '../../types/music';\nimport { ScoreService } from '../../services/scoreService';\nimport { ChordView } from '../ChordView/ChordView';\nimport { v4 as uuidv4 } from 'uuid';\n\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n\nconst ToolButton = styled.button<{ active?: boolean }>`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n\ninterface ScoreEditorProps {\n  scoreId?: string;\n}\n\nexport const ScoreEditor: React.FC<ScoreEditorProps> = ({ scoreId }) => {\n  const { currentUser } = useAuth();\n  const [title, setTitle] = useState('Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState<'note' | 'rest' | 'chord'>('note');\n  const [selectedDuration, setSelectedDuration] = useState<NoteDuration>('quarter');\n  const [selectedNote, setSelectedNote] = useState<NoteName>('C');\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [placedNotes, setPlacedNotes] = useState<MusicalNote[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showChords, setShowChords] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\n\n  // Carregar partitura existente\n  React.useEffect(() => {\n    if (scoreId && currentUser) {\n      loadScore();\n    }\n  }, [scoreId, currentUser]);\n\n  // Salvamento automático\n  React.useEffect(() => {\n    if (!currentUser || !scoreId || placedNotes.length === 0) return;\n\n    const autoSaveTimer = setTimeout(() => {\n      handleSave(true); // true indica salvamento automático\n    }, 2000); // Salvar após 2 segundos de inatividade\n\n    return () => clearTimeout(autoSaveTimer);\n  }, [placedNotes, title, currentUser, scoreId]);\n\n  const loadScore = async () => {\n    if (!scoreId || !currentUser) return;\n\n    setIsLoading(true);\n    try {\n      const score = await ScoreService.getScore(scoreId);\n      if (score && score.userId === currentUser.uid) {\n        setTitle(score.title);\n\n        // Extrair notas de todos os compassos\n        const allNotes: MusicalNote[] = [];\n        score.staffs.forEach(staff => {\n          staff.measures.forEach(measure => {\n            allNotes.push(...measure.notes);\n          });\n        });\n\n        setPlacedNotes(allNotes);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar partitura:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSave = async (isAutoSave = false) => {\n    if (!currentUser) {\n      if (!isAutoSave) {\n        alert('Você precisa estar logado para salvar partituras!');\n      }\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      // Organizar notas por compasso\n      const measureMap = new Map<number, MusicalNote[]>();\n      placedNotes.forEach(note => {\n        const measure = note.position.measure;\n        if (!measureMap.has(measure)) {\n          measureMap.set(measure, []);\n        }\n        measureMap.get(measure)!.push(note);\n      });\n\n      // Criar compassos com as notas\n      const measures = Array.from(measureMap.entries()).map(([measureNumber, notes]) => ({\n        id: uuidv4(),\n        number: measureNumber,\n        timeSignature: { numerator: 4, denominator: 4 },\n        notes: notes.sort((a, b) => a.position.beat - b.position.beat),\n        chords: []\n      }));\n\n      // Criar dados da partitura\n      const scoreData = {\n        title,\n        key: { note: 'C' as NoteName, mode: 'major' as const },\n        timeSignature: { numerator: 4, denominator: 4 },\n        tempo: 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: 'treble' as ClefType,\n          instrument: 'piano' as InstrumentType,\n          measures\n        }],\n        lyrics: [],\n        userId: currentUser.uid\n      };\n\n      if (scoreId) {\n        // Para update, removemos o userId pois não deve ser alterado\n        const { userId, ...updateData } = scoreData;\n        await ScoreService.updateScore(scoreId, updateData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n\n      setLastSaved(new Date());\n      if (!isAutoSave) {\n        alert('Partitura salva com sucesso!');\n      }\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      if (!isAutoSave) {\n        alert('Erro ao salvar partitura');\n      }\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n\n  const handleStaffClick = (event: React.MouseEvent<SVGRectElement>) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const svgRect = event.currentTarget.closest('svg')?.getBoundingClientRect();\n\n    if (!svgRect) return;\n\n    const x = event.clientX - svgRect.left;\n    const y = event.clientY - svgRect.top;\n\n    // Calcular a linha da pauta baseada na posição Y\n    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta\n    const notePositions = [\n      { y: 40, note: 'A' as NoteName, octave: 5 as Octave }, // Acima da pauta\n      { y: 50, note: 'G' as NoteName, octave: 5 as Octave }, // 5ª linha\n      { y: 60, note: 'F' as NoteName, octave: 5 as Octave }, // Entre 4ª e 5ª\n      { y: 70, note: 'E' as NoteName, octave: 5 as Octave }, // 4ª linha\n      { y: 80, note: 'D' as NoteName, octave: 5 as Octave }, // Entre 3ª e 4ª\n      { y: 90, note: 'C' as NoteName, octave: 5 as Octave }, // 3ª linha (Dó central)\n      { y: 100, note: 'B' as NoteName, octave: 4 as Octave }, // Entre 2ª e 3ª\n      { y: 110, note: 'A' as NoteName, octave: 4 as Octave }, // 2ª linha\n      { y: 120, note: 'G' as NoteName, octave: 4 as Octave }, // Entre 1ª e 2ª\n      { y: 130, note: 'F' as NoteName, octave: 4 as Octave }, // 1ª linha\n      { y: 140, note: 'E' as NoteName, octave: 4 as Octave }, // Abaixo da pauta\n    ];\n\n    // Encontrar a posição mais próxima\n    const closestPosition = notePositions.reduce((closest, current) => {\n      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;\n    });\n\n    // Calcular a posição horizontal (compasso e tempo)\n    const measureWidth = 150; // Largura aproximada de um compasso\n    const startX = 120; // Início da área de notas\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = ((relativeX % measureWidth) / measureWidth) * 4; // 4 tempos por compasso\n\n    // Criar nova nota\n    const newNote: MusicalNote = {\n      id: uuidv4(),\n      name: selectedNote, // Usar a nota selecionada no painel\n      octave: closestPosition.octave,\n      duration: selectedDuration,\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4, // Arredondar para 1/4 de tempo\n        staff: 0\n      },\n      isRest: selectedTool === 'rest'\n    };\n\n    setPlacedNotes(prev => [...prev, newNote]);\n  };\n\n  const durations: NoteDuration[] = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes: NoteName[] = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n\n  if (isLoading) {\n    return (\n      <EditorContainer>\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '400px',\n          fontSize: '1.2rem',\n          color: '#666'\n        }}>\n          🎼 Carregando partitura...\n        </div>\n      </EditorContainer>\n    );\n  }\n\n  return (\n    <EditorContainer>\n      <EditorHeader>\n        <EditorTitle>\n          <h2>Editor de Partituras</h2>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n            <input\n              type=\"text\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              placeholder=\"Nome da partitura...\"\n            />\n            {currentUser && lastSaved && (\n              <div style={{ fontSize: '0.8rem', color: 'rgba(255,255,255,0.8)' }}>\n                💾 Salvo às {lastSaved.toLocaleTimeString()}\n              </div>\n            )}\n          </div>\n        </EditorTitle>\n        <EditorActions>\n          <ActionButton\n            onClick={() => setShowChords(!showChords)}\n            variant=\"primary\"\n          >\n            {showChords ? '🎼 Partitura' : '🎸 Cifras'}\n          </ActionButton>\n          <ActionButton\n            onClick={() => setPlacedNotes([])}\n            variant=\"primary\"\n            disabled={placedNotes.length === 0}\n          >\n            🗑️ Limpar\n          </ActionButton>\n          <ActionButton onClick={handlePlay} variant=\"primary\">\n            {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}\n          </ActionButton>\n          <ActionButton\n            onClick={handleSave}\n            variant=\"secondary\"\n            disabled={!currentUser || isSaving}\n          >\n            {isSaving ? '💾 Salvando...' : '💾 Salvar'}\n          </ActionButton>\n        </EditorActions>\n      </EditorHeader>\n\n      <EditorContent>\n        <ToolPanel>\n          {!currentUser && (\n            <GuestWarning>\n              ⚠️ Modo visitante: suas alterações não serão salvas\n            </GuestWarning>\n          )}\n          \n          <ToolSection>\n            <h3>🎵 Ferramentas</h3>\n            <ToolGrid>\n              <ToolButton \n                active={selectedTool === 'note'}\n                onClick={() => setSelectedTool('note')}\n              >\n                🎵 Nota\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'rest'}\n                onClick={() => setSelectedTool('rest')}\n              >\n                🎼 Pausa\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'chord'}\n                onClick={() => setSelectedTool('chord')}\n              >\n                🎹 Acorde\n              </ToolButton>\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>⏱️ Duração</h3>\n            <ToolGrid>\n              {durations.map(duration => (\n                <ToolButton\n                  key={duration}\n                  active={selectedDuration === duration}\n                  onClick={() => setSelectedDuration(duration)}\n                >\n                  {duration === 'whole' ? '𝅝' : \n                   duration === 'half' ? '𝅗𝅥' :\n                   duration === 'quarter' ? '♩' :\n                   duration === 'eighth' ? '♫' : '♬'}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>🎼 Notas</h3>\n            <ToolGrid>\n              {notes.map(note => (\n                <ToolButton\n                  key={note}\n                  active={selectedNote === note}\n                  onClick={() => setSelectedNote(note)}\n                >\n                  {note}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>📊 Estatísticas</h3>\n            <div style={{ fontSize: '0.9rem', color: '#666' }}>\n              <div>📝 Notas: {placedNotes.filter(n => !n.isRest).length}</div>\n              <div>⏸️ Pausas: {placedNotes.filter(n => n.isRest).length}</div>\n              <div>🎵 Total: {placedNotes.length}</div>\n            </div>\n          </ToolSection>\n        </ToolPanel>\n\n        <ScoreCanvas>\n          {showChords ? (\n            <ChordView notes={placedNotes} title={title} />\n          ) : (\n            <StaffContainer>\n              <h3 style={{ margin: '0 0 1rem 0', color: '#495057' }}>Partitura: {title}</h3>\n              <Staff viewBox=\"0 0 800 200\">\n              {/* Linhas da pauta */}\n              {[0, 1, 2, 3, 4].map(line => (\n                <line\n                  key={line}\n                  x1=\"50\"\n                  y1={50 + line * 20}\n                  x2=\"750\"\n                  y2={50 + line * 20}\n                  stroke=\"#333\"\n                  strokeWidth=\"1\"\n                />\n              ))}\n              \n              {/* Clave de Sol */}\n              <text x=\"20\" y=\"90\" fontSize=\"40\" fill=\"#333\">𝄞</text>\n              \n              {/* Compasso 4/4 */}\n              <text x=\"80\" y=\"75\" fontSize=\"16\" fill=\"#333\">4</text>\n              <text x=\"80\" y=\"95\" fontSize=\"16\" fill=\"#333\">4</text>\n              \n              {/* Linha divisória inicial */}\n              <line x1=\"110\" y1=\"50\" x2=\"110\" y2=\"130\" stroke=\"#333\" strokeWidth=\"2\"/>\n\n              {/* Divisões de compasso */}\n              {[1, 2, 3, 4].map(measure => (\n                <line\n                  key={measure}\n                  x1={120 + (measure * 150)}\n                  y1=\"50\"\n                  x2={120 + (measure * 150)}\n                  y2=\"130\"\n                  stroke=\"#999\"\n                  strokeWidth=\"1\"\n                />\n              ))}\n\n              {/* Números dos compassos */}\n              {[1, 2, 3, 4].map(measure => (\n                <text\n                  key={measure}\n                  x={120 + ((measure - 1) * 150) + 75}\n                  y=\"35\"\n                  fontSize=\"12\"\n                  fill=\"#666\"\n                  textAnchor=\"middle\"\n                >\n                  {measure}\n                </text>\n              ))}\n              \n              {/* Área clicável para adicionar notas */}\n              <rect\n                x=\"120\"\n                y=\"40\"\n                width=\"600\"\n                height=\"100\"\n                fill=\"transparent\"\n                style={{ cursor: 'crosshair' }}\n                onClick={handleStaffClick}\n              />\n\n              {/* Renderizar notas colocadas */}\n              {placedNotes.map((note, index) => {\n                const x = 120 + ((note.position.measure - 1) * 150) + (note.position.beat * 30);\n                const notePositions: { [key: string]: number } = {\n                  'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,\n                  'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140\n                };\n                const y = notePositions[`${note.name}${note.octave}`] || 90;\n\n                return (\n                  <g key={note.id}>\n                    {/* Linha auxiliar para notas fora da pauta */}\n                    {(y < 50 || y > 130) && (\n                      <line\n                        x1={x - 10}\n                        y1={y}\n                        x2={x + 10}\n                        y2={y}\n                        stroke=\"#666\"\n                        strokeWidth=\"1\"\n                        strokeDasharray=\"2,2\"\n                      />\n                    )}\n\n                    {/* Nota */}\n                    {note.isRest ? (\n                      // Símbolo de pausa\n                      <g>\n                        <rect\n                          x={x - 4}\n                          y={y - 8}\n                          width=\"8\"\n                          height=\"4\"\n                          fill=\"#333\"\n                        />\n                        <rect\n                          x={x - 4}\n                          y={y + 4}\n                          width=\"8\"\n                          height=\"4\"\n                          fill=\"#333\"\n                        />\n                      </g>\n                    ) : (\n                      // Nota musical\n                      <ellipse\n                        cx={x}\n                        cy={y}\n                        rx=\"7\"\n                        ry=\"5\"\n                        fill={note.duration === 'whole' || note.duration === 'half' ? 'white' : '#333'}\n                        stroke=\"#333\"\n                        strokeWidth=\"2\"\n                        transform={`rotate(-20 ${x} ${y})`}\n                        style={{ cursor: 'pointer' }}\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          setPlacedNotes(prev => prev.filter(n => n.id !== note.id));\n                        }}\n                      />\n                    )}\n\n                    {/* Haste da nota */}\n                    {!note.isRest && note.duration !== 'whole' && note.duration !== 'half' && (\n                      <line\n                        x1={x + (y > 90 ? -7 : 7)}\n                        y1={y}\n                        x2={x + (y > 90 ? -7 : 7)}\n                        y2={y + (y > 90 ? 25 : -25)}\n                        stroke=\"#333\"\n                        strokeWidth=\"2\"\n                      />\n                    )}\n\n                    {/* Bandeirola para colcheias e semicolcheias */}\n                    {!note.isRest && (note.duration === 'eighth' || note.duration === 'sixteenth') && (\n                      <path\n                        d={y > 90 ?\n                          `M ${x - 7} ${y + 25} Q ${x - 15} ${y + 20} ${x - 12} ${y + 15}` :\n                          `M ${x + 7} ${y - 25} Q ${x + 15} ${y - 20} ${x + 12} ${y - 15}`\n                        }\n                        fill=\"#333\"\n                      />\n                    )}\n\n                    {/* Texto da nota (para debug) */}\n                    <text\n                      x={x}\n                      y={y - 15}\n                      fontSize=\"10\"\n                      fill=\"#666\"\n                      textAnchor=\"middle\"\n                      style={{ pointerEvents: 'none' }}\n                    >\n                      {note.name}{note.octave}\n                    </text>\n                  </g>\n                );\n              })}\n\n              {/* Instruções */}\n              {placedNotes.length === 0 && (\n                <text x=\"400\" y=\"100\" fontSize=\"14\" fill=\"#999\" textAnchor=\"middle\">\n                  Clique na pauta para adicionar notas • Clique em uma nota para removê-la\n                </text>\n              )}\n            </Staff>\n          </StaffContainer>\n          )}\n        </ScoreCanvas>\n      </EditorContent>\n    </EditorContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAgB,OAAO;AAC/C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AAEpD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGR,MAAM,CAACS,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,eAAe;AAUrB,MAAMG,YAAY,GAAGX,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,YAAY;AASlB,MAAME,WAAW,GAAGb,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GA1BID,WAAW;AA4BjB,MAAME,aAAa,GAAGf,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGjB,MAAM,CAACkB,MAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhCIJ,YAAY;AAkClB,MAAMK,aAAa,GAAGtB,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,aAAa;AAMnB,MAAME,SAAS,GAAGxB,MAAM,CAACS,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GANID,SAAS;AAQf,MAAME,WAAW,GAAG1B,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GATID,WAAW;AAWjB,MAAME,QAAQ,GAAG5B,MAAM,CAACS,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,QAAQ;AAMd,MAAME,UAAU,GAAG9B,MAAM,CAACkB,MAA4B;AACtD;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D,WAAWZ,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,OAAO,GAAG,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMZ,KAAK,IAAI,CAACA,KAAK,CAACY,MAAM,IAAI,sBAAsB;AACtD;AACA,CAAC;AAACC,GAAA,GAdIF,UAAU;AAgBhB,MAAMG,WAAW,GAAGjC,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GARID,WAAW;AAUjB,MAAME,cAAc,GAAGnC,MAAM,CAACS,GAAG;AACjC;AACA;AACA,CAAC;AAAC2B,IAAA,GAHID,cAAc;AAKpB,MAAME,KAAK,GAAGrC,MAAM,CAACsC,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAPIF,KAAK;AASX,MAAMG,YAAY,GAAGxC,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GATID,YAAY;AAelB,OAAO,MAAME,WAAuC,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM;IAAEC;EAAY,CAAC,GAAG5C,OAAO,CAAC,CAAC;EACjC,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,gBAAgB,CAAC;EACpD,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAA4B,MAAM,CAAC;EACnF,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAe,SAAS,CAAC;EACjF,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAW,GAAG,CAAC;EAC/D,MAAM,CAACuD,SAAS,EAAEC,YAAY,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC2D,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAgB,EAAE,CAAC;EACjE,MAAM,CAAC6D,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiE,SAAS,EAAEC,YAAY,CAAC,GAAGlE,QAAQ,CAAc,IAAI,CAAC;;EAE7D;EACAD,KAAK,CAACoE,SAAS,CAAC,MAAM;IACpB,IAAIvB,OAAO,IAAIE,WAAW,EAAE;MAC1BsB,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACxB,OAAO,EAAEE,WAAW,CAAC,CAAC;;EAE1B;EACA/C,KAAK,CAACoE,SAAS,CAAC,MAAM;IACpB,IAAI,CAACrB,WAAW,IAAI,CAACF,OAAO,IAAIe,WAAW,CAACU,MAAM,KAAK,CAAC,EAAE;IAE1D,MAAMC,aAAa,GAAGC,UAAU,CAAC,MAAM;MACrCC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMC,YAAY,CAACH,aAAa,CAAC;EAC1C,CAAC,EAAE,CAACX,WAAW,EAAEZ,KAAK,EAAED,WAAW,EAAEF,OAAO,CAAC,CAAC;EAE9C,MAAMwB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAACxB,OAAO,IAAI,CAACE,WAAW,EAAE;IAE9BgB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMY,KAAK,GAAG,MAAMvE,YAAY,CAACwE,QAAQ,CAAC/B,OAAO,CAAC;MAClD,IAAI8B,KAAK,IAAIA,KAAK,CAACE,MAAM,KAAK9B,WAAW,CAAC+B,GAAG,EAAE;QAC7C7B,QAAQ,CAAC0B,KAAK,CAAC3B,KAAK,CAAC;;QAErB;QACA,MAAM+B,QAAuB,GAAG,EAAE;QAClCJ,KAAK,CAACK,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;UAC5BA,KAAK,CAACC,QAAQ,CAACF,OAAO,CAACG,OAAO,IAAI;YAChCL,QAAQ,CAACM,IAAI,CAAC,GAAGD,OAAO,CAACE,KAAK,CAAC;UACjC,CAAC,CAAC;QACJ,CAAC,CAAC;QAEFzB,cAAc,CAACkB,QAAQ,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACRxB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMU,UAAU,GAAG,MAAAA,CAAOgB,UAAU,GAAG,KAAK,KAAK;IAC/C,IAAI,CAAC1C,WAAW,EAAE;MAChB,IAAI,CAAC0C,UAAU,EAAE;QACfC,KAAK,CAAC,mDAAmD,CAAC;MAC5D;MACA;IACF;IAEA/B,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF;MACA,MAAMgC,UAAU,GAAG,IAAIC,GAAG,CAAwB,CAAC;MACnDhC,WAAW,CAACqB,OAAO,CAACY,IAAI,IAAI;QAC1B,MAAMT,OAAO,GAAGS,IAAI,CAACC,QAAQ,CAACV,OAAO;QACrC,IAAI,CAACO,UAAU,CAACI,GAAG,CAACX,OAAO,CAAC,EAAE;UAC5BO,UAAU,CAACK,GAAG,CAACZ,OAAO,EAAE,EAAE,CAAC;QAC7B;QACAO,UAAU,CAACM,GAAG,CAACb,OAAO,CAAC,CAAEC,IAAI,CAACQ,IAAI,CAAC;MACrC,CAAC,CAAC;;MAEF;MACA,MAAMV,QAAQ,GAAGe,KAAK,CAACC,IAAI,CAACR,UAAU,CAACS,OAAO,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,aAAa,EAAEhB,KAAK,CAAC,MAAM;QACjFiB,EAAE,EAAEhG,MAAM,CAAC,CAAC;QACZiG,MAAM,EAAEF,aAAa;QACrBG,aAAa,EAAE;UAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;QAC/CrB,KAAK,EAAEA,KAAK,CAACsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACf,QAAQ,CAACiB,IAAI,GAAGD,CAAC,CAAChB,QAAQ,CAACiB,IAAI,CAAC;QAC9DC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMC,SAAS,GAAG;QAChBjE,KAAK;QACLkE,GAAG,EAAE;UAAErB,IAAI,EAAE,GAAe;UAAEsB,IAAI,EAAE;QAAiB,CAAC;QACtDV,aAAa,EAAE;UAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;QAC/CS,KAAK,EAAE,GAAG;QACVpC,MAAM,EAAE,CAAC;UACPuB,EAAE,EAAEhG,MAAM,CAAC,CAAC;UACZ8G,IAAI,EAAE,QAAoB;UAC1BC,UAAU,EAAE,OAAyB;UACrCnC;QACF,CAAC,CAAC;QACFoC,MAAM,EAAE,EAAE;QACV1C,MAAM,EAAE9B,WAAW,CAAC+B;MACtB,CAAC;MAED,IAAIjC,OAAO,EAAE;QACX;QACA,MAAM;UAAEgC,MAAM;UAAE,GAAG2C;QAAW,CAAC,GAAGP,SAAS;QAC3C,MAAM7G,YAAY,CAACqH,WAAW,CAAC5E,OAAO,EAAE2E,UAAU,CAAC;MACrD,CAAC,MAAM;QACL,MAAMpH,YAAY,CAACsH,WAAW,CAACT,SAAS,EAAElE,WAAW,CAAC+B,GAAG,CAAC;MAC5D;MAEAX,YAAY,CAAC,IAAIwD,IAAI,CAAC,CAAC,CAAC;MACxB,IAAI,CAAClC,UAAU,EAAE;QACfC,KAAK,CAAC,8BAA8B,CAAC;MACvC;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,IAAI,CAACE,UAAU,EAAE;QACfC,KAAK,CAAC,0BAA0B,CAAC;MACnC;IACF,CAAC,SAAS;MACR/B,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMiE,UAAU,GAAGA,CAAA,KAAM;IACvBnE,YAAY,CAAC,CAACD,SAAS,CAAC;IACxB;EACF,CAAC;EAED,MAAMqE,gBAAgB,GAAIC,KAAuC,IAAK;IAAA,IAAAC,qBAAA;IACpE,MAAMC,IAAI,GAAGF,KAAK,CAACG,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,OAAO,IAAAJ,qBAAA,GAAGD,KAAK,CAACG,aAAa,CAACG,OAAO,CAAC,KAAK,CAAC,cAAAL,qBAAA,uBAAlCA,qBAAA,CAAoCG,qBAAqB,CAAC,CAAC;IAE3E,IAAI,CAACC,OAAO,EAAE;IAEd,MAAME,CAAC,GAAGP,KAAK,CAACQ,OAAO,GAAGH,OAAO,CAACI,IAAI;IACtC,MAAMC,CAAC,GAAGV,KAAK,CAACW,OAAO,GAAGN,OAAO,CAACO,GAAG;;IAErC;IACA,MAAMC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3C,MAAMC,aAAa,GAAG,CACpB;MAAEJ,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,GAAG;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC,CAAE;IAAA,CACzD;;IAED;IACA,MAAMC,eAAe,GAAGF,aAAa,CAACG,MAAM,CAAC,CAACX,OAAO,EAAEY,OAAO,KAAK;MACjE,OAAOC,IAAI,CAACC,GAAG,CAACF,OAAO,CAACR,CAAC,GAAGA,CAAC,CAAC,GAAGS,IAAI,CAACC,GAAG,CAACd,OAAO,CAACI,CAAC,GAAGA,CAAC,CAAC,GAAGQ,OAAO,GAAGZ,OAAO;IAC9E,CAAC,CAAC;;IAEF;IACA,MAAMe,YAAY,GAAG,GAAG,CAAC,CAAC;IAC1B,MAAMC,MAAM,GAAG,GAAG,CAAC,CAAC;IACpB,MAAMC,SAAS,GAAGhB,CAAC,GAAGe,MAAM;IAC5B,MAAMhE,OAAO,GAAG6D,IAAI,CAACK,GAAG,CAAC,CAAC,EAAEL,IAAI,CAACM,KAAK,CAACF,SAAS,GAAGF,YAAY,CAAC,GAAG,CAAC,CAAC;IACrE,MAAMK,YAAY,GAAKH,SAAS,GAAGF,YAAY,GAAIA,YAAY,GAAI,CAAC,CAAC,CAAC;;IAEtE;IACA,MAAMM,OAAoB,GAAG;MAC3BlD,EAAE,EAAEhG,MAAM,CAAC,CAAC;MACZmJ,IAAI,EAAEpG,YAAY;MAAE;MACpBuF,MAAM,EAAEC,eAAe,CAACD,MAAM;MAC9Bc,QAAQ,EAAEvG,gBAAgB;MAC1B0C,QAAQ,EAAE;QACRV,OAAO;QACP2B,IAAI,EAAEkC,IAAI,CAACW,KAAK,CAACJ,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;QAAE;QACxCtE,KAAK,EAAE;MACT,CAAC;MACD2E,MAAM,EAAE3G,YAAY,KAAK;IAC3B,CAAC;IAEDW,cAAc,CAACiG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEL,OAAO,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMM,SAAyB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC;EACrF,MAAMzE,KAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAE7D,IAAIxB,SAAS,EAAE;IACb,oBACErD,OAAA,CAACC,eAAe;MAAAsJ,QAAA,eACdvJ,OAAA;QAAKwJ,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE,OAAO;UACfC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;QACT,CAAE;QAAAP,QAAA,EAAC;MAEH;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC;EAEtB;EAEA,oBACElK,OAAA,CAACC,eAAe;IAAAsJ,QAAA,gBACdvJ,OAAA,CAACI,YAAY;MAAAmJ,QAAA,gBACXvJ,OAAA,CAACM,WAAW;QAAAiJ,QAAA,gBACVvJ,OAAA;UAAAuJ,QAAA,EAAI;QAAoB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BlK,OAAA;UAAKwJ,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEQ,GAAG,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBACjEvJ,OAAA;YACEoK,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE9H,KAAM;YACb+H,QAAQ,EAAGC,CAAC,IAAK/H,QAAQ,CAAC+H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC1CI,WAAW,EAAC;UAAsB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,EACD5H,WAAW,IAAImB,SAAS,iBACvBzD,OAAA;YAAKwJ,KAAK,EAAE;cAAEK,QAAQ,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAwB,CAAE;YAAAP,QAAA,GAAC,2BACtD,EAAC9F,SAAS,CAACiH,kBAAkB,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdlK,OAAA,CAACQ,aAAa;QAAA+I,QAAA,gBACZvJ,OAAA,CAACU,YAAY;UACXiK,OAAO,EAAEA,CAAA,KAAMnH,aAAa,CAAC,CAACD,UAAU,CAAE;UAC1C1C,OAAO,EAAC,SAAS;UAAA0I,QAAA,EAEhBhG,UAAU,GAAG,cAAc,GAAG;QAAW;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACflK,OAAA,CAACU,YAAY;UACXiK,OAAO,EAAEA,CAAA,KAAMvH,cAAc,CAAC,EAAE,CAAE;UAClCvC,OAAO,EAAC,SAAS;UACjB+J,QAAQ,EAAEzH,WAAW,CAACU,MAAM,KAAK,CAAE;UAAA0F,QAAA,EACpC;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACflK,OAAA,CAACU,YAAY;UAACiK,OAAO,EAAExD,UAAW;UAACtG,OAAO,EAAC,SAAS;UAAA0I,QAAA,EACjDxG,SAAS,GAAG,WAAW,GAAG;QAAe;UAAAgH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACflK,OAAA,CAACU,YAAY;UACXiK,OAAO,EAAE3G,UAAW;UACpBnD,OAAO,EAAC,WAAW;UACnB+J,QAAQ,EAAE,CAACtI,WAAW,IAAIW,QAAS;UAAAsG,QAAA,EAElCtG,QAAQ,GAAG,gBAAgB,GAAG;QAAW;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEflK,OAAA,CAACe,aAAa;MAAAwI,QAAA,gBACZvJ,OAAA,CAACiB,SAAS;QAAAsI,QAAA,GACP,CAACjH,WAAW,iBACXtC,OAAA,CAACiC,YAAY;UAAAsH,QAAA,EAAC;QAEd;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CACf,eAEDlK,OAAA,CAACmB,WAAW;UAAAoI,QAAA,gBACVvJ,OAAA;YAAAuJ,QAAA,EAAI;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBlK,OAAA,CAACqB,QAAQ;YAAAkI,QAAA,gBACPvJ,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,MAAO;cAChCkI,OAAO,EAAEA,CAAA,KAAMjI,eAAe,CAAC,MAAM,CAAE;cAAA6G,QAAA,EACxC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblK,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,MAAO;cAChCkI,OAAO,EAAEA,CAAA,KAAMjI,eAAe,CAAC,MAAM,CAAE;cAAA6G,QAAA,EACxC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblK,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,OAAQ;cACjCkI,OAAO,EAAEA,CAAA,KAAMjI,eAAe,CAAC,OAAO,CAAE;cAAA6G,QAAA,EACzC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdlK,OAAA,CAACmB,WAAW;UAAAoI,QAAA,gBACVvJ,OAAA;YAAAuJ,QAAA,EAAI;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBlK,OAAA,CAACqB,QAAQ;YAAAkI,QAAA,EACND,SAAS,CAAC1D,GAAG,CAACsD,QAAQ,iBACrBlJ,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEmB,gBAAgB,KAAKuG,QAAS;cACtCyB,OAAO,EAAEA,CAAA,KAAM/H,mBAAmB,CAACsG,QAAQ,CAAE;cAAAK,QAAA,EAE5CL,QAAQ,KAAK,OAAO,GAAG,IAAI,GAC3BA,QAAQ,KAAK,MAAM,GAAG,MAAM,GAC5BA,QAAQ,KAAK,SAAS,GAAG,GAAG,GAC5BA,QAAQ,KAAK,QAAQ,GAAG,GAAG,GAAG;YAAG,GAP7BA,QAAQ;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQH,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdlK,OAAA,CAACmB,WAAW;UAAAoI,QAAA,gBACVvJ,OAAA;YAAAuJ,QAAA,EAAI;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBlK,OAAA,CAACqB,QAAQ;YAAAkI,QAAA,EACN1E,KAAK,CAACe,GAAG,CAACR,IAAI,iBACbpF,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEqB,YAAY,KAAKuC,IAAK;cAC9BuF,OAAO,EAAEA,CAAA,KAAM7H,eAAe,CAACsC,IAAI,CAAE;cAAAmE,QAAA,EAEpCnE;YAAI,GAJAA,IAAI;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKC,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdlK,OAAA,CAACmB,WAAW;UAAAoI,QAAA,gBACVvJ,OAAA;YAAAuJ,QAAA,EAAI;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBlK,OAAA;YAAKwJ,KAAK,EAAE;cAAEK,QAAQ,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAP,QAAA,gBAChDvJ,OAAA;cAAAuJ,QAAA,GAAK,sBAAU,EAACpG,WAAW,CAAC0H,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC1B,MAAM,CAAC,CAACvF,MAAM;YAAA;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChElK,OAAA;cAAAuJ,QAAA,GAAK,uBAAW,EAACpG,WAAW,CAAC0H,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,MAAM,CAAC,CAACvF,MAAM;YAAA;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChElK,OAAA;cAAAuJ,QAAA,GAAK,sBAAU,EAACpG,WAAW,CAACU,MAAM;YAAA;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEZlK,OAAA,CAAC0B,WAAW;QAAA6H,QAAA,EACThG,UAAU,gBACTvD,OAAA,CAACJ,SAAS;UAACiF,KAAK,EAAE1B,WAAY;UAACZ,KAAK,EAAEA;QAAM;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE/ClK,OAAA,CAAC4B,cAAc;UAAA2H,QAAA,gBACbvJ,OAAA;YAAIwJ,KAAK,EAAE;cAAEuB,MAAM,EAAE,YAAY;cAAEjB,KAAK,EAAE;YAAU,CAAE;YAAAP,QAAA,GAAC,aAAW,EAAChH,KAAK;UAAA;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9ElK,OAAA,CAAC8B,KAAK;YAACkJ,OAAO,EAAC,aAAa;YAAAzB,QAAA,GAE3B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC3D,GAAG,CAACqF,IAAI,iBACvBjL,OAAA;cAEEkL,EAAE,EAAC,IAAI;cACPC,EAAE,EAAE,EAAE,GAAGF,IAAI,GAAG,EAAG;cACnBG,EAAE,EAAC,KAAK;cACRC,EAAE,EAAE,EAAE,GAAGJ,IAAI,GAAG,EAAG;cACnBK,MAAM,EAAC,MAAM;cACbC,WAAW,EAAC;YAAG,GANVN,IAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV,CACF,CAAC,eAGFlK,OAAA;cAAM4H,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAAC8B,QAAQ,EAAC,IAAI;cAAC2B,IAAI,EAAC,MAAM;cAAAjC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGvDlK,OAAA;cAAM4H,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAAC8B,QAAQ,EAAC,IAAI;cAAC2B,IAAI,EAAC,MAAM;cAAAjC,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDlK,OAAA;cAAM4H,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAAC8B,QAAQ,EAAC,IAAI;cAAC2B,IAAI,EAAC,MAAM;cAAAjC,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGtDlK,OAAA;cAAMkL,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,KAAK;cAACC,MAAM,EAAC,MAAM;cAACC,WAAW,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,EAGvE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACtE,GAAG,CAACjB,OAAO,iBACvB3E,OAAA;cAEEkL,EAAE,EAAE,GAAG,GAAIvG,OAAO,GAAG,GAAK;cAC1BwG,EAAE,EAAC,IAAI;cACPC,EAAE,EAAE,GAAG,GAAIzG,OAAO,GAAG,GAAK;cAC1B0G,EAAE,EAAC,KAAK;cACRC,MAAM,EAAC,MAAM;cACbC,WAAW,EAAC;YAAG,GANV5G,OAAO;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOb,CACF,CAAC,EAGD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACtE,GAAG,CAACjB,OAAO,iBACvB3E,OAAA;cAEE4H,CAAC,EAAE,GAAG,GAAI,CAACjD,OAAO,GAAG,CAAC,IAAI,GAAI,GAAG,EAAG;cACpCoD,CAAC,EAAC,IAAI;cACN8B,QAAQ,EAAC,IAAI;cACb2B,IAAI,EAAC,MAAM;cACXC,UAAU,EAAC,QAAQ;cAAAlC,QAAA,EAElB5E;YAAO,GAPHA,OAAO;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQR,CACP,CAAC,eAGFlK,OAAA;cACE4H,CAAC,EAAC,KAAK;cACPG,CAAC,EAAC,IAAI;cACN2D,KAAK,EAAC,KAAK;cACX9B,MAAM,EAAC,KAAK;cACZ4B,IAAI,EAAC,aAAa;cAClBhC,KAAK,EAAE;gBAAEmC,MAAM,EAAE;cAAY,CAAE;cAC/BhB,OAAO,EAAEvD;YAAiB;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,EAGD/G,WAAW,CAACyC,GAAG,CAAC,CAACR,IAAI,EAAEwG,KAAK,KAAK;cAChC,MAAMhE,CAAC,GAAG,GAAG,GAAI,CAACxC,IAAI,CAACC,QAAQ,CAACV,OAAO,GAAG,CAAC,IAAI,GAAI,GAAIS,IAAI,CAACC,QAAQ,CAACiB,IAAI,GAAG,EAAG;cAC/E,MAAM6B,aAAwC,GAAG;gBAC/C,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAChD,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE;cAC9D,CAAC;cACD,MAAMJ,CAAC,GAAGI,aAAa,CAAC,GAAG/C,IAAI,CAAC6D,IAAI,GAAG7D,IAAI,CAACgD,MAAM,EAAE,CAAC,IAAI,EAAE;cAE3D,oBACEpI,OAAA;gBAAAuJ,QAAA,GAEG,CAACxB,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,kBACjB/H,OAAA;kBACEkL,EAAE,EAAEtD,CAAC,GAAG,EAAG;kBACXuD,EAAE,EAAEpD,CAAE;kBACNqD,EAAE,EAAExD,CAAC,GAAG,EAAG;kBACXyD,EAAE,EAAEtD,CAAE;kBACNuD,MAAM,EAAC,MAAM;kBACbC,WAAW,EAAC,GAAG;kBACfM,eAAe,EAAC;gBAAK;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CACF,EAGA9E,IAAI,CAACgE,MAAM;gBAAA;gBACV;gBACApJ,OAAA;kBAAAuJ,QAAA,gBACEvJ,OAAA;oBACE4H,CAAC,EAAEA,CAAC,GAAG,CAAE;oBACTG,CAAC,EAAEA,CAAC,GAAG,CAAE;oBACT2D,KAAK,EAAC,GAAG;oBACT9B,MAAM,EAAC,GAAG;oBACV4B,IAAI,EAAC;kBAAM;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACFlK,OAAA;oBACE4H,CAAC,EAAEA,CAAC,GAAG,CAAE;oBACTG,CAAC,EAAEA,CAAC,GAAG,CAAE;oBACT2D,KAAK,EAAC,GAAG;oBACT9B,MAAM,EAAC,GAAG;oBACV4B,IAAI,EAAC;kBAAM;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;gBAAA;gBAEJ;gBACAlK,OAAA;kBACE8L,EAAE,EAAElE,CAAE;kBACNmE,EAAE,EAAEhE,CAAE;kBACNiE,EAAE,EAAC,GAAG;kBACNC,EAAE,EAAC,GAAG;kBACNT,IAAI,EAAEpG,IAAI,CAAC8D,QAAQ,KAAK,OAAO,IAAI9D,IAAI,CAAC8D,QAAQ,KAAK,MAAM,GAAG,OAAO,GAAG,MAAO;kBAC/EoC,MAAM,EAAC,MAAM;kBACbC,WAAW,EAAC,GAAG;kBACfW,SAAS,EAAE,cAActE,CAAC,IAAIG,CAAC,GAAI;kBACnCyB,KAAK,EAAE;oBAAEmC,MAAM,EAAE;kBAAU,CAAE;kBAC7BhB,OAAO,EAAGJ,CAAC,IAAK;oBACdA,CAAC,CAAC4B,eAAe,CAAC,CAAC;oBACnB/I,cAAc,CAACiG,IAAI,IAAIA,IAAI,CAACwB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChF,EAAE,KAAKV,IAAI,CAACU,EAAE,CAAC,CAAC;kBAC5D;gBAAE;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF,EAGA,CAAC9E,IAAI,CAACgE,MAAM,IAAIhE,IAAI,CAAC8D,QAAQ,KAAK,OAAO,IAAI9D,IAAI,CAAC8D,QAAQ,KAAK,MAAM,iBACpElJ,OAAA;kBACEkL,EAAE,EAAEtD,CAAC,IAAIG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAE;kBAC1BoD,EAAE,EAAEpD,CAAE;kBACNqD,EAAE,EAAExD,CAAC,IAAIG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAE;kBAC1BsD,EAAE,EAAEtD,CAAC,IAAIA,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAE;kBAC5BuD,MAAM,EAAC,MAAM;kBACbC,WAAW,EAAC;gBAAG;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CACF,EAGA,CAAC9E,IAAI,CAACgE,MAAM,KAAKhE,IAAI,CAAC8D,QAAQ,KAAK,QAAQ,IAAI9D,IAAI,CAAC8D,QAAQ,KAAK,WAAW,CAAC,iBAC5ElJ,OAAA;kBACEoM,CAAC,EAAErE,CAAC,GAAG,EAAE,GACP,KAAKH,CAAC,GAAG,CAAC,IAAIG,CAAC,GAAG,EAAE,MAAMH,CAAC,GAAG,EAAE,IAAIG,CAAC,GAAG,EAAE,IAAIH,CAAC,GAAG,EAAE,IAAIG,CAAC,GAAG,EAAE,EAAE,GAChE,KAAKH,CAAC,GAAG,CAAC,IAAIG,CAAC,GAAG,EAAE,MAAMH,CAAC,GAAG,EAAE,IAAIG,CAAC,GAAG,EAAE,IAAIH,CAAC,GAAG,EAAE,IAAIG,CAAC,GAAG,EAAE,EAC/D;kBACDyD,IAAI,EAAC;gBAAM;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CACF,eAGDlK,OAAA;kBACE4H,CAAC,EAAEA,CAAE;kBACLG,CAAC,EAAEA,CAAC,GAAG,EAAG;kBACV8B,QAAQ,EAAC,IAAI;kBACb2B,IAAI,EAAC,MAAM;kBACXC,UAAU,EAAC,QAAQ;kBACnBjC,KAAK,EAAE;oBAAE6C,aAAa,EAAE;kBAAO,CAAE;kBAAA9C,QAAA,GAEhCnE,IAAI,CAAC6D,IAAI,EAAE7D,IAAI,CAACgD,MAAM;gBAAA;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA,GArFD9E,IAAI,CAACU,EAAE;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsFZ,CAAC;YAER,CAAC,CAAC,EAGD/G,WAAW,CAACU,MAAM,KAAK,CAAC,iBACvB7D,OAAA;cAAM4H,CAAC,EAAC,KAAK;cAACG,CAAC,EAAC,KAAK;cAAC8B,QAAQ,EAAC,IAAI;cAAC2B,IAAI,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAlC,QAAA,EAAC;YAEpE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MACf;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB,CAAC;AAAC7H,EAAA,CAzfWF,WAAuC;EAAA,QAC1BzC,OAAO;AAAA;AAAA4M,IAAA,GADpBnK,WAAuC;AAAA,IAAAhC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAoK,IAAA;AAAAC,YAAA,CAAApM,EAAA;AAAAoM,YAAA,CAAAlM,GAAA;AAAAkM,YAAA,CAAAhM,GAAA;AAAAgM,YAAA,CAAA9L,GAAA;AAAA8L,YAAA,CAAAzL,GAAA;AAAAyL,YAAA,CAAAvL,GAAA;AAAAuL,YAAA,CAAArL,GAAA;AAAAqL,YAAA,CAAAnL,GAAA;AAAAmL,YAAA,CAAAjL,GAAA;AAAAiL,YAAA,CAAA9K,GAAA;AAAA8K,YAAA,CAAA5K,GAAA;AAAA4K,YAAA,CAAA1K,IAAA;AAAA0K,YAAA,CAAAvK,IAAA;AAAAuK,YAAA,CAAArK,IAAA;AAAAqK,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}