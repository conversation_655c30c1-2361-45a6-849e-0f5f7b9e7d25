{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { FiMusic, FiUser, FiLogOut, FiHome, FiPlus } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LayoutContainer = styled.div`\n  display: flex;\n  min-height: 100vh;\n  background-color: #f8f9fa;\n`;\n_c = LayoutContainer;\nconst Sidebar = styled.nav`\n  width: 250px;\n  background-color: #2c3e50;\n  color: white;\n  padding: 1rem;\n  box-shadow: 2px 0 5px rgba(0,0,0,0.1);\n`;\n_c2 = Sidebar;\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.5rem;\n  font-weight: bold;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #34495e;\n`;\n_c3 = Logo;\nconst NavItem = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  width: 100%;\n  padding: 0.75rem;\n  background: none;\n  border: none;\n  color: white;\n  text-align: left;\n  cursor: pointer;\n  border-radius: 0.5rem;\n  transition: background-color 0.2s;\n  margin-bottom: 0.5rem;\n\n  &:hover {\n    background-color: #34495e;\n  }\n\n  &.active {\n    background-color: #3498db;\n  }\n`;\n_c4 = NavItem;\nconst MainContent = styled.main`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n`;\n_c5 = MainContent;\nconst Header = styled.header`\n  background-color: white;\n  padding: 1rem 2rem;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c6 = Header;\nconst UserInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;\n_c7 = UserInfo;\nconst UserName = styled.span`\n  font-weight: 500;\n`;\n_c8 = UserName;\nconst LogoutButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  background-color: #e74c3c;\n  color: white;\n  border: none;\n  border-radius: 0.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #c0392b;\n  }\n`;\n_c9 = LogoutButton;\nconst ContentArea = styled.div`\n  flex: 1;\n  padding: 2rem;\n  overflow-y: auto;\n`;\n_c0 = ContentArea;\nexport const Layout = ({\n  children,\n  currentPage = 'home',\n  onNavigate = () => {}\n}) => {\n  _s();\n  const {\n    currentUser,\n    logout\n  } = useAuth();\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Erro ao fazer logout:', error);\n    }\n  };\n  const navigationItems = [{\n    id: 'home',\n    label: 'Início',\n    icon: FiHome\n  }, {\n    id: 'scores',\n    label: 'Minhas Partituras',\n    icon: FiMusic\n  }, {\n    id: 'new-score',\n    label: 'Nova Partitura',\n    icon: FiPlus\n  }];\n  return /*#__PURE__*/_jsxDEV(LayoutContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: [/*#__PURE__*/_jsxDEV(FiMusic, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), \"Partitura Digital\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), navigationItems.map(item => /*#__PURE__*/_jsxDEV(NavItem, {\n        className: currentPage === item.id ? 'active' : '',\n        onClick: () => onNavigate(item.id),\n        children: [/*#__PURE__*/_jsxDEV(item.icon, {\n          size: 18\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), item.label]\n      }, item.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Partitura Digital\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(UserInfo, {\n          children: [/*#__PURE__*/_jsxDEV(FiUser, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(UserName, {\n            children: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.displayName) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LogoutButton, {\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(FiLogOut, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), \"Sair\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ContentArea, {\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"irPLFJ3DqMTL8RNRVsehGT4mySY=\", false, function () {\n  return [useAuth];\n});\n_c1 = Layout;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"LayoutContainer\");\n$RefreshReg$(_c2, \"Sidebar\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"NavItem\");\n$RefreshReg$(_c5, \"MainContent\");\n$RefreshReg$(_c6, \"Header\");\n$RefreshReg$(_c7, \"UserInfo\");\n$RefreshReg$(_c8, \"UserName\");\n$RefreshReg$(_c9, \"LogoutButton\");\n$RefreshReg$(_c0, \"ContentArea\");\n$RefreshReg$(_c1, \"Layout\");", "map": {"version": 3, "names": ["React", "styled", "useAuth", "FiMusic", "FiUser", "FiLogOut", "FiHome", "FiPlus", "jsxDEV", "_jsxDEV", "LayoutContainer", "div", "_c", "Sidebar", "nav", "_c2", "Logo", "_c3", "NavItem", "button", "_c4", "MainContent", "main", "_c5", "Header", "header", "_c6", "UserInfo", "_c7", "UserName", "span", "_c8", "LogoutButton", "_c9", "ContentArea", "_c0", "Layout", "children", "currentPage", "onNavigate", "_s", "currentUser", "logout", "handleLogout", "error", "console", "navigationItems", "id", "label", "icon", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "className", "onClick", "displayName", "email", "_c1", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/Layout/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { FiMusic, FiUser, FiLogOut, FiHome, FiPlus } from 'react-icons/fi';\n\nconst LayoutContainer = styled.div`\n  display: flex;\n  min-height: 100vh;\n  background-color: #f8f9fa;\n`;\n\nconst Sidebar = styled.nav`\n  width: 250px;\n  background-color: #2c3e50;\n  color: white;\n  padding: 1rem;\n  box-shadow: 2px 0 5px rgba(0,0,0,0.1);\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.5rem;\n  font-weight: bold;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #34495e;\n`;\n\nconst NavItem = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  width: 100%;\n  padding: 0.75rem;\n  background: none;\n  border: none;\n  color: white;\n  text-align: left;\n  cursor: pointer;\n  border-radius: 0.5rem;\n  transition: background-color 0.2s;\n  margin-bottom: 0.5rem;\n\n  &:hover {\n    background-color: #34495e;\n  }\n\n  &.active {\n    background-color: #3498db;\n  }\n`;\n\nconst MainContent = styled.main`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst Header = styled.header`\n  background-color: white;\n  padding: 1rem 2rem;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst UserInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;\n\nconst UserName = styled.span`\n  font-weight: 500;\n`;\n\nconst LogoutButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  background-color: #e74c3c;\n  color: white;\n  border: none;\n  border-radius: 0.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #c0392b;\n  }\n`;\n\nconst ContentArea = styled.div`\n  flex: 1;\n  padding: 2rem;\n  overflow-y: auto;\n`;\n\ninterface LayoutProps {\n  children: React.ReactNode;\n  currentPage?: string;\n  onNavigate?: (page: string) => void;\n}\n\nexport const Layout: React.FC<LayoutProps> = ({ \n  children, \n  currentPage = 'home',\n  onNavigate = () => {}\n}) => {\n  const { currentUser, logout } = useAuth();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Erro ao fazer logout:', error);\n    }\n  };\n\n  const navigationItems = [\n    { id: 'home', label: 'Início', icon: FiHome },\n    { id: 'scores', label: 'Minhas Partituras', icon: FiMusic },\n    { id: 'new-score', label: 'Nova Partitura', icon: FiPlus },\n  ];\n\n  return (\n    <LayoutContainer>\n      <Sidebar>\n        <Logo>\n          <FiMusic size={24} />\n          Partitura Digital\n        </Logo>\n        \n        {navigationItems.map(item => (\n          <NavItem\n            key={item.id}\n            className={currentPage === item.id ? 'active' : ''}\n            onClick={() => onNavigate(item.id)}\n          >\n            <item.icon size={18} />\n            {item.label}\n          </NavItem>\n        ))}\n      </Sidebar>\n\n      <MainContent>\n        <Header>\n          <h1>Partitura Digital</h1>\n          <UserInfo>\n            <FiUser size={20} />\n            <UserName>{currentUser?.displayName || currentUser?.email}</UserName>\n            <LogoutButton onClick={handleLogout}>\n              <FiLogOut size={16} />\n              Sair\n            </LogoutButton>\n          </UserInfo>\n        </Header>\n\n        <ContentArea>\n          {children}\n        </ContentArea>\n      </MainContent>\n    </LayoutContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAMC,eAAe,GAAGT,MAAM,CAACU,GAAG;AAClC;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,eAAe;AAMrB,MAAMG,OAAO,GAAGZ,MAAM,CAACa,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,OAAO;AAQb,MAAMG,IAAI,GAAGf,MAAM,CAACU,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GATID,IAAI;AAWV,MAAME,OAAO,GAAGjB,MAAM,CAACkB,MAAM;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAtBIF,OAAO;AAwBb,MAAMG,WAAW,GAAGpB,MAAM,CAACqB,IAAI;AAC/B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,WAAW;AAMjB,MAAMG,MAAM,GAAGvB,MAAM,CAACwB,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,MAAM;AASZ,MAAMG,QAAQ,GAAG1B,MAAM,CAACU,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAG5B,MAAM,CAAC6B,IAAI;AAC5B;AACA,CAAC;AAACC,GAAA,GAFIF,QAAQ;AAId,MAAMG,YAAY,GAAG/B,MAAM,CAACkB,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAfID,YAAY;AAiBlB,MAAME,WAAW,GAAGjC,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GAJID,WAAW;AAYjB,OAAO,MAAME,MAA6B,GAAGA,CAAC;EAC5CC,QAAQ;EACRC,WAAW,GAAG,MAAM;EACpBC,UAAU,GAAGA,CAAA,KAAM,CAAC;AACtB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,WAAW;IAAEC;EAAO,CAAC,GAAGxC,OAAO,CAAC,CAAC;EAEzC,MAAMyC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMD,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAME,eAAe,GAAG,CACtB;IAAEC,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE3C;EAAO,CAAC,EAC7C;IAAEyC,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE9C;EAAQ,CAAC,EAC3D;IAAE4C,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,IAAI,EAAE1C;EAAO,CAAC,CAC3D;EAED,oBACEE,OAAA,CAACC,eAAe;IAAA2B,QAAA,gBACd5B,OAAA,CAACI,OAAO;MAAAwB,QAAA,gBACN5B,OAAA,CAACO,IAAI;QAAAqB,QAAA,gBACH5B,OAAA,CAACN,OAAO;UAAC+C,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAEvB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAENR,eAAe,CAACS,GAAG,CAACC,IAAI,iBACvB/C,OAAA,CAACS,OAAO;QAENuC,SAAS,EAAEnB,WAAW,KAAKkB,IAAI,CAACT,EAAE,GAAG,QAAQ,GAAG,EAAG;QACnDW,OAAO,EAAEA,CAAA,KAAMnB,UAAU,CAACiB,IAAI,CAACT,EAAE,CAAE;QAAAV,QAAA,gBAEnC5B,OAAA,CAAC+C,IAAI,CAACP,IAAI;UAACC,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACtBE,IAAI,CAACR,KAAK;MAAA,GALNQ,IAAI,CAACT,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAML,CACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEV7C,OAAA,CAACY,WAAW;MAAAgB,QAAA,gBACV5B,OAAA,CAACe,MAAM;QAAAa,QAAA,gBACL5B,OAAA;UAAA4B,QAAA,EAAI;QAAiB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1B7C,OAAA,CAACkB,QAAQ;UAAAU,QAAA,gBACP5B,OAAA,CAACL,MAAM;YAAC8C,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpB7C,OAAA,CAACoB,QAAQ;YAAAQ,QAAA,EAAE,CAAAI,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkB,WAAW,MAAIlB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,KAAK;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACrE7C,OAAA,CAACuB,YAAY;YAAC0B,OAAO,EAAEf,YAAa;YAAAN,QAAA,gBAClC5B,OAAA,CAACJ,QAAQ;cAAC6C,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAExB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAET7C,OAAA,CAACyB,WAAW;QAAAG,QAAA,EACTA;MAAQ;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEtB,CAAC;AAACd,EAAA,CA5DWJ,MAA6B;EAAA,QAKRlC,OAAO;AAAA;AAAA2D,GAAA,GAL5BzB,MAA6B;AAAA,IAAAxB,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAA0B,GAAA;AAAAC,YAAA,CAAAlD,EAAA;AAAAkD,YAAA,CAAA/C,GAAA;AAAA+C,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAApC,GAAA;AAAAoC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}