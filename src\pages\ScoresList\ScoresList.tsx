import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { Score } from '../../types/music';
import { ScoreService } from '../../services/scoreService';

const ScoresContainer = styled.div`
  padding: 2rem;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  color: white;
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
`;

const SearchBar = styled.input`
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 12px;
  background: rgba(255,255,255,0.1);
  color: white;
  font-size: 1rem;
  width: 300px;
  backdrop-filter: blur(10px);
  
  &::placeholder {
    color: rgba(255,255,255,0.7);
  }
  
  &:focus {
    outline: none;
    border-color: rgba(255,255,255,0.6);
    background: rgba(255,255,255,0.2);
  }
`;

const ScoresGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
`;

const ScoreCard = styled.div`
  background: rgba(255,255,255,0.95);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
  }
`;

const ScoreTitle = styled.h3`
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
`;

const ScoreInfo = styled.div`
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
`;

const ScoreActions = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => {
    switch (props.variant) {
      case 'primary':
        return `
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          &:hover { transform: translateY(-1px); }
        `;
      case 'danger':
        return `
          background: #e74c3c;
          color: white;
          &:hover { background: #c0392b; }
        `;
      default:
        return `
          background: #f8f9fa;
          color: #495057;
          border: 1px solid #dee2e6;
          &:hover { background: #e9ecef; }
        `;
    }
  }}
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(255,255,255,0.8);
  
  h3 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
  }
  
  p {
    margin: 0 0 2rem 0;
    font-size: 1.1rem;
  }
`;

const CreateButton = styled.button`
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  }
`;

const LoadingState = styled.div`
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(255,255,255,0.8);
  font-size: 1.2rem;
`;

interface ScoresListProps {
  onCreateNew: () => void;
  onEditScore: (scoreId: string) => void;
}

export const ScoresList: React.FC<ScoresListProps> = ({ onCreateNew, onEditScore }) => {
  const { currentUser } = useAuth();
  const [scores, setScores] = useState<Score[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadScores();
  }, [currentUser]);

  const loadScores = async () => {
    if (!currentUser) {
      setLoading(false);
      return;
    }

    try {
      const userScores = await ScoreService.getUserScores(currentUser.uid);
      setScores(userScores);
    } catch (error) {
      console.error('Erro ao carregar partituras:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteScore = async (scoreId: string) => {
    if (!window.confirm('Tem certeza que deseja excluir esta partitura?')) {
      return;
    }

    try {
      await ScoreService.deleteScore(scoreId);
      setScores(scores.filter(score => score.id !== scoreId));
    } catch (error) {
      console.error('Erro ao excluir partitura:', error);
      alert('Erro ao excluir partitura');
    }
  };

  const handleDuplicateScore = async (score: Score) => {
    try {
      const newTitle = `${score.title} (Cópia)`;
      await ScoreService.duplicateScore(score.id, newTitle);
      loadScores(); // Recarregar lista
    } catch (error) {
      console.error('Erro ao duplicar partitura:', error);
      alert('Erro ao duplicar partitura');
    }
  };

  const filteredScores = scores.filter(score =>
    score.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (score.composer && score.composer.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (!currentUser) {
    return (
      <ScoresContainer>
        <EmptyState>
          <h3>🎵 Suas Partituras</h3>
          <p>Faça login para ver e gerenciar suas partituras salvas</p>
        </EmptyState>
      </ScoresContainer>
    );
  }

  if (loading) {
    return (
      <ScoresContainer>
        <LoadingState>
          🎼 Carregando suas partituras...
        </LoadingState>
      </ScoresContainer>
    );
  }

  return (
    <ScoresContainer>
      <Header>
        <Title>🎵 Minhas Partituras</Title>
        <SearchBar
          type="text"
          placeholder="Buscar partituras..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </Header>

      {filteredScores.length === 0 ? (
        <EmptyState>
          {scores.length === 0 ? (
            <>
              <h3>🎼 Nenhuma partitura ainda</h3>
              <p>Comece criando sua primeira partitura musical!</p>
              <CreateButton onClick={onCreateNew}>
                ➕ Criar Nova Partitura
              </CreateButton>
            </>
          ) : (
            <>
              <h3>🔍 Nenhuma partitura encontrada</h3>
              <p>Tente buscar com outros termos</p>
            </>
          )}
        </EmptyState>
      ) : (
        <ScoresGrid>
          {filteredScores.map(score => (
            <ScoreCard key={score.id} onClick={() => onEditScore(score.id)}>
              <ScoreTitle>{score.title}</ScoreTitle>
              <ScoreInfo>
                {score.composer && <div>🎭 Compositor: {score.composer}</div>}
                <div>🎹 Tom: {score.key.note} {score.key.mode}</div>
                <div>⏱️ Compasso: {score.timeSignature.numerator}/{score.timeSignature.denominator}</div>
                <div>📅 Modificado: {formatDate(score.updatedAt)}</div>
              </ScoreInfo>
              
              <ScoreActions onClick={(e) => e.stopPropagation()}>
                <ActionButton 
                  variant="primary"
                  onClick={() => onEditScore(score.id)}
                >
                  ✏️ Editar
                </ActionButton>
                <ActionButton 
                  variant="secondary"
                  onClick={() => handleDuplicateScore(score)}
                >
                  📋 Duplicar
                </ActionButton>
                <ActionButton 
                  variant="danger"
                  onClick={() => handleDeleteScore(score.id)}
                >
                  🗑️ Excluir
                </ActionButton>
              </ScoreActions>
            </ScoreCard>
          ))}
        </ScoresGrid>
      )}
    </ScoresContainer>
  );
};
