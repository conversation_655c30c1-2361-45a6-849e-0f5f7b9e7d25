{"ast": null, "code": "import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\n\n// Configuração do Firebase\n// IMPORTANTE: Substitua essas configurações pelas suas próprias do Firebase Console\n// const firebaseConfig = {\n//   apiKey: \"your-api-key-here\",\n//   authDomain: \"your-project-id.firebaseapp.com\",\n//   projectId: \"your-project-id\",\n//   storageBucket: \"your-project-id.appspot.com\",\n//   messagingSenderId: \"your-sender-id\",\n//   appId: \"your-app-id\"\n// };\n\nconst firebaseConfig = {\n  apiKey: \"AIzaSyBUJQhRgzloPxM9BiWXY_JITyVPmuqplnA\",\n  authDomain: \"partitura-digital.firebaseapp.com\",\n  projectId: \"partitura-digital\",\n  storageBucket: \"partitura-digital.firebasestorage.app\",\n  messagingSenderId: \"235102958549\",\n  appId: \"1:235102958549:web:3e6a5f95d492bc31e9835c\",\n  measurementId: \"G-KQVMBPPNKB\"\n};\n\n// Inicializar Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Inicializar serviços\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "getFirestore", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "app", "auth", "db"], "sources": ["D:/Dev/partitura_digital/src/services/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\n\n// Configuração do Firebase\n// IMPORTANTE: Substitua essas configurações pelas suas próprias do Firebase Console\n// const firebaseConfig = {\n//   apiKey: \"your-api-key-here\",\n//   authDomain: \"your-project-id.firebaseapp.com\",\n//   projectId: \"your-project-id\",\n//   storageBucket: \"your-project-id.appspot.com\",\n//   messagingSenderId: \"your-sender-id\",\n//   appId: \"your-app-id\"\n// };\n\nconst firebaseConfig = {\n  apiKey: \"AIzaSyBUJQhRgzloPxM9BiWXY_JITyVPmuqplnA\",\n  authDomain: \"partitura-digital.firebaseapp.com\",\n  projectId: \"partitura-digital\",\n  storageBucket: \"partitura-digital.firebasestorage.app\",\n  messagingSenderId: \"235102958549\",\n  appId: \"1:235102958549:web:3e6a5f95d492bc31e9835c\",\n  measurementId: \"G-KQVMBPPNKB\"\n};\n\n// Inicializar Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Inicializar serviços\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\n\nexport default app;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,YAAY,QAAQ,oBAAoB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,yCAAyC;EACjDC,UAAU,EAAE,mCAAmC;EAC/CC,SAAS,EAAE,mBAAmB;EAC9BC,aAAa,EAAE,uCAAuC;EACtDC,iBAAiB,EAAE,cAAc;EACjCC,KAAK,EAAE,2CAA2C;EAClDC,aAAa,EAAE;AACjB,CAAC;;AAED;AACA,MAAMC,GAAG,GAAGX,aAAa,CAACG,cAAc,CAAC;;AAEzC;AACA,OAAO,MAAMS,IAAI,GAAGX,OAAO,CAACU,GAAG,CAAC;AAChC,OAAO,MAAME,EAAE,GAAGX,YAAY,CAACS,GAAG,CAAC;AAEnC,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}