{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\utils\\\\icons.tsx\";\nimport React from 'react';\n\n// Componentes de ícones simples usando Unicode\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Icons = {\n  Music: ({\n    size = 20\n  }) => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: size,\n      display: 'inline-block'\n    },\n    children: \"\\uD83C\\uDFB5\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this),\n  User: ({\n    size = 20\n  }) => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: size,\n      display: 'inline-block'\n    },\n    children: \"\\uD83D\\uDC64\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this),\n  LogOut: ({\n    size = 20\n  }) => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: size,\n      display: 'inline-block'\n    },\n    children: \"\\uD83D\\uDEAA\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this),\n  Home: ({\n    size = 20\n  }) => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: size,\n      display: 'inline-block'\n    },\n    children: \"\\uD83C\\uDFE0\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this),\n  Plus: ({\n    size = 20\n  }) => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: size,\n      display: 'inline-block'\n    },\n    children: \"\\u2795\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this),\n  Mail: ({\n    size = 20\n  }) => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: size,\n      display: 'inline-block'\n    },\n    children: \"\\u2709\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this),\n  Lock: ({\n    size = 20\n  }) => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: size,\n      display: 'inline-block'\n    },\n    children: \"\\uD83D\\uDD12\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this),\n  Eye: ({\n    size = 20\n  }) => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: size,\n      display: 'inline-block'\n    },\n    children: \"\\uD83D\\uDC41\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this),\n  EyeOff: ({\n    size = 20\n  }) => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: size,\n      display: 'inline-block'\n    },\n    children: \"\\uD83D\\uDE48\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this)\n};\n\n// Tipo para os ícones", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Icons", "Music", "size", "style", "fontSize", "display", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "User", "LogOut", "Home", "Plus", "Mail", "Lock", "Eye", "Eye<PERSON>ff"], "sources": ["D:/Dev/partitura_digital/src/utils/icons.tsx"], "sourcesContent": ["import React from 'react';\n\n// Componentes de ícones simples usando Unicode\nexport const Icons = {\n  Music: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>🎵</span>\n  ),\n  User: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>👤</span>\n  ),\n  LogOut: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>🚪</span>\n  ),\n  Home: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>🏠</span>\n  ),\n  Plus: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>➕</span>\n  ),\n  Mail: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>✉️</span>\n  ),\n  Lock: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>🔒</span>\n  ),\n  Eye: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>👁️</span>\n  ),\n  EyeOff: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>🙈</span>\n  ),\n};\n\n// Tipo para os ícones\nexport type IconComponent = React.FC<{ size?: number }>;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,KAAK,GAAG;EACnBC,KAAK,EAAEA,CAAC;IAAEC,IAAI,GAAG;EAAsB,CAAC,kBACtCH,OAAA;IAAMI,KAAK,EAAE;MAAEC,QAAQ,EAAEF,IAAI;MAAEG,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CACnE;EACDC,IAAI,EAAEA,CAAC;IAAET,IAAI,GAAG;EAAsB,CAAC,kBACrCH,OAAA;IAAMI,KAAK,EAAE;MAAEC,QAAQ,EAAEF,IAAI;MAAEG,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CACnE;EACDE,MAAM,EAAEA,CAAC;IAAEV,IAAI,GAAG;EAAsB,CAAC,kBACvCH,OAAA;IAAMI,KAAK,EAAE;MAAEC,QAAQ,EAAEF,IAAI;MAAEG,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CACnE;EACDG,IAAI,EAAEA,CAAC;IAAEX,IAAI,GAAG;EAAsB,CAAC,kBACrCH,OAAA;IAAMI,KAAK,EAAE;MAAEC,QAAQ,EAAEF,IAAI;MAAEG,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CACnE;EACDI,IAAI,EAAEA,CAAC;IAAEZ,IAAI,GAAG;EAAsB,CAAC,kBACrCH,OAAA;IAAMI,KAAK,EAAE;MAAEC,QAAQ,EAAEF,IAAI;MAAEG,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAClE;EACDK,IAAI,EAAEA,CAAC;IAAEb,IAAI,GAAG;EAAsB,CAAC,kBACrCH,OAAA;IAAMI,KAAK,EAAE;MAAEC,QAAQ,EAAEF,IAAI;MAAEG,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CACnE;EACDM,IAAI,EAAEA,CAAC;IAAEd,IAAI,GAAG;EAAsB,CAAC,kBACrCH,OAAA;IAAMI,KAAK,EAAE;MAAEC,QAAQ,EAAEF,IAAI;MAAEG,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CACnE;EACDO,GAAG,EAAEA,CAAC;IAAEf,IAAI,GAAG;EAAsB,CAAC,kBACpCH,OAAA;IAAMI,KAAK,EAAE;MAAEC,QAAQ,EAAEF,IAAI;MAAEG,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,EAAC;EAAG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CACpE;EACDQ,MAAM,EAAEA,CAAC;IAAEhB,IAAI,GAAG;EAAsB,CAAC,kBACvCH,OAAA;IAAMI,KAAK,EAAE;MAAEC,QAAQ,EAAEF,IAAI;MAAEG,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AAEtE,CAAC;;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}