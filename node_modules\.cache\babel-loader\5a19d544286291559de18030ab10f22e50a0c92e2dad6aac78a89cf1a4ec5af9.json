{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";// Componente para renderizar uma nota musical com símbolos precisos\nexport const MusicalNote=_ref=>{let{note,x,y,onRemove}=_ref;const isAboveMiddle=y<90;// Determina direção da haste\nconst stemDirection=isAboveMiddle?'down':'up';const stemX=stemDirection==='up'?x+7:x-7;const stemY1=y;const stemY2=stemDirection==='up'?y-25:y+25;// Renderizar pausa\nif(note.isRest){return/*#__PURE__*/_jsxs(\"g\",{children:[note.duration==='quarter'&&/*#__PURE__*/_jsx(\"g\",{children:/*#__PURE__*/_jsx(\"path\",{d:`M ${x-6} ${y-8} L ${x+2} ${y-8} L ${x-2} ${y} L ${x+6} ${y} L ${x+2} ${y+8} L ${x-6} ${y+8} Z`,fill:\"#333\",stroke:\"#333\",strokeWidth:\"1\"})}),note.duration==='half'&&/*#__PURE__*/_jsx(\"rect\",{x:x-4,y:y-2,width:\"8\",height:\"4\",fill:\"#333\"}),note.duration==='whole'&&/*#__PURE__*/_jsx(\"rect\",{x:x-6,y:y-6,width:\"12\",height:\"4\",fill:\"#333\"}),note.duration==='eighth'&&/*#__PURE__*/_jsxs(\"g\",{children:[/*#__PURE__*/_jsx(\"path\",{d:`M ${x-4} ${y-6} Q ${x} ${y-2} ${x-2} ${y+2} Q ${x+2} ${y+6} ${x-4} ${y+4} Z`,fill:\"#333\"}),/*#__PURE__*/_jsx(\"circle\",{cx:x+2,cy:y-4,r:\"1.5\",fill:\"#333\"})]}),note.duration==='sixteenth'&&/*#__PURE__*/_jsxs(\"g\",{children:[/*#__PURE__*/_jsx(\"path\",{d:`M ${x-4} ${y-8} Q ${x} ${y-4} ${x-2} ${y} Q ${x+2} ${y+4} ${x-4} ${y+2} Z`,fill:\"#333\"}),/*#__PURE__*/_jsx(\"circle\",{cx:x+2,cy:y-6,r:\"1.5\",fill:\"#333\"}),/*#__PURE__*/_jsx(\"circle\",{cx:x+3,cy:y-2,r:\"1.5\",fill:\"#333\"})]})]});}// Renderizar nota musical\nreturn/*#__PURE__*/_jsxs(\"g\",{style:{cursor:'pointer'},onClick:e=>{e.stopPropagation();onRemove();},children:[(y<50||y>130)&&/*#__PURE__*/_jsx(\"line\",{x1:x-12,y1:y,x2:x+12,y2:y,stroke:\"#666\",strokeWidth:\"1\"}),note.accidental&&/*#__PURE__*/_jsx(\"text\",{x:x-15,y:y+3,fontSize:\"14\",fill:\"#333\",textAnchor:\"middle\",style:{pointerEvents:'none',userSelect:'none'},children:note.accidental==='sharp'?'♯':note.accidental==='flat'?'♭':note.accidental==='natural'?'♮':''}),/*#__PURE__*/_jsx(\"ellipse\",{cx:x,cy:y,rx:\"7\",ry:\"5\",fill:note.duration==='whole'||note.duration==='half'?'white':'#333',stroke:\"#333\",strokeWidth:\"2\",transform:`rotate(-20 ${x} ${y})`}),note.duration!=='whole'&&/*#__PURE__*/_jsx(\"line\",{x1:stemX,y1:stemY1,x2:stemX,y2:stemY2,stroke:\"#333\",strokeWidth:\"2\"}),note.duration==='eighth'&&/*#__PURE__*/_jsx(\"path\",{d:stemDirection==='up'?`M ${stemX} ${stemY2} Q ${stemX+8} ${stemY2+5} ${stemX+6} ${stemY2+10}`:`M ${stemX} ${stemY2} Q ${stemX-8} ${stemY2-5} ${stemX-6} ${stemY2-10}`,fill:\"#333\"}),note.duration==='sixteenth'&&/*#__PURE__*/_jsxs(\"g\",{children:[/*#__PURE__*/_jsx(\"path\",{d:stemDirection==='up'?`M ${stemX} ${stemY2} Q ${stemX+8} ${stemY2+5} ${stemX+6} ${stemY2+10}`:`M ${stemX} ${stemY2} Q ${stemX-8} ${stemY2-5} ${stemX-6} ${stemY2-10}`,fill:\"#333\"}),/*#__PURE__*/_jsx(\"path\",{d:stemDirection==='up'?`M ${stemX} ${stemY2+6} Q ${stemX+8} ${stemY2+11} ${stemX+6} ${stemY2+16}`:`M ${stemX} ${stemY2-6} Q ${stemX-8} ${stemY2-11} ${stemX-6} ${stemY2-16}`,fill:\"#333\"})]}),/*#__PURE__*/_jsxs(\"text\",{x:x,y:y-20,fontSize:\"9\",fill:\"#666\",textAnchor:\"middle\",style:{pointerEvents:'none',userSelect:'none'},children:[note.name,note.octave]}),/*#__PURE__*/_jsx(\"text\",{x:x+15,y:y+5,fontSize:\"8\",fill:\"#999\",textAnchor:\"start\",style:{pointerEvents:'none',userSelect:'none'},children:note.duration==='whole'?'𝅝':note.duration==='half'?'𝅗𝅥':note.duration==='quarter'?'♩':note.duration==='eighth'?'♫':'♬'})]});};", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "MusicalNote", "_ref", "note", "x", "y", "onRemove", "isAboveMiddle", "stemDirection", "stemX", "stemY1", "stemY2", "isRest", "children", "duration", "d", "fill", "stroke", "strokeWidth", "width", "height", "cx", "cy", "r", "style", "cursor", "onClick", "e", "stopPropagation", "x1", "y1", "x2", "y2", "accidental", "fontSize", "textAnchor", "pointerEvents", "userSelect", "rx", "ry", "transform", "name", "octave"], "sources": ["D:/Dev/partitura_digital/src/components/MusicalNote/MusicalNote.tsx"], "sourcesContent": ["import React from 'react';\nimport { MusicalNote as NoteType, NoteDuration } from '../../types/music';\n\ninterface MusicalNoteProps {\n  note: NoteType;\n  x: number;\n  y: number;\n  onRemove: () => void;\n}\n\n// Componente para renderizar uma nota musical com símbolos precisos\nexport const MusicalNote: React.FC<MusicalNoteProps> = ({ note, x, y, onRemove }) => {\n  const isAboveMiddle = y < 90; // Determina direção da haste\n  const stemDirection = isAboveMiddle ? 'down' : 'up';\n  const stemX = stemDirection === 'up' ? x + 7 : x - 7;\n  const stemY1 = y;\n  const stemY2 = stemDirection === 'up' ? y - 25 : y + 25;\n\n  // Renderizar pausa\n  if (note.isRest) {\n    return (\n      <g>\n        {/* Pausa de semínima */}\n        {note.duration === 'quarter' && (\n          <g>\n            <path\n              d={`M ${x-6} ${y-8} L ${x+2} ${y-8} L ${x-2} ${y} L ${x+6} ${y} L ${x+2} ${y+8} L ${x-6} ${y+8} Z`}\n              fill=\"#333\"\n              stroke=\"#333\"\n              strokeWidth=\"1\"\n            />\n          </g>\n        )}\n        \n        {/* Pausa de mínima */}\n        {note.duration === 'half' && (\n          <rect\n            x={x - 4}\n            y={y - 2}\n            width=\"8\"\n            height=\"4\"\n            fill=\"#333\"\n          />\n        )}\n        \n        {/* Pausa de semibreve */}\n        {note.duration === 'whole' && (\n          <rect\n            x={x - 6}\n            y={y - 6}\n            width=\"12\"\n            height=\"4\"\n            fill=\"#333\"\n          />\n        )}\n        \n        {/* Pausa de colcheia */}\n        {note.duration === 'eighth' && (\n          <g>\n            <path\n              d={`M ${x-4} ${y-6} Q ${x} ${y-2} ${x-2} ${y+2} Q ${x+2} ${y+6} ${x-4} ${y+4} Z`}\n              fill=\"#333\"\n            />\n            <circle cx={x+2} cy={y-4} r=\"1.5\" fill=\"#333\" />\n          </g>\n        )}\n        \n        {/* Pausa de semicolcheia */}\n        {note.duration === 'sixteenth' && (\n          <g>\n            <path\n              d={`M ${x-4} ${y-8} Q ${x} ${y-4} ${x-2} ${y} Q ${x+2} ${y+4} ${x-4} ${y+2} Z`}\n              fill=\"#333\"\n            />\n            <circle cx={x+2} cy={y-6} r=\"1.5\" fill=\"#333\" />\n            <circle cx={x+3} cy={y-2} r=\"1.5\" fill=\"#333\" />\n          </g>\n        )}\n      </g>\n    );\n  }\n\n  // Renderizar nota musical\n  return (\n    <g style={{ cursor: 'pointer' }} onClick={(e) => { e.stopPropagation(); onRemove(); }}>\n      {/* Linha auxiliar para notas fora da pauta */}\n      {(y < 50 || y > 130) && (\n        <line\n          x1={x - 12}\n          y1={y}\n          x2={x + 12}\n          y2={y}\n          stroke=\"#666\"\n          strokeWidth=\"1\"\n        />\n      )}\n      \n      {/* Acidente (sustenido, bemol, bequadro) */}\n      {note.accidental && (\n        <text\n          x={x - 15}\n          y={y + 3}\n          fontSize=\"14\"\n          fill=\"#333\"\n          textAnchor=\"middle\"\n          style={{ pointerEvents: 'none', userSelect: 'none' }}\n        >\n          {note.accidental === 'sharp' ? '♯' :\n           note.accidental === 'flat' ? '♭' :\n           note.accidental === 'natural' ? '♮' : ''}\n        </text>\n      )}\n\n      {/* Cabeça da nota */}\n      <ellipse\n        cx={x}\n        cy={y}\n        rx=\"7\"\n        ry=\"5\"\n        fill={note.duration === 'whole' || note.duration === 'half' ? 'white' : '#333'}\n        stroke=\"#333\"\n        strokeWidth=\"2\"\n        transform={`rotate(-20 ${x} ${y})`}\n      />\n      \n      {/* Haste da nota (exceto semibreve) */}\n      {note.duration !== 'whole' && (\n        <line\n          x1={stemX}\n          y1={stemY1}\n          x2={stemX}\n          y2={stemY2}\n          stroke=\"#333\"\n          strokeWidth=\"2\"\n        />\n      )}\n      \n      {/* Bandeirola para colcheia */}\n      {note.duration === 'eighth' && (\n        <path\n          d={stemDirection === 'up' ? \n            `M ${stemX} ${stemY2} Q ${stemX + 8} ${stemY2 + 5} ${stemX + 6} ${stemY2 + 10}` :\n            `M ${stemX} ${stemY2} Q ${stemX - 8} ${stemY2 - 5} ${stemX - 6} ${stemY2 - 10}`\n          }\n          fill=\"#333\"\n        />\n      )}\n      \n      {/* Bandeirolas duplas para semicolcheia */}\n      {note.duration === 'sixteenth' && (\n        <g>\n          <path\n            d={stemDirection === 'up' ? \n              `M ${stemX} ${stemY2} Q ${stemX + 8} ${stemY2 + 5} ${stemX + 6} ${stemY2 + 10}` :\n              `M ${stemX} ${stemY2} Q ${stemX - 8} ${stemY2 - 5} ${stemX - 6} ${stemY2 - 10}`\n            }\n            fill=\"#333\"\n          />\n          <path\n            d={stemDirection === 'up' ? \n              `M ${stemX} ${stemY2 + 6} Q ${stemX + 8} ${stemY2 + 11} ${stemX + 6} ${stemY2 + 16}` :\n              `M ${stemX} ${stemY2 - 6} Q ${stemX - 8} ${stemY2 - 11} ${stemX - 6} ${stemY2 - 16}`\n            }\n            fill=\"#333\"\n          />\n        </g>\n      )}\n      \n      {/* Ponto de aumento (para notas pontuadas - futuro) */}\n      {/* Pode ser implementado futuramente */}\n      \n      {/* Texto da nota (para debug/referência) */}\n      <text\n        x={x}\n        y={y - 20}\n        fontSize=\"9\"\n        fill=\"#666\"\n        textAnchor=\"middle\"\n        style={{ pointerEvents: 'none', userSelect: 'none' }}\n      >\n        {note.name}{note.octave}\n      </text>\n      \n      {/* Indicador de duração */}\n      <text\n        x={x + 15}\n        y={y + 5}\n        fontSize=\"8\"\n        fill=\"#999\"\n        textAnchor=\"start\"\n        style={{ pointerEvents: 'none', userSelect: 'none' }}\n      >\n        {note.duration === 'whole' ? '𝅝' : \n         note.duration === 'half' ? '𝅗𝅥' :\n         note.duration === 'quarter' ? '♩' :\n         note.duration === 'eighth' ? '♫' : '♬'}\n      </text>\n    </g>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU1B;AACA,MAAO,MAAM,CAAAC,WAAuC,CAAGC,IAAA,EAA8B,IAA7B,CAAEC,IAAI,CAAEC,CAAC,CAAEC,CAAC,CAAEC,QAAS,CAAC,CAAAJ,IAAA,CAC9E,KAAM,CAAAK,aAAa,CAAGF,CAAC,CAAG,EAAE,CAAE;AAC9B,KAAM,CAAAG,aAAa,CAAGD,aAAa,CAAG,MAAM,CAAG,IAAI,CACnD,KAAM,CAAAE,KAAK,CAAGD,aAAa,GAAK,IAAI,CAAGJ,CAAC,CAAG,CAAC,CAAGA,CAAC,CAAG,CAAC,CACpD,KAAM,CAAAM,MAAM,CAAGL,CAAC,CAChB,KAAM,CAAAM,MAAM,CAAGH,aAAa,GAAK,IAAI,CAAGH,CAAC,CAAG,EAAE,CAAGA,CAAC,CAAG,EAAE,CAEvD;AACA,GAAIF,IAAI,CAACS,MAAM,CAAE,CACf,mBACEZ,KAAA,MAAAa,QAAA,EAEGV,IAAI,CAACW,QAAQ,GAAK,SAAS,eAC1BhB,IAAA,MAAAe,QAAA,cACEf,IAAA,SACEiB,CAAC,CAAE,KAAKX,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,MAAMD,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,MAAMD,CAAC,CAAC,CAAC,IAAIC,CAAC,MAAMD,CAAC,CAAC,CAAC,IAAIC,CAAC,MAAMD,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,MAAMD,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,IAAK,CACnGW,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,MAAM,CACbC,WAAW,CAAC,GAAG,CAChB,CAAC,CACD,CACJ,CAGAf,IAAI,CAACW,QAAQ,GAAK,MAAM,eACvBhB,IAAA,SACEM,CAAC,CAAEA,CAAC,CAAG,CAAE,CACTC,CAAC,CAAEA,CAAC,CAAG,CAAE,CACTc,KAAK,CAAC,GAAG,CACTC,MAAM,CAAC,GAAG,CACVJ,IAAI,CAAC,MAAM,CACZ,CACF,CAGAb,IAAI,CAACW,QAAQ,GAAK,OAAO,eACxBhB,IAAA,SACEM,CAAC,CAAEA,CAAC,CAAG,CAAE,CACTC,CAAC,CAAEA,CAAC,CAAG,CAAE,CACTc,KAAK,CAAC,IAAI,CACVC,MAAM,CAAC,GAAG,CACVJ,IAAI,CAAC,MAAM,CACZ,CACF,CAGAb,IAAI,CAACW,QAAQ,GAAK,QAAQ,eACzBd,KAAA,MAAAa,QAAA,eACEf,IAAA,SACEiB,CAAC,CAAE,KAAKX,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,MAAMD,CAAC,IAAIC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,MAAMD,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,IAAK,CACjFW,IAAI,CAAC,MAAM,CACZ,CAAC,cACFlB,IAAA,WAAQuB,EAAE,CAAEjB,CAAC,CAAC,CAAE,CAACkB,EAAE,CAAEjB,CAAC,CAAC,CAAE,CAACkB,CAAC,CAAC,KAAK,CAACP,IAAI,CAAC,MAAM,CAAE,CAAC,EAC/C,CACJ,CAGAb,IAAI,CAACW,QAAQ,GAAK,WAAW,eAC5Bd,KAAA,MAAAa,QAAA,eACEf,IAAA,SACEiB,CAAC,CAAE,KAAKX,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,MAAMD,CAAC,IAAIC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,IAAIC,CAAC,MAAMD,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,IAAK,CAC/EW,IAAI,CAAC,MAAM,CACZ,CAAC,cACFlB,IAAA,WAAQuB,EAAE,CAAEjB,CAAC,CAAC,CAAE,CAACkB,EAAE,CAAEjB,CAAC,CAAC,CAAE,CAACkB,CAAC,CAAC,KAAK,CAACP,IAAI,CAAC,MAAM,CAAE,CAAC,cAChDlB,IAAA,WAAQuB,EAAE,CAAEjB,CAAC,CAAC,CAAE,CAACkB,EAAE,CAAEjB,CAAC,CAAC,CAAE,CAACkB,CAAC,CAAC,KAAK,CAACP,IAAI,CAAC,MAAM,CAAE,CAAC,EAC/C,CACJ,EACA,CAAC,CAER,CAEA;AACA,mBACEhB,KAAA,MAAGwB,KAAK,CAAE,CAAEC,MAAM,CAAE,SAAU,CAAE,CAACC,OAAO,CAAGC,CAAC,EAAK,CAAEA,CAAC,CAACC,eAAe,CAAC,CAAC,CAAEtB,QAAQ,CAAC,CAAC,CAAE,CAAE,CAAAO,QAAA,EAEnF,CAACR,CAAC,CAAG,EAAE,EAAIA,CAAC,CAAG,GAAG,gBACjBP,IAAA,SACE+B,EAAE,CAAEzB,CAAC,CAAG,EAAG,CACX0B,EAAE,CAAEzB,CAAE,CACN0B,EAAE,CAAE3B,CAAC,CAAG,EAAG,CACX4B,EAAE,CAAE3B,CAAE,CACNY,MAAM,CAAC,MAAM,CACbC,WAAW,CAAC,GAAG,CAChB,CACF,CAGAf,IAAI,CAAC8B,UAAU,eACdnC,IAAA,SACEM,CAAC,CAAEA,CAAC,CAAG,EAAG,CACVC,CAAC,CAAEA,CAAC,CAAG,CAAE,CACT6B,QAAQ,CAAC,IAAI,CACblB,IAAI,CAAC,MAAM,CACXmB,UAAU,CAAC,QAAQ,CACnBX,KAAK,CAAE,CAAEY,aAAa,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAAxB,QAAA,CAEpDV,IAAI,CAAC8B,UAAU,GAAK,OAAO,CAAG,GAAG,CACjC9B,IAAI,CAAC8B,UAAU,GAAK,MAAM,CAAG,GAAG,CAChC9B,IAAI,CAAC8B,UAAU,GAAK,SAAS,CAAG,GAAG,CAAG,EAAE,CACrC,CACP,cAGDnC,IAAA,YACEuB,EAAE,CAAEjB,CAAE,CACNkB,EAAE,CAAEjB,CAAE,CACNiC,EAAE,CAAC,GAAG,CACNC,EAAE,CAAC,GAAG,CACNvB,IAAI,CAAEb,IAAI,CAACW,QAAQ,GAAK,OAAO,EAAIX,IAAI,CAACW,QAAQ,GAAK,MAAM,CAAG,OAAO,CAAG,MAAO,CAC/EG,MAAM,CAAC,MAAM,CACbC,WAAW,CAAC,GAAG,CACfsB,SAAS,CAAE,cAAcpC,CAAC,IAAIC,CAAC,GAAI,CACpC,CAAC,CAGDF,IAAI,CAACW,QAAQ,GAAK,OAAO,eACxBhB,IAAA,SACE+B,EAAE,CAAEpB,KAAM,CACVqB,EAAE,CAAEpB,MAAO,CACXqB,EAAE,CAAEtB,KAAM,CACVuB,EAAE,CAAErB,MAAO,CACXM,MAAM,CAAC,MAAM,CACbC,WAAW,CAAC,GAAG,CAChB,CACF,CAGAf,IAAI,CAACW,QAAQ,GAAK,QAAQ,eACzBhB,IAAA,SACEiB,CAAC,CAAEP,aAAa,GAAK,IAAI,CACvB,KAAKC,KAAK,IAAIE,MAAM,MAAMF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,CAAC,IAAIF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,EAAE,EAAE,CAC/E,KAAKF,KAAK,IAAIE,MAAM,MAAMF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,CAAC,IAAIF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,EAAE,EAC9E,CACDK,IAAI,CAAC,MAAM,CACZ,CACF,CAGAb,IAAI,CAACW,QAAQ,GAAK,WAAW,eAC5Bd,KAAA,MAAAa,QAAA,eACEf,IAAA,SACEiB,CAAC,CAAEP,aAAa,GAAK,IAAI,CACvB,KAAKC,KAAK,IAAIE,MAAM,MAAMF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,CAAC,IAAIF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,EAAE,EAAE,CAC/E,KAAKF,KAAK,IAAIE,MAAM,MAAMF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,CAAC,IAAIF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,EAAE,EAC9E,CACDK,IAAI,CAAC,MAAM,CACZ,CAAC,cACFlB,IAAA,SACEiB,CAAC,CAAEP,aAAa,GAAK,IAAI,CACvB,KAAKC,KAAK,IAAIE,MAAM,CAAG,CAAC,MAAMF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,EAAE,IAAIF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,EAAE,EAAE,CACpF,KAAKF,KAAK,IAAIE,MAAM,CAAG,CAAC,MAAMF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,EAAE,IAAIF,KAAK,CAAG,CAAC,IAAIE,MAAM,CAAG,EAAE,EACnF,CACDK,IAAI,CAAC,MAAM,CACZ,CAAC,EACD,CACJ,cAMDhB,KAAA,SACEI,CAAC,CAAEA,CAAE,CACLC,CAAC,CAAEA,CAAC,CAAG,EAAG,CACV6B,QAAQ,CAAC,GAAG,CACZlB,IAAI,CAAC,MAAM,CACXmB,UAAU,CAAC,QAAQ,CACnBX,KAAK,CAAE,CAAEY,aAAa,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAAxB,QAAA,EAEpDV,IAAI,CAACsC,IAAI,CAAEtC,IAAI,CAACuC,MAAM,EACnB,CAAC,cAGP5C,IAAA,SACEM,CAAC,CAAEA,CAAC,CAAG,EAAG,CACVC,CAAC,CAAEA,CAAC,CAAG,CAAE,CACT6B,QAAQ,CAAC,GAAG,CACZlB,IAAI,CAAC,MAAM,CACXmB,UAAU,CAAC,OAAO,CAClBX,KAAK,CAAE,CAAEY,aAAa,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAAxB,QAAA,CAEpDV,IAAI,CAACW,QAAQ,GAAK,OAAO,CAAG,IAAI,CAChCX,IAAI,CAACW,QAAQ,GAAK,MAAM,CAAG,MAAM,CACjCX,IAAI,CAACW,QAAQ,GAAK,SAAS,CAAG,GAAG,CACjCX,IAAI,CAACW,QAAQ,GAAK,QAAQ,CAAG,GAAG,CAAG,GAAG,CACnC,CAAC,EACN,CAAC,CAER,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}