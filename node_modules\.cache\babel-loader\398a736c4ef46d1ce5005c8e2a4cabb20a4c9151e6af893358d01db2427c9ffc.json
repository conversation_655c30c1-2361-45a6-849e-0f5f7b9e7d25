{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ProfessionalScore\\\\ProfessionalScore.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport styled from 'styled-components';\n// import { Factory, Renderer, Stave, StaveNote, Voice, Formatter, Accidental } from 'vexflow';\nimport * as Tone from 'tone';\nimport { getOrchestralInstrument } from '../../utils/orchestralInstruments';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScoreContainer = styled.div`\n  background: white;\n  border-radius: 16px;\n  padding: 2rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  margin: 1rem 0;\n  overflow-x: auto;\n`;\n_c = ScoreContainer;\nconst ScoreHeader = styled.div`\n  text-align: center;\n  margin-bottom: 2rem;\n  border-bottom: 2px solid #e9ecef;\n  padding-bottom: 1rem;\n`;\n_c2 = ScoreHeader;\nconst ScoreTitle = styled.h1`\n  font-size: 2rem;\n  color: #2c3e50;\n  margin: 0 0 0.5rem 0;\n  font-family: 'Times New Roman', serif;\n`;\n_c3 = ScoreTitle;\nconst ScoreSubtitle = styled.div`\n  font-size: 1.1rem;\n  color: #666;\n  font-style: italic;\n`;\n_c4 = ScoreSubtitle;\nconst ScoreCanvas = styled.div`\n  min-height: 400px;\n  width: 100%;\n  \n  svg {\n    width: 100%;\n    height: auto;\n  }\n`;\n_c5 = ScoreCanvas;\nconst PlaybackControls = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 1rem;\n  margin: 1rem 0;\n  padding: 1rem;\n  background: #f8f9fa;\n  border-radius: 12px;\n`;\n_c6 = PlaybackControls;\nconst PlayButton = styled.button`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 50px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.isPlaying ? `\n    background: #e74c3c;\n    color: white;\n    &:hover { background: #c0392b; }\n  ` : `\n    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\n    color: white;\n    &:hover { \n      background: linear-gradient(135deg, #229954 0%, #27ae60 100%);\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);\n    }\n  `}\n`;\n_c7 = PlayButton;\nconst TempoControl = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  \n  label {\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input {\n    width: 80px;\n    padding: 0.5rem;\n    border: 1px solid #dee2e6;\n    border-radius: 6px;\n    text-align: center;\n  }\n`;\n_c8 = TempoControl;\nconst ProgressBar = styled.div`\n  flex: 1;\n  height: 6px;\n  background: #e9ecef;\n  border-radius: 3px;\n  overflow: hidden;\n  position: relative;\n`;\n_c9 = ProgressBar;\nconst ProgressFill = styled.div`\n  height: 100%;\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\n  width: ${props => props.progress}%;\n  transition: width 0.1s ease;\n`;\n_c0 = ProgressFill;\nconst PlaybackLine = styled.line`\n  stroke: #e74c3c;\n  stroke-width: 2;\n  opacity: ${props => props.visible ? 1 : 0};\n  transition: opacity 0.2s ease;\n`;\nexport const ProfessionalScore = ({\n  title,\n  composer,\n  notes,\n  lyrics,\n  instruments,\n  tempo,\n  onTempoChange,\n  onNoteClick\n}) => {\n  _s();\n  const scoreRef = useRef(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [playbackProgress, setPlaybackProgress] = useState(0);\n  const [playbackPosition, setPlaybackPosition] = useState(0);\n  const [synth, setSynth] = useState(null);\n  const [renderer, setRenderer] = useState(null);\n\n  // Inicializar Tone.js\n  useEffect(() => {\n    const synthInstance = new Tone.PolySynth(Tone.Synth).toDestination();\n    setSynth(synthInstance);\n    return () => {\n      synthInstance.dispose();\n    };\n  }, []);\n\n  // Renderizar partitura (versão simplificada)\n  useEffect(() => {\n    if (!scoreRef.current) return;\n\n    // Limpar canvas anterior\n    scoreRef.current.innerHTML = '';\n\n    // Criar SVG simples para demonstração\n    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    svg.setAttribute('width', '800');\n    svg.setAttribute('height', '600');\n    svg.style.background = 'white';\n    svg.style.border = '1px solid #e9ecef';\n    svg.style.borderRadius = '8px';\n\n    // Adicionar título\n    const titleText = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n    titleText.setAttribute('x', '400');\n    titleText.setAttribute('y', '30');\n    titleText.setAttribute('text-anchor', 'middle');\n    titleText.setAttribute('font-size', '18');\n    titleText.setAttribute('font-weight', 'bold');\n    titleText.textContent = title || 'Partitura';\n    svg.appendChild(titleText);\n\n    // Criar pautas para cada instrumento\n    let yPosition = 60;\n    const systemHeight = 120;\n    instruments.forEach((instrumentId, instrumentIndex) => {\n      const template = getOrchestralInstrument(instrumentId);\n\n      // Desenhar 5 linhas da pauta\n      for (let i = 0; i < 5; i++) {\n        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');\n        line.setAttribute('x1', '50');\n        line.setAttribute('y1', (yPosition + i * 10).toString());\n        line.setAttribute('x2', '750');\n        line.setAttribute('y2', (yPosition + i * 10).toString());\n        line.setAttribute('stroke', '#000');\n        line.setAttribute('stroke-width', '1');\n        svg.appendChild(line);\n      }\n\n      // Adicionar nome do instrumento\n      const instrumentText = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n      instrumentText.setAttribute('x', '10');\n      instrumentText.setAttribute('y', (yPosition + 20).toString());\n      instrumentText.setAttribute('font-size', '12');\n      instrumentText.setAttribute('font-weight', 'bold');\n      instrumentText.textContent = template.name;\n      svg.appendChild(instrumentText);\n\n      // Adicionar clave\n      const clefText = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n      clefText.setAttribute('x', '60');\n      clefText.setAttribute('y', (yPosition + 25).toString());\n      clefText.setAttribute('font-size', '24');\n      clefText.textContent = template.clef === 'treble' ? '𝄞' : template.clef === 'bass' ? '𝄢' : '𝄡';\n      svg.appendChild(clefText);\n\n      // Renderizar notas deste instrumento\n      const instrumentNotes = notes.filter(note => note.position.staff === instrumentIndex);\n      instrumentNotes.forEach((note, noteIndex) => {\n        const x = 100 + (note.position.measure - 1) * 150 + (note.position.beat - 1) * 30;\n        const y = yPosition + 20; // Posição simplificada\n\n        // Desenhar nota\n        const noteCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');\n        noteCircle.setAttribute('cx', x.toString());\n        noteCircle.setAttribute('cy', y.toString());\n        noteCircle.setAttribute('r', '4');\n        noteCircle.setAttribute('fill', note.isRest ? 'none' : '#000');\n        noteCircle.setAttribute('stroke', '#000');\n        svg.appendChild(noteCircle);\n\n        // Adicionar nome da nota\n        const noteText = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n        noteText.setAttribute('x', x.toString());\n        noteText.setAttribute('y', (y + 20).toString());\n        noteText.setAttribute('font-size', '10');\n        noteText.setAttribute('text-anchor', 'middle');\n        noteText.textContent = note.isRest ? '𝄽' : note.name;\n        svg.appendChild(noteText);\n      });\n\n      // Adicionar letras\n      const instrumentLyrics = lyrics.filter(lyric => !lyric.position.staff || lyric.position.staff === instrumentIndex);\n      instrumentLyrics.forEach(lyric => {\n        const x = 100 + (lyric.position.measure - 1) * 150 + (lyric.position.beat - 1) * 30;\n        const y = yPosition + 60;\n        const lyricText = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n        lyricText.setAttribute('x', x.toString());\n        lyricText.setAttribute('y', y.toString());\n        lyricText.setAttribute('font-size', '12');\n        lyricText.setAttribute('text-anchor', 'middle');\n        lyricText.textContent = lyric.text;\n        svg.appendChild(lyricText);\n      });\n      yPosition += systemHeight;\n    });\n    scoreRef.current.appendChild(svg);\n  }, [notes, lyrics, instruments, title]);\n\n  // Função para converter duração para formato VexFlow\n  const convertDurationToVex = duration => {\n    const durationMap = {\n      'whole': 'w',\n      'half': 'h',\n      'quarter': 'q',\n      'eighth': '8',\n      'sixteenth': '16'\n    };\n    return durationMap[duration] || 'q';\n  };\n\n  // Função para reproduzir partitura\n  const handlePlay = async () => {\n    if (!synth) return;\n    if (isPlaying) {\n      // Parar reprodução\n      Tone.Transport.stop();\n      Tone.Transport.cancel();\n      setIsPlaying(false);\n      setPlaybackProgress(0);\n      setPlaybackPosition(0);\n      return;\n    }\n\n    // Iniciar Tone.js se necessário\n    if (Tone.context.state !== 'running') {\n      await Tone.start();\n    }\n\n    // Configurar tempo\n    Tone.Transport.bpm.value = tempo;\n\n    // Agendar notas para reprodução\n    const sortedNotes = [...notes].filter(note => !note.isRest).sort((a, b) => {\n      if (a.position.measure !== b.position.measure) {\n        return a.position.measure - b.position.measure;\n      }\n      return a.position.beat - b.position.beat;\n    });\n    sortedNotes.forEach(note => {\n      const time = ((note.position.measure - 1) * 4 + (note.position.beat - 1)) * (60 / tempo);\n      const noteName = `${note.name}${note.octave}`;\n      const duration = getDurationInSeconds(note.duration, tempo);\n      Tone.Transport.schedule(time => {\n        synth.triggerAttackRelease(noteName, duration);\n      }, time);\n    });\n\n    // Calcular duração total\n    const totalDuration = sortedNotes.length > 0 ? ((sortedNotes[sortedNotes.length - 1].position.measure - 1) * 4 + sortedNotes[sortedNotes.length - 1].position.beat) * (60 / tempo) : 4 * (60 / tempo);\n\n    // Atualizar progresso\n    const updateProgress = () => {\n      if (Tone.Transport.state === 'started') {\n        const currentTime = Tone.Transport.seconds;\n        const progress = currentTime / totalDuration * 100;\n        setPlaybackProgress(Math.min(progress, 100));\n        setPlaybackPosition(currentTime);\n        if (progress < 100) {\n          requestAnimationFrame(updateProgress);\n        } else {\n          setIsPlaying(false);\n          setPlaybackProgress(0);\n          setPlaybackPosition(0);\n        }\n      }\n    };\n\n    // Iniciar reprodução\n    Tone.Transport.start();\n    setIsPlaying(true);\n    updateProgress();\n  };\n\n  // Função para calcular duração em segundos\n  const getDurationInSeconds = (duration, bpm) => {\n    const beatDuration = 60 / bpm;\n    const durationMap = {\n      'whole': beatDuration * 4,\n      'half': beatDuration * 2,\n      'quarter': beatDuration,\n      'eighth': beatDuration / 2,\n      'sixteenth': beatDuration / 4\n    };\n    return durationMap[duration] || beatDuration;\n  };\n\n  // Função para lidar com cliques na partitura\n  const handleScoreClick = event => {\n    var _scoreRef$current;\n    if (!onNoteClick || !renderer) return;\n    const rect = (_scoreRef$current = scoreRef.current) === null || _scoreRef$current === void 0 ? void 0 : _scoreRef$current.getBoundingClientRect();\n    if (!rect) return;\n    const x = event.clientX - rect.left;\n    const y = event.clientY - rect.top;\n\n    // Determinar qual pauta foi clicada\n    const staffIndex = Math.floor((y - 40) / 120);\n    if (staffIndex >= 0 && staffIndex < instruments.length) {\n      onNoteClick(x, y, staffIndex);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(ScoreContainer, {\n    children: [/*#__PURE__*/_jsxDEV(ScoreHeader, {\n      children: [/*#__PURE__*/_jsxDEV(ScoreTitle, {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), composer && /*#__PURE__*/_jsxDEV(ScoreSubtitle, {\n        children: [\"por \", composer]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 22\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PlaybackControls, {\n      children: [/*#__PURE__*/_jsxDEV(PlayButton, {\n        isPlaying: isPlaying,\n        onClick: handlePlay,\n        children: isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TempoControl, {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Tempo:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          min: \"60\",\n          max: \"200\",\n          value: tempo,\n          onChange: e => onTempoChange(parseInt(e.target.value) || 120)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"BPM\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n        children: /*#__PURE__*/_jsxDEV(ProgressFill, {\n          progress: playbackProgress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ScoreCanvas, {\n      ref: scoreRef,\n      onClick: handleScoreClick,\n      style: {\n        cursor: onNoteClick ? 'crosshair' : 'default'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 392,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfessionalScore, \"KWXRS/9Xx0zlUkq31CWesRsXKj8=\");\n_c1 = ProfessionalScore;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"ScoreContainer\");\n$RefreshReg$(_c2, \"ScoreHeader\");\n$RefreshReg$(_c3, \"ScoreTitle\");\n$RefreshReg$(_c4, \"ScoreSubtitle\");\n$RefreshReg$(_c5, \"ScoreCanvas\");\n$RefreshReg$(_c6, \"PlaybackControls\");\n$RefreshReg$(_c7, \"PlayButton\");\n$RefreshReg$(_c8, \"TempoControl\");\n$RefreshReg$(_c9, \"ProgressBar\");\n$RefreshReg$(_c0, \"ProgressFill\");\n$RefreshReg$(_c1, \"ProfessionalScore\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "styled", "<PERSON><PERSON>", "getOrchestralInstrument", "jsxDEV", "_jsxDEV", "ScoreContainer", "div", "_c", "ScoreHeader", "_c2", "ScoreTitle", "h1", "_c3", "ScoreSubtitle", "_c4", "ScoreCanvas", "_c5", "PlaybackControls", "_c6", "PlayButton", "button", "props", "isPlaying", "_c7", "TempoControl", "_c8", "ProgressBar", "_c9", "ProgressFill", "progress", "_c0", "PlaybackLine", "line", "visible", "ProfessionalScore", "title", "composer", "notes", "lyrics", "instruments", "tempo", "onTempoChange", "onNoteClick", "_s", "scoreRef", "setIsPlaying", "playbackProgress", "setPlaybackProgress", "playbackPosition", "setPlaybackPosition", "synth", "setSynth", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "synthInstance", "PolySynth", "Synth", "toDestination", "dispose", "current", "innerHTML", "svg", "document", "createElementNS", "setAttribute", "style", "background", "border", "borderRadius", "titleText", "textContent", "append<PERSON><PERSON><PERSON>", "yPosition", "systemHeight", "for<PERSON>ach", "instrumentId", "instrumentIndex", "template", "i", "toString", "instrumentText", "name", "clefText", "clef", "instrumentNotes", "filter", "note", "position", "staff", "noteIndex", "x", "measure", "beat", "y", "noteCircle", "isRest", "noteText", "instrumentLyrics", "lyric", "lyricText", "text", "convertDurationToVex", "duration", "durationMap", "handlePlay", "Transport", "stop", "cancel", "context", "state", "start", "bpm", "value", "sortedNotes", "sort", "a", "b", "time", "noteName", "octave", "getDurationInSeconds", "schedule", "triggerAttackRelease", "totalDuration", "length", "updateProgress", "currentTime", "seconds", "Math", "min", "requestAnimationFrame", "beatDuration", "handleScoreClick", "event", "_scoreRef$current", "rect", "getBoundingClientRect", "clientX", "left", "clientY", "top", "staffIndex", "floor", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "max", "onChange", "e", "parseInt", "target", "ref", "cursor", "_c1", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ProfessionalScore/ProfessionalScore.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport styled from 'styled-components';\n// import { Factory, Renderer, Stave, StaveNote, Voice, Formatter, Accidental } from 'vexflow';\nimport * as Tone from 'tone';\nimport { MusicalNote, Lyrics, InstrumentType } from '../../types/music';\nimport { getOrchestralInstrument } from '../../utils/orchestralInstruments';\n\nconst ScoreContainer = styled.div`\n  background: white;\n  border-radius: 16px;\n  padding: 2rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  margin: 1rem 0;\n  overflow-x: auto;\n`;\n\nconst ScoreHeader = styled.div`\n  text-align: center;\n  margin-bottom: 2rem;\n  border-bottom: 2px solid #e9ecef;\n  padding-bottom: 1rem;\n`;\n\nconst ScoreTitle = styled.h1`\n  font-size: 2rem;\n  color: #2c3e50;\n  margin: 0 0 0.5rem 0;\n  font-family: 'Times New Roman', serif;\n`;\n\nconst ScoreSubtitle = styled.div`\n  font-size: 1.1rem;\n  color: #666;\n  font-style: italic;\n`;\n\nconst ScoreCanvas = styled.div`\n  min-height: 400px;\n  width: 100%;\n  \n  svg {\n    width: 100%;\n    height: auto;\n  }\n`;\n\nconst PlaybackControls = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 1rem;\n  margin: 1rem 0;\n  padding: 1rem;\n  background: #f8f9fa;\n  border-radius: 12px;\n`;\n\nconst PlayButton = styled.button<{ isPlaying?: boolean }>`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 50px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.isPlaying ? `\n    background: #e74c3c;\n    color: white;\n    &:hover { background: #c0392b; }\n  ` : `\n    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\n    color: white;\n    &:hover { \n      background: linear-gradient(135deg, #229954 0%, #27ae60 100%);\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);\n    }\n  `}\n`;\n\nconst TempoControl = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  \n  label {\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input {\n    width: 80px;\n    padding: 0.5rem;\n    border: 1px solid #dee2e6;\n    border-radius: 6px;\n    text-align: center;\n  }\n`;\n\nconst ProgressBar = styled.div`\n  flex: 1;\n  height: 6px;\n  background: #e9ecef;\n  border-radius: 3px;\n  overflow: hidden;\n  position: relative;\n`;\n\nconst ProgressFill = styled.div<{ progress: number }>`\n  height: 100%;\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\n  width: ${props => props.progress}%;\n  transition: width 0.1s ease;\n`;\n\nconst PlaybackLine = styled.line<{ visible: boolean }>`\n  stroke: #e74c3c;\n  stroke-width: 2;\n  opacity: ${props => props.visible ? 1 : 0};\n  transition: opacity 0.2s ease;\n`;\n\ninterface ProfessionalScoreProps {\n  title: string;\n  composer?: string;\n  notes: MusicalNote[];\n  lyrics: Lyrics[];\n  instruments: InstrumentType[];\n  tempo: number;\n  onTempoChange: (tempo: number) => void;\n  onNoteClick?: (x: number, y: number, staffIndex: number) => void;\n}\n\nexport const ProfessionalScore: React.FC<ProfessionalScoreProps> = ({\n  title,\n  composer,\n  notes,\n  lyrics,\n  instruments,\n  tempo,\n  onTempoChange,\n  onNoteClick\n}) => {\n  const scoreRef = useRef<HTMLDivElement>(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [playbackProgress, setPlaybackProgress] = useState(0);\n  const [playbackPosition, setPlaybackPosition] = useState(0);\n  const [synth, setSynth] = useState<Tone.PolySynth | null>(null);\n  const [renderer, setRenderer] = useState<any>(null);\n\n  // Inicializar Tone.js\n  useEffect(() => {\n    const synthInstance = new Tone.PolySynth(Tone.Synth).toDestination();\n    setSynth(synthInstance);\n    \n    return () => {\n      synthInstance.dispose();\n    };\n  }, []);\n\n  // Renderizar partitura (versão simplificada)\n  useEffect(() => {\n    if (!scoreRef.current) return;\n\n    // Limpar canvas anterior\n    scoreRef.current.innerHTML = '';\n\n    // Criar SVG simples para demonstração\n    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    svg.setAttribute('width', '800');\n    svg.setAttribute('height', '600');\n    svg.style.background = 'white';\n    svg.style.border = '1px solid #e9ecef';\n    svg.style.borderRadius = '8px';\n\n    // Adicionar título\n    const titleText = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n    titleText.setAttribute('x', '400');\n    titleText.setAttribute('y', '30');\n    titleText.setAttribute('text-anchor', 'middle');\n    titleText.setAttribute('font-size', '18');\n    titleText.setAttribute('font-weight', 'bold');\n    titleText.textContent = title || 'Partitura';\n    svg.appendChild(titleText);\n\n    // Criar pautas para cada instrumento\n    let yPosition = 60;\n    const systemHeight = 120;\n\n    instruments.forEach((instrumentId, instrumentIndex) => {\n      const template = getOrchestralInstrument(instrumentId);\n\n      // Desenhar 5 linhas da pauta\n      for (let i = 0; i < 5; i++) {\n        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');\n        line.setAttribute('x1', '50');\n        line.setAttribute('y1', (yPosition + i * 10).toString());\n        line.setAttribute('x2', '750');\n        line.setAttribute('y2', (yPosition + i * 10).toString());\n        line.setAttribute('stroke', '#000');\n        line.setAttribute('stroke-width', '1');\n        svg.appendChild(line);\n      }\n\n      // Adicionar nome do instrumento\n      const instrumentText = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n      instrumentText.setAttribute('x', '10');\n      instrumentText.setAttribute('y', (yPosition + 20).toString());\n      instrumentText.setAttribute('font-size', '12');\n      instrumentText.setAttribute('font-weight', 'bold');\n      instrumentText.textContent = template.name;\n      svg.appendChild(instrumentText);\n\n      // Adicionar clave\n      const clefText = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n      clefText.setAttribute('x', '60');\n      clefText.setAttribute('y', (yPosition + 25).toString());\n      clefText.setAttribute('font-size', '24');\n      clefText.textContent = template.clef === 'treble' ? '𝄞' : template.clef === 'bass' ? '𝄢' : '𝄡';\n      svg.appendChild(clefText);\n\n      // Renderizar notas deste instrumento\n      const instrumentNotes = notes.filter(note =>\n        note.position.staff === instrumentIndex\n      );\n\n      instrumentNotes.forEach((note, noteIndex) => {\n        const x = 100 + (note.position.measure - 1) * 150 + (note.position.beat - 1) * 30;\n        const y = yPosition + 20; // Posição simplificada\n\n        // Desenhar nota\n        const noteCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');\n        noteCircle.setAttribute('cx', x.toString());\n        noteCircle.setAttribute('cy', y.toString());\n        noteCircle.setAttribute('r', '4');\n        noteCircle.setAttribute('fill', note.isRest ? 'none' : '#000');\n        noteCircle.setAttribute('stroke', '#000');\n        svg.appendChild(noteCircle);\n\n        // Adicionar nome da nota\n        const noteText = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n        noteText.setAttribute('x', x.toString());\n        noteText.setAttribute('y', (y + 20).toString());\n        noteText.setAttribute('font-size', '10');\n        noteText.setAttribute('text-anchor', 'middle');\n        noteText.textContent = note.isRest ? '𝄽' : note.name;\n        svg.appendChild(noteText);\n      });\n\n      // Adicionar letras\n      const instrumentLyrics = lyrics.filter(lyric =>\n        !lyric.position.staff || lyric.position.staff === instrumentIndex\n      );\n\n      instrumentLyrics.forEach((lyric) => {\n        const x = 100 + (lyric.position.measure - 1) * 150 + (lyric.position.beat - 1) * 30;\n        const y = yPosition + 60;\n\n        const lyricText = document.createElementNS('http://www.w3.org/2000/svg', 'text');\n        lyricText.setAttribute('x', x.toString());\n        lyricText.setAttribute('y', y.toString());\n        lyricText.setAttribute('font-size', '12');\n        lyricText.setAttribute('text-anchor', 'middle');\n        lyricText.textContent = lyric.text;\n        svg.appendChild(lyricText);\n      });\n\n      yPosition += systemHeight;\n    });\n\n    scoreRef.current.appendChild(svg);\n\n  }, [notes, lyrics, instruments, title]);\n\n  // Função para converter duração para formato VexFlow\n  const convertDurationToVex = (duration: string): string => {\n    const durationMap: { [key: string]: string } = {\n      'whole': 'w',\n      'half': 'h',\n      'quarter': 'q',\n      'eighth': '8',\n      'sixteenth': '16'\n    };\n    return durationMap[duration] || 'q';\n  };\n\n  // Função para reproduzir partitura\n  const handlePlay = async () => {\n    if (!synth) return;\n\n    if (isPlaying) {\n      // Parar reprodução\n      Tone.Transport.stop();\n      Tone.Transport.cancel();\n      setIsPlaying(false);\n      setPlaybackProgress(0);\n      setPlaybackPosition(0);\n      return;\n    }\n\n    // Iniciar Tone.js se necessário\n    if (Tone.context.state !== 'running') {\n      await Tone.start();\n    }\n\n    // Configurar tempo\n    Tone.Transport.bpm.value = tempo;\n\n    // Agendar notas para reprodução\n    const sortedNotes = [...notes]\n      .filter(note => !note.isRest)\n      .sort((a, b) => {\n        if (a.position.measure !== b.position.measure) {\n          return a.position.measure - b.position.measure;\n        }\n        return a.position.beat - b.position.beat;\n      });\n\n    sortedNotes.forEach(note => {\n      const time = ((note.position.measure - 1) * 4 + (note.position.beat - 1)) * (60 / tempo);\n      const noteName = `${note.name}${note.octave}`;\n      const duration = getDurationInSeconds(note.duration, tempo);\n\n      Tone.Transport.schedule((time) => {\n        synth.triggerAttackRelease(noteName, duration);\n      }, time);\n    });\n\n    // Calcular duração total\n    const totalDuration = sortedNotes.length > 0 \n      ? ((sortedNotes[sortedNotes.length - 1].position.measure - 1) * 4 + \n         sortedNotes[sortedNotes.length - 1].position.beat) * (60 / tempo)\n      : 4 * (60 / tempo);\n\n    // Atualizar progresso\n    const updateProgress = () => {\n      if (Tone.Transport.state === 'started') {\n        const currentTime = Tone.Transport.seconds;\n        const progress = (currentTime / totalDuration) * 100;\n        setPlaybackProgress(Math.min(progress, 100));\n        setPlaybackPosition(currentTime);\n        \n        if (progress < 100) {\n          requestAnimationFrame(updateProgress);\n        } else {\n          setIsPlaying(false);\n          setPlaybackProgress(0);\n          setPlaybackPosition(0);\n        }\n      }\n    };\n\n    // Iniciar reprodução\n    Tone.Transport.start();\n    setIsPlaying(true);\n    updateProgress();\n  };\n\n  // Função para calcular duração em segundos\n  const getDurationInSeconds = (duration: string, bpm: number): number => {\n    const beatDuration = 60 / bpm;\n    const durationMap: { [key: string]: number } = {\n      'whole': beatDuration * 4,\n      'half': beatDuration * 2,\n      'quarter': beatDuration,\n      'eighth': beatDuration / 2,\n      'sixteenth': beatDuration / 4\n    };\n    return durationMap[duration] || beatDuration;\n  };\n\n  // Função para lidar com cliques na partitura\n  const handleScoreClick = (event: React.MouseEvent) => {\n    if (!onNoteClick || !renderer) return;\n\n    const rect = scoreRef.current?.getBoundingClientRect();\n    if (!rect) return;\n\n    const x = event.clientX - rect.left;\n    const y = event.clientY - rect.top;\n\n    // Determinar qual pauta foi clicada\n    const staffIndex = Math.floor((y - 40) / 120);\n    \n    if (staffIndex >= 0 && staffIndex < instruments.length) {\n      onNoteClick(x, y, staffIndex);\n    }\n  };\n\n  return (\n    <ScoreContainer>\n      <ScoreHeader>\n        <ScoreTitle>{title}</ScoreTitle>\n        {composer && <ScoreSubtitle>por {composer}</ScoreSubtitle>}\n      </ScoreHeader>\n\n      <PlaybackControls>\n        <PlayButton isPlaying={isPlaying} onClick={handlePlay}>\n          {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}\n        </PlayButton>\n        \n        <TempoControl>\n          <label>Tempo:</label>\n          <input\n            type=\"number\"\n            min=\"60\"\n            max=\"200\"\n            value={tempo}\n            onChange={(e) => onTempoChange(parseInt(e.target.value) || 120)}\n          />\n          <span>BPM</span>\n        </TempoControl>\n\n        <ProgressBar>\n          <ProgressFill progress={playbackProgress} />\n        </ProgressBar>\n      </PlaybackControls>\n\n      <ScoreCanvas \n        ref={scoreRef} \n        onClick={handleScoreClick}\n        style={{ cursor: onNoteClick ? 'crosshair' : 'default' }}\n      />\n    </ScoreContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,MAAM,MAAM,mBAAmB;AACtC;AACA,OAAO,KAAKC,IAAI,MAAM,MAAM;AAE5B,SAASC,uBAAuB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5E,MAAMC,cAAc,GAAGL,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,cAAc;AASpB,MAAMG,WAAW,GAAGR,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,WAAW;AAOjB,MAAME,UAAU,GAAGV,MAAM,CAACW,EAAE;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,UAAU;AAOhB,MAAMG,aAAa,GAAGb,MAAM,CAACM,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAJID,aAAa;AAMnB,MAAME,WAAW,GAAGf,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GARID,WAAW;AAUjB,MAAME,gBAAgB,GAAGjB,MAAM,CAACM,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GATID,gBAAgB;AAWtB,MAAME,UAAU,GAAGnB,MAAM,CAACoB,MAA+B;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAG;AAC/B;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACC,GAAA,GAtBIJ,UAAU;AAwBhB,MAAMK,YAAY,GAAGxB,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GAjBID,YAAY;AAmBlB,MAAME,WAAW,GAAG1B,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAPID,WAAW;AASjB,MAAME,YAAY,GAAG5B,MAAM,CAACM,GAAyB;AACrD;AACA;AACA,WAAWe,KAAK,IAAIA,KAAK,CAACQ,QAAQ;AAClC;AACA,CAAC;AAACC,GAAA,GALIF,YAAY;AAOlB,MAAMG,YAAY,GAAG/B,MAAM,CAACgC,IAA0B;AACtD;AACA;AACA,aAAaX,KAAK,IAAIA,KAAK,CAACY,OAAO,GAAG,CAAC,GAAG,CAAC;AAC3C;AACA,CAAC;AAaD,OAAO,MAAMC,iBAAmD,GAAGA,CAAC;EAClEC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC,MAAM;EACNC,WAAW;EACXC,KAAK;EACLC,aAAa;EACbC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAG9C,MAAM,CAAiB,IAAI,CAAC;EAC7C,MAAM,CAACwB,SAAS,EAAEuB,YAAY,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAwB,IAAI,CAAC;EAC/D,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAM,IAAI,CAAC;;EAEnD;EACAF,SAAS,CAAC,MAAM;IACd,MAAMyD,aAAa,GAAG,IAAIrD,IAAI,CAACsD,SAAS,CAACtD,IAAI,CAACuD,KAAK,CAAC,CAACC,aAAa,CAAC,CAAC;IACpEN,QAAQ,CAACG,aAAa,CAAC;IAEvB,OAAO,MAAM;MACXA,aAAa,CAACI,OAAO,CAAC,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7D,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+C,QAAQ,CAACe,OAAO,EAAE;;IAEvB;IACAf,QAAQ,CAACe,OAAO,CAACC,SAAS,GAAG,EAAE;;IAE/B;IACA,MAAMC,GAAG,GAAGC,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;IACzEF,GAAG,CAACG,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;IAChCH,GAAG,CAACG,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC;IACjCH,GAAG,CAACI,KAAK,CAACC,UAAU,GAAG,OAAO;IAC9BL,GAAG,CAACI,KAAK,CAACE,MAAM,GAAG,mBAAmB;IACtCN,GAAG,CAACI,KAAK,CAACG,YAAY,GAAG,KAAK;;IAE9B;IACA,MAAMC,SAAS,GAAGP,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC;IAChFM,SAAS,CAACL,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;IAClCK,SAAS,CAACL,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC;IACjCK,SAAS,CAACL,YAAY,CAAC,aAAa,EAAE,QAAQ,CAAC;IAC/CK,SAAS,CAACL,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC;IACzCK,SAAS,CAACL,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC7CK,SAAS,CAACC,WAAW,GAAGnC,KAAK,IAAI,WAAW;IAC5C0B,GAAG,CAACU,WAAW,CAACF,SAAS,CAAC;;IAE1B;IACA,IAAIG,SAAS,GAAG,EAAE;IAClB,MAAMC,YAAY,GAAG,GAAG;IAExBlC,WAAW,CAACmC,OAAO,CAAC,CAACC,YAAY,EAAEC,eAAe,KAAK;MACrD,MAAMC,QAAQ,GAAG3E,uBAAuB,CAACyE,YAAY,CAAC;;MAEtD;MACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAM9C,IAAI,GAAG8B,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC;QAC3E/B,IAAI,CAACgC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;QAC7BhC,IAAI,CAACgC,YAAY,CAAC,IAAI,EAAE,CAACQ,SAAS,GAAGM,CAAC,GAAG,EAAE,EAAEC,QAAQ,CAAC,CAAC,CAAC;QACxD/C,IAAI,CAACgC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC;QAC9BhC,IAAI,CAACgC,YAAY,CAAC,IAAI,EAAE,CAACQ,SAAS,GAAGM,CAAC,GAAG,EAAE,EAAEC,QAAQ,CAAC,CAAC,CAAC;QACxD/C,IAAI,CAACgC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;QACnChC,IAAI,CAACgC,YAAY,CAAC,cAAc,EAAE,GAAG,CAAC;QACtCH,GAAG,CAACU,WAAW,CAACvC,IAAI,CAAC;MACvB;;MAEA;MACA,MAAMgD,cAAc,GAAGlB,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC;MACrFiB,cAAc,CAAChB,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC;MACtCgB,cAAc,CAAChB,YAAY,CAAC,GAAG,EAAE,CAACQ,SAAS,GAAG,EAAE,EAAEO,QAAQ,CAAC,CAAC,CAAC;MAC7DC,cAAc,CAAChB,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC;MAC9CgB,cAAc,CAAChB,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAClDgB,cAAc,CAACV,WAAW,GAAGO,QAAQ,CAACI,IAAI;MAC1CpB,GAAG,CAACU,WAAW,CAACS,cAAc,CAAC;;MAE/B;MACA,MAAME,QAAQ,GAAGpB,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC;MAC/EmB,QAAQ,CAAClB,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC;MAChCkB,QAAQ,CAAClB,YAAY,CAAC,GAAG,EAAE,CAACQ,SAAS,GAAG,EAAE,EAAEO,QAAQ,CAAC,CAAC,CAAC;MACvDG,QAAQ,CAAClB,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC;MACxCkB,QAAQ,CAACZ,WAAW,GAAGO,QAAQ,CAACM,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAGN,QAAQ,CAACM,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG,IAAI;MACjGtB,GAAG,CAACU,WAAW,CAACW,QAAQ,CAAC;;MAEzB;MACA,MAAME,eAAe,GAAG/C,KAAK,CAACgD,MAAM,CAACC,IAAI,IACvCA,IAAI,CAACC,QAAQ,CAACC,KAAK,KAAKZ,eAC1B,CAAC;MAEDQ,eAAe,CAACV,OAAO,CAAC,CAACY,IAAI,EAAEG,SAAS,KAAK;QAC3C,MAAMC,CAAC,GAAG,GAAG,GAAG,CAACJ,IAAI,CAACC,QAAQ,CAACI,OAAO,GAAG,CAAC,IAAI,GAAG,GAAG,CAACL,IAAI,CAACC,QAAQ,CAACK,IAAI,GAAG,CAAC,IAAI,EAAE;QACjF,MAAMC,CAAC,GAAGrB,SAAS,GAAG,EAAE,CAAC,CAAC;;QAE1B;QACA,MAAMsB,UAAU,GAAGhC,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,QAAQ,CAAC;QACnF+B,UAAU,CAAC9B,YAAY,CAAC,IAAI,EAAE0B,CAAC,CAACX,QAAQ,CAAC,CAAC,CAAC;QAC3Ce,UAAU,CAAC9B,YAAY,CAAC,IAAI,EAAE6B,CAAC,CAACd,QAAQ,CAAC,CAAC,CAAC;QAC3Ce,UAAU,CAAC9B,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC;QACjC8B,UAAU,CAAC9B,YAAY,CAAC,MAAM,EAAEsB,IAAI,CAACS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;QAC9DD,UAAU,CAAC9B,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;QACzCH,GAAG,CAACU,WAAW,CAACuB,UAAU,CAAC;;QAE3B;QACA,MAAME,QAAQ,GAAGlC,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC;QAC/EiC,QAAQ,CAAChC,YAAY,CAAC,GAAG,EAAE0B,CAAC,CAACX,QAAQ,CAAC,CAAC,CAAC;QACxCiB,QAAQ,CAAChC,YAAY,CAAC,GAAG,EAAE,CAAC6B,CAAC,GAAG,EAAE,EAAEd,QAAQ,CAAC,CAAC,CAAC;QAC/CiB,QAAQ,CAAChC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC;QACxCgC,QAAQ,CAAChC,YAAY,CAAC,aAAa,EAAE,QAAQ,CAAC;QAC9CgC,QAAQ,CAAC1B,WAAW,GAAGgB,IAAI,CAACS,MAAM,GAAG,IAAI,GAAGT,IAAI,CAACL,IAAI;QACrDpB,GAAG,CAACU,WAAW,CAACyB,QAAQ,CAAC;MAC3B,CAAC,CAAC;;MAEF;MACA,MAAMC,gBAAgB,GAAG3D,MAAM,CAAC+C,MAAM,CAACa,KAAK,IAC1C,CAACA,KAAK,CAACX,QAAQ,CAACC,KAAK,IAAIU,KAAK,CAACX,QAAQ,CAACC,KAAK,KAAKZ,eACpD,CAAC;MAEDqB,gBAAgB,CAACvB,OAAO,CAAEwB,KAAK,IAAK;QAClC,MAAMR,CAAC,GAAG,GAAG,GAAG,CAACQ,KAAK,CAACX,QAAQ,CAACI,OAAO,GAAG,CAAC,IAAI,GAAG,GAAG,CAACO,KAAK,CAACX,QAAQ,CAACK,IAAI,GAAG,CAAC,IAAI,EAAE;QACnF,MAAMC,CAAC,GAAGrB,SAAS,GAAG,EAAE;QAExB,MAAM2B,SAAS,GAAGrC,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC;QAChFoC,SAAS,CAACnC,YAAY,CAAC,GAAG,EAAE0B,CAAC,CAACX,QAAQ,CAAC,CAAC,CAAC;QACzCoB,SAAS,CAACnC,YAAY,CAAC,GAAG,EAAE6B,CAAC,CAACd,QAAQ,CAAC,CAAC,CAAC;QACzCoB,SAAS,CAACnC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC;QACzCmC,SAAS,CAACnC,YAAY,CAAC,aAAa,EAAE,QAAQ,CAAC;QAC/CmC,SAAS,CAAC7B,WAAW,GAAG4B,KAAK,CAACE,IAAI;QAClCvC,GAAG,CAACU,WAAW,CAAC4B,SAAS,CAAC;MAC5B,CAAC,CAAC;MAEF3B,SAAS,IAAIC,YAAY;IAC3B,CAAC,CAAC;IAEF7B,QAAQ,CAACe,OAAO,CAACY,WAAW,CAACV,GAAG,CAAC;EAEnC,CAAC,EAAE,CAACxB,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEJ,KAAK,CAAC,CAAC;;EAEvC;EACA,MAAMkE,oBAAoB,GAAIC,QAAgB,IAAa;IACzD,MAAMC,WAAsC,GAAG;MAC7C,OAAO,EAAE,GAAG;MACZ,MAAM,EAAE,GAAG;MACX,SAAS,EAAE,GAAG;MACd,QAAQ,EAAE,GAAG;MACb,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,WAAW,CAACD,QAAQ,CAAC,IAAI,GAAG;EACrC,CAAC;;EAED;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACtD,KAAK,EAAE;IAEZ,IAAI5B,SAAS,EAAE;MACb;MACArB,IAAI,CAACwG,SAAS,CAACC,IAAI,CAAC,CAAC;MACrBzG,IAAI,CAACwG,SAAS,CAACE,MAAM,CAAC,CAAC;MACvB9D,YAAY,CAAC,KAAK,CAAC;MACnBE,mBAAmB,CAAC,CAAC,CAAC;MACtBE,mBAAmB,CAAC,CAAC,CAAC;MACtB;IACF;;IAEA;IACA,IAAIhD,IAAI,CAAC2G,OAAO,CAACC,KAAK,KAAK,SAAS,EAAE;MACpC,MAAM5G,IAAI,CAAC6G,KAAK,CAAC,CAAC;IACpB;;IAEA;IACA7G,IAAI,CAACwG,SAAS,CAACM,GAAG,CAACC,KAAK,GAAGxE,KAAK;;IAEhC;IACA,MAAMyE,WAAW,GAAG,CAAC,GAAG5E,KAAK,CAAC,CAC3BgD,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACS,MAAM,CAAC,CAC5BmB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACd,IAAID,CAAC,CAAC5B,QAAQ,CAACI,OAAO,KAAKyB,CAAC,CAAC7B,QAAQ,CAACI,OAAO,EAAE;QAC7C,OAAOwB,CAAC,CAAC5B,QAAQ,CAACI,OAAO,GAAGyB,CAAC,CAAC7B,QAAQ,CAACI,OAAO;MAChD;MACA,OAAOwB,CAAC,CAAC5B,QAAQ,CAACK,IAAI,GAAGwB,CAAC,CAAC7B,QAAQ,CAACK,IAAI;IAC1C,CAAC,CAAC;IAEJqB,WAAW,CAACvC,OAAO,CAACY,IAAI,IAAI;MAC1B,MAAM+B,IAAI,GAAG,CAAC,CAAC/B,IAAI,CAACC,QAAQ,CAACI,OAAO,GAAG,CAAC,IAAI,CAAC,IAAIL,IAAI,CAACC,QAAQ,CAACK,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,GAAGpD,KAAK,CAAC;MACxF,MAAM8E,QAAQ,GAAG,GAAGhC,IAAI,CAACL,IAAI,GAAGK,IAAI,CAACiC,MAAM,EAAE;MAC7C,MAAMjB,QAAQ,GAAGkB,oBAAoB,CAAClC,IAAI,CAACgB,QAAQ,EAAE9D,KAAK,CAAC;MAE3DvC,IAAI,CAACwG,SAAS,CAACgB,QAAQ,CAAEJ,IAAI,IAAK;QAChCnE,KAAK,CAACwE,oBAAoB,CAACJ,QAAQ,EAAEhB,QAAQ,CAAC;MAChD,CAAC,EAAEe,IAAI,CAAC;IACV,CAAC,CAAC;;IAEF;IACA,MAAMM,aAAa,GAAGV,WAAW,CAACW,MAAM,GAAG,CAAC,GACxC,CAAC,CAACX,WAAW,CAACA,WAAW,CAACW,MAAM,GAAG,CAAC,CAAC,CAACrC,QAAQ,CAACI,OAAO,GAAG,CAAC,IAAI,CAAC,GAC9DsB,WAAW,CAACA,WAAW,CAACW,MAAM,GAAG,CAAC,CAAC,CAACrC,QAAQ,CAACK,IAAI,KAAK,EAAE,GAAGpD,KAAK,CAAC,GAClE,CAAC,IAAI,EAAE,GAAGA,KAAK,CAAC;;IAEpB;IACA,MAAMqF,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAI5H,IAAI,CAACwG,SAAS,CAACI,KAAK,KAAK,SAAS,EAAE;QACtC,MAAMiB,WAAW,GAAG7H,IAAI,CAACwG,SAAS,CAACsB,OAAO;QAC1C,MAAMlG,QAAQ,GAAIiG,WAAW,GAAGH,aAAa,GAAI,GAAG;QACpD5E,mBAAmB,CAACiF,IAAI,CAACC,GAAG,CAACpG,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC5CoB,mBAAmB,CAAC6E,WAAW,CAAC;QAEhC,IAAIjG,QAAQ,GAAG,GAAG,EAAE;UAClBqG,qBAAqB,CAACL,cAAc,CAAC;QACvC,CAAC,MAAM;UACLhF,YAAY,CAAC,KAAK,CAAC;UACnBE,mBAAmB,CAAC,CAAC,CAAC;UACtBE,mBAAmB,CAAC,CAAC,CAAC;QACxB;MACF;IACF,CAAC;;IAED;IACAhD,IAAI,CAACwG,SAAS,CAACK,KAAK,CAAC,CAAC;IACtBjE,YAAY,CAAC,IAAI,CAAC;IAClBgF,cAAc,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAML,oBAAoB,GAAGA,CAAClB,QAAgB,EAAES,GAAW,KAAa;IACtE,MAAMoB,YAAY,GAAG,EAAE,GAAGpB,GAAG;IAC7B,MAAMR,WAAsC,GAAG;MAC7C,OAAO,EAAE4B,YAAY,GAAG,CAAC;MACzB,MAAM,EAAEA,YAAY,GAAG,CAAC;MACxB,SAAS,EAAEA,YAAY;MACvB,QAAQ,EAAEA,YAAY,GAAG,CAAC;MAC1B,WAAW,EAAEA,YAAY,GAAG;IAC9B,CAAC;IACD,OAAO5B,WAAW,CAACD,QAAQ,CAAC,IAAI6B,YAAY;EAC9C,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,KAAuB,IAAK;IAAA,IAAAC,iBAAA;IACpD,IAAI,CAAC5F,WAAW,IAAI,CAACU,QAAQ,EAAE;IAE/B,MAAMmF,IAAI,IAAAD,iBAAA,GAAG1F,QAAQ,CAACe,OAAO,cAAA2E,iBAAA,uBAAhBA,iBAAA,CAAkBE,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAACD,IAAI,EAAE;IAEX,MAAM7C,CAAC,GAAG2C,KAAK,CAACI,OAAO,GAAGF,IAAI,CAACG,IAAI;IACnC,MAAM7C,CAAC,GAAGwC,KAAK,CAACM,OAAO,GAAGJ,IAAI,CAACK,GAAG;;IAElC;IACA,MAAMC,UAAU,GAAGb,IAAI,CAACc,KAAK,CAAC,CAACjD,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;IAE7C,IAAIgD,UAAU,IAAI,CAAC,IAAIA,UAAU,GAAGtG,WAAW,CAACqF,MAAM,EAAE;MACtDlF,WAAW,CAACgD,CAAC,EAAEG,CAAC,EAAEgD,UAAU,CAAC;IAC/B;EACF,CAAC;EAED,oBACEzI,OAAA,CAACC,cAAc;IAAA0I,QAAA,gBACb3I,OAAA,CAACI,WAAW;MAAAuI,QAAA,gBACV3I,OAAA,CAACM,UAAU;QAAAqI,QAAA,EAAE5G;MAAK;QAAA6G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,EAC/B/G,QAAQ,iBAAIhC,OAAA,CAACS,aAAa;QAAAkI,QAAA,GAAC,MAAI,EAAC3G,QAAQ;MAAA;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eAEd/I,OAAA,CAACa,gBAAgB;MAAA8H,QAAA,gBACf3I,OAAA,CAACe,UAAU;QAACG,SAAS,EAAEA,SAAU;QAAC8H,OAAO,EAAE5C,UAAW;QAAAuC,QAAA,EACnDzH,SAAS,GAAG,WAAW,GAAG;MAAe;QAAA0H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAEb/I,OAAA,CAACoB,YAAY;QAAAuH,QAAA,gBACX3I,OAAA;UAAA2I,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrB/I,OAAA;UACEiJ,IAAI,EAAC,QAAQ;UACbpB,GAAG,EAAC,IAAI;UACRqB,GAAG,EAAC,KAAK;UACTtC,KAAK,EAAExE,KAAM;UACb+G,QAAQ,EAAGC,CAAC,IAAK/G,aAAa,CAACgH,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAC1C,KAAK,CAAC,IAAI,GAAG;QAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eACF/I,OAAA;UAAA2I,QAAA,EAAM;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEf/I,OAAA,CAACsB,WAAW;QAAAqH,QAAA,eACV3I,OAAA,CAACwB,YAAY;UAACC,QAAQ,EAAEiB;QAAiB;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEnB/I,OAAA,CAACW,WAAW;MACV4I,GAAG,EAAE/G,QAAS;MACdwG,OAAO,EAAEhB,gBAAiB;MAC1BnE,KAAK,EAAE;QAAE2F,MAAM,EAAElH,WAAW,GAAG,WAAW,GAAG;MAAU;IAAE;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAErB,CAAC;AAACxG,EAAA,CApSWT,iBAAmD;AAAA2H,GAAA,GAAnD3H,iBAAmD;AAAA,IAAA3B,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAA+H,GAAA;AAAAC,YAAA,CAAAvJ,EAAA;AAAAuJ,YAAA,CAAArJ,GAAA;AAAAqJ,YAAA,CAAAlJ,GAAA;AAAAkJ,YAAA,CAAAhJ,GAAA;AAAAgJ,YAAA,CAAA9I,GAAA;AAAA8I,YAAA,CAAA5I,GAAA;AAAA4I,YAAA,CAAAvI,GAAA;AAAAuI,YAAA,CAAArI,GAAA;AAAAqI,YAAA,CAAAnI,GAAA;AAAAmI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}