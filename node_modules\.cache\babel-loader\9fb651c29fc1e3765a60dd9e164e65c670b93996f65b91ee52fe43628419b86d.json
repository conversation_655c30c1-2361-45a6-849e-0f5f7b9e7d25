{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { LoginPage } from './pages/Auth/LoginPage';\nimport { RegisterPage } from './pages/Auth/RegisterPage';\nimport { Layout } from './components/Layout/Layout';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  font-size: 1.2rem;\n  color: #666;\n`;\n_c = LoadingContainer;\nconst HomePage = styled.div`\n  text-align: center;\n  padding: 2rem;\n`;\n_c2 = HomePage;\nconst WelcomeTitle = styled.h1`\n  color: #2c3e50;\n  margin-bottom: 1rem;\n`;\n_c3 = WelcomeTitle;\nconst WelcomeText = styled.p`\n  color: #666;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  max-width: 600px;\n  margin: 0 auto;\n`;\n\n// Componente principal da aplicação\n_c4 = WelcomeText;\nconst MainApp = () => {\n  _s();\n  const [currentPage, setCurrentPage] = useState('home');\n  const [showAuth, setShowAuth] = useState(false);\n  const [authMode, setAuthMode] = useState('login');\n  const {\n    currentUser\n  } = useAuth();\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return /*#__PURE__*/_jsxDEV(HomePage, {\n          children: [/*#__PURE__*/_jsxDEV(WelcomeTitle, {\n            children: \"\\uD83C\\uDFB5 Bem-vindo ao Partitura Digital!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(WelcomeText, {\n            children: \"Crie e edite suas partituras musicais de forma intuitiva. Adicione notas, acordes, letras e muito mais. Visualize suas composi\\xE7\\xF5es tanto em nota\\xE7\\xE3o tradicional quanto em cifras.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this), !currentUser && /*#__PURE__*/_jsxDEV(GuestNotice, {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\uD83D\\uDCA1 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Modo Visitante:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 23\n              }, this), \" Voc\\xEA pode usar o editor, mas suas partituras n\\xE3o ser\\xE3o salvas.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Fa\\xE7a login para salvar suas cria\\xE7\\xF5es!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this);\n      case 'scores':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Lista de Partituras (Em desenvolvimento)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 16\n        }, this);\n      case 'new-score':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Editor de Partituras (Em desenvolvimento)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 16\n        }, this);\n      case 'auth':\n        return authMode === 'login' ? /*#__PURE__*/_jsxDEV(LoginPage, {\n          onSwitchToRegister: () => setAuthMode('register')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(RegisterPage, {\n          onSwitchToLogin: () => setAuthMode('login')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"P\\xE1gina n\\xE3o encontrada\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const handleNavigate = page => {\n    if (page === 'login') {\n      setAuthMode('login');\n      setCurrentPage('auth');\n    } else if (page === 'register') {\n      setAuthMode('register');\n      setCurrentPage('auth');\n    } else {\n      setCurrentPage(page);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    currentPage: currentPage,\n    onNavigate: handleNavigate,\n    showAuthInSidebar: !currentUser,\n    children: renderPage()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n};\n\n// Componente de autenticação\n_s(MainApp, \"K4p5CPXw0OTyzqdK3m2H6FSeEpo=\", false, function () {\n  return [useAuth];\n});\n_c5 = MainApp;\nconst AuthApp = () => {\n  _s2();\n  const [isLogin, setIsLogin] = useState(true);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(FirebaseTest, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), isLogin ? /*#__PURE__*/_jsxDEV(LoginPage, {\n      onSwitchToRegister: () => setIsLogin(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(RegisterPage, {\n      onSwitchToLogin: () => setIsLogin(true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n\n// Componente principal que decide qual app mostrar\n_s2(AuthApp, \"juHMKC6x2j1wnRvCiB5VrABnZyE=\");\n_c6 = AuthApp;\nconst AppContent = () => {\n  _s3();\n  const {\n    currentUser,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingContainer, {\n      children: \"Carregando...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 12\n    }, this);\n  }\n  return currentUser ? /*#__PURE__*/_jsxDEV(AuthenticatedApp, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 24\n  }, this) : /*#__PURE__*/_jsxDEV(AuthApp, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 47\n  }, this);\n};\n\n// App principal com provider\n_s3(AppContent, \"+loUN5XsQVjYs/gtfuWkb9VBZ7Q=\", false, function () {\n  return [useAuth];\n});\n_c7 = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n}\n_c8 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"LoadingContainer\");\n$RefreshReg$(_c2, \"HomePage\");\n$RefreshReg$(_c3, \"WelcomeTitle\");\n$RefreshReg$(_c4, \"WelcomeText\");\n$RefreshReg$(_c5, \"MainApp\");\n$RefreshReg$(_c6, \"AuthApp\");\n$RefreshReg$(_c7, \"AppContent\");\n$RefreshReg$(_c8, \"App\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "LoginPage", "RegisterPage", "Layout", "styled", "jsxDEV", "_jsxDEV", "LoadingContainer", "div", "_c", "HomePage", "_c2", "WelcomeTitle", "h1", "_c3", "WelcomeText", "p", "_c4", "MainApp", "_s", "currentPage", "setCurrentPage", "showAuth", "setShowAuth", "authMode", "setAuthMode", "currentUser", "renderPage", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "GuestNotice", "onSwitchToRegister", "onSwitchToLogin", "handleNavigate", "page", "onNavigate", "showAuthInSidebar", "_c5", "AuthApp", "_s2", "is<PERSON>ogin", "setIsLogin", "FirebaseTest", "_c6", "A<PERSON><PERSON><PERSON>nt", "_s3", "loading", "AuthenticatedApp", "_c7", "App", "_c8", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { LoginPage } from './pages/Auth/LoginPage';\nimport { RegisterPage } from './pages/Auth/RegisterPage';\nimport { Layout } from './components/Layout/Layout';\nimport styled from 'styled-components';\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  font-size: 1.2rem;\n  color: #666;\n`;\n\nconst HomePage = styled.div`\n  text-align: center;\n  padding: 2rem;\n`;\n\nconst WelcomeTitle = styled.h1`\n  color: #2c3e50;\n  margin-bottom: 1rem;\n`;\n\nconst WelcomeText = styled.p`\n  color: #666;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  max-width: 600px;\n  margin: 0 auto;\n`;\n\n// Componente principal da aplicação\nconst MainApp: React.FC = () => {\n  const [currentPage, setCurrentPage] = useState('home');\n  const [showAuth, setShowAuth] = useState(false);\n  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');\n  const { currentUser } = useAuth();\n\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return (\n          <HomePage>\n            <WelcomeTitle>🎵 Bem-vindo ao Partitura Digital!</WelcomeTitle>\n            <WelcomeText>\n              Crie e edite suas partituras musicais de forma intuitiva.\n              Adicione notas, acordes, letras e muito mais.\n              Visualize suas composições tanto em notação tradicional quanto em cifras.\n            </WelcomeText>\n            {!currentUser && (\n              <GuestNotice>\n                <p>💡 <strong>Modo Visitante:</strong> Você pode usar o editor, mas suas partituras não serão salvas.</p>\n                <p>Faça login para salvar suas criações!</p>\n              </GuestNotice>\n            )}\n          </HomePage>\n        );\n      case 'scores':\n        return <div>Lista de Partituras (Em desenvolvimento)</div>;\n      case 'new-score':\n        return <div>Editor de Partituras (Em desenvolvimento)</div>;\n      case 'auth':\n        return authMode === 'login' ? (\n          <LoginPage onSwitchToRegister={() => setAuthMode('register')} />\n        ) : (\n          <RegisterPage onSwitchToLogin={() => setAuthMode('login')} />\n        );\n      default:\n        return <div>Página não encontrada</div>;\n    }\n  };\n\n  const handleNavigate = (page: string) => {\n    if (page === 'login') {\n      setAuthMode('login');\n      setCurrentPage('auth');\n    } else if (page === 'register') {\n      setAuthMode('register');\n      setCurrentPage('auth');\n    } else {\n      setCurrentPage(page);\n    }\n  };\n\n  return (\n    <Layout\n      currentPage={currentPage}\n      onNavigate={handleNavigate}\n      showAuthInSidebar={!currentUser}\n    >\n      {renderPage()}\n    </Layout>\n  );\n};\n\n// Componente de autenticação\nconst AuthApp: React.FC = () => {\n  const [isLogin, setIsLogin] = useState(true);\n\n  return (\n    <div>\n      <FirebaseTest />\n      {isLogin ? (\n        <LoginPage onSwitchToRegister={() => setIsLogin(false)} />\n      ) : (\n        <RegisterPage onSwitchToLogin={() => setIsLogin(true)} />\n      )}\n    </div>\n  );\n};\n\n// Componente principal que decide qual app mostrar\nconst AppContent: React.FC = () => {\n  const { currentUser, loading } = useAuth();\n\n  if (loading) {\n    return <LoadingContainer>Carregando...</LoadingContainer>;\n  }\n\n  return currentUser ? <AuthenticatedApp /> : <AuthApp />;\n};\n\n// App principal com provider\nfunction App() {\n  return (\n    <AuthProvider>\n      <AppContent />\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,MAAM,QAAQ,4BAA4B;AACnD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,gBAAgB,GAAGH,MAAM,CAACI,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,gBAAgB;AAStB,MAAMG,QAAQ,GAAGN,MAAM,CAACI,GAAG;AAC3B;AACA;AACA,CAAC;AAACG,GAAA,GAHID,QAAQ;AAKd,MAAME,YAAY,GAAGR,MAAM,CAACS,EAAE;AAC9B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,YAAY;AAKlB,MAAMG,WAAW,GAAGX,MAAM,CAACY,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GARMF,WAAW;AASjB,MAAMG,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,MAAM,CAAC;EACtD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAuB,OAAO,CAAC;EACvE,MAAM;IAAE4B;EAAY,CAAC,GAAG1B,OAAO,CAAC,CAAC;EAEjC,MAAM2B,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQP,WAAW;MACjB,KAAK,MAAM;QACT,oBACEd,OAAA,CAACI,QAAQ;UAAAkB,QAAA,gBACPtB,OAAA,CAACM,YAAY;YAAAgB,QAAA,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAC/D1B,OAAA,CAACS,WAAW;YAAAa,QAAA,EAAC;UAIb;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,EACb,CAACN,WAAW,iBACXpB,OAAA,CAAC2B,WAAW;YAAAL,QAAA,gBACVtB,OAAA;cAAAsB,QAAA,GAAG,eAAG,eAAAtB,OAAA;gBAAAsB,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,4EAA+D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzG1B,OAAA;cAAAsB,QAAA,EAAG;YAAqC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CACd;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAEf,KAAK,QAAQ;QACX,oBAAO1B,OAAA;UAAAsB,QAAA,EAAK;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC5D,KAAK,WAAW;QACd,oBAAO1B,OAAA;UAAAsB,QAAA,EAAK;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC7D,KAAK,MAAM;QACT,OAAOR,QAAQ,KAAK,OAAO,gBACzBlB,OAAA,CAACL,SAAS;UAACiC,kBAAkB,EAAEA,CAAA,KAAMT,WAAW,CAAC,UAAU;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEhE1B,OAAA,CAACJ,YAAY;UAACiC,eAAe,EAAEA,CAAA,KAAMV,WAAW,CAAC,OAAO;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC7D;MACH;QACE,oBAAO1B,OAAA;UAAAsB,QAAA,EAAK;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IAC3C;EACF,CAAC;EAED,MAAMI,cAAc,GAAIC,IAAY,IAAK;IACvC,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpBZ,WAAW,CAAC,OAAO,CAAC;MACpBJ,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,MAAM,IAAIgB,IAAI,KAAK,UAAU,EAAE;MAC9BZ,WAAW,CAAC,UAAU,CAAC;MACvBJ,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,MAAM;MACLA,cAAc,CAACgB,IAAI,CAAC;IACtB;EACF,CAAC;EAED,oBACE/B,OAAA,CAACH,MAAM;IACLiB,WAAW,EAAEA,WAAY;IACzBkB,UAAU,EAAEF,cAAe;IAC3BG,iBAAiB,EAAE,CAACb,WAAY;IAAAE,QAAA,EAE/BD,UAAU,CAAC;EAAC;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEb,CAAC;;AAED;AAAAb,EAAA,CA/DMD,OAAiB;EAAA,QAIGlB,OAAO;AAAA;AAAAwC,GAAA,GAJ3BtB,OAAiB;AAgEvB,MAAMuB,OAAiB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC9B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAE5C,oBACEQ,OAAA;IAAAsB,QAAA,gBACEtB,OAAA,CAACuC,YAAY;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACfW,OAAO,gBACNrC,OAAA,CAACL,SAAS;MAACiC,kBAAkB,EAAEA,CAAA,KAAMU,UAAU,CAAC,KAAK;IAAE;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAE1D1B,OAAA,CAACJ,YAAY;MAACiC,eAAe,EAAEA,CAAA,KAAMS,UAAU,CAAC,IAAI;IAAE;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACzD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAU,GAAA,CAfMD,OAAiB;AAAAK,GAAA,GAAjBL,OAAiB;AAgBvB,MAAMM,UAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAM;IAAEtB,WAAW;IAAEuB;EAAQ,CAAC,GAAGjD,OAAO,CAAC,CAAC;EAE1C,IAAIiD,OAAO,EAAE;IACX,oBAAO3C,OAAA,CAACC,gBAAgB;MAAAqB,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAkB,CAAC;EAC3D;EAEA,OAAON,WAAW,gBAAGpB,OAAA,CAAC4C,gBAAgB;IAAArB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,gBAAG1B,OAAA,CAACmC,OAAO;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACzD,CAAC;;AAED;AAAAgB,GAAA,CAVMD,UAAoB;EAAA,QACS/C,OAAO;AAAA;AAAAmD,GAAA,GADpCJ,UAAoB;AAW1B,SAASK,GAAGA,CAAA,EAAG;EACb,oBACE9C,OAAA,CAACP,YAAY;IAAA6B,QAAA,eACXtB,OAAA,CAACyC,UAAU;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;AAACqB,GAAA,GANQD,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAA3C,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAuB,GAAA,EAAAM,GAAA,EAAAK,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAA7C,EAAA;AAAA6C,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}