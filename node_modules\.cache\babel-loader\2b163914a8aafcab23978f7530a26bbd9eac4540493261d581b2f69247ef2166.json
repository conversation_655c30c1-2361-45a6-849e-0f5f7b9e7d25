{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\Layout\\\\Layout.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Icons } from '../../utils/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LayoutContainer = styled.div`\n  display: flex;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n`;\n_c = LayoutContainer;\nconst Sidebar = styled.nav`\n  width: 280px;\n  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);\n  color: white;\n  padding: 1.5rem;\n  box-shadow: 4px 0 15px rgba(0,0,0,0.2);\n  backdrop-filter: blur(10px);\n`;\n_c2 = Sidebar;\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 1.6rem;\n  font-weight: bold;\n  margin-bottom: 2.5rem;\n  padding-bottom: 1.5rem;\n  border-bottom: 2px solid rgba(255,255,255,0.2);\n  background: linear-gradient(45deg, #fff, #e8f4fd);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n_c3 = Logo;\nconst NavItem = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  width: 100%;\n  padding: 1rem 1.25rem;\n  background: none;\n  border: none;\n  color: rgba(255,255,255,0.9);\n  text-align: left;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  margin-bottom: 0.75rem;\n  font-size: 0.95rem;\n  font-weight: 500;\n\n  &:hover {\n    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);\n    transform: translateX(5px);\n    color: white;\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n  }\n\n  &.active {\n    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\n    color: white;\n    transform: translateX(8px);\n    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);\n  }\n`;\n_c4 = NavItem;\nconst MainContent = styled.main`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n`;\n_c5 = MainContent;\nconst Header = styled.header`\n  background: rgba(255,255,255,0.95);\n  backdrop-filter: blur(10px);\n  padding: 1.5rem 2.5rem;\n  box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid rgba(255,255,255,0.2);\n\n  h1 {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n    font-size: 1.8rem;\n    font-weight: 700;\n    margin: 0;\n  }\n`;\n_c6 = Header;\nconst UserInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;\n_c7 = UserInfo;\nconst UserName = styled.span`\n  font-weight: 500;\n`;\n_c8 = UserName;\nconst LogoutButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  background-color: #e74c3c;\n  color: white;\n  border: none;\n  border-radius: 0.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #c0392b;\n  }\n`;\n_c9 = LogoutButton;\nconst ContentArea = styled.div`\n  flex: 1;\n  padding: 2.5rem;\n  overflow-y: auto;\n  background: rgba(255,255,255,0.1);\n  backdrop-filter: blur(5px);\n  margin: 1rem;\n  border-radius: 20px;\n  box-shadow: inset 0 1px 0 rgba(255,255,255,0.2);\n`;\n_c0 = ContentArea;\nexport const Layout = ({\n  children,\n  currentPage = 'home',\n  onNavigate = () => {},\n  showAuthInSidebar = false\n}) => {\n  _s();\n  const {\n    currentUser,\n    logout\n  } = useAuth();\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Erro ao fazer logout:', error);\n    }\n  };\n  const navigationItems = [{\n    id: 'home',\n    label: 'Início',\n    icon: Icons.Home\n  }, {\n    id: 'new-score',\n    label: 'Criar Partitura',\n    icon: Icons.Plus\n  }, {\n    id: 'professional-editor',\n    label: '🎼 Editor Profissional',\n    icon: Icons.Music\n  }, ...(currentUser ? [{\n    id: 'scores',\n    label: 'Minhas Partituras',\n    icon: Icons.Music\n  }] : []), ...(showAuthInSidebar ? [{\n    id: 'login',\n    label: 'Entrar',\n    icon: Icons.User\n  }, {\n    id: 'register',\n    label: 'Cadastrar',\n    icon: Icons.Plus\n  }] : [])];\n  return /*#__PURE__*/_jsxDEV(LayoutContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: [/*#__PURE__*/_jsxDEV(Icons.Music, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), \"Partitura Digital\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), navigationItems.map(item => {\n        const IconComponent = item.icon;\n        return /*#__PURE__*/_jsxDEV(NavItem, {\n          className: currentPage === item.id ? 'active' : '',\n          onClick: () => onNavigate(item.id),\n          children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 15\n          }, this), item.label]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"\\uD83C\\uDFB5 Partitura Digital\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), currentUser ? /*#__PURE__*/_jsxDEV(UserInfo, {\n          children: [/*#__PURE__*/_jsxDEV(Icons.User, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(UserName, {\n            children: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.displayName) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.email)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LogoutButton, {\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(Icons.LogOut, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), \"Sair\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(UserInfo, {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#666'\n            },\n            children: \"Modo Visitante\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ContentArea, {\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 168,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"irPLFJ3DqMTL8RNRVsehGT4mySY=\", false, function () {\n  return [useAuth];\n});\n_c1 = Layout;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"LayoutContainer\");\n$RefreshReg$(_c2, \"Sidebar\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"NavItem\");\n$RefreshReg$(_c5, \"MainContent\");\n$RefreshReg$(_c6, \"Header\");\n$RefreshReg$(_c7, \"UserInfo\");\n$RefreshReg$(_c8, \"UserName\");\n$RefreshReg$(_c9, \"LogoutButton\");\n$RefreshReg$(_c0, \"ContentArea\");\n$RefreshReg$(_c1, \"Layout\");", "map": {"version": 3, "names": ["React", "styled", "useAuth", "Icons", "jsxDEV", "_jsxDEV", "LayoutContainer", "div", "_c", "Sidebar", "nav", "_c2", "Logo", "_c3", "NavItem", "button", "_c4", "MainContent", "main", "_c5", "Header", "header", "_c6", "UserInfo", "_c7", "UserName", "span", "_c8", "LogoutButton", "_c9", "ContentArea", "_c0", "Layout", "children", "currentPage", "onNavigate", "showAuthInSidebar", "_s", "currentUser", "logout", "handleLogout", "error", "console", "navigationItems", "id", "label", "icon", "Home", "Plus", "Music", "User", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "IconComponent", "className", "onClick", "displayName", "email", "LogOut", "style", "color", "_c1", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/Layout/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Icons, IconComponent } from '../../utils/icons';\n\nconst LayoutContainer = styled.div`\n  display: flex;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n`;\n\nconst Sidebar = styled.nav`\n  width: 280px;\n  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);\n  color: white;\n  padding: 1.5rem;\n  box-shadow: 4px 0 15px rgba(0,0,0,0.2);\n  backdrop-filter: blur(10px);\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 1.6rem;\n  font-weight: bold;\n  margin-bottom: 2.5rem;\n  padding-bottom: 1.5rem;\n  border-bottom: 2px solid rgba(255,255,255,0.2);\n  background: linear-gradient(45deg, #fff, #e8f4fd);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n\nconst NavItem = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  width: 100%;\n  padding: 1rem 1.25rem;\n  background: none;\n  border: none;\n  color: rgba(255,255,255,0.9);\n  text-align: left;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  margin-bottom: 0.75rem;\n  font-size: 0.95rem;\n  font-weight: 500;\n\n  &:hover {\n    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);\n    transform: translateX(5px);\n    color: white;\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n  }\n\n  &.active {\n    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\n    color: white;\n    transform: translateX(8px);\n    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);\n  }\n`;\n\nconst MainContent = styled.main`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst Header = styled.header`\n  background: rgba(255,255,255,0.95);\n  backdrop-filter: blur(10px);\n  padding: 1.5rem 2.5rem;\n  box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid rgba(255,255,255,0.2);\n\n  h1 {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n    font-size: 1.8rem;\n    font-weight: 700;\n    margin: 0;\n  }\n`;\n\nconst UserInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;\n\nconst UserName = styled.span`\n  font-weight: 500;\n`;\n\nconst LogoutButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  background-color: #e74c3c;\n  color: white;\n  border: none;\n  border-radius: 0.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #c0392b;\n  }\n`;\n\nconst ContentArea = styled.div`\n  flex: 1;\n  padding: 2.5rem;\n  overflow-y: auto;\n  background: rgba(255,255,255,0.1);\n  backdrop-filter: blur(5px);\n  margin: 1rem;\n  border-radius: 20px;\n  box-shadow: inset 0 1px 0 rgba(255,255,255,0.2);\n`;\n\ninterface LayoutProps {\n  children: React.ReactNode;\n  currentPage?: string;\n  onNavigate?: (page: string) => void;\n  showAuthInSidebar?: boolean;\n}\n\nexport const Layout: React.FC<LayoutProps> = ({\n  children,\n  currentPage = 'home',\n  onNavigate = () => {},\n  showAuthInSidebar = false\n}) => {\n  const { currentUser, logout } = useAuth();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Erro ao fazer logout:', error);\n    }\n  };\n\n  const navigationItems = [\n    { id: 'home', label: 'Início', icon: Icons.Home },\n    { id: 'new-score', label: 'Criar Partitura', icon: Icons.Plus },\n    { id: 'professional-editor', label: '🎼 Editor Profissional', icon: Icons.Music },\n    ...(currentUser ? [{ id: 'scores', label: 'Minhas Partituras', icon: Icons.Music }] : []),\n    ...(showAuthInSidebar ? [\n      { id: 'login', label: 'Entrar', icon: Icons.User },\n      { id: 'register', label: 'Cadastrar', icon: Icons.Plus }\n    ] : [])\n  ];\n\n  return (\n    <LayoutContainer>\n      <Sidebar>\n        <Logo>\n          <Icons.Music size={24} />\n          Partitura Digital\n        </Logo>\n\n        {navigationItems.map(item => {\n          const IconComponent = item.icon;\n          return (\n            <NavItem\n              key={item.id}\n              className={currentPage === item.id ? 'active' : ''}\n              onClick={() => onNavigate(item.id)}\n            >\n              <IconComponent size={18} />\n              {item.label}\n            </NavItem>\n          );\n        })}\n      </Sidebar>\n\n      <MainContent>\n        <Header>\n          <h1>🎵 Partitura Digital</h1>\n          {currentUser ? (\n            <UserInfo>\n              <Icons.User size={20} />\n              <UserName>{currentUser?.displayName || currentUser?.email}</UserName>\n              <LogoutButton onClick={handleLogout}>\n                <Icons.LogOut size={16} />\n                Sair\n              </LogoutButton>\n            </UserInfo>\n          ) : (\n            <UserInfo>\n              <span style={{ color: '#666' }}>Modo Visitante</span>\n            </UserInfo>\n          )}\n        </Header>\n\n        <ContentArea>\n          {children}\n        </ContentArea>\n      </MainContent>\n    </LayoutContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,KAAK,QAAuB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,eAAe,GAAGL,MAAM,CAACM,GAAG;AAClC;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,eAAe;AAMrB,MAAMG,OAAO,GAAGR,MAAM,CAACS,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,OAAO;AASb,MAAMG,IAAI,GAAGX,MAAM,CAACM,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAbID,IAAI;AAeV,MAAME,OAAO,GAAGb,MAAM,CAACc,MAAM;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GA9BIF,OAAO;AAgCb,MAAMG,WAAW,GAAGhB,MAAM,CAACiB,IAAI;AAC/B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,WAAW;AAMjB,MAAMG,MAAM,GAAGnB,MAAM,CAACoB,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIF,MAAM;AAqBZ,MAAMG,QAAQ,GAAGtB,MAAM,CAACM,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAGxB,MAAM,CAACyB,IAAI;AAC5B;AACA,CAAC;AAACC,GAAA,GAFIF,QAAQ;AAId,MAAMG,YAAY,GAAG3B,MAAM,CAACc,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAfID,YAAY;AAiBlB,MAAME,WAAW,GAAG7B,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GATID,WAAW;AAkBjB,OAAO,MAAME,MAA6B,GAAGA,CAAC;EAC5CC,QAAQ;EACRC,WAAW,GAAG,MAAM;EACpBC,UAAU,GAAGA,CAAA,KAAM,CAAC,CAAC;EACrBC,iBAAiB,GAAG;AACtB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,WAAW;IAAEC;EAAO,CAAC,GAAGrC,OAAO,CAAC,CAAC;EAEzC,MAAMsC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMD,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAME,eAAe,GAAG,CACtB;IAAEC,EAAE,EAAE,MAAM;IAAEC,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE3C,KAAK,CAAC4C;EAAK,CAAC,EACjD;IAAEH,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,IAAI,EAAE3C,KAAK,CAAC6C;EAAK,CAAC,EAC/D;IAAEJ,EAAE,EAAE,qBAAqB;IAAEC,KAAK,EAAE,wBAAwB;IAAEC,IAAI,EAAE3C,KAAK,CAAC8C;EAAM,CAAC,EACjF,IAAIX,WAAW,GAAG,CAAC;IAAEM,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,IAAI,EAAE3C,KAAK,CAAC8C;EAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EACzF,IAAIb,iBAAiB,GAAG,CACtB;IAAEQ,EAAE,EAAE,OAAO;IAAEC,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAE3C,KAAK,CAAC+C;EAAK,CAAC,EAClD;IAAEN,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,WAAW;IAAEC,IAAI,EAAE3C,KAAK,CAAC6C;EAAK,CAAC,CACzD,GAAG,EAAE,CAAC,CACR;EAED,oBACE3C,OAAA,CAACC,eAAe;IAAA2B,QAAA,gBACd5B,OAAA,CAACI,OAAO;MAAAwB,QAAA,gBACN5B,OAAA,CAACO,IAAI;QAAAqB,QAAA,gBACH5B,OAAA,CAACF,KAAK,CAAC8C,KAAK;UAACE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAE3B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAENZ,eAAe,CAACa,GAAG,CAACC,IAAI,IAAI;QAC3B,MAAMC,aAAa,GAAGD,IAAI,CAACX,IAAI;QAC/B,oBACEzC,OAAA,CAACS,OAAO;UAEN6C,SAAS,EAAEzB,WAAW,KAAKuB,IAAI,CAACb,EAAE,GAAG,QAAQ,GAAG,EAAG;UACnDgB,OAAO,EAAEA,CAAA,KAAMzB,UAAU,CAACsB,IAAI,CAACb,EAAE,CAAE;UAAAX,QAAA,gBAEnC5B,OAAA,CAACqD,aAAa;YAACP,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC1BE,IAAI,CAACZ,KAAK;QAAA,GALNY,IAAI,CAACb,EAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAML,CAAC;MAEd,CAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEVlD,OAAA,CAACY,WAAW;MAAAgB,QAAA,gBACV5B,OAAA,CAACe,MAAM;QAAAa,QAAA,gBACL5B,OAAA;UAAA4B,QAAA,EAAI;QAAoB;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC5BjB,WAAW,gBACVjC,OAAA,CAACkB,QAAQ;UAAAU,QAAA,gBACP5B,OAAA,CAACF,KAAK,CAAC+C,IAAI;YAACC,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxBlD,OAAA,CAACoB,QAAQ;YAAAQ,QAAA,EAAE,CAAAK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuB,WAAW,MAAIvB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwB,KAAK;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eACrElD,OAAA,CAACuB,YAAY;YAACgC,OAAO,EAAEpB,YAAa;YAAAP,QAAA,gBAClC5B,OAAA,CAACF,KAAK,CAAC4D,MAAM;cAACZ,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAE5B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,gBAEXlD,OAAA,CAACkB,QAAQ;UAAAU,QAAA,eACP5B,OAAA;YAAM2D,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAhC,QAAA,EAAC;UAAc;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAETlD,OAAA,CAACyB,WAAW;QAAAG,QAAA,EACTA;MAAQ;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEtB,CAAC;AAAClB,EAAA,CA3EWL,MAA6B;EAAA,QAMR9B,OAAO;AAAA;AAAAgE,GAAA,GAN5BlC,MAA6B;AAAA,IAAAxB,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAmC,GAAA;AAAAC,YAAA,CAAA3D,EAAA;AAAA2D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAAnD,GAAA;AAAAmD,YAAA,CAAAhD,GAAA;AAAAgD,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAApC,GAAA;AAAAoC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}