{"ast": null, "code": "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n  options = options || {};\n  const rnds = options.random ?? options.rng?.() ?? rng();\n  if (rnds.length < 16) {\n    throw new Error('Random bytes length must be >= 16');\n  }\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80;\n  if (buf) {\n    offset = offset || 0;\n    if (offset < 0 || offset + 16 > buf.length) {\n      throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n    }\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n    return buf;\n  }\n  return unsafeStringify(rnds);\n}\nexport default v4;", "map": {"version": 3, "names": ["native", "rng", "unsafeStringify", "v4", "options", "buf", "offset", "randomUUID", "rnds", "random", "length", "Error", "RangeError", "i"], "sources": ["D:/Dev/partitura_digital/node_modules/uuid/dist/esm-browser/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAChC,OAAOC,GAAG,MAAM,UAAU;AAC1B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,EAAEA,CAACC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAC9B,IAAIN,MAAM,CAACO,UAAU,IAAI,CAACF,GAAG,IAAI,CAACD,OAAO,EAAE;IACvC,OAAOJ,MAAM,CAACO,UAAU,CAAC,CAAC;EAC9B;EACAH,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAMI,IAAI,GAAGJ,OAAO,CAACK,MAAM,IAAIL,OAAO,CAACH,GAAG,GAAG,CAAC,IAAIA,GAAG,CAAC,CAAC;EACvD,IAAIO,IAAI,CAACE,MAAM,GAAG,EAAE,EAAE;IAClB,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;EACxD;EACAH,IAAI,CAAC,CAAC,CAAC,GAAIA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,IAAI;EACjCA,IAAI,CAAC,CAAC,CAAC,GAAIA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,IAAI;EACjC,IAAIH,GAAG,EAAE;IACLC,MAAM,GAAGA,MAAM,IAAI,CAAC;IACpB,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,EAAE,GAAGD,GAAG,CAACK,MAAM,EAAE;MACxC,MAAM,IAAIE,UAAU,CAAC,mBAAmBN,MAAM,IAAIA,MAAM,GAAG,EAAE,0BAA0B,CAAC;IAC5F;IACA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MACzBR,GAAG,CAACC,MAAM,GAAGO,CAAC,CAAC,GAAGL,IAAI,CAACK,CAAC,CAAC;IAC7B;IACA,OAAOR,GAAG;EACd;EACA,OAAOH,eAAe,CAACM,IAAI,CAAC;AAChC;AACA,eAAeL,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}