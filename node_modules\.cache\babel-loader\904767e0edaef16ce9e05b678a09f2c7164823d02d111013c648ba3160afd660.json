{"ast": null, "code": "import React from'react';import styled from'styled-components';import{useAuth}from'../../contexts/AuthContext';import{Icons}from'../../utils/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LayoutContainer=styled.div`\n  display: flex;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n`;const Sidebar=styled.nav`\n  width: 280px;\n  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);\n  color: white;\n  padding: 1.5rem;\n  box-shadow: 4px 0 15px rgba(0,0,0,0.2);\n  backdrop-filter: blur(10px);\n`;const Logo=styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 1.6rem;\n  font-weight: bold;\n  margin-bottom: 2.5rem;\n  padding-bottom: 1.5rem;\n  border-bottom: 2px solid rgba(255,255,255,0.2);\n  background: linear-gradient(45deg, #fff, #e8f4fd);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;const NavItem=styled.button`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  width: 100%;\n  padding: 1rem 1.25rem;\n  background: none;\n  border: none;\n  color: rgba(255,255,255,0.9);\n  text-align: left;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  margin-bottom: 0.75rem;\n  font-size: 0.95rem;\n  font-weight: 500;\n\n  &:hover {\n    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);\n    transform: translateX(5px);\n    color: white;\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n  }\n\n  &.active {\n    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\n    color: white;\n    transform: translateX(8px);\n    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);\n  }\n`;const MainContent=styled.main`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n`;const Header=styled.header`\n  background: rgba(255,255,255,0.95);\n  backdrop-filter: blur(10px);\n  padding: 1.5rem 2.5rem;\n  box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid rgba(255,255,255,0.2);\n\n  h1 {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n    font-size: 1.8rem;\n    font-weight: 700;\n    margin: 0;\n  }\n`;const UserInfo=styled.div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;const UserName=styled.span`\n  font-weight: 500;\n`;const LogoutButton=styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  background-color: #e74c3c;\n  color: white;\n  border: none;\n  border-radius: 0.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #c0392b;\n  }\n`;const ContentArea=styled.div`\n  flex: 1;\n  padding: 2.5rem;\n  overflow-y: auto;\n  background: rgba(255,255,255,0.1);\n  backdrop-filter: blur(5px);\n  margin: 1rem;\n  border-radius: 20px;\n  box-shadow: inset 0 1px 0 rgba(255,255,255,0.2);\n`;export const Layout=_ref=>{let{children,currentPage='home',onNavigate=()=>{},showAuthInSidebar=false}=_ref;const{currentUser,logout}=useAuth();const handleLogout=async()=>{try{await logout();}catch(error){console.error('Erro ao fazer logout:',error);}};const navigationItems=[{id:'home',label:'Início',icon:Icons.Home},{id:'new-score',label:'Criar Partitura',icon:Icons.Plus},...(currentUser?[{id:'scores',label:'Minhas Partituras',icon:Icons.Music}]:[]),...(showAuthInSidebar?[{id:'login',label:'Entrar',icon:Icons.User},{id:'register',label:'Cadastrar',icon:Icons.Plus}]:[])];return/*#__PURE__*/_jsxs(LayoutContainer,{children:[/*#__PURE__*/_jsxs(Sidebar,{children:[/*#__PURE__*/_jsxs(Logo,{children:[/*#__PURE__*/_jsx(Icons.Music,{size:24}),\"Partitura Digital\"]}),navigationItems.map(item=>{const IconComponent=item.icon;return/*#__PURE__*/_jsxs(NavItem,{className:currentPage===item.id?'active':'',onClick:()=>onNavigate(item.id),children:[/*#__PURE__*/_jsx(IconComponent,{size:18}),item.label]},item.id);})]}),/*#__PURE__*/_jsxs(MainContent,{children:[/*#__PURE__*/_jsxs(Header,{children:[/*#__PURE__*/_jsx(\"h1\",{children:\"\\uD83C\\uDFB5 Partitura Digital\"}),currentUser?/*#__PURE__*/_jsxs(UserInfo,{children:[/*#__PURE__*/_jsx(Icons.User,{size:20}),/*#__PURE__*/_jsx(UserName,{children:(currentUser===null||currentUser===void 0?void 0:currentUser.displayName)||(currentUser===null||currentUser===void 0?void 0:currentUser.email)}),/*#__PURE__*/_jsxs(LogoutButton,{onClick:handleLogout,children:[/*#__PURE__*/_jsx(Icons.LogOut,{size:16}),\"Sair\"]})]}):/*#__PURE__*/_jsx(UserInfo,{children:/*#__PURE__*/_jsx(\"span\",{style:{color:'#666'},children:\"Modo Visitante\"})})]}),/*#__PURE__*/_jsx(ContentArea,{children:children})]})]});};", "map": {"version": 3, "names": ["React", "styled", "useAuth", "Icons", "jsx", "_jsx", "jsxs", "_jsxs", "LayoutContainer", "div", "Sidebar", "nav", "Logo", "NavItem", "button", "MainContent", "main", "Header", "header", "UserInfo", "UserName", "span", "LogoutButton", "ContentArea", "Layout", "_ref", "children", "currentPage", "onNavigate", "showAuthInSidebar", "currentUser", "logout", "handleLogout", "error", "console", "navigationItems", "id", "label", "icon", "Home", "Plus", "Music", "User", "size", "map", "item", "IconComponent", "className", "onClick", "displayName", "email", "LogOut", "style", "color"], "sources": ["D:/Dev/partitura_digital/src/components/Layout/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Icons, IconComponent } from '../../utils/icons';\n\nconst LayoutContainer = styled.div`\n  display: flex;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n`;\n\nconst Sidebar = styled.nav`\n  width: 280px;\n  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);\n  color: white;\n  padding: 1.5rem;\n  box-shadow: 4px 0 15px rgba(0,0,0,0.2);\n  backdrop-filter: blur(10px);\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 1.6rem;\n  font-weight: bold;\n  margin-bottom: 2.5rem;\n  padding-bottom: 1.5rem;\n  border-bottom: 2px solid rgba(255,255,255,0.2);\n  background: linear-gradient(45deg, #fff, #e8f4fd);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n\nconst NavItem = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  width: 100%;\n  padding: 1rem 1.25rem;\n  background: none;\n  border: none;\n  color: rgba(255,255,255,0.9);\n  text-align: left;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  margin-bottom: 0.75rem;\n  font-size: 0.95rem;\n  font-weight: 500;\n\n  &:hover {\n    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);\n    transform: translateX(5px);\n    color: white;\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n  }\n\n  &.active {\n    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\n    color: white;\n    transform: translateX(8px);\n    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);\n  }\n`;\n\nconst MainContent = styled.main`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n`;\n\nconst Header = styled.header`\n  background: rgba(255,255,255,0.95);\n  backdrop-filter: blur(10px);\n  padding: 1.5rem 2.5rem;\n  box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid rgba(255,255,255,0.2);\n\n  h1 {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n    font-size: 1.8rem;\n    font-weight: 700;\n    margin: 0;\n  }\n`;\n\nconst UserInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n`;\n\nconst UserName = styled.span`\n  font-weight: 500;\n`;\n\nconst LogoutButton = styled.button`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  background-color: #e74c3c;\n  color: white;\n  border: none;\n  border-radius: 0.25rem;\n  cursor: pointer;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #c0392b;\n  }\n`;\n\nconst ContentArea = styled.div`\n  flex: 1;\n  padding: 2.5rem;\n  overflow-y: auto;\n  background: rgba(255,255,255,0.1);\n  backdrop-filter: blur(5px);\n  margin: 1rem;\n  border-radius: 20px;\n  box-shadow: inset 0 1px 0 rgba(255,255,255,0.2);\n`;\n\ninterface LayoutProps {\n  children: React.ReactNode;\n  currentPage?: string;\n  onNavigate?: (page: string) => void;\n  showAuthInSidebar?: boolean;\n}\n\nexport const Layout: React.FC<LayoutProps> = ({\n  children,\n  currentPage = 'home',\n  onNavigate = () => {},\n  showAuthInSidebar = false\n}) => {\n  const { currentUser, logout } = useAuth();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Erro ao fazer logout:', error);\n    }\n  };\n\n  const navigationItems = [\n    { id: 'home', label: 'Início', icon: Icons.Home },\n    { id: 'new-score', label: 'Criar Partitura', icon: Icons.Plus },\n    ...(currentUser ? [{ id: 'scores', label: 'Minhas Partituras', icon: Icons.Music }] : []),\n    ...(showAuthInSidebar ? [\n      { id: 'login', label: 'Entrar', icon: Icons.User },\n      { id: 'register', label: 'Cadastrar', icon: Icons.Plus }\n    ] : [])\n  ];\n\n  return (\n    <LayoutContainer>\n      <Sidebar>\n        <Logo>\n          <Icons.Music size={24} />\n          Partitura Digital\n        </Logo>\n\n        {navigationItems.map(item => {\n          const IconComponent = item.icon;\n          return (\n            <NavItem\n              key={item.id}\n              className={currentPage === item.id ? 'active' : ''}\n              onClick={() => onNavigate(item.id)}\n            >\n              <IconComponent size={18} />\n              {item.label}\n            </NavItem>\n          );\n        })}\n      </Sidebar>\n\n      <MainContent>\n        <Header>\n          <h1>🎵 Partitura Digital</h1>\n          {currentUser ? (\n            <UserInfo>\n              <Icons.User size={20} />\n              <UserName>{currentUser?.displayName || currentUser?.email}</UserName>\n              <LogoutButton onClick={handleLogout}>\n                <Icons.LogOut size={16} />\n                Sair\n              </LogoutButton>\n            </UserInfo>\n          ) : (\n            <UserInfo>\n              <span style={{ color: '#666' }}>Modo Visitante</span>\n            </UserInfo>\n          )}\n        </Header>\n\n        <ContentArea>\n          {children}\n        </ContentArea>\n      </MainContent>\n    </LayoutContainer>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,KAAK,KAAuB,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzD,KAAM,CAAAC,eAAe,CAAGP,MAAM,CAACQ,GAAG;AAClC;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,OAAO,CAAGT,MAAM,CAACU,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,IAAI,CAAGX,MAAM,CAACQ,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,OAAO,CAAGZ,MAAM,CAACa,MAAM;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGd,MAAM,CAACe,IAAI;AAC/B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,MAAM,CAAGhB,MAAM,CAACiB,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGlB,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAW,QAAQ,CAAGnB,MAAM,CAACoB,IAAI;AAC5B;AACA,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGrB,MAAM,CAACa,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAS,WAAW,CAAGtB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CASD,MAAO,MAAM,CAAAe,MAA6B,CAAGC,IAAA,EAKvC,IALwC,CAC5CC,QAAQ,CACRC,WAAW,CAAG,MAAM,CACpBC,UAAU,CAAGA,CAAA,GAAM,CAAC,CAAC,CACrBC,iBAAiB,CAAG,KACtB,CAAC,CAAAJ,IAAA,CACC,KAAM,CAAEK,WAAW,CAAEC,MAAO,CAAC,CAAG7B,OAAO,CAAC,CAAC,CAEzC,KAAM,CAAA8B,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAD,MAAM,CAAC,CAAC,CAChB,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CACF,CAAC,CAED,KAAM,CAAAE,eAAe,CAAG,CACtB,CAAEC,EAAE,CAAE,MAAM,CAAEC,KAAK,CAAE,QAAQ,CAAEC,IAAI,CAAEnC,KAAK,CAACoC,IAAK,CAAC,CACjD,CAAEH,EAAE,CAAE,WAAW,CAAEC,KAAK,CAAE,iBAAiB,CAAEC,IAAI,CAAEnC,KAAK,CAACqC,IAAK,CAAC,CAC/D,IAAIV,WAAW,CAAG,CAAC,CAAEM,EAAE,CAAE,QAAQ,CAAEC,KAAK,CAAE,mBAAmB,CAAEC,IAAI,CAAEnC,KAAK,CAACsC,KAAM,CAAC,CAAC,CAAG,EAAE,CAAC,CACzF,IAAIZ,iBAAiB,CAAG,CACtB,CAAEO,EAAE,CAAE,OAAO,CAAEC,KAAK,CAAE,QAAQ,CAAEC,IAAI,CAAEnC,KAAK,CAACuC,IAAK,CAAC,CAClD,CAAEN,EAAE,CAAE,UAAU,CAAEC,KAAK,CAAE,WAAW,CAAEC,IAAI,CAAEnC,KAAK,CAACqC,IAAK,CAAC,CACzD,CAAG,EAAE,CAAC,CACR,CAED,mBACEjC,KAAA,CAACC,eAAe,EAAAkB,QAAA,eACdnB,KAAA,CAACG,OAAO,EAAAgB,QAAA,eACNnB,KAAA,CAACK,IAAI,EAAAc,QAAA,eACHrB,IAAA,CAACF,KAAK,CAACsC,KAAK,EAACE,IAAI,CAAE,EAAG,CAAE,CAAC,oBAE3B,EAAM,CAAC,CAENR,eAAe,CAACS,GAAG,CAACC,IAAI,EAAI,CAC3B,KAAM,CAAAC,aAAa,CAAGD,IAAI,CAACP,IAAI,CAC/B,mBACE/B,KAAA,CAACM,OAAO,EAENkC,SAAS,CAAEpB,WAAW,GAAKkB,IAAI,CAACT,EAAE,CAAG,QAAQ,CAAG,EAAG,CACnDY,OAAO,CAAEA,CAAA,GAAMpB,UAAU,CAACiB,IAAI,CAACT,EAAE,CAAE,CAAAV,QAAA,eAEnCrB,IAAA,CAACyC,aAAa,EAACH,IAAI,CAAE,EAAG,CAAE,CAAC,CAC1BE,IAAI,CAACR,KAAK,GALNQ,IAAI,CAACT,EAMH,CAAC,CAEd,CAAC,CAAC,EACK,CAAC,cAEV7B,KAAA,CAACQ,WAAW,EAAAW,QAAA,eACVnB,KAAA,CAACU,MAAM,EAAAS,QAAA,eACLrB,IAAA,OAAAqB,QAAA,CAAI,gCAAoB,CAAI,CAAC,CAC5BI,WAAW,cACVvB,KAAA,CAACY,QAAQ,EAAAO,QAAA,eACPrB,IAAA,CAACF,KAAK,CAACuC,IAAI,EAACC,IAAI,CAAE,EAAG,CAAE,CAAC,cACxBtC,IAAA,CAACe,QAAQ,EAAAM,QAAA,CAAE,CAAAI,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEmB,WAAW,IAAInB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEoB,KAAK,EAAW,CAAC,cACrE3C,KAAA,CAACe,YAAY,EAAC0B,OAAO,CAAEhB,YAAa,CAAAN,QAAA,eAClCrB,IAAA,CAACF,KAAK,CAACgD,MAAM,EAACR,IAAI,CAAE,EAAG,CAAE,CAAC,OAE5B,EAAc,CAAC,EACP,CAAC,cAEXtC,IAAA,CAACc,QAAQ,EAAAO,QAAA,cACPrB,IAAA,SAAM+C,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAA3B,QAAA,CAAC,gBAAc,CAAM,CAAC,CAC7C,CACX,EACK,CAAC,cAETrB,IAAA,CAACkB,WAAW,EAAAG,QAAA,CACTA,QAAQ,CACE,CAAC,EACH,CAAC,EACC,CAAC,CAEtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}