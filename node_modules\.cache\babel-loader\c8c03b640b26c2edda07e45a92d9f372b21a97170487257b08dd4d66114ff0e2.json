{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ChordView\\\\ChordView.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ChordContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n_c = ChordContainer;\nconst ChordHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n`;\n_c2 = ChordHeader;\nconst ChordTitle = styled.h3`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n`;\n_c3 = ChordTitle;\nconst ExportButton = styled.button`\n  padding: 0.5rem 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n  }\n`;\n_c4 = ExportButton;\nconst ChordGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n`;\n_c5 = ChordGrid;\nconst MeasureCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n`;\n_c6 = MeasureCard;\nconst MeasureNumber = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;\n_c7 = MeasureNumber;\nconst ChordSymbol = styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n  text-align: center;\n`;\n_c8 = ChordSymbol;\nconst ChordNotes = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  text-align: center;\n`;\n_c9 = ChordNotes;\nconst EmptyState = styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;\n_c0 = EmptyState;\nconst ProgressionView = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  border: 2px solid #e9ecef;\n`;\n_c1 = ProgressionView;\nconst ProgressionTitle = styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;\n_c10 = ProgressionTitle;\nconst ProgressionChords = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  align-items: center;\n`;\n_c11 = ProgressionChords;\nconst ProgressionChord = styled.span`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 8px;\n  font-weight: 600;\n  font-size: 1.1rem;\n`;\n_c12 = ProgressionChord;\nconst ProgressionArrow = styled.span`\n  color: #666;\n  font-size: 1.2rem;\n`;\n_c13 = ProgressionArrow;\n// Função para determinar o acorde baseado nas notas\nconst analyzeChord = notes => {\n  if (notes.length === 0) return '';\n\n  // Filtrar apenas notas (não pausas) e aplicar acidentes\n  const processedNotes = notes.filter(note => !note.isRest).map(note => {\n    let noteName = note.name;\n\n    // Aplicar acidentes\n    if (note.accidental === 'sharp') {\n      const sharpMap = {\n        'C': 'C#',\n        'D': 'D#',\n        'F': 'F#',\n        'G': 'G#',\n        'A': 'A#'\n      };\n      noteName = sharpMap[noteName] || noteName;\n    } else if (note.accidental === 'flat') {\n      const flatMap = {\n        'D': 'Db',\n        'E': 'Eb',\n        'G': 'Gb',\n        'A': 'Ab',\n        'B': 'Bb'\n      };\n      noteName = flatMap[noteName] || noteName;\n    }\n    return noteName;\n  }).filter((name, index, arr) => arr.indexOf(name) === index); // Remover duplicatas\n\n  if (processedNotes.length === 0) return '';\n  if (processedNotes.length === 1) return processedNotes[0];\n\n  // Normalizar notas para análise (converter tudo para sustenidos)\n  const normalizeNote = note => {\n    const enharmonics = {\n      'Db': 'C#',\n      'Eb': 'D#',\n      'Gb': 'F#',\n      'Ab': 'G#',\n      'Bb': 'A#'\n    };\n    return enharmonics[note] || note;\n  };\n  const normalizedNotes = processedNotes.map(normalizeNote).sort();\n\n  // Definir acordes conhecidos\n  const chordPatterns = {\n    // Acordes maiores\n    'C,E,G': 'C',\n    'C#,F,G#': 'C#',\n    'D,F#,A': 'D',\n    'D#,G,A#': 'D#',\n    'E,G#,B': 'E',\n    'F,A,C': 'F',\n    'F#,A#,C#': 'F#',\n    'G,B,D': 'G',\n    'G#,C,D#': 'G#',\n    'A,C#,E': 'A',\n    'A#,D,F': 'A#',\n    'B,D#,F#': 'B',\n    // Acordes menores\n    'C,D#,G': 'Cm',\n    'C#,E,G#': 'C#m',\n    'D,F,A': 'Dm',\n    'D#,F#,A#': 'D#m',\n    'E,G,B': 'Em',\n    'F,G#,C': 'Fm',\n    'F#,A,C#': 'F#m',\n    'G,A#,D': 'Gm',\n    'G#,B,D#': 'G#m',\n    'A,C,E': 'Am',\n    'A#,C#,F': 'A#m',\n    'B,D,F#': 'Bm',\n    // Acordes de sétima\n    'C,E,G,A#': 'C7',\n    'D,F#,A,C': 'D7',\n    'E,G#,B,D': 'E7',\n    'F,A,C,D#': 'F7',\n    'G,B,D,F': 'G7',\n    'A,C#,E,G': 'A7',\n    'B,D#,F#,A': 'B7'\n  };\n  const noteString = normalizedNotes.join(',');\n\n  // Verificar padrões conhecidos\n  if (chordPatterns[noteString]) {\n    return chordPatterns[noteString];\n  }\n\n  // Se não encontrou um padrão, tentar identificar pela fundamental\n  const root = normalizedNotes[0];\n  const intervals = normalizedNotes.slice(1);\n  if (intervals.length >= 2) {\n    // Tentar identificar como acorde básico\n    return `${root}?`; // Indica acorde não identificado com fundamental\n  }\n\n  // Retornar notas individuais\n  return processedNotes.join(' - ');\n};\nexport const ChordView = ({\n  notes,\n  title\n}) => {\n  // Agrupar notas por compasso\n  const measureMap = new Map();\n  notes.forEach(note => {\n    const measure = note.position.measure;\n    if (!measureMap.has(measure)) {\n      measureMap.set(measure, []);\n    }\n    measureMap.get(measure).push(note);\n  });\n\n  // Converter para array ordenado\n  const measures = Array.from(measureMap.entries()).sort(([a], [b]) => a - b).map(([measureNumber, measureNotes]) => ({\n    number: measureNumber,\n    notes: measureNotes.sort((a, b) => a.position.beat - b.position.beat),\n    chord: analyzeChord(measureNotes)\n  }));\n\n  // Função para exportar cifras como texto\n  const exportChords = () => {\n    const progression = measures.filter(m => m.chord && m.chord !== '—').map(m => m.chord);\n    const exportText = `\nPartitura: ${title}\nProgressão Harmônica: ${progression.join(' - ')}\n\nDetalhes por Compasso:\n${measures.map(m => `Compasso ${m.number}: ${m.chord || '—'}`).join('\\n')}\n    `.trim();\n\n    // Copiar para clipboard\n    navigator.clipboard.writeText(exportText).then(() => {\n      alert('Cifras copiadas para a área de transferência!');\n    }).catch(() => {\n      // Fallback: mostrar em alert\n      alert(exportText);\n    });\n  };\n\n  // Criar progressão harmônica\n  const progression = measures.filter(m => m.chord && m.chord !== '—').map(m => m.chord);\n  return /*#__PURE__*/_jsxDEV(ChordContainer, {\n    children: [/*#__PURE__*/_jsxDEV(ChordHeader, {\n      children: [/*#__PURE__*/_jsxDEV(ChordTitle, {\n        children: [\"\\uD83C\\uDFB8 Cifras - \", title]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), measures.length > 0 && /*#__PURE__*/_jsxDEV(ExportButton, {\n        onClick: exportChords,\n        children: \"\\uD83D\\uDCCB Exportar Cifras\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this), measures.length === 0 ? /*#__PURE__*/_jsxDEV(EmptyState, {\n      children: \"Adicione algumas notas na partitura para ver as cifras aqui\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [progression.length > 0 && /*#__PURE__*/_jsxDEV(ProgressionView, {\n        children: [/*#__PURE__*/_jsxDEV(ProgressionTitle, {\n          children: \"\\uD83C\\uDFBC Progress\\xE3o Harm\\xF4nica\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ProgressionChords, {\n          children: progression.map((chord, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ProgressionChord, {\n              children: chord\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 21\n            }, this), index < progression.length - 1 && /*#__PURE__*/_jsxDEV(ProgressionArrow, {\n              children: \"\\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 23\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ChordGrid, {\n        children: measures.map(measure => /*#__PURE__*/_jsxDEV(MeasureCard, {\n          children: [/*#__PURE__*/_jsxDEV(MeasureNumber, {\n            children: [\"Compasso \", measure.number]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ChordSymbol, {\n            children: measure.chord || '—'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ChordNotes, {\n            children: measure.notes.filter(note => !note.isRest).map(note => {\n              let noteName = note.name;\n              if (note.accidental === 'sharp') noteName += '♯';\n              if (note.accidental === 'flat') noteName += '♭';\n              if (note.accidental === 'natural') noteName += '♮';\n              return `${noteName}${note.octave}`;\n            }).join(', ') || 'Pausas'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 17\n          }, this)]\n        }, measure.number, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 274,\n    columnNumber: 5\n  }, this);\n};\n_c14 = ChordView;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"ChordContainer\");\n$RefreshReg$(_c2, \"ChordHeader\");\n$RefreshReg$(_c3, \"ChordTitle\");\n$RefreshReg$(_c4, \"ExportButton\");\n$RefreshReg$(_c5, \"ChordGrid\");\n$RefreshReg$(_c6, \"MeasureCard\");\n$RefreshReg$(_c7, \"MeasureNumber\");\n$RefreshReg$(_c8, \"ChordSymbol\");\n$RefreshReg$(_c9, \"ChordNotes\");\n$RefreshReg$(_c0, \"EmptyState\");\n$RefreshReg$(_c1, \"ProgressionView\");\n$RefreshReg$(_c10, \"ProgressionTitle\");\n$RefreshReg$(_c11, \"ProgressionChords\");\n$RefreshReg$(_c12, \"ProgressionChord\");\n$RefreshReg$(_c13, \"ProgressionArrow\");\n$RefreshReg$(_c14, \"ChordView\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_c2", "ChordTitle", "h3", "_c3", "ExportButton", "button", "_c4", "ChordGrid", "_c5", "MeasureCard", "_c6", "MeasureNumber", "_c7", "ChordSymbol", "_c8", "ChordNotes", "_c9", "EmptyState", "_c0", "ProgressionView", "_c1", "ProgressionTitle", "h4", "_c10", "ProgressionChords", "_c11", "ProgressionChord", "span", "_c12", "ProgressionArrow", "_c13", "analyzeChord", "notes", "length", "processedNotes", "filter", "note", "isRest", "map", "noteName", "name", "accidental", "sharpMap", "flatMap", "index", "arr", "indexOf", "normalizeNote", "enharmonics", "normalizedNotes", "sort", "chordPatterns", "noteString", "join", "root", "intervals", "slice", "ChordView", "title", "measureMap", "Map", "for<PERSON>ach", "measure", "position", "has", "set", "get", "push", "measures", "Array", "from", "entries", "a", "b", "measureNumber", "measureNotes", "number", "beat", "chord", "exportChords", "progression", "m", "exportText", "trim", "navigator", "clipboard", "writeText", "then", "alert", "catch", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "octave", "_c14", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ChordView/ChordView.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { MusicalNote, NoteName } from '../../types/music';\n\nconst ChordContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst ChordHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n`;\n\nconst ChordTitle = styled.h3`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n`;\n\nconst ExportButton = styled.button`\n  padding: 0.5rem 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n  }\n`;\n\nconst ChordGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n`;\n\nconst MeasureCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n`;\n\nconst MeasureNumber = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;\n\nconst ChordSymbol = styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n  text-align: center;\n`;\n\nconst ChordNotes = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  text-align: center;\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;\n\nconst ProgressionView = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  border: 2px solid #e9ecef;\n`;\n\nconst ProgressionTitle = styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;\n\nconst ProgressionChords = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  align-items: center;\n`;\n\nconst ProgressionChord = styled.span`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 8px;\n  font-weight: 600;\n  font-size: 1.1rem;\n`;\n\nconst ProgressionArrow = styled.span`\n  color: #666;\n  font-size: 1.2rem;\n`;\n\ninterface ChordViewProps {\n  notes: MusicalNote[];\n  title: string;\n}\n\n// Função para determinar o acorde baseado nas notas\nconst analyzeChord = (notes: MusicalNote[]): string => {\n  if (notes.length === 0) return '';\n\n  // Filtrar apenas notas (não pausas) e aplicar acidentes\n  const processedNotes = notes\n    .filter(note => !note.isRest)\n    .map(note => {\n      let noteName = note.name;\n\n      // Aplicar acidentes\n      if (note.accidental === 'sharp') {\n        const sharpMap: { [key: string]: string } = {\n          'C': 'C#', 'D': 'D#', 'F': 'F#', 'G': 'G#', 'A': 'A#'\n        };\n        noteName = sharpMap[noteName] || noteName;\n      } else if (note.accidental === 'flat') {\n        const flatMap: { [key: string]: string } = {\n          'D': 'Db', 'E': 'Eb', 'G': 'Gb', 'A': 'Ab', 'B': 'Bb'\n        };\n        noteName = flatMap[noteName] || noteName;\n      }\n\n      return noteName;\n    })\n    .filter((name, index, arr) => arr.indexOf(name) === index); // Remover duplicatas\n\n  if (processedNotes.length === 0) return '';\n  if (processedNotes.length === 1) return processedNotes[0];\n\n  // Normalizar notas para análise (converter tudo para sustenidos)\n  const normalizeNote = (note: string): string => {\n    const enharmonics: { [key: string]: string } = {\n      'Db': 'C#', 'Eb': 'D#', 'Gb': 'F#', 'Ab': 'G#', 'Bb': 'A#'\n    };\n    return enharmonics[note] || note;\n  };\n\n  const normalizedNotes = processedNotes.map(normalizeNote).sort();\n\n  // Definir acordes conhecidos\n  const chordPatterns: { [key: string]: string } = {\n    // Acordes maiores\n    'C,E,G': 'C',\n    'C#,F,G#': 'C#',\n    'D,F#,A': 'D',\n    'D#,G,A#': 'D#',\n    'E,G#,B': 'E',\n    'F,A,C': 'F',\n    'F#,A#,C#': 'F#',\n    'G,B,D': 'G',\n    'G#,C,D#': 'G#',\n    'A,C#,E': 'A',\n    'A#,D,F': 'A#',\n    'B,D#,F#': 'B',\n\n    // Acordes menores\n    'C,D#,G': 'Cm',\n    'C#,E,G#': 'C#m',\n    'D,F,A': 'Dm',\n    'D#,F#,A#': 'D#m',\n    'E,G,B': 'Em',\n    'F,G#,C': 'Fm',\n    'F#,A,C#': 'F#m',\n    'G,A#,D': 'Gm',\n    'G#,B,D#': 'G#m',\n    'A,C,E': 'Am',\n    'A#,C#,F': 'A#m',\n    'B,D,F#': 'Bm',\n\n    // Acordes de sétima\n    'C,E,G,A#': 'C7',\n    'D,F#,A,C': 'D7',\n    'E,G#,B,D': 'E7',\n    'F,A,C,D#': 'F7',\n    'G,B,D,F': 'G7',\n    'A,C#,E,G': 'A7',\n    'B,D#,F#,A': 'B7'\n  };\n\n  const noteString = normalizedNotes.join(',');\n\n  // Verificar padrões conhecidos\n  if (chordPatterns[noteString]) {\n    return chordPatterns[noteString];\n  }\n\n  // Se não encontrou um padrão, tentar identificar pela fundamental\n  const root = normalizedNotes[0];\n  const intervals = normalizedNotes.slice(1);\n\n  if (intervals.length >= 2) {\n    // Tentar identificar como acorde básico\n    return `${root}?`; // Indica acorde não identificado com fundamental\n  }\n\n  // Retornar notas individuais\n  return processedNotes.join(' - ');\n};\n\nexport const ChordView: React.FC<ChordViewProps> = ({ notes, title }) => {\n  // Agrupar notas por compasso\n  const measureMap = new Map<number, MusicalNote[]>();\n\n  notes.forEach(note => {\n    const measure = note.position.measure;\n    if (!measureMap.has(measure)) {\n      measureMap.set(measure, []);\n    }\n    measureMap.get(measure)!.push(note);\n  });\n\n  // Converter para array ordenado\n  const measures = Array.from(measureMap.entries())\n    .sort(([a], [b]) => a - b)\n    .map(([measureNumber, measureNotes]) => ({\n      number: measureNumber,\n      notes: measureNotes.sort((a, b) => a.position.beat - b.position.beat),\n      chord: analyzeChord(measureNotes)\n    }));\n\n  // Função para exportar cifras como texto\n  const exportChords = () => {\n    const progression = measures\n      .filter(m => m.chord && m.chord !== '—')\n      .map(m => m.chord);\n\n    const exportText = `\nPartitura: ${title}\nProgressão Harmônica: ${progression.join(' - ')}\n\nDetalhes por Compasso:\n${measures.map(m => `Compasso ${m.number}: ${m.chord || '—'}`).join('\\n')}\n    `.trim();\n\n    // Copiar para clipboard\n    navigator.clipboard.writeText(exportText).then(() => {\n      alert('Cifras copiadas para a área de transferência!');\n    }).catch(() => {\n      // Fallback: mostrar em alert\n      alert(exportText);\n    });\n  };\n\n  // Criar progressão harmônica\n  const progression = measures\n    .filter(m => m.chord && m.chord !== '—')\n    .map(m => m.chord);\n\n  return (\n    <ChordContainer>\n      <ChordHeader>\n        <ChordTitle>🎸 Cifras - {title}</ChordTitle>\n        {measures.length > 0 && (\n          <ExportButton onClick={exportChords}>\n            📋 Exportar Cifras\n          </ExportButton>\n        )}\n      </ChordHeader>\n\n      {measures.length === 0 ? (\n        <EmptyState>\n          Adicione algumas notas na partitura para ver as cifras aqui\n        </EmptyState>\n      ) : (\n        <>\n          {/* Progressão Harmônica */}\n          {progression.length > 0 && (\n            <ProgressionView>\n              <ProgressionTitle>🎼 Progressão Harmônica</ProgressionTitle>\n              <ProgressionChords>\n                {progression.map((chord, index) => (\n                  <React.Fragment key={index}>\n                    <ProgressionChord>{chord}</ProgressionChord>\n                    {index < progression.length - 1 && (\n                      <ProgressionArrow>→</ProgressionArrow>\n                    )}\n                  </React.Fragment>\n                ))}\n              </ProgressionChords>\n            </ProgressionView>\n          )}\n\n          {/* Detalhes por Compasso */}\n          <ChordGrid>\n            {measures.map(measure => (\n              <MeasureCard key={measure.number}>\n                <MeasureNumber>Compasso {measure.number}</MeasureNumber>\n                <ChordSymbol>\n                  {measure.chord || '—'}\n                </ChordSymbol>\n                <ChordNotes>\n                  {measure.notes\n                    .filter(note => !note.isRest)\n                    .map(note => {\n                      let noteName = note.name;\n                      if (note.accidental === 'sharp') noteName += '♯';\n                      if (note.accidental === 'flat') noteName += '♭';\n                      if (note.accidental === 'natural') noteName += '♮';\n                      return `${noteName}${note.octave}`;\n                    })\n                    .join(', ') || 'Pausas'}\n                </ChordNotes>\n              </MeasureCard>\n            ))}\n          </ChordGrid>\n        </>\n      )}\n    </ChordContainer>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGvC,MAAMC,cAAc,GAAGL,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,cAAc;AAQpB,MAAMG,WAAW,GAAGR,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,WAAW;AAOjB,MAAME,UAAU,GAAGV,MAAM,CAACW,EAAE;AAC5B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,UAAU;AAMhB,MAAMG,YAAY,GAAGb,MAAM,CAACc,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,YAAY;AAiBlB,MAAMG,SAAS,GAAGhB,MAAM,CAACM,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAJID,SAAS;AAMf,MAAME,WAAW,GAAGlB,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GALID,WAAW;AAOjB,MAAME,aAAa,GAAGpB,MAAM,CAACM,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GALID,aAAa;AAOnB,MAAME,WAAW,GAAGtB,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GANID,WAAW;AAQjB,MAAME,UAAU,GAAGxB,MAAM,CAACM,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GAJID,UAAU;AAMhB,MAAME,UAAU,GAAG1B,MAAM,CAACM,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GALID,UAAU;AAOhB,MAAME,eAAe,GAAG5B,MAAM,CAACM,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GANID,eAAe;AAQrB,MAAME,gBAAgB,GAAG9B,MAAM,CAAC+B,EAAE;AAClC;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAJIF,gBAAgB;AAMtB,MAAMG,iBAAiB,GAAGjC,MAAM,CAACM,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GALID,iBAAiB;AAOvB,MAAME,gBAAgB,GAAGnC,MAAM,CAACoC,IAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAPIF,gBAAgB;AAStB,MAAMG,gBAAgB,GAAGtC,MAAM,CAACoC,IAAI;AACpC;AACA;AACA,CAAC;AAACG,IAAA,GAHID,gBAAgB;AAUtB;AACA,MAAME,YAAY,GAAIC,KAAoB,IAAa;EACrD,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;;EAEjC;EACA,MAAMC,cAAc,GAAGF,KAAK,CACzBG,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,CAAC,CAC5BC,GAAG,CAACF,IAAI,IAAI;IACX,IAAIG,QAAQ,GAAGH,IAAI,CAACI,IAAI;;IAExB;IACA,IAAIJ,IAAI,CAACK,UAAU,KAAK,OAAO,EAAE;MAC/B,MAAMC,QAAmC,GAAG;QAC1C,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE;MACnD,CAAC;MACDH,QAAQ,GAAGG,QAAQ,CAACH,QAAQ,CAAC,IAAIA,QAAQ;IAC3C,CAAC,MAAM,IAAIH,IAAI,CAACK,UAAU,KAAK,MAAM,EAAE;MACrC,MAAME,OAAkC,GAAG;QACzC,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE;MACnD,CAAC;MACDJ,QAAQ,GAAGI,OAAO,CAACJ,QAAQ,CAAC,IAAIA,QAAQ;IAC1C;IAEA,OAAOA,QAAQ;EACjB,CAAC,CAAC,CACDJ,MAAM,CAAC,CAACK,IAAI,EAAEI,KAAK,EAAEC,GAAG,KAAKA,GAAG,CAACC,OAAO,CAACN,IAAI,CAAC,KAAKI,KAAK,CAAC,CAAC,CAAC;;EAE9D,IAAIV,cAAc,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;EAC1C,IAAIC,cAAc,CAACD,MAAM,KAAK,CAAC,EAAE,OAAOC,cAAc,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAMa,aAAa,GAAIX,IAAY,IAAa;IAC9C,MAAMY,WAAsC,GAAG;MAC7C,IAAI,EAAE,IAAI;MAAE,IAAI,EAAE,IAAI;MAAE,IAAI,EAAE,IAAI;MAAE,IAAI,EAAE,IAAI;MAAE,IAAI,EAAE;IACxD,CAAC;IACD,OAAOA,WAAW,CAACZ,IAAI,CAAC,IAAIA,IAAI;EAClC,CAAC;EAED,MAAMa,eAAe,GAAGf,cAAc,CAACI,GAAG,CAACS,aAAa,CAAC,CAACG,IAAI,CAAC,CAAC;;EAEhE;EACA,MAAMC,aAAwC,GAAG;IAC/C;IACA,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,GAAG;IACb,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,UAAU,EAAE,IAAI;IAChB,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,GAAG;IAEd;IACA,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,KAAK;IAChB,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,KAAK;IACjB,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,KAAK;IAChB,OAAO,EAAE,IAAI;IACb,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,IAAI;IAEd;IACA,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,IAAI;IAChB,WAAW,EAAE;EACf,CAAC;EAED,MAAMC,UAAU,GAAGH,eAAe,CAACI,IAAI,CAAC,GAAG,CAAC;;EAE5C;EACA,IAAIF,aAAa,CAACC,UAAU,CAAC,EAAE;IAC7B,OAAOD,aAAa,CAACC,UAAU,CAAC;EAClC;;EAEA;EACA,MAAME,IAAI,GAAGL,eAAe,CAAC,CAAC,CAAC;EAC/B,MAAMM,SAAS,GAAGN,eAAe,CAACO,KAAK,CAAC,CAAC,CAAC;EAE1C,IAAID,SAAS,CAACtB,MAAM,IAAI,CAAC,EAAE;IACzB;IACA,OAAO,GAAGqB,IAAI,GAAG,CAAC,CAAC;EACrB;;EAEA;EACA,OAAOpB,cAAc,CAACmB,IAAI,CAAC,KAAK,CAAC;AACnC,CAAC;AAED,OAAO,MAAMI,SAAmC,GAAGA,CAAC;EAAEzB,KAAK;EAAE0B;AAAM,CAAC,KAAK;EACvE;EACA,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAwB,CAAC;EAEnD5B,KAAK,CAAC6B,OAAO,CAACzB,IAAI,IAAI;IACpB,MAAM0B,OAAO,GAAG1B,IAAI,CAAC2B,QAAQ,CAACD,OAAO;IACrC,IAAI,CAACH,UAAU,CAACK,GAAG,CAACF,OAAO,CAAC,EAAE;MAC5BH,UAAU,CAACM,GAAG,CAACH,OAAO,EAAE,EAAE,CAAC;IAC7B;IACAH,UAAU,CAACO,GAAG,CAACJ,OAAO,CAAC,CAAEK,IAAI,CAAC/B,IAAI,CAAC;EACrC,CAAC,CAAC;;EAEF;EACA,MAAMgC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACX,UAAU,CAACY,OAAO,CAAC,CAAC,CAAC,CAC9CrB,IAAI,CAAC,CAAC,CAACsB,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CACzBnC,GAAG,CAAC,CAAC,CAACoC,aAAa,EAAEC,YAAY,CAAC,MAAM;IACvCC,MAAM,EAAEF,aAAa;IACrB1C,KAAK,EAAE2C,YAAY,CAACzB,IAAI,CAAC,CAACsB,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACT,QAAQ,CAACc,IAAI,GAAGJ,CAAC,CAACV,QAAQ,CAACc,IAAI,CAAC;IACrEC,KAAK,EAAE/C,YAAY,CAAC4C,YAAY;EAClC,CAAC,CAAC,CAAC;;EAEL;EACA,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,WAAW,GAAGZ,QAAQ,CACzBjC,MAAM,CAAC8C,CAAC,IAAIA,CAAC,CAACH,KAAK,IAAIG,CAAC,CAACH,KAAK,KAAK,GAAG,CAAC,CACvCxC,GAAG,CAAC2C,CAAC,IAAIA,CAAC,CAACH,KAAK,CAAC;IAEpB,MAAMI,UAAU,GAAG;AACvB,aAAaxB,KAAK;AAClB,wBAAwBsB,WAAW,CAAC3B,IAAI,CAAC,KAAK,CAAC;AAC/C;AACA;AACA,EAAEe,QAAQ,CAAC9B,GAAG,CAAC2C,CAAC,IAAI,YAAYA,CAAC,CAACL,MAAM,KAAKK,CAAC,CAACH,KAAK,IAAI,GAAG,EAAE,CAAC,CAACzB,IAAI,CAAC,IAAI,CAAC;AACzE,KAAK,CAAC8B,IAAI,CAAC,CAAC;;IAER;IACAC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACJ,UAAU,CAAC,CAACK,IAAI,CAAC,MAAM;MACnDC,KAAK,CAAC,+CAA+C,CAAC;IACxD,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM;MACb;MACAD,KAAK,CAACN,UAAU,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMF,WAAW,GAAGZ,QAAQ,CACzBjC,MAAM,CAAC8C,CAAC,IAAIA,CAAC,CAACH,KAAK,IAAIG,CAAC,CAACH,KAAK,KAAK,GAAG,CAAC,CACvCxC,GAAG,CAAC2C,CAAC,IAAIA,CAAC,CAACH,KAAK,CAAC;EAEpB,oBACErF,OAAA,CAACG,cAAc;IAAA8F,QAAA,gBACbjG,OAAA,CAACM,WAAW;MAAA2F,QAAA,gBACVjG,OAAA,CAACQ,UAAU;QAAAyF,QAAA,GAAC,wBAAY,EAAChC,KAAK;MAAA;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,EAC3C1B,QAAQ,CAACnC,MAAM,GAAG,CAAC,iBAClBxC,OAAA,CAACW,YAAY;QAAC2F,OAAO,EAAEhB,YAAa;QAAAW,QAAA,EAAC;MAErC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CACf;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC,EAEb1B,QAAQ,CAACnC,MAAM,KAAK,CAAC,gBACpBxC,OAAA,CAACwB,UAAU;MAAAyE,QAAA,EAAC;IAEZ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,gBAEbrG,OAAA,CAAAE,SAAA;MAAA+F,QAAA,GAEGV,WAAW,CAAC/C,MAAM,GAAG,CAAC,iBACrBxC,OAAA,CAAC0B,eAAe;QAAAuE,QAAA,gBACdjG,OAAA,CAAC4B,gBAAgB;UAAAqE,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC,eAC5DrG,OAAA,CAAC+B,iBAAiB;UAAAkE,QAAA,EACfV,WAAW,CAAC1C,GAAG,CAAC,CAACwC,KAAK,EAAElC,KAAK,kBAC5BnD,OAAA,CAACH,KAAK,CAACI,QAAQ;YAAAgG,QAAA,gBACbjG,OAAA,CAACiC,gBAAgB;cAAAgE,QAAA,EAAEZ;YAAK;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,EAC3ClD,KAAK,GAAGoC,WAAW,CAAC/C,MAAM,GAAG,CAAC,iBAC7BxC,OAAA,CAACoC,gBAAgB;cAAA6D,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAkB,CACtC;UAAA,GAJkBlD,KAAK;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKV,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAClB,eAGDrG,OAAA,CAACc,SAAS;QAAAmF,QAAA,EACPtB,QAAQ,CAAC9B,GAAG,CAACwB,OAAO,iBACnBrE,OAAA,CAACgB,WAAW;UAAAiF,QAAA,gBACVjG,OAAA,CAACkB,aAAa;YAAA+E,QAAA,GAAC,WAAS,EAAC5B,OAAO,CAACc,MAAM;UAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB,CAAC,eACxDrG,OAAA,CAACoB,WAAW;YAAA6E,QAAA,EACT5B,OAAO,CAACgB,KAAK,IAAI;UAAG;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACdrG,OAAA,CAACsB,UAAU;YAAA2E,QAAA,EACR5B,OAAO,CAAC9B,KAAK,CACXG,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,CAAC,CAC5BC,GAAG,CAACF,IAAI,IAAI;cACX,IAAIG,QAAQ,GAAGH,IAAI,CAACI,IAAI;cACxB,IAAIJ,IAAI,CAACK,UAAU,KAAK,OAAO,EAAEF,QAAQ,IAAI,GAAG;cAChD,IAAIH,IAAI,CAACK,UAAU,KAAK,MAAM,EAAEF,QAAQ,IAAI,GAAG;cAC/C,IAAIH,IAAI,CAACK,UAAU,KAAK,SAAS,EAAEF,QAAQ,IAAI,GAAG;cAClD,OAAO,GAAGA,QAAQ,GAAGH,IAAI,CAAC4D,MAAM,EAAE;YACpC,CAAC,CAAC,CACD3C,IAAI,CAAC,IAAI,CAAC,IAAI;UAAQ;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA,GAhBGhC,OAAO,CAACc,MAAM;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBnB,CACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA,eACZ,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAErB,CAAC;AAACG,IAAA,GA9GWxC,SAAmC;AAAA,IAAA3D,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAmE,IAAA;AAAAC,YAAA,CAAApG,EAAA;AAAAoG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}