import React, { useState, useCallback } from 'react';
import styled from 'styled-components';
import { MusicalNote, Lyrics, InstrumentType } from '../../types/music';
import { ProfessionalScore } from '../ProfessionalScore/ProfessionalScore';
import { OrchestralSelector } from '../OrchestralSelector/OrchestralSelector';
import { AdvancedLyricsEditor } from '../AdvancedLyricsEditor/AdvancedLyricsEditor';
import { getOrchestralInstrument } from '../../utils/orchestralInstruments';
import { v4 as uuidv4 } from 'uuid';

const EditorContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
`;

const EditorWrapper = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  background: rgba(255,255,255,0.95);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0,0,0,0.2);
`;

const EditorHeader = styled.div`
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 2rem;
  text-align: center;
`;

const EditorTitle = styled.h1`
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
`;

const EditorSubtitle = styled.p`
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
`;

const EditorContent = styled.div`
  padding: 2rem;
`;

const ControlsSection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
`;

const ScoreSection = styled.div`
  margin-bottom: 2rem;
`;

const PlaybackControls = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 16px;
`;

const PlayButton = styled.button<{ isPlaying?: boolean }>`
  padding: 1rem 2rem;
  border: none;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => props.isPlaying ? `
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    &:hover { 
      background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
    }
  ` : `
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    &:hover { 
      background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
    }
  `}
`;

const TempoControl = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  
  label {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.1rem;
  }
  
  input {
    width: 100px;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
    
    &:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`
  padding: 1rem 2rem;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => {
    switch(props.variant) {
      case 'primary':
        return `
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          &:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
          }
        `;
      case 'danger':
        return `
          background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
          color: white;
          &:hover {
            background: linear-gradient(135deg, #d62c1a 0%, #a93226 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
          }
        `;
      default:
        return `
          background: #f8f9fa;
          color: #495057;
          border: 2px solid #e9ecef;
          &:hover {
            background: #e9ecef;
            transform: translateY(-2px);
          }
        `;
    }
  }}
`;

interface ProfessionalScoreEditorProps {
  initialTitle?: string;
  initialComposer?: string;
}

export const ProfessionalScoreEditor: React.FC<ProfessionalScoreEditorProps> = ({
  initialTitle = "Nova Partitura",
  initialComposer = ""
}) => {
  const [title, setTitle] = useState(initialTitle);
  const [composer, setComposer] = useState(initialComposer);
  const [selectedInstruments, setSelectedInstruments] = useState<string[]>(['piano']);
  const [notes, setNotes] = useState<MusicalNote[]>([]);
  const [lyrics, setLyrics] = useState<Lyrics[]>([]);
  const [tempo, setTempo] = useState(120);
  const [isPlaying, setIsPlaying] = useState(false);

  const handleInstrumentAdd = useCallback((instrumentId: string) => {
    setSelectedInstruments(prev => [...prev, instrumentId]);
  }, []);

  const handleInstrumentRemove = useCallback((instrumentId: string) => {
    setSelectedInstruments(prev => prev.filter(id => id !== instrumentId));
  }, []);

  const handleLyricsAdd = useCallback((lyric: Lyrics) => {
    setLyrics(prev => [...prev, lyric]);
  }, []);

  const handleLyricsRemove = useCallback((index: number) => {
    setLyrics(prev => prev.filter((_, i) => i !== index));
  }, []);

  const handleLyricsClear = useCallback(() => {
    setLyrics([]);
  }, []);

  const handleNoteClick = useCallback((x: number, y: number, staffIndex: number) => {
    // Calcular posição da nota baseada no clique
    const measureWidth = 200;
    const startX = 50;
    const relativeX = x - startX;
    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);
    const beatPosition = ((relativeX % measureWidth) / measureWidth) * 4;

    // Calcular altura da nota (simplificado)
    const staffHeight = 120;
    const lineSpacing = 10;
    const staffTop = 40 + (staffIndex * 140);
    const relativeY = y - staffTop;
    const lineIndex = Math.round(relativeY / lineSpacing);
    
    // Mapear para notas (clave de sol)
    const noteMap = ['F', 'E', 'D', 'C', 'B', 'A', 'G', 'F', 'E'];
    const octaveMap = [5, 5, 5, 5, 4, 4, 4, 4, 4];
    const noteIndex = Math.max(0, Math.min(noteMap.length - 1, lineIndex + 4));

    const newNote: MusicalNote = {
      id: uuidv4(),
      name: noteMap[noteIndex] as any,
      octave: octaveMap[noteIndex] as any,
      duration: 'quarter',
      position: {
        measure,
        beat: Math.round(beatPosition * 4) / 4,
        staff: staffIndex
      },
      isRest: false
    };

    setNotes(prev => [...prev, newNote]);
  }, []);

  const handlePlay = useCallback(() => {
    setIsPlaying(!isPlaying);
  }, [isPlaying]);

  const handleClearAll = useCallback(() => {
    if (window.confirm('Tem certeza que deseja limpar toda a partitura?')) {
      setNotes([]);
      setLyrics([]);
    }
  }, []);

  const handleExport = useCallback(() => {
    // TODO: Implementar exportação
    alert('Funcionalidade de exportação em desenvolvimento!');
  }, []);

  return (
    <EditorContainer>
      <EditorWrapper>
        <EditorHeader>
          <EditorTitle>{title}</EditorTitle>
          <EditorSubtitle>
            {composer && `por ${composer}`}
            {selectedInstruments.length > 0 && (
              <span> • {selectedInstruments.length} instrumento{selectedInstruments.length > 1 ? 's' : ''}</span>
            )}
          </EditorSubtitle>
        </EditorHeader>

        <EditorContent>
          <ControlsSection>
            <OrchestralSelector
              selectedInstruments={selectedInstruments}
              onInstrumentAdd={handleInstrumentAdd}
              onInstrumentRemove={handleInstrumentRemove}
            />
            
            <AdvancedLyricsEditor
              lyrics={lyrics}
              onLyricsAdd={handleLyricsAdd}
              onLyricsRemove={handleLyricsRemove}
              onLyricsClear={handleLyricsClear}
            />
          </ControlsSection>

          <ScoreSection>
            <ProfessionalScore
              title={title}
              composer={composer}
              notes={notes}
              lyrics={lyrics}
              instruments={selectedInstruments as any[]}
              tempo={tempo}
              onTempoChange={setTempo}
              onNoteClick={handleNoteClick}
            />
          </ScoreSection>

          <PlaybackControls>
            <PlayButton isPlaying={isPlaying} onClick={handlePlay}>
              {isPlaying ? '⏸️ Pausar Reprodução' : '▶️ Reproduzir Partitura'}
            </PlayButton>
            
            <TempoControl>
              <label>Andamento:</label>
              <input
                type="number"
                min="60"
                max="200"
                value={tempo}
                onChange={(e) => setTempo(parseInt(e.target.value) || 120)}
              />
              <span>BPM</span>
            </TempoControl>
          </PlaybackControls>

          <ActionButtons>
            <ActionButton onClick={handleExport} variant="primary">
              📄 Exportar PDF
            </ActionButton>
            <ActionButton onClick={() => alert('Salvar em desenvolvimento!')}>
              💾 Salvar Projeto
            </ActionButton>
            <ActionButton onClick={handleClearAll} variant="danger">
              🗑️ Limpar Tudo
            </ActionButton>
          </ActionButtons>
        </EditorContent>
      </EditorWrapper>
    </EditorContainer>
  );
};
