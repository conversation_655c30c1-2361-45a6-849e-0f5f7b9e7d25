{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\MusicalNote\\\\MusicalNote.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Componente para renderizar uma nota musical com símbolos precisos\nexport const MusicalNote = ({\n  note,\n  x,\n  y,\n  onRemove\n}) => {\n  const isAboveMiddle = y < 90; // Determina direção da haste\n  const stemDirection = isAboveMiddle ? 'down' : 'up';\n  const stemX = stemDirection === 'up' ? x + 7 : x - 7;\n  const stemY1 = y;\n  const stemY2 = stemDirection === 'up' ? y - 25 : y + 25;\n\n  // Renderizar pausa\n  if (note.isRest) {\n    return /*#__PURE__*/_jsxDEV(\"g\", {\n      children: [note.duration === 'quarter' && /*#__PURE__*/_jsxDEV(\"g\", {\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: `M ${x - 6} ${y - 8} L ${x + 2} ${y - 8} L ${x - 2} ${y} L ${x + 6} ${y} L ${x + 2} ${y + 8} L ${x - 6} ${y + 8} Z`,\n          fill: \"#333\",\n          stroke: \"#333\",\n          strokeWidth: \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 11\n      }, this), note.duration === 'half' && /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: x - 4,\n        y: y - 2,\n        width: \"8\",\n        height: \"4\",\n        fill: \"#333\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 11\n      }, this), note.duration === 'whole' && /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: x - 6,\n        y: y - 6,\n        width: \"12\",\n        height: \"4\",\n        fill: \"#333\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this), note.duration === 'eighth' && /*#__PURE__*/_jsxDEV(\"g\", {\n        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n          d: `M ${x - 4} ${y - 6} Q ${x} ${y - 2} ${x - 2} ${y + 2} Q ${x + 2} ${y + 6} ${x - 4} ${y + 4} Z`,\n          fill: \"#333\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n          cx: x + 2,\n          cy: y - 4,\n          r: \"1.5\",\n          fill: \"#333\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this), note.duration === 'sixteenth' && /*#__PURE__*/_jsxDEV(\"g\", {\n        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n          d: `M ${x - 4} ${y - 8} Q ${x} ${y - 4} ${x - 2} ${y} Q ${x + 2} ${y + 4} ${x - 4} ${y + 2} Z`,\n          fill: \"#333\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n          cx: x + 2,\n          cy: y - 6,\n          r: \"1.5\",\n          fill: \"#333\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n          cx: x + 3,\n          cy: y - 2,\n          r: \"1.5\",\n          fill: \"#333\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Renderizar nota musical\n  return /*#__PURE__*/_jsxDEV(\"g\", {\n    style: {\n      cursor: 'pointer'\n    },\n    onClick: e => {\n      e.stopPropagation();\n      onRemove();\n    },\n    children: [(y < 50 || y > 130) && /*#__PURE__*/_jsxDEV(\"line\", {\n      x1: x - 12,\n      y1: y,\n      x2: x + 12,\n      y2: y,\n      stroke: \"#666\",\n      strokeWidth: \"1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 9\n    }, this), note.accidental && /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x - 15,\n      y: y + 3,\n      fontSize: \"14\",\n      fill: \"#333\",\n      textAnchor: \"middle\",\n      style: {\n        pointerEvents: 'none',\n        userSelect: 'none'\n      },\n      children: note.accidental === 'sharp' ? '♯' : note.accidental === 'flat' ? '♭' : note.accidental === 'natural' ? '♮' : ''\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"ellipse\", {\n      cx: x,\n      cy: y,\n      rx: \"7\",\n      ry: \"5\",\n      fill: note.duration === 'whole' || note.duration === 'half' ? 'white' : '#333',\n      stroke: \"#333\",\n      strokeWidth: \"2\",\n      transform: `rotate(-20 ${x} ${y})`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), note.duration !== 'whole' && /*#__PURE__*/_jsxDEV(\"line\", {\n      x1: stemX,\n      y1: stemY1,\n      x2: stemX,\n      y2: stemY2,\n      stroke: \"#333\",\n      strokeWidth: \"2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 9\n    }, this), note.duration === 'eighth' && /*#__PURE__*/_jsxDEV(\"path\", {\n      d: stemDirection === 'up' ? `M ${stemX} ${stemY2} Q ${stemX + 8} ${stemY2 + 5} ${stemX + 6} ${stemY2 + 10}` : `M ${stemX} ${stemY2} Q ${stemX - 8} ${stemY2 - 5} ${stemX - 6} ${stemY2 - 10}`,\n      fill: \"#333\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 9\n    }, this), note.duration === 'sixteenth' && /*#__PURE__*/_jsxDEV(\"g\", {\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        d: stemDirection === 'up' ? `M ${stemX} ${stemY2} Q ${stemX + 8} ${stemY2 + 5} ${stemX + 6} ${stemY2 + 10}` : `M ${stemX} ${stemY2} Q ${stemX - 8} ${stemY2 - 5} ${stemX - 6} ${stemY2 - 10}`,\n        fill: \"#333\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: stemDirection === 'up' ? `M ${stemX} ${stemY2 + 6} Q ${stemX + 8} ${stemY2 + 11} ${stemX + 6} ${stemY2 + 16}` : `M ${stemX} ${stemY2 - 6} Q ${stemX - 8} ${stemY2 - 11} ${stemX - 6} ${stemY2 - 16}`,\n        fill: \"#333\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x,\n      y: y - 20,\n      fontSize: \"9\",\n      fill: \"#666\",\n      textAnchor: \"middle\",\n      style: {\n        pointerEvents: 'none',\n        userSelect: 'none'\n      },\n      children: [note.name, note.octave]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x + 15,\n      y: y + 5,\n      fontSize: \"8\",\n      fill: \"#999\",\n      textAnchor: \"start\",\n      style: {\n        pointerEvents: 'none',\n        userSelect: 'none'\n      },\n      children: note.duration === 'whole' ? '𝅝' : note.duration === 'half' ? '𝅗𝅥' : note.duration === 'quarter' ? '♩' : note.duration === 'eighth' ? '♫' : '♬'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_c = MusicalNote;\nvar _c;\n$RefreshReg$(_c, \"MusicalNote\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "MusicalNote", "note", "x", "y", "onRemove", "isAboveMiddle", "stemDirection", "stemX", "stemY1", "stemY2", "isRest", "children", "duration", "d", "fill", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "cx", "cy", "r", "style", "cursor", "onClick", "e", "stopPropagation", "x1", "y1", "x2", "y2", "accidental", "fontSize", "textAnchor", "pointerEvents", "userSelect", "rx", "ry", "transform", "name", "octave", "_c", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/MusicalNote/MusicalNote.tsx"], "sourcesContent": ["import React from 'react';\nimport { MusicalNote as NoteType, NoteDuration } from '../../types/music';\n\ninterface MusicalNoteProps {\n  note: NoteType;\n  x: number;\n  y: number;\n  onRemove: () => void;\n}\n\n// Componente para renderizar uma nota musical com símbolos precisos\nexport const MusicalNote: React.FC<MusicalNoteProps> = ({ note, x, y, onRemove }) => {\n  const isAboveMiddle = y < 90; // Determina direção da haste\n  const stemDirection = isAboveMiddle ? 'down' : 'up';\n  const stemX = stemDirection === 'up' ? x + 7 : x - 7;\n  const stemY1 = y;\n  const stemY2 = stemDirection === 'up' ? y - 25 : y + 25;\n\n  // Renderizar pausa\n  if (note.isRest) {\n    return (\n      <g>\n        {/* Pausa de semínima */}\n        {note.duration === 'quarter' && (\n          <g>\n            <path\n              d={`M ${x-6} ${y-8} L ${x+2} ${y-8} L ${x-2} ${y} L ${x+6} ${y} L ${x+2} ${y+8} L ${x-6} ${y+8} Z`}\n              fill=\"#333\"\n              stroke=\"#333\"\n              strokeWidth=\"1\"\n            />\n          </g>\n        )}\n        \n        {/* Pausa de mínima */}\n        {note.duration === 'half' && (\n          <rect\n            x={x - 4}\n            y={y - 2}\n            width=\"8\"\n            height=\"4\"\n            fill=\"#333\"\n          />\n        )}\n        \n        {/* Pausa de semibreve */}\n        {note.duration === 'whole' && (\n          <rect\n            x={x - 6}\n            y={y - 6}\n            width=\"12\"\n            height=\"4\"\n            fill=\"#333\"\n          />\n        )}\n        \n        {/* Pausa de colcheia */}\n        {note.duration === 'eighth' && (\n          <g>\n            <path\n              d={`M ${x-4} ${y-6} Q ${x} ${y-2} ${x-2} ${y+2} Q ${x+2} ${y+6} ${x-4} ${y+4} Z`}\n              fill=\"#333\"\n            />\n            <circle cx={x+2} cy={y-4} r=\"1.5\" fill=\"#333\" />\n          </g>\n        )}\n        \n        {/* Pausa de semicolcheia */}\n        {note.duration === 'sixteenth' && (\n          <g>\n            <path\n              d={`M ${x-4} ${y-8} Q ${x} ${y-4} ${x-2} ${y} Q ${x+2} ${y+4} ${x-4} ${y+2} Z`}\n              fill=\"#333\"\n            />\n            <circle cx={x+2} cy={y-6} r=\"1.5\" fill=\"#333\" />\n            <circle cx={x+3} cy={y-2} r=\"1.5\" fill=\"#333\" />\n          </g>\n        )}\n      </g>\n    );\n  }\n\n  // Renderizar nota musical\n  return (\n    <g style={{ cursor: 'pointer' }} onClick={(e) => { e.stopPropagation(); onRemove(); }}>\n      {/* Linha auxiliar para notas fora da pauta */}\n      {(y < 50 || y > 130) && (\n        <line\n          x1={x - 12}\n          y1={y}\n          x2={x + 12}\n          y2={y}\n          stroke=\"#666\"\n          strokeWidth=\"1\"\n        />\n      )}\n      \n      {/* Acidente (sustenido, bemol, bequadro) */}\n      {note.accidental && (\n        <text\n          x={x - 15}\n          y={y + 3}\n          fontSize=\"14\"\n          fill=\"#333\"\n          textAnchor=\"middle\"\n          style={{ pointerEvents: 'none', userSelect: 'none' }}\n        >\n          {note.accidental === 'sharp' ? '♯' :\n           note.accidental === 'flat' ? '♭' :\n           note.accidental === 'natural' ? '♮' : ''}\n        </text>\n      )}\n\n      {/* Cabeça da nota */}\n      <ellipse\n        cx={x}\n        cy={y}\n        rx=\"7\"\n        ry=\"5\"\n        fill={note.duration === 'whole' || note.duration === 'half' ? 'white' : '#333'}\n        stroke=\"#333\"\n        strokeWidth=\"2\"\n        transform={`rotate(-20 ${x} ${y})`}\n      />\n      \n      {/* Haste da nota (exceto semibreve) */}\n      {note.duration !== 'whole' && (\n        <line\n          x1={stemX}\n          y1={stemY1}\n          x2={stemX}\n          y2={stemY2}\n          stroke=\"#333\"\n          strokeWidth=\"2\"\n        />\n      )}\n      \n      {/* Bandeirola para colcheia */}\n      {note.duration === 'eighth' && (\n        <path\n          d={stemDirection === 'up' ? \n            `M ${stemX} ${stemY2} Q ${stemX + 8} ${stemY2 + 5} ${stemX + 6} ${stemY2 + 10}` :\n            `M ${stemX} ${stemY2} Q ${stemX - 8} ${stemY2 - 5} ${stemX - 6} ${stemY2 - 10}`\n          }\n          fill=\"#333\"\n        />\n      )}\n      \n      {/* Bandeirolas duplas para semicolcheia */}\n      {note.duration === 'sixteenth' && (\n        <g>\n          <path\n            d={stemDirection === 'up' ? \n              `M ${stemX} ${stemY2} Q ${stemX + 8} ${stemY2 + 5} ${stemX + 6} ${stemY2 + 10}` :\n              `M ${stemX} ${stemY2} Q ${stemX - 8} ${stemY2 - 5} ${stemX - 6} ${stemY2 - 10}`\n            }\n            fill=\"#333\"\n          />\n          <path\n            d={stemDirection === 'up' ? \n              `M ${stemX} ${stemY2 + 6} Q ${stemX + 8} ${stemY2 + 11} ${stemX + 6} ${stemY2 + 16}` :\n              `M ${stemX} ${stemY2 - 6} Q ${stemX - 8} ${stemY2 - 11} ${stemX - 6} ${stemY2 - 16}`\n            }\n            fill=\"#333\"\n          />\n        </g>\n      )}\n      \n      {/* Ponto de aumento (para notas pontuadas - futuro) */}\n      {/* Pode ser implementado futuramente */}\n      \n      {/* Texto da nota (para debug/referência) */}\n      <text\n        x={x}\n        y={y - 20}\n        fontSize=\"9\"\n        fill=\"#666\"\n        textAnchor=\"middle\"\n        style={{ pointerEvents: 'none', userSelect: 'none' }}\n      >\n        {note.name}{note.octave}\n      </text>\n      \n      {/* Indicador de duração */}\n      <text\n        x={x + 15}\n        y={y + 5}\n        fontSize=\"8\"\n        fill=\"#999\"\n        textAnchor=\"start\"\n        style={{ pointerEvents: 'none', userSelect: 'none' }}\n      >\n        {note.duration === 'whole' ? '𝅝' : \n         note.duration === 'half' ? '𝅗𝅥' :\n         note.duration === 'quarter' ? '♩' :\n         note.duration === 'eighth' ? '♫' : '♬'}\n      </text>\n    </g>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU1B;AACA,OAAO,MAAMC,WAAuC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,CAAC;EAAEC,CAAC;EAAEC;AAAS,CAAC,KAAK;EACnF,MAAMC,aAAa,GAAGF,CAAC,GAAG,EAAE,CAAC,CAAC;EAC9B,MAAMG,aAAa,GAAGD,aAAa,GAAG,MAAM,GAAG,IAAI;EACnD,MAAME,KAAK,GAAGD,aAAa,KAAK,IAAI,GAAGJ,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC;EACpD,MAAMM,MAAM,GAAGL,CAAC;EAChB,MAAMM,MAAM,GAAGH,aAAa,KAAK,IAAI,GAAGH,CAAC,GAAG,EAAE,GAAGA,CAAC,GAAG,EAAE;;EAEvD;EACA,IAAIF,IAAI,CAACS,MAAM,EAAE;IACf,oBACEX,OAAA;MAAAY,QAAA,GAEGV,IAAI,CAACW,QAAQ,KAAK,SAAS,iBAC1Bb,OAAA;QAAAY,QAAA,eACEZ,OAAA;UACEc,CAAC,EAAE,KAAKX,CAAC,GAAC,CAAC,IAAIC,CAAC,GAAC,CAAC,MAAMD,CAAC,GAAC,CAAC,IAAIC,CAAC,GAAC,CAAC,MAAMD,CAAC,GAAC,CAAC,IAAIC,CAAC,MAAMD,CAAC,GAAC,CAAC,IAAIC,CAAC,MAAMD,CAAC,GAAC,CAAC,IAAIC,CAAC,GAAC,CAAC,MAAMD,CAAC,GAAC,CAAC,IAAIC,CAAC,GAAC,CAAC,IAAK;UACnGW,IAAI,EAAC,MAAM;UACXC,MAAM,EAAC,MAAM;UACbC,WAAW,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACJ,EAGAnB,IAAI,CAACW,QAAQ,KAAK,MAAM,iBACvBb,OAAA;QACEG,CAAC,EAAEA,CAAC,GAAG,CAAE;QACTC,CAAC,EAAEA,CAAC,GAAG,CAAE;QACTkB,KAAK,EAAC,GAAG;QACTC,MAAM,EAAC,GAAG;QACVR,IAAI,EAAC;MAAM;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACF,EAGAnB,IAAI,CAACW,QAAQ,KAAK,OAAO,iBACxBb,OAAA;QACEG,CAAC,EAAEA,CAAC,GAAG,CAAE;QACTC,CAAC,EAAEA,CAAC,GAAG,CAAE;QACTkB,KAAK,EAAC,IAAI;QACVC,MAAM,EAAC,GAAG;QACVR,IAAI,EAAC;MAAM;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACF,EAGAnB,IAAI,CAACW,QAAQ,KAAK,QAAQ,iBACzBb,OAAA;QAAAY,QAAA,gBACEZ,OAAA;UACEc,CAAC,EAAE,KAAKX,CAAC,GAAC,CAAC,IAAIC,CAAC,GAAC,CAAC,MAAMD,CAAC,IAAIC,CAAC,GAAC,CAAC,IAAID,CAAC,GAAC,CAAC,IAAIC,CAAC,GAAC,CAAC,MAAMD,CAAC,GAAC,CAAC,IAAIC,CAAC,GAAC,CAAC,IAAID,CAAC,GAAC,CAAC,IAAIC,CAAC,GAAC,CAAC,IAAK;UACjFW,IAAI,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACFrB,OAAA;UAAQwB,EAAE,EAAErB,CAAC,GAAC,CAAE;UAACsB,EAAE,EAAErB,CAAC,GAAC,CAAE;UAACsB,CAAC,EAAC,KAAK;UAACX,IAAI,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CACJ,EAGAnB,IAAI,CAACW,QAAQ,KAAK,WAAW,iBAC5Bb,OAAA;QAAAY,QAAA,gBACEZ,OAAA;UACEc,CAAC,EAAE,KAAKX,CAAC,GAAC,CAAC,IAAIC,CAAC,GAAC,CAAC,MAAMD,CAAC,IAAIC,CAAC,GAAC,CAAC,IAAID,CAAC,GAAC,CAAC,IAAIC,CAAC,MAAMD,CAAC,GAAC,CAAC,IAAIC,CAAC,GAAC,CAAC,IAAID,CAAC,GAAC,CAAC,IAAIC,CAAC,GAAC,CAAC,IAAK;UAC/EW,IAAI,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACFrB,OAAA;UAAQwB,EAAE,EAAErB,CAAC,GAAC,CAAE;UAACsB,EAAE,EAAErB,CAAC,GAAC,CAAE;UAACsB,CAAC,EAAC,KAAK;UAACX,IAAI,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDrB,OAAA;UAAQwB,EAAE,EAAErB,CAAC,GAAC,CAAE;UAACsB,EAAE,EAAErB,CAAC,GAAC,CAAE;UAACsB,CAAC,EAAC,KAAK;UAACX,IAAI,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CACJ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAER;;EAEA;EACA,oBACErB,OAAA;IAAG2B,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAU,CAAE;IAACC,OAAO,EAAGC,CAAC,IAAK;MAAEA,CAAC,CAACC,eAAe,CAAC,CAAC;MAAE1B,QAAQ,CAAC,CAAC;IAAE,CAAE;IAAAO,QAAA,GAEnF,CAACR,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,kBACjBJ,OAAA;MACEgC,EAAE,EAAE7B,CAAC,GAAG,EAAG;MACX8B,EAAE,EAAE7B,CAAE;MACN8B,EAAE,EAAE/B,CAAC,GAAG,EAAG;MACXgC,EAAE,EAAE/B,CAAE;MACNY,MAAM,EAAC,MAAM;MACbC,WAAW,EAAC;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACF,EAGAnB,IAAI,CAACkC,UAAU,iBACdpC,OAAA;MACEG,CAAC,EAAEA,CAAC,GAAG,EAAG;MACVC,CAAC,EAAEA,CAAC,GAAG,CAAE;MACTiC,QAAQ,EAAC,IAAI;MACbtB,IAAI,EAAC,MAAM;MACXuB,UAAU,EAAC,QAAQ;MACnBX,KAAK,EAAE;QAAEY,aAAa,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAA5B,QAAA,EAEpDV,IAAI,CAACkC,UAAU,KAAK,OAAO,GAAG,GAAG,GACjClC,IAAI,CAACkC,UAAU,KAAK,MAAM,GAAG,GAAG,GAChClC,IAAI,CAACkC,UAAU,KAAK,SAAS,GAAG,GAAG,GAAG;IAAE;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CACP,eAGDrB,OAAA;MACEwB,EAAE,EAAErB,CAAE;MACNsB,EAAE,EAAErB,CAAE;MACNqC,EAAE,EAAC,GAAG;MACNC,EAAE,EAAC,GAAG;MACN3B,IAAI,EAAEb,IAAI,CAACW,QAAQ,KAAK,OAAO,IAAIX,IAAI,CAACW,QAAQ,KAAK,MAAM,GAAG,OAAO,GAAG,MAAO;MAC/EG,MAAM,EAAC,MAAM;MACbC,WAAW,EAAC,GAAG;MACf0B,SAAS,EAAE,cAAcxC,CAAC,IAAIC,CAAC;IAAI;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,EAGDnB,IAAI,CAACW,QAAQ,KAAK,OAAO,iBACxBb,OAAA;MACEgC,EAAE,EAAExB,KAAM;MACVyB,EAAE,EAAExB,MAAO;MACXyB,EAAE,EAAE1B,KAAM;MACV2B,EAAE,EAAEzB,MAAO;MACXM,MAAM,EAAC,MAAM;MACbC,WAAW,EAAC;IAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACF,EAGAnB,IAAI,CAACW,QAAQ,KAAK,QAAQ,iBACzBb,OAAA;MACEc,CAAC,EAAEP,aAAa,KAAK,IAAI,GACvB,KAAKC,KAAK,IAAIE,MAAM,MAAMF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,CAAC,IAAIF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,EAAE,EAAE,GAC/E,KAAKF,KAAK,IAAIE,MAAM,MAAMF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,CAAC,IAAIF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,EAAE,EAC9E;MACDK,IAAI,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACF,EAGAnB,IAAI,CAACW,QAAQ,KAAK,WAAW,iBAC5Bb,OAAA;MAAAY,QAAA,gBACEZ,OAAA;QACEc,CAAC,EAAEP,aAAa,KAAK,IAAI,GACvB,KAAKC,KAAK,IAAIE,MAAM,MAAMF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,CAAC,IAAIF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,EAAE,EAAE,GAC/E,KAAKF,KAAK,IAAIE,MAAM,MAAMF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,CAAC,IAAIF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,EAAE,EAC9E;QACDK,IAAI,EAAC;MAAM;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACFrB,OAAA;QACEc,CAAC,EAAEP,aAAa,KAAK,IAAI,GACvB,KAAKC,KAAK,IAAIE,MAAM,GAAG,CAAC,MAAMF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,EAAE,IAAIF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,EAAE,EAAE,GACpF,KAAKF,KAAK,IAAIE,MAAM,GAAG,CAAC,MAAMF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,EAAE,IAAIF,KAAK,GAAG,CAAC,IAAIE,MAAM,GAAG,EAAE,EACnF;QACDK,IAAI,EAAC;MAAM;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACJ,eAMDrB,OAAA;MACEG,CAAC,EAAEA,CAAE;MACLC,CAAC,EAAEA,CAAC,GAAG,EAAG;MACViC,QAAQ,EAAC,GAAG;MACZtB,IAAI,EAAC,MAAM;MACXuB,UAAU,EAAC,QAAQ;MACnBX,KAAK,EAAE;QAAEY,aAAa,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAA5B,QAAA,GAEpDV,IAAI,CAAC0C,IAAI,EAAE1C,IAAI,CAAC2C,MAAM;IAAA;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAGPrB,OAAA;MACEG,CAAC,EAAEA,CAAC,GAAG,EAAG;MACVC,CAAC,EAAEA,CAAC,GAAG,CAAE;MACTiC,QAAQ,EAAC,GAAG;MACZtB,IAAI,EAAC,MAAM;MACXuB,UAAU,EAAC,OAAO;MAClBX,KAAK,EAAE;QAAEY,aAAa,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAA5B,QAAA,EAEpDV,IAAI,CAACW,QAAQ,KAAK,OAAO,GAAG,IAAI,GAChCX,IAAI,CAACW,QAAQ,KAAK,MAAM,GAAG,MAAM,GACjCX,IAAI,CAACW,QAAQ,KAAK,SAAS,GAAG,GAAG,GACjCX,IAAI,CAACW,QAAQ,KAAK,QAAQ,GAAG,GAAG,GAAG;IAAG;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAER,CAAC;AAACyB,EAAA,GA5LW7C,WAAuC;AAAA,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}