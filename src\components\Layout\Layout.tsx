import React from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { Icons, IconComponent } from '../../utils/icons';

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`;

const Sidebar = styled.nav`
  width: 280px;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 1.5rem;
  box-shadow: 4px 0 15px rgba(0,0,0,0.2);
  backdrop-filter: blur(10px);
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.6rem;
  font-weight: bold;
  margin-bottom: 2.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid rgba(255,255,255,0.2);
  background: linear-gradient(45deg, #fff, #e8f4fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const NavItem = styled.button`
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 1rem 1.25rem;
  background: none;
  border: none;
  color: rgba(255,255,255,0.9);
  text-align: left;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  font-weight: 500;

  &:hover {
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    transform: translateX(5px);
    color: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  }

  &.active {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    transform: translateX(8px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
  }
`;

const MainContent = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const Header = styled.header`
  background: rgba(255,255,255,0.95);
  backdrop-filter: blur(10px);
  padding: 1.5rem 2.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255,255,255,0.2);

  h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
  }
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const UserName = styled.span`
  font-weight: 500;
`;

const LogoutButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #c0392b;
  }
`;

const ContentArea = styled.div`
  flex: 1;
  padding: 2.5rem;
  overflow-y: auto;
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(5px);
  margin: 1rem;
  border-radius: 20px;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.2);
`;

interface LayoutProps {
  children: React.ReactNode;
  currentPage?: string;
  onNavigate?: (page: string) => void;
  showAuthInSidebar?: boolean;
}

export const Layout: React.FC<LayoutProps> = ({
  children,
  currentPage = 'home',
  onNavigate = () => {},
  showAuthInSidebar = false
}) => {
  const { currentUser, logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  const navigationItems = [
    { id: 'home', label: 'Início', icon: Icons.Home },
    { id: 'new-score', label: 'Criar Partitura', icon: Icons.Plus },
    ...(currentUser ? [{ id: 'scores', label: 'Minhas Partituras', icon: Icons.Music }] : []),
    ...(showAuthInSidebar ? [
      { id: 'login', label: 'Entrar', icon: Icons.User },
      { id: 'register', label: 'Cadastrar', icon: Icons.Plus }
    ] : [])
  ];

  return (
    <LayoutContainer>
      <Sidebar>
        <Logo>
          <Icons.Music size={24} />
          Partitura Digital
        </Logo>

        {navigationItems.map(item => {
          const IconComponent = item.icon;
          return (
            <NavItem
              key={item.id}
              className={currentPage === item.id ? 'active' : ''}
              onClick={() => onNavigate(item.id)}
            >
              <IconComponent size={18} />
              {item.label}
            </NavItem>
          );
        })}
      </Sidebar>

      <MainContent>
        <Header>
          <h1>🎵 Partitura Digital</h1>
          {currentUser ? (
            <UserInfo>
              <Icons.User size={20} />
              <UserName>{currentUser?.displayName || currentUser?.email}</UserName>
              <LogoutButton onClick={handleLogout}>
                <Icons.LogOut size={16} />
                Sair
              </LogoutButton>
            </UserInfo>
          ) : (
            <UserInfo>
              <span style={{ color: '#666' }}>Modo Visitante</span>
            </UserInfo>
          )}
        </Header>

        <ContentArea>
          {children}
        </ContentArea>
      </MainContent>
    </LayoutContainer>
  );
};
