import React from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { Icons, IconComponent } from '../../utils/icons';

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
`;

const Sidebar = styled.nav`
  width: 250px;
  background-color: #2c3e50;
  color: white;
  padding: 1rem;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #34495e;
`;

const NavItem = styled.button`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem;
  background: none;
  border: none;
  color: white;
  text-align: left;
  cursor: pointer;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
  margin-bottom: 0.5rem;

  &:hover {
    background-color: #34495e;
  }

  &.active {
    background-color: #3498db;
  }
`;

const MainContent = styled.main`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const Header = styled.header`
  background-color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const UserName = styled.span`
  font-weight: 500;
`;

const LogoutButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #c0392b;
  }
`;

const ContentArea = styled.div`
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
`;

interface LayoutProps {
  children: React.ReactNode;
  currentPage?: string;
  onNavigate?: (page: string) => void;
}

export const Layout: React.FC<LayoutProps> = ({ 
  children, 
  currentPage = 'home',
  onNavigate = () => {}
}) => {
  const { currentUser, logout } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  const navigationItems = [
    { id: 'home', label: 'Início', icon: Icons.Home },
    { id: 'scores', label: 'Minhas Partituras', icon: Icons.Music },
    { id: 'new-score', label: 'Nova Partitura', icon: Icons.Plus },
  ];

  return (
    <LayoutContainer>
      <Sidebar>
        <Logo>
          <Icons.Music size={24} />
          Partitura Digital
        </Logo>

        {navigationItems.map(item => {
          const IconComponent = item.icon;
          return (
            <NavItem
              key={item.id}
              className={currentPage === item.id ? 'active' : ''}
              onClick={() => onNavigate(item.id)}
            >
              <IconComponent size={18} />
              {item.label}
            </NavItem>
          );
        })}
      </Sidebar>

      <MainContent>
        <Header>
          <h1>Partitura Digital</h1>
          <UserInfo>
            <Icons.User size={20} />
            <UserName>{currentUser?.displayName || currentUser?.email}</UserName>
            <LogoutButton onClick={handleLogout}>
              <Icons.LogOut size={16} />
              Sair
            </LogoutButton>
          </UserInfo>
        </Header>

        <ContentArea>
          {children}
        </ContentArea>
      </MainContent>
    </LayoutContainer>
  );
};
