{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ChordView\\\\ChordView.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChordContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n_c = ChordContainer;\nconst ChordTitle = styled.h3`\n  margin: 0 0 1.5rem 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n  text-align: center;\n`;\n_c2 = ChordTitle;\nconst ChordGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n`;\n_c3 = ChordGrid;\nconst MeasureCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n`;\n_c4 = MeasureCard;\nconst MeasureNumber = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;\n_c5 = MeasureNumber;\nconst ChordSymbol = styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n  text-align: center;\n`;\n_c6 = ChordSymbol;\nconst ChordNotes = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  text-align: center;\n`;\n_c7 = ChordNotes;\nconst EmptyState = styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;\n_c8 = EmptyState;\n// Função para determinar o acorde baseado nas notas\nconst analyzeChord = notes => {\n  if (notes.length === 0) return '';\n\n  // Agrupar notas por nome (ignorando oitava)\n  const noteNames = notes.filter(note => !note.isRest).map(note => note.name).filter((name, index, arr) => arr.indexOf(name) === index); // Remover duplicatas\n\n  if (noteNames.length === 0) return '';\n  if (noteNames.length === 1) return noteNames[0];\n\n  // Lógica básica para identificar acordes comuns\n  const sortedNotes = noteNames.sort();\n  const noteString = sortedNotes.join('');\n\n  // Acordes maiores\n  const majorChords = {\n    'CEG': 'C',\n    'DFA': 'D',\n    'EGB': 'E',\n    'FAC': 'F',\n    'GBD': 'G',\n    'ACE': 'A',\n    'BDF': 'B'\n  };\n\n  // Acordes menores\n  const minorChords = {\n    'CEG': 'Cm',\n    // C-Eb-G (aproximação)\n    'DFA': 'Dm',\n    'EGB': 'Em',\n    'FAC': 'Fm',\n    'GBD': 'Gm',\n    'ACE': 'Am',\n    'BDF': 'Bm'\n  };\n\n  // Verificar acordes maiores primeiro\n  if (majorChords[noteString]) {\n    return majorChords[noteString];\n  }\n\n  // Se não encontrou, retornar as notas individuais\n  return noteNames.join(' - ');\n};\nexport const ChordView = ({\n  notes,\n  title\n}) => {\n  // Agrupar notas por compasso\n  const measureMap = new Map();\n  notes.forEach(note => {\n    const measure = note.position.measure;\n    if (!measureMap.has(measure)) {\n      measureMap.set(measure, []);\n    }\n    measureMap.get(measure).push(note);\n  });\n\n  // Converter para array ordenado\n  const measures = Array.from(measureMap.entries()).sort(([a], [b]) => a - b).map(([measureNumber, measureNotes]) => ({\n    number: measureNumber,\n    notes: measureNotes.sort((a, b) => a.position.beat - b.position.beat),\n    chord: analyzeChord(measureNotes)\n  }));\n  return /*#__PURE__*/_jsxDEV(ChordContainer, {\n    children: [/*#__PURE__*/_jsxDEV(ChordTitle, {\n      children: [\"\\uD83C\\uDFB8 Cifras - \", title]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), measures.length === 0 ? /*#__PURE__*/_jsxDEV(EmptyState, {\n      children: \"Adicione algumas notas na partitura para ver as cifras aqui\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(ChordGrid, {\n      children: measures.map(measure => /*#__PURE__*/_jsxDEV(MeasureCard, {\n        children: [/*#__PURE__*/_jsxDEV(MeasureNumber, {\n          children: [\"Compasso \", measure.number]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ChordSymbol, {\n          children: measure.chord || '—'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ChordNotes, {\n          children: measure.notes.filter(note => !note.isRest).map(note => `${note.name}${note.octave}`).join(', ') || 'Pausas'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 15\n        }, this)]\n      }, measure.number, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_c9 = ChordView;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"ChordContainer\");\n$RefreshReg$(_c2, \"ChordTitle\");\n$RefreshReg$(_c3, \"ChordGrid\");\n$RefreshReg$(_c4, \"MeasureCard\");\n$RefreshReg$(_c5, \"MeasureNumber\");\n$RefreshReg$(_c6, \"ChordSymbol\");\n$RefreshReg$(_c7, \"ChordNotes\");\n$RefreshReg$(_c8, \"EmptyState\");\n$RefreshReg$(_c9, \"ChordView\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "ChordTitle", "h3", "_c2", "ChordGrid", "_c3", "MeasureCard", "_c4", "MeasureNumber", "_c5", "ChordSymbol", "_c6", "ChordNotes", "_c7", "EmptyState", "_c8", "analyzeChord", "notes", "length", "noteNames", "filter", "note", "isRest", "map", "name", "index", "arr", "indexOf", "sortedNotes", "sort", "noteString", "join", "majorChords", "minorChords", "ChordView", "title", "measureMap", "Map", "for<PERSON>ach", "measure", "position", "has", "set", "get", "push", "measures", "Array", "from", "entries", "a", "b", "measureNumber", "measureNotes", "number", "beat", "chord", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "octave", "_c9", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ChordView/ChordView.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { MusicalNote, NoteName } from '../../types/music';\n\nconst ChordContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst ChordTitle = styled.h3`\n  margin: 0 0 1.5rem 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n  text-align: center;\n`;\n\nconst ChordGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n`;\n\nconst MeasureCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n`;\n\nconst MeasureNumber = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;\n\nconst ChordSymbol = styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n  text-align: center;\n`;\n\nconst ChordNotes = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  text-align: center;\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;\n\ninterface ChordViewProps {\n  notes: MusicalNote[];\n  title: string;\n}\n\n// Função para determinar o acorde baseado nas notas\nconst analyzeChord = (notes: MusicalNote[]): string => {\n  if (notes.length === 0) return '';\n  \n  // Agrupar notas por nome (ignorando oitava)\n  const noteNames = notes\n    .filter(note => !note.isRest)\n    .map(note => note.name)\n    .filter((name, index, arr) => arr.indexOf(name) === index); // Remover duplicatas\n  \n  if (noteNames.length === 0) return '';\n  if (noteNames.length === 1) return noteNames[0];\n  \n  // Lógica básica para identificar acordes comuns\n  const sortedNotes = noteNames.sort();\n  const noteString = sortedNotes.join('');\n  \n  // Acordes maiores\n  const majorChords: { [key: string]: string } = {\n    'CEG': 'C',\n    'DFA': 'D', \n    'EGB': 'E',\n    'FAC': 'F',\n    'GBD': 'G',\n    'ACE': 'A',\n    'BDF': 'B'\n  };\n  \n  // Acordes menores\n  const minorChords: { [key: string]: string } = {\n    'CEG': 'Cm', // C-Eb-G (aproximação)\n    'DFA': 'Dm',\n    'EGB': 'Em',\n    'FAC': 'Fm',\n    'GBD': 'Gm',\n    'ACE': 'Am',\n    'BDF': 'Bm'\n  };\n  \n  // Verificar acordes maiores primeiro\n  if (majorChords[noteString]) {\n    return majorChords[noteString];\n  }\n  \n  // Se não encontrou, retornar as notas individuais\n  return noteNames.join(' - ');\n};\n\nexport const ChordView: React.FC<ChordViewProps> = ({ notes, title }) => {\n  // Agrupar notas por compasso\n  const measureMap = new Map<number, MusicalNote[]>();\n  \n  notes.forEach(note => {\n    const measure = note.position.measure;\n    if (!measureMap.has(measure)) {\n      measureMap.set(measure, []);\n    }\n    measureMap.get(measure)!.push(note);\n  });\n  \n  // Converter para array ordenado\n  const measures = Array.from(measureMap.entries())\n    .sort(([a], [b]) => a - b)\n    .map(([measureNumber, measureNotes]) => ({\n      number: measureNumber,\n      notes: measureNotes.sort((a, b) => a.position.beat - b.position.beat),\n      chord: analyzeChord(measureNotes)\n    }));\n\n  return (\n    <ChordContainer>\n      <ChordTitle>🎸 Cifras - {title}</ChordTitle>\n      \n      {measures.length === 0 ? (\n        <EmptyState>\n          Adicione algumas notas na partitura para ver as cifras aqui\n        </EmptyState>\n      ) : (\n        <ChordGrid>\n          {measures.map(measure => (\n            <MeasureCard key={measure.number}>\n              <MeasureNumber>Compasso {measure.number}</MeasureNumber>\n              <ChordSymbol>\n                {measure.chord || '—'}\n              </ChordSymbol>\n              <ChordNotes>\n                {measure.notes\n                  .filter(note => !note.isRest)\n                  .map(note => `${note.name}${note.octave}`)\n                  .join(', ') || 'Pausas'}\n              </ChordNotes>\n            </MeasureCard>\n          ))}\n        </ChordGrid>\n      )}\n    </ChordContainer>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGvC,MAAMC,cAAc,GAAGH,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,cAAc;AAQpB,MAAMG,UAAU,GAAGN,MAAM,CAACO,EAAE;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,UAAU;AAOhB,MAAMG,SAAS,GAAGT,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAJID,SAAS;AAMf,MAAME,WAAW,GAAGX,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GALID,WAAW;AAOjB,MAAME,aAAa,GAAGb,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GALID,aAAa;AAOnB,MAAME,WAAW,GAAGf,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GANID,WAAW;AAQjB,MAAME,UAAU,GAAGjB,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,UAAU;AAMhB,MAAME,UAAU,GAAGnB,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GALID,UAAU;AAYhB;AACA,MAAME,YAAY,GAAIC,KAAoB,IAAa;EACrD,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;;EAEjC;EACA,MAAMC,SAAS,GAAGF,KAAK,CACpBG,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,CAAC,CAC5BC,GAAG,CAACF,IAAI,IAAIA,IAAI,CAACG,IAAI,CAAC,CACtBJ,MAAM,CAAC,CAACI,IAAI,EAAEC,KAAK,EAAEC,GAAG,KAAKA,GAAG,CAACC,OAAO,CAACH,IAAI,CAAC,KAAKC,KAAK,CAAC,CAAC,CAAC;;EAE9D,IAAIN,SAAS,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;EACrC,IAAIC,SAAS,CAACD,MAAM,KAAK,CAAC,EAAE,OAAOC,SAAS,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAMS,WAAW,GAAGT,SAAS,CAACU,IAAI,CAAC,CAAC;EACpC,MAAMC,UAAU,GAAGF,WAAW,CAACG,IAAI,CAAC,EAAE,CAAC;;EAEvC;EACA,MAAMC,WAAsC,GAAG;IAC7C,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE,GAAG;IACV,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAMC,WAAsC,GAAG;IAC7C,KAAK,EAAE,IAAI;IAAE;IACb,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE,IAAI;IACX,KAAK,EAAE;EACT,CAAC;;EAED;EACA,IAAID,WAAW,CAACF,UAAU,CAAC,EAAE;IAC3B,OAAOE,WAAW,CAACF,UAAU,CAAC;EAChC;;EAEA;EACA,OAAOX,SAAS,CAACY,IAAI,CAAC,KAAK,CAAC;AAC9B,CAAC;AAED,OAAO,MAAMG,SAAmC,GAAGA,CAAC;EAAEjB,KAAK;EAAEkB;AAAM,CAAC,KAAK;EACvE;EACA,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAwB,CAAC;EAEnDpB,KAAK,CAACqB,OAAO,CAACjB,IAAI,IAAI;IACpB,MAAMkB,OAAO,GAAGlB,IAAI,CAACmB,QAAQ,CAACD,OAAO;IACrC,IAAI,CAACH,UAAU,CAACK,GAAG,CAACF,OAAO,CAAC,EAAE;MAC5BH,UAAU,CAACM,GAAG,CAACH,OAAO,EAAE,EAAE,CAAC;IAC7B;IACAH,UAAU,CAACO,GAAG,CAACJ,OAAO,CAAC,CAAEK,IAAI,CAACvB,IAAI,CAAC;EACrC,CAAC,CAAC;;EAEF;EACA,MAAMwB,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACX,UAAU,CAACY,OAAO,CAAC,CAAC,CAAC,CAC9CnB,IAAI,CAAC,CAAC,CAACoB,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CACzB3B,GAAG,CAAC,CAAC,CAAC4B,aAAa,EAAEC,YAAY,CAAC,MAAM;IACvCC,MAAM,EAAEF,aAAa;IACrBlC,KAAK,EAAEmC,YAAY,CAACvB,IAAI,CAAC,CAACoB,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACT,QAAQ,CAACc,IAAI,GAAGJ,CAAC,CAACV,QAAQ,CAACc,IAAI,CAAC;IACrEC,KAAK,EAAEvC,YAAY,CAACoC,YAAY;EAClC,CAAC,CAAC,CAAC;EAEL,oBACEvD,OAAA,CAACC,cAAc;IAAA0D,QAAA,gBACb3D,OAAA,CAACI,UAAU;MAAAuD,QAAA,GAAC,wBAAY,EAACrB,KAAK;IAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,EAE3Cf,QAAQ,CAAC3B,MAAM,KAAK,CAAC,gBACpBrB,OAAA,CAACiB,UAAU;MAAA0C,QAAA,EAAC;IAEZ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,gBAEb/D,OAAA,CAACO,SAAS;MAAAoD,QAAA,EACPX,QAAQ,CAACtB,GAAG,CAACgB,OAAO,iBACnB1C,OAAA,CAACS,WAAW;QAAAkD,QAAA,gBACV3D,OAAA,CAACW,aAAa;UAAAgD,QAAA,GAAC,WAAS,EAACjB,OAAO,CAACc,MAAM;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eACxD/D,OAAA,CAACa,WAAW;UAAA8C,QAAA,EACTjB,OAAO,CAACgB,KAAK,IAAI;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACd/D,OAAA,CAACe,UAAU;UAAA4C,QAAA,EACRjB,OAAO,CAACtB,KAAK,CACXG,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,CAAC,CAC5BC,GAAG,CAACF,IAAI,IAAI,GAAGA,IAAI,CAACG,IAAI,GAAGH,IAAI,CAACwC,MAAM,EAAE,CAAC,CACzC9B,IAAI,CAAC,IAAI,CAAC,IAAI;QAAQ;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA,GAVGrB,OAAO,CAACc,MAAM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWnB,CACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CACZ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAErB,CAAC;AAACE,GAAA,GAjDW5B,SAAmC;AAAA,IAAAlC,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAA+C,GAAA;AAAAC,YAAA,CAAA/D,EAAA;AAAA+D,YAAA,CAAA5D,GAAA;AAAA4D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAApD,GAAA;AAAAoD,YAAA,CAAAlD,GAAA;AAAAkD,YAAA,CAAAhD,GAAA;AAAAgD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}