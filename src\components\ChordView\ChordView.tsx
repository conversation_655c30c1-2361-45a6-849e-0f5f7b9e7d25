import React from 'react';
import styled from 'styled-components';
import { MusicalNote, NoteName } from '../../types/music';

const ChordContainer = styled.div`
  background: rgba(255,255,255,0.95);
  border-radius: 16px;
  padding: 2rem;
  margin: 1rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
`;

const ChordHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const ChordTitle = styled.h3`
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
`;

const ExportButton = styled.button`
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
`;

const ChordGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
`;

const MeasureCard = styled.div`
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1rem;
  border: 2px solid #e9ecef;
`;

const MeasureNumber = styled.div`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  font-weight: 600;
`;

const ChordSymbol = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  text-align: center;
`;

const ChordNotes = styled.div`
  font-size: 0.9rem;
  color: #666;
  text-align: center;
`;

const EmptyState = styled.div`
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
`;

const ProgressionView = styled.div`
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 2px solid #e9ecef;
`;

const ProgressionTitle = styled.h4`
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
`;

const ProgressionChords = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
`;

const ProgressionChord = styled.span`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
`;

const ProgressionArrow = styled.span`
  color: #666;
  font-size: 1.2rem;
`;

interface ChordViewProps {
  notes: MusicalNote[];
  title: string;
}

// Função para determinar o acorde baseado nas notas
const analyzeChord = (notes: MusicalNote[]): string => {
  if (notes.length === 0) return '';

  // Filtrar apenas notas (não pausas) e aplicar acidentes
  const processedNotes = notes
    .filter(note => !note.isRest)
    .map(note => {
      let noteName = note.name;

      // Aplicar acidentes
      if (note.accidental === 'sharp') {
        const sharpMap: { [key: string]: string } = {
          'C': 'C#', 'D': 'D#', 'F': 'F#', 'G': 'G#', 'A': 'A#'
        };
        noteName = sharpMap[noteName] || noteName;
      } else if (note.accidental === 'flat') {
        const flatMap: { [key: string]: string } = {
          'D': 'Db', 'E': 'Eb', 'G': 'Gb', 'A': 'Ab', 'B': 'Bb'
        };
        noteName = flatMap[noteName] || noteName;
      }

      return noteName;
    })
    .filter((name, index, arr) => arr.indexOf(name) === index); // Remover duplicatas

  if (processedNotes.length === 0) return '';
  if (processedNotes.length === 1) return processedNotes[0];

  // Normalizar notas para análise (converter tudo para sustenidos)
  const normalizeNote = (note: string): string => {
    const enharmonics: { [key: string]: string } = {
      'Db': 'C#', 'Eb': 'D#', 'Gb': 'F#', 'Ab': 'G#', 'Bb': 'A#'
    };
    return enharmonics[note] || note;
  };

  const normalizedNotes = processedNotes.map(normalizeNote).sort();

  // Definir acordes conhecidos
  const chordPatterns: { [key: string]: string } = {
    // Acordes maiores
    'C,E,G': 'C',
    'C#,F,G#': 'C#',
    'D,F#,A': 'D',
    'D#,G,A#': 'D#',
    'E,G#,B': 'E',
    'F,A,C': 'F',
    'F#,A#,C#': 'F#',
    'G,B,D': 'G',
    'G#,C,D#': 'G#',
    'A,C#,E': 'A',
    'A#,D,F': 'A#',
    'B,D#,F#': 'B',

    // Acordes menores
    'C,D#,G': 'Cm',
    'C#,E,G#': 'C#m',
    'D,F,A': 'Dm',
    'D#,F#,A#': 'D#m',
    'E,G,B': 'Em',
    'F,G#,C': 'Fm',
    'F#,A,C#': 'F#m',
    'G,A#,D': 'Gm',
    'G#,B,D#': 'G#m',
    'A,C,E': 'Am',
    'A#,C#,F': 'A#m',
    'B,D,F#': 'Bm',

    // Acordes de sétima
    'C,E,G,A#': 'C7',
    'D,F#,A,C': 'D7',
    'E,G#,B,D': 'E7',
    'F,A,C,D#': 'F7',
    'G,B,D,F': 'G7',
    'A,C#,E,G': 'A7',
    'B,D#,F#,A': 'B7'
  };

  const noteString = normalizedNotes.join(',');

  // Verificar padrões conhecidos
  if (chordPatterns[noteString]) {
    return chordPatterns[noteString];
  }

  // Se não encontrou um padrão, tentar identificar pela fundamental
  const root = normalizedNotes[0];
  const intervals = normalizedNotes.slice(1);

  if (intervals.length >= 2) {
    // Tentar identificar como acorde básico
    return `${root}?`; // Indica acorde não identificado com fundamental
  }

  // Retornar notas individuais
  return processedNotes.join(' - ');
};

export const ChordView: React.FC<ChordViewProps> = ({ notes, title }) => {
  // Agrupar notas por compasso
  const measureMap = new Map<number, MusicalNote[]>();

  notes.forEach(note => {
    const measure = note.position.measure;
    if (!measureMap.has(measure)) {
      measureMap.set(measure, []);
    }
    measureMap.get(measure)!.push(note);
  });

  // Converter para array ordenado
  const measures = Array.from(measureMap.entries())
    .sort(([a], [b]) => a - b)
    .map(([measureNumber, measureNotes]) => ({
      number: measureNumber,
      notes: measureNotes.sort((a, b) => a.position.beat - b.position.beat),
      chord: analyzeChord(measureNotes)
    }));

  // Função para exportar cifras como texto
  const exportChords = () => {
    const progression = measures
      .filter(m => m.chord && m.chord !== '—')
      .map(m => m.chord);

    const exportText = `
Partitura: ${title}
Progressão Harmônica: ${progression.join(' - ')}

Detalhes por Compasso:
${measures.map(m => `Compasso ${m.number}: ${m.chord || '—'}`).join('\n')}
    `.trim();

    // Copiar para clipboard
    navigator.clipboard.writeText(exportText).then(() => {
      alert('Cifras copiadas para a área de transferência!');
    }).catch(() => {
      // Fallback: mostrar em alert
      alert(exportText);
    });
  };

  // Criar progressão harmônica
  const progression = measures
    .filter(m => m.chord && m.chord !== '—')
    .map(m => m.chord);

  return (
    <ChordContainer>
      <ChordHeader>
        <ChordTitle>🎸 Cifras - {title}</ChordTitle>
        {measures.length > 0 && (
          <ExportButton onClick={exportChords}>
            📋 Exportar Cifras
          </ExportButton>
        )}
      </ChordHeader>

      {measures.length === 0 ? (
        <EmptyState>
          Adicione algumas notas na partitura para ver as cifras aqui
        </EmptyState>
      ) : (
        <>
          {/* Progressão Harmônica */}
          {progression.length > 0 && (
            <ProgressionView>
              <ProgressionTitle>🎼 Progressão Harmônica</ProgressionTitle>
              <ProgressionChords>
                {progression.map((chord, index) => (
                  <React.Fragment key={index}>
                    <ProgressionChord>{chord}</ProgressionChord>
                    {index < progression.length - 1 && (
                      <ProgressionArrow>→</ProgressionArrow>
                    )}
                  </React.Fragment>
                ))}
              </ProgressionChords>
            </ProgressionView>
          )}

          {/* Detalhes por Compasso */}
          <ChordGrid>
            {measures.map(measure => (
              <MeasureCard key={measure.number}>
                <MeasureNumber>Compasso {measure.number}</MeasureNumber>
                <ChordSymbol>
                  {measure.chord || '—'}
                </ChordSymbol>
                <ChordNotes>
                  {measure.notes
                    .filter(note => !note.isRest)
                    .map(note => {
                      let noteName = note.name;
                      if (note.accidental === 'sharp') noteName += '♯';
                      if (note.accidental === 'flat') noteName += '♭';
                      if (note.accidental === 'natural') noteName += '♮';
                      return `${noteName}${note.octave}`;
                    })
                    .join(', ') || 'Pausas'}
                </ChordNotes>
              </MeasureCard>
            ))}
          </ChordGrid>
        </>
      )}
    </ChordContainer>
  );
};
