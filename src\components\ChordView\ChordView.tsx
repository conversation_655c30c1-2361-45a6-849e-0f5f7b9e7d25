import React from 'react';
import styled from 'styled-components';
import { MusicalNote, NoteName } from '../../types/music';

const ChordContainer = styled.div`
  background: rgba(255,255,255,0.95);
  border-radius: 16px;
  padding: 2rem;
  margin: 1rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
`;

const ChordTitle = styled.h3`
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  text-align: center;
`;

const ChordGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
`;

const MeasureCard = styled.div`
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1rem;
  border: 2px solid #e9ecef;
`;

const MeasureNumber = styled.div`
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  font-weight: 600;
`;

const ChordSymbol = styled.div`
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  text-align: center;
`;

const ChordNotes = styled.div`
  font-size: 0.9rem;
  color: #666;
  text-align: center;
`;

const EmptyState = styled.div`
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
`;

interface ChordViewProps {
  notes: MusicalNote[];
  title: string;
}

// Função para determinar o acorde baseado nas notas
const analyzeChord = (notes: MusicalNote[]): string => {
  if (notes.length === 0) return '';
  
  // Agrupar notas por nome (ignorando oitava)
  const noteNames = notes
    .filter(note => !note.isRest)
    .map(note => note.name)
    .filter((name, index, arr) => arr.indexOf(name) === index); // Remover duplicatas
  
  if (noteNames.length === 0) return '';
  if (noteNames.length === 1) return noteNames[0];
  
  // Lógica básica para identificar acordes comuns
  const sortedNotes = noteNames.sort();
  const noteString = sortedNotes.join('');
  
  // Acordes maiores
  const majorChords: { [key: string]: string } = {
    'CEG': 'C',
    'DFA': 'D', 
    'EGB': 'E',
    'FAC': 'F',
    'GBD': 'G',
    'ACE': 'A',
    'BDF': 'B'
  };
  
  // Acordes menores
  const minorChords: { [key: string]: string } = {
    'CEG': 'Cm', // C-Eb-G (aproximação)
    'DFA': 'Dm',
    'EGB': 'Em',
    'FAC': 'Fm',
    'GBD': 'Gm',
    'ACE': 'Am',
    'BDF': 'Bm'
  };
  
  // Verificar acordes maiores primeiro
  if (majorChords[noteString]) {
    return majorChords[noteString];
  }
  
  // Se não encontrou, retornar as notas individuais
  return noteNames.join(' - ');
};

export const ChordView: React.FC<ChordViewProps> = ({ notes, title }) => {
  // Agrupar notas por compasso
  const measureMap = new Map<number, MusicalNote[]>();
  
  notes.forEach(note => {
    const measure = note.position.measure;
    if (!measureMap.has(measure)) {
      measureMap.set(measure, []);
    }
    measureMap.get(measure)!.push(note);
  });
  
  // Converter para array ordenado
  const measures = Array.from(measureMap.entries())
    .sort(([a], [b]) => a - b)
    .map(([measureNumber, measureNotes]) => ({
      number: measureNumber,
      notes: measureNotes.sort((a, b) => a.position.beat - b.position.beat),
      chord: analyzeChord(measureNotes)
    }));

  return (
    <ChordContainer>
      <ChordTitle>🎸 Cifras - {title}</ChordTitle>
      
      {measures.length === 0 ? (
        <EmptyState>
          Adicione algumas notas na partitura para ver as cifras aqui
        </EmptyState>
      ) : (
        <ChordGrid>
          {measures.map(measure => (
            <MeasureCard key={measure.number}>
              <MeasureNumber>Compasso {measure.number}</MeasureNumber>
              <ChordSymbol>
                {measure.chord || '—'}
              </ChordSymbol>
              <ChordNotes>
                {measure.notes
                  .filter(note => !note.isRest)
                  .map(note => `${note.name}${note.octave}`)
                  .join(', ') || 'Pausas'}
              </ChordNotes>
            </MeasureCard>
          ))}
        </ChordGrid>
      )}
    </ChordContainer>
  );
};
