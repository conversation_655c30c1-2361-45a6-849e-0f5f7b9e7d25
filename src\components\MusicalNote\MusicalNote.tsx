import React from 'react';
import { MusicalNote as NoteType, NoteDuration } from '../../types/music';

interface MusicalNoteProps {
  note: NoteType;
  x: number;
  y: number;
  onRemove: () => void;
}

// Componente para renderizar uma nota musical com símbolos precisos
export const MusicalNote: React.FC<MusicalNoteProps> = ({ note, x, y, onRemove }) => {
  const isAboveMiddle = y < 90; // Determina direção da haste
  const stemDirection = isAboveMiddle ? 'down' : 'up';
  const stemX = stemDirection === 'up' ? x + 7 : x - 7;
  const stemY1 = y;
  const stemY2 = stemDirection === 'up' ? y - 25 : y + 25;

  // Renderizar pausa
  if (note.isRest) {
    return (
      <g>
        {/* Pausa de semínima */}
        {note.duration === 'quarter' && (
          <g>
            <path
              d={`M ${x-6} ${y-8} L ${x+2} ${y-8} L ${x-2} ${y} L ${x+6} ${y} L ${x+2} ${y+8} L ${x-6} ${y+8} Z`}
              fill="#333"
              stroke="#333"
              strokeWidth="1"
            />
          </g>
        )}
        
        {/* Pausa de mínima */}
        {note.duration === 'half' && (
          <rect
            x={x - 4}
            y={y - 2}
            width="8"
            height="4"
            fill="#333"
          />
        )}
        
        {/* Pausa de semibreve */}
        {note.duration === 'whole' && (
          <rect
            x={x - 6}
            y={y - 6}
            width="12"
            height="4"
            fill="#333"
          />
        )}
        
        {/* Pausa de colcheia */}
        {note.duration === 'eighth' && (
          <g>
            <path
              d={`M ${x-4} ${y-6} Q ${x} ${y-2} ${x-2} ${y+2} Q ${x+2} ${y+6} ${x-4} ${y+4} Z`}
              fill="#333"
            />
            <circle cx={x+2} cy={y-4} r="1.5" fill="#333" />
          </g>
        )}
        
        {/* Pausa de semicolcheia */}
        {note.duration === 'sixteenth' && (
          <g>
            <path
              d={`M ${x-4} ${y-8} Q ${x} ${y-4} ${x-2} ${y} Q ${x+2} ${y+4} ${x-4} ${y+2} Z`}
              fill="#333"
            />
            <circle cx={x+2} cy={y-6} r="1.5" fill="#333" />
            <circle cx={x+3} cy={y-2} r="1.5" fill="#333" />
          </g>
        )}
      </g>
    );
  }

  // Renderizar nota musical
  return (
    <g style={{ cursor: 'pointer' }} onClick={(e) => { e.stopPropagation(); onRemove(); }}>
      {/* Linha auxiliar para notas fora da pauta */}
      {(y < 50 || y > 130) && (
        <line
          x1={x - 12}
          y1={y}
          x2={x + 12}
          y2={y}
          stroke="#666"
          strokeWidth="1"
        />
      )}
      
      {/* Acidente (sustenido, bemol, bequadro) */}
      {note.accidental && (
        <text
          x={x - 15}
          y={y + 3}
          fontSize="14"
          fill="#333"
          textAnchor="middle"
          style={{ pointerEvents: 'none', userSelect: 'none' }}
        >
          {note.accidental === 'sharp' ? '♯' :
           note.accidental === 'flat' ? '♭' :
           note.accidental === 'natural' ? '♮' : ''}
        </text>
      )}

      {/* Cabeça da nota */}
      <ellipse
        cx={x}
        cy={y}
        rx="7"
        ry="5"
        fill={note.duration === 'whole' || note.duration === 'half' ? 'white' : '#333'}
        stroke="#333"
        strokeWidth="2"
        transform={`rotate(-20 ${x} ${y})`}
      />
      
      {/* Haste da nota (exceto semibreve) */}
      {note.duration !== 'whole' && (
        <line
          x1={stemX}
          y1={stemY1}
          x2={stemX}
          y2={stemY2}
          stroke="#333"
          strokeWidth="2"
        />
      )}
      
      {/* Bandeirola para colcheia */}
      {note.duration === 'eighth' && (
        <path
          d={stemDirection === 'up' ? 
            `M ${stemX} ${stemY2} Q ${stemX + 8} ${stemY2 + 5} ${stemX + 6} ${stemY2 + 10}` :
            `M ${stemX} ${stemY2} Q ${stemX - 8} ${stemY2 - 5} ${stemX - 6} ${stemY2 - 10}`
          }
          fill="#333"
        />
      )}
      
      {/* Bandeirolas duplas para semicolcheia */}
      {note.duration === 'sixteenth' && (
        <g>
          <path
            d={stemDirection === 'up' ? 
              `M ${stemX} ${stemY2} Q ${stemX + 8} ${stemY2 + 5} ${stemX + 6} ${stemY2 + 10}` :
              `M ${stemX} ${stemY2} Q ${stemX - 8} ${stemY2 - 5} ${stemX - 6} ${stemY2 - 10}`
            }
            fill="#333"
          />
          <path
            d={stemDirection === 'up' ? 
              `M ${stemX} ${stemY2 + 6} Q ${stemX + 8} ${stemY2 + 11} ${stemX + 6} ${stemY2 + 16}` :
              `M ${stemX} ${stemY2 - 6} Q ${stemX - 8} ${stemY2 - 11} ${stemX - 6} ${stemY2 - 16}`
            }
            fill="#333"
          />
        </g>
      )}
      
      {/* Ponto de aumento (para notas pontuadas - futuro) */}
      {/* Pode ser implementado futuramente */}
      
      {/* Texto da nota (para debug/referência) */}
      <text
        x={x}
        y={y - 20}
        fontSize="9"
        fill="#666"
        textAnchor="middle"
        style={{ pointerEvents: 'none', userSelect: 'none' }}
      >
        {note.name}{note.octave}
      </text>
      
      {/* Indicador de duração */}
      <text
        x={x + 15}
        y={y + 5}
        fontSize="8"
        fill="#999"
        textAnchor="start"
        style={{ pointerEvents: 'none', userSelect: 'none' }}
      >
        {note.duration === 'whole' ? '𝅝' : 
         note.duration === 'half' ? '𝅗𝅥' :
         note.duration === 'quarter' ? '♩' :
         note.duration === 'eighth' ? '♫' : '♬'}
      </text>
    </g>
  );
};
