{"ast": null, "code": "import parse from './parse.js';\nimport { unsafeStringify } from './stringify.js';\nexport function stringToBytes(str) {\n  str = unescape(encodeURIComponent(str));\n  const bytes = new Uint8Array(str.length);\n  for (let i = 0; i < str.length; ++i) {\n    bytes[i] = str.charCodeAt(i);\n  }\n  return bytes;\n}\nexport const DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport const URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function v35(version, hash, value, namespace, buf, offset) {\n  const valueBytes = typeof value === 'string' ? stringToBytes(value) : value;\n  const namespaceBytes = typeof namespace === 'string' ? parse(namespace) : namespace;\n  if (typeof namespace === 'string') {\n    namespace = parse(namespace);\n  }\n  if (namespace?.length !== 16) {\n    throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n  }\n  let bytes = new Uint8Array(16 + valueBytes.length);\n  bytes.set(namespaceBytes);\n  bytes.set(valueBytes, namespaceBytes.length);\n  bytes = hash(bytes);\n  bytes[6] = bytes[6] & 0x0f | version;\n  bytes[8] = bytes[8] & 0x3f | 0x80;\n  if (buf) {\n    offset = offset || 0;\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = bytes[i];\n    }\n    return buf;\n  }\n  return unsafeStringify(bytes);\n}", "map": {"version": 3, "names": ["parse", "unsafeStringify", "stringToBytes", "str", "unescape", "encodeURIComponent", "bytes", "Uint8Array", "length", "i", "charCodeAt", "DNS", "URL", "v35", "version", "hash", "value", "namespace", "buf", "offset", "valueBytes", "namespaceBytes", "TypeError", "set"], "sources": ["D:/Dev/partitura_digital/node_modules/uuid/dist/esm-browser/v35.js"], "sourcesContent": ["import parse from './parse.js';\nimport { unsafeStringify } from './stringify.js';\nexport function stringToBytes(str) {\n    str = unescape(encodeURIComponent(str));\n    const bytes = new Uint8Array(str.length);\n    for (let i = 0; i < str.length; ++i) {\n        bytes[i] = str.charCodeAt(i);\n    }\n    return bytes;\n}\nexport const DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport const URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function v35(version, hash, value, namespace, buf, offset) {\n    const valueBytes = typeof value === 'string' ? stringToBytes(value) : value;\n    const namespaceBytes = typeof namespace === 'string' ? parse(namespace) : namespace;\n    if (typeof namespace === 'string') {\n        namespace = parse(namespace);\n    }\n    if (namespace?.length !== 16) {\n        throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    }\n    let bytes = new Uint8Array(16 + valueBytes.length);\n    bytes.set(namespaceBytes);\n    bytes.set(valueBytes, namespaceBytes.length);\n    bytes = hash(bytes);\n    bytes[6] = (bytes[6] & 0x0f) | version;\n    bytes[8] = (bytes[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = bytes[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(bytes);\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAC9B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAO,SAASC,aAAaA,CAACC,GAAG,EAAE;EAC/BA,GAAG,GAAGC,QAAQ,CAACC,kBAAkB,CAACF,GAAG,CAAC,CAAC;EACvC,MAAMG,KAAK,GAAG,IAAIC,UAAU,CAACJ,GAAG,CAACK,MAAM,CAAC;EACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,GAAG,CAACK,MAAM,EAAE,EAAEC,CAAC,EAAE;IACjCH,KAAK,CAACG,CAAC,CAAC,GAAGN,GAAG,CAACO,UAAU,CAACD,CAAC,CAAC;EAChC;EACA,OAAOH,KAAK;AAChB;AACA,OAAO,MAAMK,GAAG,GAAG,sCAAsC;AACzD,OAAO,MAAMC,GAAG,GAAG,sCAAsC;AACzD,eAAe,SAASC,GAAGA,CAACC,OAAO,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,GAAG,EAAEC,MAAM,EAAE;EACtE,MAAMC,UAAU,GAAG,OAAOJ,KAAK,KAAK,QAAQ,GAAGd,aAAa,CAACc,KAAK,CAAC,GAAGA,KAAK;EAC3E,MAAMK,cAAc,GAAG,OAAOJ,SAAS,KAAK,QAAQ,GAAGjB,KAAK,CAACiB,SAAS,CAAC,GAAGA,SAAS;EACnF,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IAC/BA,SAAS,GAAGjB,KAAK,CAACiB,SAAS,CAAC;EAChC;EACA,IAAIA,SAAS,EAAET,MAAM,KAAK,EAAE,EAAE;IAC1B,MAAMc,SAAS,CAAC,kEAAkE,CAAC;EACvF;EACA,IAAIhB,KAAK,GAAG,IAAIC,UAAU,CAAC,EAAE,GAAGa,UAAU,CAACZ,MAAM,CAAC;EAClDF,KAAK,CAACiB,GAAG,CAACF,cAAc,CAAC;EACzBf,KAAK,CAACiB,GAAG,CAACH,UAAU,EAAEC,cAAc,CAACb,MAAM,CAAC;EAC5CF,KAAK,GAAGS,IAAI,CAACT,KAAK,CAAC;EACnBA,KAAK,CAAC,CAAC,CAAC,GAAIA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAIQ,OAAO;EACtCR,KAAK,CAAC,CAAC,CAAC,GAAIA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,IAAI;EACnC,IAAIY,GAAG,EAAE;IACLC,MAAM,GAAGA,MAAM,IAAI,CAAC;IACpB,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MACzBS,GAAG,CAACC,MAAM,GAAGV,CAAC,CAAC,GAAGH,KAAK,CAACG,CAAC,CAAC;IAC9B;IACA,OAAOS,GAAG;EACd;EACA,OAAOjB,eAAe,CAACK,KAAK,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}