import React, { useEffect, useState } from 'react';
import { auth, db } from '../services/firebase';
import { connectAuthEmulator } from 'firebase/auth';
import { connectFirestoreEmulator } from 'firebase/firestore';

export const FirebaseTest: React.FC = () => {
  const [status, setStatus] = useState<string>('Testando conexão...');

  useEffect(() => {
    const testFirebase = async () => {
      try {
        // Testar se o Firebase foi inicializado corretamente
        console.log('Auth instance:', auth);
        console.log('Firestore instance:', db);
        
        // Verificar se o auth está funcionando
        if (auth) {
          setStatus('✅ Firebase configurado corretamente!');
        } else {
          setStatus('❌ Erro na configuração do Firebase Auth');
        }
      } catch (error) {
        console.error('Erro ao testar Firebase:', error);
        setStatus(`❌ Erro: ${error}`);
      }
    };

    testFirebase();
  }, []);

  return (
    <div style={{ 
      padding: '20px', 
      margin: '20px', 
      border: '1px solid #ccc', 
      borderRadius: '8px',
      backgroundColor: '#f9f9f9'
    }}>
      <h3>🔥 Teste de Conexão Firebase</h3>
      <p><strong>Status:</strong> {status}</p>
      <p><strong>Project ID:</strong> {auth.app.options.projectId}</p>
      <p><strong>Auth Domain:</strong> {auth.app.options.authDomain}</p>
    </div>
  );
};
