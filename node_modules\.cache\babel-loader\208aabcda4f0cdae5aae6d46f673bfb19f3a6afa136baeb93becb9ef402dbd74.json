{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\LyricsEditor\\\\LyricsEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { v4 as uuidv4 } from 'uuid';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LyricsContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n_c = LyricsContainer;\nconst LyricsHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n`;\n_c2 = LyricsHeader;\nconst LyricsTitle = styled.h3`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n`;\n_c3 = LyricsTitle;\nconst AddLyricButton = styled.button`\n  padding: 0.5rem 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n  }\n`;\n_c4 = AddLyricButton;\nconst LyricsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n`;\n_c5 = LyricsGrid;\nconst LyricCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n  position: relative;\n`;\n_c6 = LyricCard;\nconst LyricPosition = styled.div`\n  font-size: 0.8rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;\n_c7 = LyricPosition;\nconst LyricText = styled.input`\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #dee2e6;\n  border-radius: 6px;\n  font-size: 1rem;\n  margin-bottom: 0.5rem;\n  box-sizing: border-box;\n  \n  &:focus {\n    outline: none;\n    border-color: #667eea;\n  }\n`;\n_c8 = LyricText;\nconst LyricActions = styled.div`\n  display: flex;\n  gap: 0.5rem;\n`;\n_c9 = LyricActions;\nconst ActionButton = styled.button`\n  padding: 0.25rem 0.5rem;\n  border: none;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'danger' ? `\n    background: #e74c3c;\n    color: white;\n    &:hover { background: #c0392b; }\n  ` : `\n    background: #6c757d;\n    color: white;\n    &:hover { background: #5a6268; }\n  `}\n`;\n_c0 = ActionButton;\nconst AddLyricForm = styled.div`\n  background: #e3f2fd;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  border: 2px solid #bbdefb;\n`;\n_c1 = AddLyricForm;\nconst FormRow = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  align-items: end;\n`;\n_c10 = FormRow;\nconst FormGroup = styled.div`\n  flex: 1;\n  \n  label {\n    display: block;\n    margin-bottom: 0.25rem;\n    font-size: 0.9rem;\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input, select {\n    width: 100%;\n    padding: 0.5rem;\n    border: 1px solid #ced4da;\n    border-radius: 6px;\n    font-size: 0.9rem;\n    box-sizing: border-box;\n    \n    &:focus {\n      outline: none;\n      border-color: #667eea;\n    }\n  }\n`;\n_c11 = FormGroup;\nconst FormActions = styled.div`\n  display: flex;\n  gap: 0.5rem;\n`;\n_c12 = FormActions;\nconst FormButton = styled.button`\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 6px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: #667eea;\n    color: white;\n    &:hover { background: #5a67d8; }\n  ` : `\n    background: #6c757d;\n    color: white;\n    &:hover { background: #5a6268; }\n  `}\n`;\n_c13 = FormButton;\nconst EmptyState = styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;\n_c14 = EmptyState;\nconst LyricsPreview = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  border: 2px solid #e9ecef;\n  margin-top: 1rem;\n`;\n_c15 = LyricsPreview;\nconst PreviewTitle = styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;\n_c16 = PreviewTitle;\nconst PreviewText = styled.div`\n  line-height: 1.8;\n  font-size: 1.1rem;\n  color: #333;\n  white-space: pre-wrap;\n`;\n_c17 = PreviewText;\nexport const LyricsEditor = ({\n  lyrics,\n  notes,\n  onLyricsChange,\n  title\n}) => {\n  _s();\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newLyric, setNewLyric] = useState({\n    text: '',\n    measure: 1,\n    beat: 1\n  });\n  const handleAddLyric = () => {\n    if (!newLyric.text.trim()) return;\n    const lyric = {\n      id: uuidv4(),\n      text: newLyric.text.trim(),\n      position: {\n        measure: newLyric.measure,\n        beat: newLyric.beat\n      }\n    };\n    onLyricsChange([...lyrics, lyric]);\n    setNewLyric({\n      text: '',\n      measure: 1,\n      beat: 1\n    });\n    setShowAddForm(false);\n  };\n  const handleUpdateLyric = (id, text) => {\n    const updatedLyrics = lyrics.map(lyric => lyric.id === id ? {\n      ...lyric,\n      text\n    } : lyric);\n    onLyricsChange(updatedLyrics);\n  };\n  const handleDeleteLyric = id => {\n    const updatedLyrics = lyrics.filter(lyric => lyric.id !== id);\n    onLyricsChange(updatedLyrics);\n  };\n\n  // Ordenar letras por posição\n  const sortedLyrics = [...lyrics].sort((a, b) => {\n    if (a.position.measure !== b.position.measure) {\n      return a.position.measure - b.position.measure;\n    }\n    return a.position.beat - b.position.beat;\n  });\n\n  // Gerar preview das letras\n  const generatePreview = () => {\n    return sortedLyrics.map(lyric => lyric.text).join(' ');\n  };\n\n  // Obter compassos disponíveis baseado nas notas\n  const availableMeasures = notes.length > 0 ? Array.from(new Set(notes.map(note => note.position.measure))).sort((a, b) => a - b) : [1];\n  return /*#__PURE__*/_jsxDEV(LyricsContainer, {\n    children: [/*#__PURE__*/_jsxDEV(LyricsHeader, {\n      children: [/*#__PURE__*/_jsxDEV(LyricsTitle, {\n        children: [\"\\uD83C\\uDFA4 Letras - \", title]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AddLyricButton, {\n        onClick: () => setShowAddForm(!showAddForm),\n        children: showAddForm ? '❌ Cancelar' : '➕ Adicionar Letra'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), showAddForm && /*#__PURE__*/_jsxDEV(AddLyricForm, {\n      children: [/*#__PURE__*/_jsxDEV(FormRow, {\n        children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Texto da Letra:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LyricText, {\n            value: newLyric.text,\n            onChange: e => setNewLyric({\n              ...newLyric,\n              text: e.target.value\n            }),\n            placeholder: \"Digite a palavra ou frase...\",\n            autoFocus: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          style: {\n            flex: '0 0 120px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Compasso:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: newLyric.measure,\n            onChange: e => setNewLyric({\n              ...newLyric,\n              measure: parseInt(e.target.value)\n            }),\n            children: availableMeasures.map(measure => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: measure,\n              children: measure\n            }, measure, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          style: {\n            flex: '0 0 100px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Tempo:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: newLyric.beat,\n            onChange: e => setNewLyric({\n              ...newLyric,\n              beat: parseInt(e.target.value)\n            }),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: 1,\n              children: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 2,\n              children: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 3,\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: 4,\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(FormActions, {\n        children: [/*#__PURE__*/_jsxDEV(FormButton, {\n          variant: \"primary\",\n          onClick: handleAddLyric,\n          children: \"\\u2705 Adicionar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FormButton, {\n          variant: \"secondary\",\n          onClick: () => setShowAddForm(false),\n          children: \"\\u274C Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 9\n    }, this), sortedLyrics.length === 0 ? /*#__PURE__*/_jsxDEV(EmptyState, {\n      children: \"Adicione letras para sincronizar com sua partitura\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(LyricsGrid, {\n        children: sortedLyrics.map(lyric => /*#__PURE__*/_jsxDEV(LyricCard, {\n          children: [/*#__PURE__*/_jsxDEV(LyricPosition, {\n            children: [\"\\uD83D\\uDCCD Compasso \", lyric.position.measure, \", Tempo \", lyric.position.beat]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(LyricText, {\n            value: lyric.text,\n            onChange: e => handleUpdateLyric(lyric.id, e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(LyricActions, {\n            children: /*#__PURE__*/_jsxDEV(ActionButton, {\n              variant: \"danger\",\n              onClick: () => handleDeleteLyric(lyric.id),\n              children: \"\\uD83D\\uDDD1\\uFE0F Excluir\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 17\n          }, this)]\n        }, lyric.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(LyricsPreview, {\n        children: [/*#__PURE__*/_jsxDEV(PreviewTitle, {\n          children: \"\\uD83D\\uDCDD Preview da Letra\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(PreviewText, {\n          children: generatePreview() || 'Nenhuma letra adicionada ainda...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 5\n  }, this);\n};\n_s(LyricsEditor, \"HpIv8aliufnrwKMX9WGX9AFh9h0=\");\n_c18 = LyricsEditor;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"LyricsContainer\");\n$RefreshReg$(_c2, \"LyricsHeader\");\n$RefreshReg$(_c3, \"LyricsTitle\");\n$RefreshReg$(_c4, \"AddLyricButton\");\n$RefreshReg$(_c5, \"LyricsGrid\");\n$RefreshReg$(_c6, \"LyricCard\");\n$RefreshReg$(_c7, \"LyricPosition\");\n$RefreshReg$(_c8, \"LyricText\");\n$RefreshReg$(_c9, \"LyricActions\");\n$RefreshReg$(_c0, \"ActionButton\");\n$RefreshReg$(_c1, \"AddLyricForm\");\n$RefreshReg$(_c10, \"FormRow\");\n$RefreshReg$(_c11, \"FormGroup\");\n$RefreshReg$(_c12, \"FormActions\");\n$RefreshReg$(_c13, \"FormButton\");\n$RefreshReg$(_c14, \"EmptyState\");\n$RefreshReg$(_c15, \"LyricsPreview\");\n$RefreshReg$(_c16, \"PreviewTitle\");\n$RefreshReg$(_c17, \"PreviewText\");\n$RefreshReg$(_c18, \"LyricsEditor\");", "map": {"version": 3, "names": ["React", "useState", "styled", "v4", "uuidv4", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Lyrics<PERSON><PERSON><PERSON>", "div", "_c", "LyricsHeader", "_c2", "LyricsTitle", "h3", "_c3", "AddLyricButton", "button", "_c4", "<PERSON><PERSON><PERSON>", "_c5", "LyricCard", "_c6", "LyricPosition", "_c7", "LyricText", "input", "_c8", "LyricActions", "_c9", "ActionButton", "props", "variant", "_c0", "AddLyricForm", "_c1", "FormRow", "_c10", "FormGroup", "_c11", "FormActions", "_c12", "FormButton", "_c13", "EmptyState", "_c14", "LyricsPreview", "_c15", "PreviewTitle", "h4", "_c16", "PreviewText", "_c17", "LyricsEditor", "lyrics", "notes", "onLyricsChange", "title", "_s", "showAddForm", "setShowAddForm", "newLyric", "setNewLyric", "text", "measure", "beat", "handleAddLyric", "trim", "lyric", "id", "position", "handleUpdateLyric", "updatedLyrics", "map", "handleDeleteLyric", "filter", "sortedLyrics", "sort", "a", "b", "generatePreview", "join", "availableMeasures", "length", "Array", "from", "Set", "note", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "value", "onChange", "e", "target", "placeholder", "autoFocus", "style", "flex", "parseInt", "_c18", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/LyricsEditor/LyricsEditor.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { Lyrics, MusicalNote } from '../../types/music';\nimport { v4 as uuidv4 } from 'uuid';\n\nconst LyricsContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst LyricsHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n`;\n\nconst LyricsTitle = styled.h3`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n`;\n\nconst AddLyricButton = styled.button`\n  padding: 0.5rem 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n  }\n`;\n\nconst LyricsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n`;\n\nconst LyricCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n  position: relative;\n`;\n\nconst LyricPosition = styled.div`\n  font-size: 0.8rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;\n\nconst LyricText = styled.input`\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #dee2e6;\n  border-radius: 6px;\n  font-size: 1rem;\n  margin-bottom: 0.5rem;\n  box-sizing: border-box;\n  \n  &:focus {\n    outline: none;\n    border-color: #667eea;\n  }\n`;\n\nconst LyricActions = styled.div`\n  display: flex;\n  gap: 0.5rem;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'danger' }>`\n  padding: 0.25rem 0.5rem;\n  border: none;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'danger' ? `\n    background: #e74c3c;\n    color: white;\n    &:hover { background: #c0392b; }\n  ` : `\n    background: #6c757d;\n    color: white;\n    &:hover { background: #5a6268; }\n  `}\n`;\n\nconst AddLyricForm = styled.div`\n  background: #e3f2fd;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  border: 2px solid #bbdefb;\n`;\n\nconst FormRow = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  align-items: end;\n`;\n\nconst FormGroup = styled.div`\n  flex: 1;\n  \n  label {\n    display: block;\n    margin-bottom: 0.25rem;\n    font-size: 0.9rem;\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input, select {\n    width: 100%;\n    padding: 0.5rem;\n    border: 1px solid #ced4da;\n    border-radius: 6px;\n    font-size: 0.9rem;\n    box-sizing: border-box;\n    \n    &:focus {\n      outline: none;\n      border-color: #667eea;\n    }\n  }\n`;\n\nconst FormActions = styled.div`\n  display: flex;\n  gap: 0.5rem;\n`;\n\nconst FormButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 6px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: #667eea;\n    color: white;\n    &:hover { background: #5a67d8; }\n  ` : `\n    background: #6c757d;\n    color: white;\n    &:hover { background: #5a6268; }\n  `}\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;\n\nconst LyricsPreview = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  border: 2px solid #e9ecef;\n  margin-top: 1rem;\n`;\n\nconst PreviewTitle = styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;\n\nconst PreviewText = styled.div`\n  line-height: 1.8;\n  font-size: 1.1rem;\n  color: #333;\n  white-space: pre-wrap;\n`;\n\ninterface LyricsEditorProps {\n  lyrics: Lyrics[];\n  notes: MusicalNote[];\n  onLyricsChange: (lyrics: Lyrics[]) => void;\n  title: string;\n}\n\nexport const LyricsEditor: React.FC<LyricsEditorProps> = ({ \n  lyrics, \n  notes, \n  onLyricsChange, \n  title \n}) => {\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newLyric, setNewLyric] = useState({\n    text: '',\n    measure: 1,\n    beat: 1\n  });\n\n  const handleAddLyric = () => {\n    if (!newLyric.text.trim()) return;\n    \n    const lyric: Lyrics = {\n      id: uuidv4(),\n      text: newLyric.text.trim(),\n      position: {\n        measure: newLyric.measure,\n        beat: newLyric.beat\n      }\n    };\n    \n    onLyricsChange([...lyrics, lyric]);\n    setNewLyric({ text: '', measure: 1, beat: 1 });\n    setShowAddForm(false);\n  };\n\n  const handleUpdateLyric = (id: string, text: string) => {\n    const updatedLyrics = lyrics.map(lyric =>\n      lyric.id === id ? { ...lyric, text } : lyric\n    );\n    onLyricsChange(updatedLyrics);\n  };\n\n  const handleDeleteLyric = (id: string) => {\n    const updatedLyrics = lyrics.filter(lyric => lyric.id !== id);\n    onLyricsChange(updatedLyrics);\n  };\n\n  // Ordenar letras por posição\n  const sortedLyrics = [...lyrics].sort((a, b) => {\n    if (a.position.measure !== b.position.measure) {\n      return a.position.measure - b.position.measure;\n    }\n    return a.position.beat - b.position.beat;\n  });\n\n  // Gerar preview das letras\n  const generatePreview = () => {\n    return sortedLyrics.map(lyric => lyric.text).join(' ');\n  };\n\n  // Obter compassos disponíveis baseado nas notas\n  const availableMeasures = notes.length > 0 \n    ? Array.from(new Set(notes.map(note => note.position.measure))).sort((a, b) => a - b)\n    : [1];\n\n  return (\n    <LyricsContainer>\n      <LyricsHeader>\n        <LyricsTitle>🎤 Letras - {title}</LyricsTitle>\n        <AddLyricButton onClick={() => setShowAddForm(!showAddForm)}>\n          {showAddForm ? '❌ Cancelar' : '➕ Adicionar Letra'}\n        </AddLyricButton>\n      </LyricsHeader>\n\n      {showAddForm && (\n        <AddLyricForm>\n          <FormRow>\n            <FormGroup>\n              <label>Texto da Letra:</label>\n              <LyricText\n                value={newLyric.text}\n                onChange={(e) => setNewLyric({ ...newLyric, text: e.target.value })}\n                placeholder=\"Digite a palavra ou frase...\"\n                autoFocus\n              />\n            </FormGroup>\n            <FormGroup style={{ flex: '0 0 120px' }}>\n              <label>Compasso:</label>\n              <select\n                value={newLyric.measure}\n                onChange={(e) => setNewLyric({ ...newLyric, measure: parseInt(e.target.value) })}\n              >\n                {availableMeasures.map(measure => (\n                  <option key={measure} value={measure}>\n                    {measure}\n                  </option>\n                ))}\n              </select>\n            </FormGroup>\n            <FormGroup style={{ flex: '0 0 100px' }}>\n              <label>Tempo:</label>\n              <select\n                value={newLyric.beat}\n                onChange={(e) => setNewLyric({ ...newLyric, beat: parseInt(e.target.value) })}\n              >\n                <option value={1}>1</option>\n                <option value={2}>2</option>\n                <option value={3}>3</option>\n                <option value={4}>4</option>\n              </select>\n            </FormGroup>\n          </FormRow>\n          <FormActions>\n            <FormButton variant=\"primary\" onClick={handleAddLyric}>\n              ✅ Adicionar\n            </FormButton>\n            <FormButton variant=\"secondary\" onClick={() => setShowAddForm(false)}>\n              ❌ Cancelar\n            </FormButton>\n          </FormActions>\n        </AddLyricForm>\n      )}\n\n      {sortedLyrics.length === 0 ? (\n        <EmptyState>\n          Adicione letras para sincronizar com sua partitura\n        </EmptyState>\n      ) : (\n        <>\n          <LyricsGrid>\n            {sortedLyrics.map(lyric => (\n              <LyricCard key={lyric.id}>\n                <LyricPosition>\n                  📍 Compasso {lyric.position.measure}, Tempo {lyric.position.beat}\n                </LyricPosition>\n                <LyricText\n                  value={lyric.text}\n                  onChange={(e) => handleUpdateLyric(lyric.id, e.target.value)}\n                />\n                <LyricActions>\n                  <ActionButton \n                    variant=\"danger\"\n                    onClick={() => handleDeleteLyric(lyric.id)}\n                  >\n                    🗑️ Excluir\n                  </ActionButton>\n                </LyricActions>\n              </LyricCard>\n            ))}\n          </LyricsGrid>\n\n          <LyricsPreview>\n            <PreviewTitle>📝 Preview da Letra</PreviewTitle>\n            <PreviewText>\n              {generatePreview() || 'Nenhuma letra adicionada ainda...'}\n            </PreviewText>\n          </LyricsPreview>\n        </>\n      )}\n    </LyricsContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AAEtC,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,eAAe,GAAGP,MAAM,CAACQ,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,eAAe;AAQrB,MAAMG,YAAY,GAAGV,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,YAAY;AAOlB,MAAME,WAAW,GAAGZ,MAAM,CAACa,EAAE;AAC7B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,WAAW;AAMjB,MAAMG,cAAc,GAAGf,MAAM,CAACgB,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,cAAc;AAiBpB,MAAMG,UAAU,GAAGlB,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GALID,UAAU;AAOhB,MAAME,SAAS,GAAGpB,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GANID,SAAS;AAQf,MAAME,aAAa,GAAGtB,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GALID,aAAa;AAOnB,MAAME,SAAS,GAAGxB,MAAM,CAACyB,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,SAAS;AAef,MAAMG,YAAY,GAAG3B,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA,CAAC;AAACoB,GAAA,GAHID,YAAY;AAKlB,MAAME,YAAY,GAAG7B,MAAM,CAACgB,MAA8B;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIc,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,QAAQ,GAAG;AAC1C;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACC,GAAA,GAjBIH,YAAY;AAmBlB,MAAMI,YAAY,GAAGjC,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GANID,YAAY;AAQlB,MAAME,OAAO,GAAGnC,MAAM,CAACQ,GAAG;AAC1B;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GALID,OAAO;AAOb,MAAME,SAAS,GAAGrC,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8B,IAAA,GAxBID,SAAS;AA0Bf,MAAME,WAAW,GAAGvC,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA,CAAC;AAACgC,IAAA,GAHID,WAAW;AAKjB,MAAME,UAAU,GAAGzC,MAAM,CAACgB,MAA6C;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIc,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG;AAC3C;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACW,IAAA,GAlBID,UAAU;AAoBhB,MAAME,UAAU,GAAG3C,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GALID,UAAU;AAOhB,MAAME,aAAa,GAAG7C,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsC,IAAA,GANID,aAAa;AAQnB,MAAME,YAAY,GAAG/C,MAAM,CAACgD,EAAE;AAC9B;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAJIF,YAAY;AAMlB,MAAMG,WAAW,GAAGlD,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAAC2C,IAAA,GALID,WAAW;AAcjB,OAAO,MAAME,YAAyC,GAAGA,CAAC;EACxDC,MAAM;EACNC,KAAK;EACLC,cAAc;EACdC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6D,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAC;IACvC+D,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE,CAAC;IACVC,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACL,QAAQ,CAACE,IAAI,CAACI,IAAI,CAAC,CAAC,EAAE;IAE3B,MAAMC,KAAa,GAAG;MACpBC,EAAE,EAAElE,MAAM,CAAC,CAAC;MACZ4D,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACI,IAAI,CAAC,CAAC;MAC1BG,QAAQ,EAAE;QACRN,OAAO,EAAEH,QAAQ,CAACG,OAAO;QACzBC,IAAI,EAAEJ,QAAQ,CAACI;MACjB;IACF,CAAC;IAEDT,cAAc,CAAC,CAAC,GAAGF,MAAM,EAAEc,KAAK,CAAC,CAAC;IAClCN,WAAW,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAC,CAAC;IAC9CL,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMW,iBAAiB,GAAGA,CAACF,EAAU,EAAEN,IAAY,KAAK;IACtD,MAAMS,aAAa,GAAGlB,MAAM,CAACmB,GAAG,CAACL,KAAK,IACpCA,KAAK,CAACC,EAAE,KAAKA,EAAE,GAAG;MAAE,GAAGD,KAAK;MAAEL;IAAK,CAAC,GAAGK,KACzC,CAAC;IACDZ,cAAc,CAACgB,aAAa,CAAC;EAC/B,CAAC;EAED,MAAME,iBAAiB,GAAIL,EAAU,IAAK;IACxC,MAAMG,aAAa,GAAGlB,MAAM,CAACqB,MAAM,CAACP,KAAK,IAAIA,KAAK,CAACC,EAAE,KAAKA,EAAE,CAAC;IAC7Db,cAAc,CAACgB,aAAa,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMI,YAAY,GAAG,CAAC,GAAGtB,MAAM,CAAC,CAACuB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC9C,IAAID,CAAC,CAACR,QAAQ,CAACN,OAAO,KAAKe,CAAC,CAACT,QAAQ,CAACN,OAAO,EAAE;MAC7C,OAAOc,CAAC,CAACR,QAAQ,CAACN,OAAO,GAAGe,CAAC,CAACT,QAAQ,CAACN,OAAO;IAChD;IACA,OAAOc,CAAC,CAACR,QAAQ,CAACL,IAAI,GAAGc,CAAC,CAACT,QAAQ,CAACL,IAAI;EAC1C,CAAC,CAAC;;EAEF;EACA,MAAMe,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAOJ,YAAY,CAACH,GAAG,CAACL,KAAK,IAAIA,KAAK,CAACL,IAAI,CAAC,CAACkB,IAAI,CAAC,GAAG,CAAC;EACxD,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG3B,KAAK,CAAC4B,MAAM,GAAG,CAAC,GACtCC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC/B,KAAK,CAACkB,GAAG,CAACc,IAAI,IAAIA,IAAI,CAACjB,QAAQ,CAACN,OAAO,CAAC,CAAC,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,GACnF,CAAC,CAAC,CAAC;EAEP,oBACE1E,OAAA,CAACG,eAAe;IAAAgF,QAAA,gBACdnF,OAAA,CAACM,YAAY;MAAA6E,QAAA,gBACXnF,OAAA,CAACQ,WAAW;QAAA2E,QAAA,GAAC,wBAAY,EAAC/B,KAAK;MAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAC9CvF,OAAA,CAACW,cAAc;QAAC6E,OAAO,EAAEA,CAAA,KAAMjC,cAAc,CAAC,CAACD,WAAW,CAAE;QAAA6B,QAAA,EACzD7B,WAAW,GAAG,YAAY,GAAG;MAAmB;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEdjC,WAAW,iBACVtD,OAAA,CAAC6B,YAAY;MAAAsD,QAAA,gBACXnF,OAAA,CAAC+B,OAAO;QAAAoD,QAAA,gBACNnF,OAAA,CAACiC,SAAS;UAAAkD,QAAA,gBACRnF,OAAA;YAAAmF,QAAA,EAAO;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9BvF,OAAA,CAACoB,SAAS;YACRqE,KAAK,EAAEjC,QAAQ,CAACE,IAAK;YACrBgC,QAAQ,EAAGC,CAAC,IAAKlC,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEE,IAAI,EAAEiC,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACpEI,WAAW,EAAC,8BAA8B;YAC1CC,SAAS;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eACZvF,OAAA,CAACiC,SAAS;UAAC8D,KAAK,EAAE;YAAEC,IAAI,EAAE;UAAY,CAAE;UAAAb,QAAA,gBACtCnF,OAAA;YAAAmF,QAAA,EAAO;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACxBvF,OAAA;YACEyF,KAAK,EAAEjC,QAAQ,CAACG,OAAQ;YACxB+B,QAAQ,EAAGC,CAAC,IAAKlC,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEG,OAAO,EAAEsC,QAAQ,CAACN,CAAC,CAACC,MAAM,CAACH,KAAK;YAAE,CAAC,CAAE;YAAAN,QAAA,EAEhFN,iBAAiB,CAACT,GAAG,CAACT,OAAO,iBAC5B3D,OAAA;cAAsByF,KAAK,EAAE9B,OAAQ;cAAAwB,QAAA,EAClCxB;YAAO,GADGA,OAAO;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEZ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZvF,OAAA,CAACiC,SAAS;UAAC8D,KAAK,EAAE;YAAEC,IAAI,EAAE;UAAY,CAAE;UAAAb,QAAA,gBACtCnF,OAAA;YAAAmF,QAAA,EAAO;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrBvF,OAAA;YACEyF,KAAK,EAAEjC,QAAQ,CAACI,IAAK;YACrB8B,QAAQ,EAAGC,CAAC,IAAKlC,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEI,IAAI,EAAEqC,QAAQ,CAACN,CAAC,CAACC,MAAM,CAACH,KAAK;YAAE,CAAC,CAAE;YAAAN,QAAA,gBAE9EnF,OAAA;cAAQyF,KAAK,EAAE,CAAE;cAAAN,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5BvF,OAAA;cAAQyF,KAAK,EAAE,CAAE;cAAAN,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5BvF,OAAA;cAAQyF,KAAK,EAAE,CAAE;cAAAN,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5BvF,OAAA;cAAQyF,KAAK,EAAE,CAAE;cAAAN,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACVvF,OAAA,CAACmC,WAAW;QAAAgD,QAAA,gBACVnF,OAAA,CAACqC,UAAU;UAACV,OAAO,EAAC,SAAS;UAAC6D,OAAO,EAAE3B,cAAe;UAAAsB,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvF,OAAA,CAACqC,UAAU;UAACV,OAAO,EAAC,WAAW;UAAC6D,OAAO,EAAEA,CAAA,KAAMjC,cAAc,CAAC,KAAK,CAAE;UAAA4B,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACf,EAEAhB,YAAY,CAACO,MAAM,KAAK,CAAC,gBACxB9E,OAAA,CAACuC,UAAU;MAAA4C,QAAA,EAAC;IAEZ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,gBAEbvF,OAAA,CAAAE,SAAA;MAAAiF,QAAA,gBACEnF,OAAA,CAACc,UAAU;QAAAqE,QAAA,EACRZ,YAAY,CAACH,GAAG,CAACL,KAAK,iBACrB/D,OAAA,CAACgB,SAAS;UAAAmE,QAAA,gBACRnF,OAAA,CAACkB,aAAa;YAAAiE,QAAA,GAAC,wBACD,EAACpB,KAAK,CAACE,QAAQ,CAACN,OAAO,EAAC,UAAQ,EAACI,KAAK,CAACE,QAAQ,CAACL,IAAI;UAAA;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAChBvF,OAAA,CAACoB,SAAS;YACRqE,KAAK,EAAE1B,KAAK,CAACL,IAAK;YAClBgC,QAAQ,EAAGC,CAAC,IAAKzB,iBAAiB,CAACH,KAAK,CAACC,EAAE,EAAE2B,CAAC,CAACC,MAAM,CAACH,KAAK;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACFvF,OAAA,CAACuB,YAAY;YAAA4D,QAAA,eACXnF,OAAA,CAACyB,YAAY;cACXE,OAAO,EAAC,QAAQ;cAChB6D,OAAO,EAAEA,CAAA,KAAMnB,iBAAiB,CAACN,KAAK,CAACC,EAAE,CAAE;cAAAmB,QAAA,EAC5C;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAfDxB,KAAK,CAACC,EAAE;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBb,CACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAEbvF,OAAA,CAACyC,aAAa;QAAA0C,QAAA,gBACZnF,OAAA,CAAC2C,YAAY;UAAAwC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAChDvF,OAAA,CAAC8C,WAAW;UAAAqC,QAAA,EACTR,eAAe,CAAC,CAAC,IAAI;QAAmC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA,eAChB,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAEtB,CAAC;AAAClC,EAAA,CA5JWL,YAAyC;AAAAkD,IAAA,GAAzClD,YAAyC;AAAA,IAAA3C,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAmD,IAAA;AAAAC,YAAA,CAAA9F,EAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}