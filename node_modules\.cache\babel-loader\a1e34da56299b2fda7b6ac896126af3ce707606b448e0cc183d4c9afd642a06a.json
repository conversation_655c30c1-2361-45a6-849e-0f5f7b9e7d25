{"ast": null, "code": "import React,{useState,useEffect}from'react';import styled from'styled-components';import{useAuth}from'../../contexts/AuthContext';import{ScoreService}from'../../services/scoreService';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ScoresContainer=styled.div`\n  padding: 2rem;\n`;const Header=styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n`;const Title=styled.h1`\n  color: white;\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 600;\n`;const SearchBar=styled.input`\n  padding: 0.75rem 1rem;\n  border: 1px solid rgba(255,255,255,0.3);\n  border-radius: 12px;\n  background: rgba(255,255,255,0.1);\n  color: white;\n  font-size: 1rem;\n  width: 300px;\n  backdrop-filter: blur(10px);\n  \n  &::placeholder {\n    color: rgba(255,255,255,0.7);\n  }\n  \n  &:focus {\n    outline: none;\n    border-color: rgba(255,255,255,0.6);\n    background: rgba(255,255,255,0.2);\n  }\n`;const ScoresGrid=styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1.5rem;\n`;const ScoreCard=styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 1.5rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  \n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: 0 12px 40px rgba(0,0,0,0.15);\n  }\n`;const ScoreTitle=styled.h3`\n  margin: 0 0 0.5rem 0;\n  color: #2c3e50;\n  font-size: 1.2rem;\n  font-weight: 600;\n`;const ScoreInfo=styled.div`\n  color: #666;\n  font-size: 0.9rem;\n  margin-bottom: 1rem;\n`;const ScoreActions=styled.div`\n  display: flex;\n  gap: 0.5rem;\n  margin-top: 1rem;\n`;const ActionButton=styled.button`\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props=>{switch(props.variant){case'primary':return`\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          &:hover { transform: translateY(-1px); }\n        `;case'danger':return`\n          background: #e74c3c;\n          color: white;\n          &:hover { background: #c0392b; }\n        `;default:return`\n          background: #f8f9fa;\n          color: #495057;\n          border: 1px solid #dee2e6;\n          &:hover { background: #e9ecef; }\n        `;}}}\n`;const EmptyState=styled.div`\n  text-align: center;\n  padding: 4rem 2rem;\n  color: rgba(255,255,255,0.8);\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    font-size: 1.5rem;\n  }\n  \n  p {\n    margin: 0 0 2rem 0;\n    font-size: 1.1rem;\n  }\n`;const CreateButton=styled.button`\n  padding: 1rem 2rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n  }\n`;const LoadingState=styled.div`\n  text-align: center;\n  padding: 4rem 2rem;\n  color: rgba(255,255,255,0.8);\n  font-size: 1.2rem;\n`;export const ScoresList=_ref=>{let{onCreateNew,onEditScore}=_ref;const{currentUser}=useAuth();const[scores,setScores]=useState([]);const[loading,setLoading]=useState(true);const[searchTerm,setSearchTerm]=useState('');useEffect(()=>{loadScores();},[currentUser]);const loadScores=async()=>{if(!currentUser){setLoading(false);return;}try{const userScores=await ScoreService.getUserScores(currentUser.uid);setScores(userScores);}catch(error){console.error('Erro ao carregar partituras:',error);}finally{setLoading(false);}};const handleDeleteScore=async scoreId=>{if(!window.confirm('Tem certeza que deseja excluir esta partitura?')){return;}try{await ScoreService.deleteScore(scoreId);setScores(scores.filter(score=>score.id!==scoreId));}catch(error){console.error('Erro ao excluir partitura:',error);alert('Erro ao excluir partitura');}};const handleDuplicateScore=async score=>{try{const newTitle=`${score.title} (Cópia)`;await ScoreService.duplicateScore(score.id,newTitle);loadScores();// Recarregar lista\n}catch(error){console.error('Erro ao duplicar partitura:',error);alert('Erro ao duplicar partitura');}};const filteredScores=scores.filter(score=>score.title.toLowerCase().includes(searchTerm.toLowerCase())||score.composer&&score.composer.toLowerCase().includes(searchTerm.toLowerCase()));const formatDate=date=>{return new Intl.DateTimeFormat('pt-BR',{day:'2-digit',month:'2-digit',year:'numeric',hour:'2-digit',minute:'2-digit'}).format(date);};if(!currentUser){return/*#__PURE__*/_jsx(ScoresContainer,{children:/*#__PURE__*/_jsxs(EmptyState,{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83C\\uDFB5 Suas Partituras\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Fa\\xE7a login para ver e gerenciar suas partituras salvas\"})]})});}if(loading){return/*#__PURE__*/_jsx(ScoresContainer,{children:/*#__PURE__*/_jsx(LoadingState,{children:\"\\uD83C\\uDFBC Carregando suas partituras...\"})});}return/*#__PURE__*/_jsxs(ScoresContainer,{children:[/*#__PURE__*/_jsxs(Header,{children:[/*#__PURE__*/_jsx(Title,{children:\"\\uD83C\\uDFB5 Minhas Partituras\"}),/*#__PURE__*/_jsx(SearchBar,{type:\"text\",placeholder:\"Buscar partituras...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})]}),filteredScores.length===0?/*#__PURE__*/_jsx(EmptyState,{children:scores.length===0?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83C\\uDFBC Nenhuma partitura ainda\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Comece criando sua primeira partitura musical!\"}),/*#__PURE__*/_jsx(CreateButton,{onClick:onCreateNew,children:\"\\u2795 Criar Nova Partitura\"})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDD0D Nenhuma partitura encontrada\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Tente buscar com outros termos\"})]})}):/*#__PURE__*/_jsx(ScoresGrid,{children:filteredScores.map(score=>/*#__PURE__*/_jsxs(ScoreCard,{onClick:()=>onEditScore(score.id),children:[/*#__PURE__*/_jsx(ScoreTitle,{children:score.title}),/*#__PURE__*/_jsxs(ScoreInfo,{children:[score.composer&&/*#__PURE__*/_jsxs(\"div\",{children:[\"\\uD83C\\uDFAD Compositor: \",score.composer]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\uD83C\\uDFB9 Tom: \",score.key.note,\" \",score.key.mode]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u23F1\\uFE0F Compasso: \",score.timeSignature.numerator,\"/\",score.timeSignature.denominator]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\uD83D\\uDCC5 Modificado: \",formatDate(score.updatedAt)]})]}),/*#__PURE__*/_jsxs(ScoreActions,{onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsx(ActionButton,{variant:\"primary\",onClick:()=>onEditScore(score.id),children:\"\\u270F\\uFE0F Editar\"}),/*#__PURE__*/_jsx(ActionButton,{variant:\"secondary\",onClick:()=>handleDuplicateScore(score),children:\"\\uD83D\\uDCCB Duplicar\"}),/*#__PURE__*/_jsx(ActionButton,{variant:\"danger\",onClick:()=>handleDeleteScore(score.id),children:\"\\uD83D\\uDDD1\\uFE0F Excluir\"})]})]},score.id))})]});};", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "useAuth", "ScoreService", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ScoresContainer", "div", "Header", "Title", "h1", "SearchBar", "input", "ScoresGrid", "ScoreCard", "ScoreTitle", "h3", "ScoreInfo", "ScoreActions", "ActionButton", "button", "props", "variant", "EmptyState", "CreateButton", "LoadingState", "ScoresList", "_ref", "onCreateNew", "onEditScore", "currentUser", "scores", "setScores", "loading", "setLoading", "searchTerm", "setSearchTerm", "loadScores", "userScores", "getUserScores", "uid", "error", "console", "handleDeleteScore", "scoreId", "window", "confirm", "deleteScore", "filter", "score", "id", "alert", "handleDuplicateScore", "newTitle", "title", "duplicateScore", "filteredScores", "toLowerCase", "includes", "composer", "formatDate", "date", "Intl", "DateTimeFormat", "day", "month", "year", "hour", "minute", "format", "children", "type", "placeholder", "value", "onChange", "e", "target", "length", "onClick", "map", "key", "note", "mode", "timeSignature", "numerator", "denominator", "updatedAt", "stopPropagation"], "sources": ["D:/Dev/partitura_digital/src/pages/ScoresList/ScoresList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Score } from '../../types/music';\nimport { ScoreService } from '../../services/scoreService';\n\nconst ScoresContainer = styled.div`\n  padding: 2rem;\n`;\n\nconst Header = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n`;\n\nconst Title = styled.h1`\n  color: white;\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 600;\n`;\n\nconst SearchBar = styled.input`\n  padding: 0.75rem 1rem;\n  border: 1px solid rgba(255,255,255,0.3);\n  border-radius: 12px;\n  background: rgba(255,255,255,0.1);\n  color: white;\n  font-size: 1rem;\n  width: 300px;\n  backdrop-filter: blur(10px);\n  \n  &::placeholder {\n    color: rgba(255,255,255,0.7);\n  }\n  \n  &:focus {\n    outline: none;\n    border-color: rgba(255,255,255,0.6);\n    background: rgba(255,255,255,0.2);\n  }\n`;\n\nconst ScoresGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1.5rem;\n`;\n\nconst ScoreCard = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 1.5rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n  \n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: 0 12px 40px rgba(0,0,0,0.15);\n  }\n`;\n\nconst ScoreTitle = styled.h3`\n  margin: 0 0 0.5rem 0;\n  color: #2c3e50;\n  font-size: 1.2rem;\n  font-weight: 600;\n`;\n\nconst ScoreInfo = styled.div`\n  color: #666;\n  font-size: 0.9rem;\n  margin-bottom: 1rem;\n`;\n\nconst ScoreActions = styled.div`\n  display: flex;\n  gap: 0.5rem;\n  margin-top: 1rem;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => {\n    switch (props.variant) {\n      case 'primary':\n        return `\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          &:hover { transform: translateY(-1px); }\n        `;\n      case 'danger':\n        return `\n          background: #e74c3c;\n          color: white;\n          &:hover { background: #c0392b; }\n        `;\n      default:\n        return `\n          background: #f8f9fa;\n          color: #495057;\n          border: 1px solid #dee2e6;\n          &:hover { background: #e9ecef; }\n        `;\n    }\n  }}\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: 4rem 2rem;\n  color: rgba(255,255,255,0.8);\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    font-size: 1.5rem;\n  }\n  \n  p {\n    margin: 0 0 2rem 0;\n    font-size: 1.1rem;\n  }\n`;\n\nconst CreateButton = styled.button`\n  padding: 1rem 2rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n  }\n`;\n\nconst LoadingState = styled.div`\n  text-align: center;\n  padding: 4rem 2rem;\n  color: rgba(255,255,255,0.8);\n  font-size: 1.2rem;\n`;\n\ninterface ScoresListProps {\n  onCreateNew: () => void;\n  onEditScore: (scoreId: string) => void;\n}\n\nexport const ScoresList: React.FC<ScoresListProps> = ({ onCreateNew, onEditScore }) => {\n  const { currentUser } = useAuth();\n  const [scores, setScores] = useState<Score[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  useEffect(() => {\n    loadScores();\n  }, [currentUser]);\n\n  const loadScores = async () => {\n    if (!currentUser) {\n      setLoading(false);\n      return;\n    }\n\n    try {\n      const userScores = await ScoreService.getUserScores(currentUser.uid);\n      setScores(userScores);\n    } catch (error) {\n      console.error('Erro ao carregar partituras:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteScore = async (scoreId: string) => {\n    if (!window.confirm('Tem certeza que deseja excluir esta partitura?')) {\n      return;\n    }\n\n    try {\n      await ScoreService.deleteScore(scoreId);\n      setScores(scores.filter(score => score.id !== scoreId));\n    } catch (error) {\n      console.error('Erro ao excluir partitura:', error);\n      alert('Erro ao excluir partitura');\n    }\n  };\n\n  const handleDuplicateScore = async (score: Score) => {\n    try {\n      const newTitle = `${score.title} (Cópia)`;\n      await ScoreService.duplicateScore(score.id, newTitle);\n      loadScores(); // Recarregar lista\n    } catch (error) {\n      console.error('Erro ao duplicar partitura:', error);\n      alert('Erro ao duplicar partitura');\n    }\n  };\n\n  const filteredScores = scores.filter(score =>\n    score.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (score.composer && score.composer.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  const formatDate = (date: Date) => {\n    return new Intl.DateTimeFormat('pt-BR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    }).format(date);\n  };\n\n  if (!currentUser) {\n    return (\n      <ScoresContainer>\n        <EmptyState>\n          <h3>🎵 Suas Partituras</h3>\n          <p>Faça login para ver e gerenciar suas partituras salvas</p>\n        </EmptyState>\n      </ScoresContainer>\n    );\n  }\n\n  if (loading) {\n    return (\n      <ScoresContainer>\n        <LoadingState>\n          🎼 Carregando suas partituras...\n        </LoadingState>\n      </ScoresContainer>\n    );\n  }\n\n  return (\n    <ScoresContainer>\n      <Header>\n        <Title>🎵 Minhas Partituras</Title>\n        <SearchBar\n          type=\"text\"\n          placeholder=\"Buscar partituras...\"\n          value={searchTerm}\n          onChange={(e) => setSearchTerm(e.target.value)}\n        />\n      </Header>\n\n      {filteredScores.length === 0 ? (\n        <EmptyState>\n          {scores.length === 0 ? (\n            <>\n              <h3>🎼 Nenhuma partitura ainda</h3>\n              <p>Comece criando sua primeira partitura musical!</p>\n              <CreateButton onClick={onCreateNew}>\n                ➕ Criar Nova Partitura\n              </CreateButton>\n            </>\n          ) : (\n            <>\n              <h3>🔍 Nenhuma partitura encontrada</h3>\n              <p>Tente buscar com outros termos</p>\n            </>\n          )}\n        </EmptyState>\n      ) : (\n        <ScoresGrid>\n          {filteredScores.map(score => (\n            <ScoreCard key={score.id} onClick={() => onEditScore(score.id)}>\n              <ScoreTitle>{score.title}</ScoreTitle>\n              <ScoreInfo>\n                {score.composer && <div>🎭 Compositor: {score.composer}</div>}\n                <div>🎹 Tom: {score.key.note} {score.key.mode}</div>\n                <div>⏱️ Compasso: {score.timeSignature.numerator}/{score.timeSignature.denominator}</div>\n                <div>📅 Modificado: {formatDate(score.updatedAt)}</div>\n              </ScoreInfo>\n              \n              <ScoreActions onClick={(e) => e.stopPropagation()}>\n                <ActionButton \n                  variant=\"primary\"\n                  onClick={() => onEditScore(score.id)}\n                >\n                  ✏️ Editar\n                </ActionButton>\n                <ActionButton \n                  variant=\"secondary\"\n                  onClick={() => handleDuplicateScore(score)}\n                >\n                  📋 Duplicar\n                </ActionButton>\n                <ActionButton \n                  variant=\"danger\"\n                  onClick={() => handleDeleteScore(score.id)}\n                >\n                  🗑️ Excluir\n                </ActionButton>\n              </ScoreActions>\n            </ScoreCard>\n          ))}\n        </ScoresGrid>\n      )}\n    </ScoresContainer>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,4BAA4B,CAEpD,OAASC,YAAY,KAAQ,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE3D,KAAM,CAAAC,eAAe,CAAGT,MAAM,CAACU,GAAG;AAClC;AACA,CAAC,CAED,KAAM,CAAAC,MAAM,CAAGX,MAAM,CAACU,GAAG;AACzB;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAE,KAAK,CAAGZ,MAAM,CAACa,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,SAAS,CAAGd,MAAM,CAACe,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGhB,MAAM,CAACU,GAAG;AAC7B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAO,SAAS,CAAGjB,MAAM,CAACU,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAQ,UAAU,CAAGlB,MAAM,CAACmB,EAAE;AAC5B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,SAAS,CAAGpB,MAAM,CAACU,GAAG;AAC5B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAW,YAAY,CAAGrB,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAY,YAAY,CAAGtB,MAAM,CAACuB,MAAwD;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,EAAI,CACT,OAAQA,KAAK,CAACC,OAAO,EACnB,IAAK,SAAS,CACZ,MAAO;AACf;AACA;AACA;AACA,SAAS,CACH,IAAK,QAAQ,CACX,MAAO;AACf;AACA;AACA;AACA,SAAS,CACH,QACE,MAAO;AACf;AACA;AACA;AACA;AACA,SAAS,CACL,CACF,CAAC;AACH,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG1B,MAAM,CAACU,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAiB,YAAY,CAAG3B,MAAM,CAACuB,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAK,YAAY,CAAG5B,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC,CAOD,MAAO,MAAM,CAAAmB,UAAqC,CAAGC,IAAA,EAAkC,IAAjC,CAAEC,WAAW,CAAEC,WAAY,CAAC,CAAAF,IAAA,CAChF,KAAM,CAAEG,WAAY,CAAC,CAAGhC,OAAO,CAAC,CAAC,CACjC,KAAM,CAACiC,MAAM,CAAEC,SAAS,CAAC,CAAGrC,QAAQ,CAAU,EAAE,CAAC,CACjD,KAAM,CAACsC,OAAO,CAAEC,UAAU,CAAC,CAAGvC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACwC,UAAU,CAAEC,aAAa,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAEhDC,SAAS,CAAC,IAAM,CACdyC,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,CAACP,WAAW,CAAC,CAAC,CAEjB,KAAM,CAAAO,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CAACP,WAAW,CAAE,CAChBI,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA,GAAI,CACF,KAAM,CAAAI,UAAU,CAAG,KAAM,CAAAvC,YAAY,CAACwC,aAAa,CAACT,WAAW,CAACU,GAAG,CAAC,CACpER,SAAS,CAACM,UAAU,CAAC,CACvB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACtD,CAAC,OAAS,CACRP,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAS,iBAAiB,CAAG,KAAO,CAAAC,OAAe,EAAK,CACnD,GAAI,CAACC,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,CAAE,CACrE,OACF,CAEA,GAAI,CACF,KAAM,CAAA/C,YAAY,CAACgD,WAAW,CAACH,OAAO,CAAC,CACvCZ,SAAS,CAACD,MAAM,CAACiB,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,EAAE,GAAKN,OAAO,CAAC,CAAC,CACzD,CAAE,MAAOH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDU,KAAK,CAAC,2BAA2B,CAAC,CACpC,CACF,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAG,KAAO,CAAAH,KAAY,EAAK,CACnD,GAAI,CACF,KAAM,CAAAI,QAAQ,CAAG,GAAGJ,KAAK,CAACK,KAAK,UAAU,CACzC,KAAM,CAAAvD,YAAY,CAACwD,cAAc,CAACN,KAAK,CAACC,EAAE,CAAEG,QAAQ,CAAC,CACrDhB,UAAU,CAAC,CAAC,CAAE;AAChB,CAAE,MAAOI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDU,KAAK,CAAC,4BAA4B,CAAC,CACrC,CACF,CAAC,CAED,KAAM,CAAAK,cAAc,CAAGzB,MAAM,CAACiB,MAAM,CAACC,KAAK,EACxCA,KAAK,CAACK,KAAK,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,EAC3DR,KAAK,CAACU,QAAQ,EAAIV,KAAK,CAACU,QAAQ,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvB,UAAU,CAACsB,WAAW,CAAC,CAAC,CACnF,CAAC,CAED,KAAM,CAAAG,UAAU,CAAIC,IAAU,EAAK,CACjC,MAAO,IAAI,CAAAC,IAAI,CAACC,cAAc,CAAC,OAAO,CAAE,CACtCC,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SAAS,CACfC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CAACC,MAAM,CAACR,IAAI,CAAC,CACjB,CAAC,CAED,GAAI,CAAC/B,WAAW,CAAE,CAChB,mBACE7B,IAAA,CAACK,eAAe,EAAAgE,QAAA,cACdnE,KAAA,CAACoB,UAAU,EAAA+C,QAAA,eACTrE,IAAA,OAAAqE,QAAA,CAAI,8BAAkB,CAAI,CAAC,cAC3BrE,IAAA,MAAAqE,QAAA,CAAG,2DAAsD,CAAG,CAAC,EACnD,CAAC,CACE,CAAC,CAEtB,CAEA,GAAIrC,OAAO,CAAE,CACX,mBACEhC,IAAA,CAACK,eAAe,EAAAgE,QAAA,cACdrE,IAAA,CAACwB,YAAY,EAAA6C,QAAA,CAAC,4CAEd,CAAc,CAAC,CACA,CAAC,CAEtB,CAEA,mBACEnE,KAAA,CAACG,eAAe,EAAAgE,QAAA,eACdnE,KAAA,CAACK,MAAM,EAAA8D,QAAA,eACLrE,IAAA,CAACQ,KAAK,EAAA6D,QAAA,CAAC,gCAAoB,CAAO,CAAC,cACnCrE,IAAA,CAACU,SAAS,EACR4D,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,sBAAsB,CAClCC,KAAK,CAAEtC,UAAW,CAClBuC,QAAQ,CAAGC,CAAC,EAAKvC,aAAa,CAACuC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChD,CAAC,EACI,CAAC,CAERjB,cAAc,CAACqB,MAAM,GAAK,CAAC,cAC1B5E,IAAA,CAACsB,UAAU,EAAA+C,QAAA,CACRvC,MAAM,CAAC8C,MAAM,GAAK,CAAC,cAClB1E,KAAA,CAAAE,SAAA,EAAAiE,QAAA,eACErE,IAAA,OAAAqE,QAAA,CAAI,sCAA0B,CAAI,CAAC,cACnCrE,IAAA,MAAAqE,QAAA,CAAG,gDAA8C,CAAG,CAAC,cACrDrE,IAAA,CAACuB,YAAY,EAACsD,OAAO,CAAElD,WAAY,CAAA0C,QAAA,CAAC,6BAEpC,CAAc,CAAC,EACf,CAAC,cAEHnE,KAAA,CAAAE,SAAA,EAAAiE,QAAA,eACErE,IAAA,OAAAqE,QAAA,CAAI,2CAA+B,CAAI,CAAC,cACxCrE,IAAA,MAAAqE,QAAA,CAAG,gCAA8B,CAAG,CAAC,EACrC,CACH,CACS,CAAC,cAEbrE,IAAA,CAACY,UAAU,EAAAyD,QAAA,CACRd,cAAc,CAACuB,GAAG,CAAC9B,KAAK,eACvB9C,KAAA,CAACW,SAAS,EAAgBgE,OAAO,CAAEA,CAAA,GAAMjD,WAAW,CAACoB,KAAK,CAACC,EAAE,CAAE,CAAAoB,QAAA,eAC7DrE,IAAA,CAACc,UAAU,EAAAuD,QAAA,CAAErB,KAAK,CAACK,KAAK,CAAa,CAAC,cACtCnD,KAAA,CAACc,SAAS,EAAAqD,QAAA,EACPrB,KAAK,CAACU,QAAQ,eAAIxD,KAAA,QAAAmE,QAAA,EAAK,2BAAe,CAACrB,KAAK,CAACU,QAAQ,EAAM,CAAC,cAC7DxD,KAAA,QAAAmE,QAAA,EAAK,oBAAQ,CAACrB,KAAK,CAAC+B,GAAG,CAACC,IAAI,CAAC,GAAC,CAAChC,KAAK,CAAC+B,GAAG,CAACE,IAAI,EAAM,CAAC,cACpD/E,KAAA,QAAAmE,QAAA,EAAK,yBAAa,CAACrB,KAAK,CAACkC,aAAa,CAACC,SAAS,CAAC,GAAC,CAACnC,KAAK,CAACkC,aAAa,CAACE,WAAW,EAAM,CAAC,cACzFlF,KAAA,QAAAmE,QAAA,EAAK,2BAAe,CAACV,UAAU,CAACX,KAAK,CAACqC,SAAS,CAAC,EAAM,CAAC,EAC9C,CAAC,cAEZnF,KAAA,CAACe,YAAY,EAAC4D,OAAO,CAAGH,CAAC,EAAKA,CAAC,CAACY,eAAe,CAAC,CAAE,CAAAjB,QAAA,eAChDrE,IAAA,CAACkB,YAAY,EACXG,OAAO,CAAC,SAAS,CACjBwD,OAAO,CAAEA,CAAA,GAAMjD,WAAW,CAACoB,KAAK,CAACC,EAAE,CAAE,CAAAoB,QAAA,CACtC,qBAED,CAAc,CAAC,cACfrE,IAAA,CAACkB,YAAY,EACXG,OAAO,CAAC,WAAW,CACnBwD,OAAO,CAAEA,CAAA,GAAM1B,oBAAoB,CAACH,KAAK,CAAE,CAAAqB,QAAA,CAC5C,uBAED,CAAc,CAAC,cACfrE,IAAA,CAACkB,YAAY,EACXG,OAAO,CAAC,QAAQ,CAChBwD,OAAO,CAAEA,CAAA,GAAMnC,iBAAiB,CAACM,KAAK,CAACC,EAAE,CAAE,CAAAoB,QAAA,CAC5C,4BAED,CAAc,CAAC,EACH,CAAC,GA5BDrB,KAAK,CAACC,EA6BX,CACZ,CAAC,CACQ,CACb,EACc,CAAC,CAEtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}