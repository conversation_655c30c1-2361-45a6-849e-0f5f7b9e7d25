{"ast": null, "code": "import{initializeApp}from'firebase/app';import{getAuth}from'firebase/auth';import{getFirestore}from'firebase/firestore';// Configuração do Firebase\n// IMPORTANTE: Substitua essas configurações pelas suas próprias do Firebase Console\n// const firebaseConfig = {\n//   apiKey: \"your-api-key-here\",\n//   authDomain: \"your-project-id.firebaseapp.com\",\n//   projectId: \"your-project-id\",\n//   storageBucket: \"your-project-id.appspot.com\",\n//   messagingSenderId: \"your-sender-id\",\n//   appId: \"your-app-id\"\n// };\nconst firebaseConfig={apiKey:\"AIzaSyBUJQhRgzloPxM9BiWXY_JITyVPmuqplnA\",authDomain:\"partitura-digital.firebaseapp.com\",projectId:\"partitura-digital\",storageBucket:\"partitura-digital.firebasestorage.app\",messagingSenderId:\"235102958549\",appId:\"1:235102958549:web:3e6a5f95d492bc31e9835c\",measurementId:\"G-KQVMBPPNKB\"};// Inicializar Firebase\nconst app=initializeApp(firebaseConfig);// Inicializar serviços\nexport const auth=getAuth(app);export const db=getFirestore(app);export default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "getFirestore", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "app", "auth", "db"], "sources": ["D:/Dev/partitura_digital/src/services/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\n\n// Configuração do Firebase\n// IMPORTANTE: Substitua essas configurações pelas suas próprias do Firebase Console\n// const firebaseConfig = {\n//   apiKey: \"your-api-key-here\",\n//   authDomain: \"your-project-id.firebaseapp.com\",\n//   projectId: \"your-project-id\",\n//   storageBucket: \"your-project-id.appspot.com\",\n//   messagingSenderId: \"your-sender-id\",\n//   appId: \"your-app-id\"\n// };\n\nconst firebaseConfig = {\n  apiKey: \"AIzaSyBUJQhRgzloPxM9BiWXY_JITyVPmuqplnA\",\n  authDomain: \"partitura-digital.firebaseapp.com\",\n  projectId: \"partitura-digital\",\n  storageBucket: \"partitura-digital.firebasestorage.app\",\n  messagingSenderId: \"235102958549\",\n  appId: \"1:235102958549:web:3e6a5f95d492bc31e9835c\",\n  measurementId: \"G-KQVMBPPNKB\"\n};\n\n// Inicializar Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Inicializar serviços\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\n\nexport default app;\n"], "mappings": "AAAA,OAASA,aAAa,KAAQ,cAAc,CAC5C,OAASC,OAAO,KAAQ,eAAe,CACvC,OAASC,YAAY,KAAQ,oBAAoB,CAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAAC,cAAc,CAAG,CACrBC,MAAM,CAAE,yCAAyC,CACjDC,UAAU,CAAE,mCAAmC,CAC/CC,SAAS,CAAE,mBAAmB,CAC9BC,aAAa,CAAE,uCAAuC,CACtDC,iBAAiB,CAAE,cAAc,CACjCC,KAAK,CAAE,2CAA2C,CAClDC,aAAa,CAAE,cACjB,CAAC,CAED;AACA,KAAM,CAAAC,GAAG,CAAGX,aAAa,CAACG,cAAc,CAAC,CAEzC;AACA,MAAO,MAAM,CAAAS,IAAI,CAAGX,OAAO,CAACU,GAAG,CAAC,CAChC,MAAO,MAAM,CAAAE,EAAE,CAAGX,YAAY,CAACS,GAAG,CAAC,CAEnC,cAAe,CAAAA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}