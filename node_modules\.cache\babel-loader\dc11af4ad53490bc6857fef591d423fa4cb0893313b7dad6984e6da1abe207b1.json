{"ast": null, "code": "import parse from './parse.js';\nimport { unsafeStringify } from './stringify.js';\nexport default function v6ToV1(uuid) {\n  const v6Bytes = typeof uuid === 'string' ? parse(uuid) : uuid;\n  const v1Bytes = _v6ToV1(v6Bytes);\n  return typeof uuid === 'string' ? unsafeStringify(v1Bytes) : v1Bytes;\n}\nfunction _v6ToV1(v6Bytes) {\n  return Uint8Array.of((v6Bytes[3] & 0x0f) << 4 | v6Bytes[4] >> 4 & 0x0f, (v6Bytes[4] & 0x0f) << 4 | (v6Bytes[5] & 0xf0) >> 4, (v6Bytes[5] & 0x0f) << 4 | v6Bytes[6] & 0x0f, v6Bytes[7], (v6Bytes[1] & 0x0f) << 4 | (v6Bytes[2] & 0xf0) >> 4, (v6Bytes[2] & 0x0f) << 4 | (v6Bytes[3] & 0xf0) >> 4, 0x10 | (v6Bytes[0] & 0xf0) >> 4, (v6Bytes[0] & 0x0f) << 4 | (v6Bytes[1] & 0xf0) >> 4, v6Bytes[8], v6Bytes[9], v6Bytes[10], v6Bytes[11], v6Bytes[12], v6Bytes[13], v6Bytes[14], v6Bytes[15]);\n}", "map": {"version": 3, "names": ["parse", "unsafeStringify", "v6ToV1", "uuid", "v6Bytes", "v1Bytes", "_v6ToV1", "Uint8Array", "of"], "sources": ["D:/Dev/partitura_digital/node_modules/uuid/dist/esm-browser/v6ToV1.js"], "sourcesContent": ["import parse from './parse.js';\nimport { unsafeStringify } from './stringify.js';\nexport default function v6ToV1(uuid) {\n    const v6Bytes = typeof uuid === 'string' ? parse(uuid) : uuid;\n    const v1Bytes = _v6ToV1(v6Bytes);\n    return typeof uuid === 'string' ? unsafeStringify(v1Bytes) : v1Bytes;\n}\nfunction _v6ToV1(v6Bytes) {\n    return Uint8Array.of(((v6Bytes[3] & 0x0f) << 4) | ((v6Bytes[4] >> 4) & 0x0f), ((v6Bytes[4] & 0x0f) << 4) | ((v6Bytes[5] & 0xf0) >> 4), ((v6Bytes[5] & 0x0f) << 4) | (v6Bytes[6] & 0x0f), v6Bytes[7], ((v6Bytes[1] & 0x0f) << 4) | ((v6Bytes[2] & 0xf0) >> 4), ((v6Bytes[2] & 0x0f) << 4) | ((v6Bytes[3] & 0xf0) >> 4), 0x10 | ((v6Bytes[0] & 0xf0) >> 4), ((v6Bytes[0] & 0x0f) << 4) | ((v6Bytes[1] & 0xf0) >> 4), v6Bytes[8], v6Bytes[9], v6Bytes[10], v6Bytes[11], v6Bytes[12], v6Bytes[13], v6Bytes[14], v6Bytes[15]);\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAC9B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,eAAe,SAASC,MAAMA,CAACC,IAAI,EAAE;EACjC,MAAMC,OAAO,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAGH,KAAK,CAACG,IAAI,CAAC,GAAGA,IAAI;EAC7D,MAAME,OAAO,GAAGC,OAAO,CAACF,OAAO,CAAC;EAChC,OAAO,OAAOD,IAAI,KAAK,QAAQ,GAAGF,eAAe,CAACI,OAAO,CAAC,GAAGA,OAAO;AACxE;AACA,SAASC,OAAOA,CAACF,OAAO,EAAE;EACtB,OAAOG,UAAU,CAACC,EAAE,CAAE,CAACJ,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAMA,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,IAAK,EAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAK,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,EAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAKA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAK,EAAEA,OAAO,CAAC,CAAC,CAAC,EAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAK,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,EAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAK,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,EAAE,IAAI,GAAI,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,EAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAK,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,EAAEA,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,CAAC;AAC5f", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}