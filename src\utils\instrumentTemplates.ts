import { InstrumentType, ClefType, NoteName } from '../types/music';

export interface InstrumentTemplate {
  id: InstrumentType;
  name: string;
  emoji: string;
  clef: ClefType;
  range: {
    lowest: { note: NoteName; octave: number };
    highest: { note: NoteName; octave: number };
  };
  transposition?: number; // Semitons para transposição
  tuning?: string[]; // Para instrumentos de corda
  description: string;
  commonKeys: string[]; // Tonalidades mais comuns
  staffCount: number; // Número de pautas (1 para maioria, 2 para piano)
}

export const INSTRUMENT_TEMPLATES: InstrumentTemplate[] = [
  {
    id: 'piano',
    name: 'Piano',
    emoji: '🎹',
    clef: 'treble',
    range: {
      lowest: { note: 'A', octave: 0 },
      highest: { note: 'C', octave: 8 }
    },
    description: 'Instrumento de teclas com ampla extensão e capacidade polifônica',
    commonKeys: ['C', 'G', 'D', 'A', 'E', 'F', 'Bb', 'Eb', 'Ab'],
    staffCount: 2 // Clave de sol e fá
  },
  {
    id: 'guitar',
    name: 'Violão/Guitarra',
    emoji: '🎸',
    clef: 'treble',
    range: {
      lowest: { note: 'E', octave: 2 },
      highest: { note: 'E', octave: 6 }
    },
    transposition: -12, // Soa uma oitava abaixo do escrito
    tuning: ['E2', 'A2', 'D3', 'G3', 'B3', 'E4'],
    description: 'Instrumento de cordas dedilhadas, versátil para diversos estilos',
    commonKeys: ['E', 'A', 'D', 'G', 'C', 'F', 'B', 'Em', 'Am', 'Dm'],
    staffCount: 1
  },
  {
    id: 'violin',
    name: 'Violino',
    emoji: '🎻',
    clef: 'treble',
    range: {
      lowest: { note: 'G', octave: 3 },
      highest: { note: 'E', octave: 7 }
    },
    tuning: ['G3', 'D4', 'A4', 'E5'],
    description: 'Instrumento de cordas friccionadas, líder da família das cordas',
    commonKeys: ['G', 'D', 'A', 'E', 'C', 'F', 'Bb'],
    staffCount: 1
  },
  {
    id: 'flute',
    name: 'Flauta',
    emoji: '🪈',
    clef: 'treble',
    range: {
      lowest: { note: 'C', octave: 4 },
      highest: { note: 'D', octave: 7 }
    },
    description: 'Instrumento de sopro da família das madeiras, som brilhante',
    commonKeys: ['C', 'G', 'D', 'A', 'F', 'Bb'],
    staffCount: 1
  },
  {
    id: 'trumpet',
    name: 'Trompete',
    emoji: '🎺',
    clef: 'treble',
    range: {
      lowest: { note: 'F#', octave: 3 },
      highest: { note: 'C', octave: 6 }
    },
    transposition: -2, // Instrumento em Bb
    description: 'Instrumento de sopro da família dos metais, som brilhante e penetrante',
    commonKeys: ['Bb', 'F', 'C', 'G', 'D', 'Eb'],
    staffCount: 1
  },
  {
    id: 'drums',
    name: 'Bateria',
    emoji: '🥁',
    clef: 'treble', // Usa clave de sol mas com notação especial
    range: {
      lowest: { note: 'C', octave: 3 },
      highest: { note: 'C', octave: 6 }
    },
    description: 'Conjunto de instrumentos de percussão para ritmo e acompanhamento',
    commonKeys: ['C'], // Bateria não tem tonalidade específica
    staffCount: 1
  },
  {
    id: 'bass',
    name: 'Baixo',
    emoji: '🎸',
    clef: 'bass',
    range: {
      lowest: { note: 'E', octave: 1 },
      highest: { note: 'G', octave: 4 }
    },
    transposition: -12, // Soa uma oitava abaixo
    tuning: ['E1', 'A1', 'D2', 'G2'],
    description: 'Instrumento de cordas graves, base harmônica e rítmica',
    commonKeys: ['E', 'A', 'D', 'G', 'C', 'F', 'B'],
    staffCount: 1
  },
  {
    id: 'other',
    name: 'Outro',
    emoji: '🎵',
    clef: 'treble',
    range: {
      lowest: { note: 'C', octave: 3 },
      highest: { note: 'C', octave: 6 }
    },
    description: 'Configuração genérica para outros instrumentos',
    commonKeys: ['C', 'G', 'D', 'A', 'E', 'F', 'Bb'],
    staffCount: 1
  }
];

// Função para obter template por ID
export const getInstrumentTemplate = (id: InstrumentType): InstrumentTemplate => {
  return INSTRUMENT_TEMPLATES.find(template => template.id === id) || INSTRUMENT_TEMPLATES[INSTRUMENT_TEMPLATES.length - 1];
};

// Função para obter todas as categorias de instrumentos
export const getInstrumentCategories = () => {
  return {
    keyboard: ['piano'],
    strings: ['guitar', 'violin', 'bass'],
    winds: ['flute', 'trumpet'],
    percussion: ['drums'],
    other: ['other']
  };
};

// Função para verificar se uma nota está no range do instrumento
export const isNoteInRange = (
  note: NoteName, 
  octave: number, 
  instrument: InstrumentType
): boolean => {
  const template = getInstrumentTemplate(instrument);
  const { lowest, highest } = template.range;
  
  // Converter notas para números para comparação
  const noteToNumber = (noteName: NoteName, oct: number): number => {
    const noteValues = { C: 0, D: 2, E: 4, F: 5, G: 7, A: 9, B: 11 };
    return oct * 12 + noteValues[noteName];
  };
  
  const noteNumber = noteToNumber(note, octave);
  const lowestNumber = noteToNumber(lowest.note, lowest.octave);
  const highestNumber = noteToNumber(highest.note, highest.octave);
  
  return noteNumber >= lowestNumber && noteNumber <= highestNumber;
};

// Função para sugerir tonalidade baseada no instrumento
export const suggestKeyForInstrument = (instrument: InstrumentType): string => {
  const template = getInstrumentTemplate(instrument);
  return template.commonKeys[0]; // Retorna a tonalidade mais comum
};

// Função para obter informações de afinação
export const getTuningInfo = (instrument: InstrumentType): string[] | null => {
  const template = getInstrumentTemplate(instrument);
  return template.tuning || null;
};

// Função para aplicar transposição do instrumento
export const applyInstrumentTransposition = (
  note: NoteName, 
  octave: number, 
  instrument: InstrumentType
): { note: NoteName; octave: number } => {
  const template = getInstrumentTemplate(instrument);
  
  if (!template.transposition) {
    return { note, octave };
  }
  
  // Lógica de transposição (simplificada)
  const noteValues = { C: 0, D: 2, E: 4, F: 5, G: 7, A: 9, B: 11 };
  const noteNames: NoteName[] = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
  
  let noteNumber = octave * 12 + noteValues[note];
  noteNumber += template.transposition;
  
  const newOctave = Math.floor(noteNumber / 12);
  const newNoteIndex = noteNumber % 12;
  const newNote = noteNames[newNoteIndex] as NoteName;
  
  return { note: newNote, octave: newOctave };
};
