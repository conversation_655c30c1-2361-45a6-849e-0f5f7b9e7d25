{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ScoreEditor\\\\ScoreEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { ScoreService } from '../../services/scoreService';\nimport { ChordView } from '../ChordView/ChordView';\nimport { MusicalNote as MusicalNoteComponent } from '../MusicalNote/MusicalNote';\nimport { LyricsEditor } from '../LyricsEditor/LyricsEditor';\nimport { InstrumentSelector } from '../InstrumentSelector/InstrumentSelector';\nimport { getInstrumentTemplate } from '../../utils/instrumentTemplates';\nimport { v4 as uuidv4 } from 'uuid';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n_c = EditorContainer;\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c2 = EditorHeader;\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n_c3 = EditorTitle;\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n_c4 = EditorActions;\nconst ActionButton = styled.button`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c5 = ActionButton;\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n_c6 = EditorContent;\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n_c7 = ToolPanel;\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n_c8 = ToolSection;\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n_c9 = ToolGrid;\nconst ToolButton = styled.button`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n_c0 = ToolButton;\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n_c1 = ScoreCanvas;\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n_c10 = StaffContainer;\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n_c11 = Staff;\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n_c12 = GuestWarning;\nexport const ScoreEditor = ({\n  scoreId,\n  initialConfig\n}) => {\n  _s();\n  const {\n    currentUser\n  } = useAuth();\n  const [title, setTitle] = useState((initialConfig === null || initialConfig === void 0 ? void 0 : initialConfig.title) || 'Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState('note');\n  const [selectedDuration, setSelectedDuration] = useState('quarter');\n  const [selectedNote, setSelectedNote] = useState('C');\n  const [selectedAccidental, setSelectedAccidental] = useState(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [placedNotes, setPlacedNotes] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showChords, setShowChords] = useState(false);\n  const [showLyrics, setShowLyrics] = useState(false);\n  const [lastSaved, setLastSaved] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(1);\n  const [lyrics, setLyrics] = useState([]);\n  const [selectedInstrument, setSelectedInstrument] = useState((initialConfig === null || initialConfig === void 0 ? void 0 : initialConfig.instrument) || 'piano');\n\n  // Carregar partitura existente\n  React.useEffect(() => {\n    if (scoreId && currentUser) {\n      loadScore();\n    }\n  }, [scoreId, currentUser]);\n\n  // Salvamento automático\n  React.useEffect(() => {\n    if (!currentUser || !scoreId || placedNotes.length === 0) return;\n    const autoSaveTimer = setTimeout(() => {\n      handleSave(true); // true indica salvamento automático\n    }, 2000); // Salvar após 2 segundos de inatividade\n\n    return () => clearTimeout(autoSaveTimer);\n  }, [placedNotes, lyrics, title, currentUser, scoreId]);\n  const loadScore = async () => {\n    if (!scoreId || !currentUser) return;\n    setIsLoading(true);\n    try {\n      const score = await ScoreService.getScore(scoreId);\n      if (score && score.userId === currentUser.uid) {\n        setTitle(score.title);\n\n        // Extrair notas de todos os compassos\n        const allNotes = [];\n        score.staffs.forEach(staff => {\n          staff.measures.forEach(measure => {\n            allNotes.push(...measure.notes);\n          });\n        });\n        setPlacedNotes(allNotes);\n        setLyrics(score.lyrics || []);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar partitura:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSave = async (isAutoSave = false) => {\n    if (!currentUser) {\n      if (!isAutoSave) {\n        alert('Você precisa estar logado para salvar partituras!');\n      }\n      return;\n    }\n    setIsSaving(true);\n    try {\n      // Organizar notas por compasso\n      const measureMap = new Map();\n      placedNotes.forEach(note => {\n        const measure = note.position.measure;\n        if (!measureMap.has(measure)) {\n          measureMap.set(measure, []);\n        }\n        measureMap.get(measure).push(note);\n      });\n\n      // Criar compassos com as notas\n      const measures = Array.from(measureMap.entries()).map(([measureNumber, notes]) => ({\n        id: uuidv4(),\n        number: measureNumber,\n        timeSignature: {\n          numerator: 4,\n          denominator: 4\n        },\n        notes: notes.sort((a, b) => a.position.beat - b.position.beat),\n        chords: []\n      }));\n\n      // Criar dados da partitura\n      const scoreData = {\n        title,\n        composer: initialConfig === null || initialConfig === void 0 ? void 0 : initialConfig.composer,\n        key: {\n          note: (initialConfig === null || initialConfig === void 0 ? void 0 : initialConfig.key) || 'C',\n          mode: (initialConfig === null || initialConfig === void 0 ? void 0 : initialConfig.keyMode) || 'major'\n        },\n        timeSignature: {\n          numerator: (initialConfig === null || initialConfig === void 0 ? void 0 : initialConfig.timeSignatureNum) || 4,\n          denominator: (initialConfig === null || initialConfig === void 0 ? void 0 : initialConfig.timeSignatureDen) || 4\n        },\n        tempo: (initialConfig === null || initialConfig === void 0 ? void 0 : initialConfig.tempo) || 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: getInstrumentTemplate(selectedInstrument).clef,\n          instrument: selectedInstrument,\n          measures\n        }],\n        lyrics,\n        userId: currentUser.uid\n      };\n      if (scoreId) {\n        // Para update, removemos o userId pois não deve ser alterado\n        const {\n          userId,\n          ...updateData\n        } = scoreData;\n        await ScoreService.updateScore(scoreId, updateData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n      setLastSaved(new Date());\n      if (!isAutoSave) {\n        alert('Partitura salva com sucesso!');\n      }\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      if (!isAutoSave) {\n        alert('Erro ao salvar partitura');\n      }\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n  const handleStaffClick = event => {\n    var _event$currentTarget$;\n    const rect = event.currentTarget.getBoundingClientRect();\n    const svgRect = (_event$currentTarget$ = event.currentTarget.closest('svg')) === null || _event$currentTarget$ === void 0 ? void 0 : _event$currentTarget$.getBoundingClientRect();\n    if (!svgRect) return;\n    const x = event.clientX - svgRect.left;\n    const y = event.clientY - svgRect.top;\n\n    // Calcular a linha da pauta baseada na posição Y\n    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta\n    const notePositions = [{\n      y: 40,\n      note: 'A',\n      octave: 5\n    },\n    // Acima da pauta\n    {\n      y: 50,\n      note: 'G',\n      octave: 5\n    },\n    // 5ª linha\n    {\n      y: 60,\n      note: 'F',\n      octave: 5\n    },\n    // Entre 4ª e 5ª\n    {\n      y: 70,\n      note: 'E',\n      octave: 5\n    },\n    // 4ª linha\n    {\n      y: 80,\n      note: 'D',\n      octave: 5\n    },\n    // Entre 3ª e 4ª\n    {\n      y: 90,\n      note: 'C',\n      octave: 5\n    },\n    // 3ª linha (Dó central)\n    {\n      y: 100,\n      note: 'B',\n      octave: 4\n    },\n    // Entre 2ª e 3ª\n    {\n      y: 110,\n      note: 'A',\n      octave: 4\n    },\n    // 2ª linha\n    {\n      y: 120,\n      note: 'G',\n      octave: 4\n    },\n    // Entre 1ª e 2ª\n    {\n      y: 130,\n      note: 'F',\n      octave: 4\n    },\n    // 1ª linha\n    {\n      y: 140,\n      note: 'E',\n      octave: 4\n    } // Abaixo da pauta\n    ];\n\n    // Encontrar a posição mais próxima\n    const closestPosition = notePositions.reduce((closest, current) => {\n      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;\n    });\n\n    // Calcular a posição horizontal (compasso e tempo)\n    const measureWidth = 150; // Largura aproximada de um compasso\n    const startX = 120; // Início da área de notas\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = relativeX % measureWidth / measureWidth * 4; // 4 tempos por compasso\n\n    // Criar nova nota\n    const newNote = {\n      id: uuidv4(),\n      name: selectedNote,\n      // Usar a nota selecionada no painel\n      octave: closestPosition.octave,\n      duration: selectedDuration,\n      accidental: selectedAccidental || undefined,\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4,\n        // Arredondar para 1/4 de tempo\n        staff: 0\n      },\n      isRest: selectedTool === 'rest'\n    };\n    setPlacedNotes(prev => [...prev, newNote]);\n  };\n  const durations = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(EditorContainer, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '400px',\n          fontSize: '1.2rem',\n          color: '#666'\n        },\n        children: \"\\uD83C\\uDFBC Carregando partitura...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(EditorContainer, {\n    children: [/*#__PURE__*/_jsxDEV(EditorHeader, {\n      children: [/*#__PURE__*/_jsxDEV(EditorTitle, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Editor de Partituras\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: title,\n            onChange: e => setTitle(e.target.value),\n            placeholder: \"Nome da partitura...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), currentUser && lastSaved && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              color: 'rgba(255,255,255,0.8)'\n            },\n            children: [\"\\uD83D\\uDCBE Salvo \\xE0s \", lastSaved.toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorActions, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setZoomLevel(prev => Math.max(0.5, prev - 0.1)),\n          variant: \"primary\",\n          disabled: zoomLevel <= 0.5,\n          children: \"\\uD83D\\uDD0D\\u2796\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setZoomLevel(prev => Math.min(2, prev + 0.1)),\n          variant: \"primary\",\n          disabled: zoomLevel >= 2,\n          children: \"\\uD83D\\uDD0D\\u2795\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => {\n            setShowChords(!showChords);\n            setShowLyrics(false);\n          },\n          variant: \"primary\",\n          children: showChords ? '🎼 Partitura' : '🎸 Cifras'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => {\n            setShowLyrics(!showLyrics);\n            setShowChords(false);\n          },\n          variant: \"primary\",\n          children: showLyrics ? '🎼 Partitura' : '🎤 Letras'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setPlacedNotes([]),\n          variant: \"primary\",\n          disabled: placedNotes.length === 0,\n          children: \"\\uD83D\\uDDD1\\uFE0F Limpar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handlePlay,\n          variant: \"primary\",\n          children: isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handleSave,\n          variant: \"secondary\",\n          disabled: !currentUser || isSaving,\n          children: isSaving ? '💾 Salvando...' : '💾 Salvar'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditorContent, {\n      children: [/*#__PURE__*/_jsxDEV(ToolPanel, {\n        children: [!currentUser && /*#__PURE__*/_jsxDEV(GuestWarning, {\n          children: \"\\u26A0\\uFE0F Modo visitante: suas altera\\xE7\\xF5es n\\xE3o ser\\xE3o salvas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(InstrumentSelector, {\n          selectedInstrument: selectedInstrument,\n          onInstrumentChange: setSelectedInstrument,\n          compact: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFB5 Ferramentas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: [/*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'note',\n              onClick: () => setSelectedTool('note'),\n              children: \"\\uD83C\\uDFB5 Nota\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'rest',\n              onClick: () => setSelectedTool('rest'),\n              children: \"\\uD83C\\uDFBC Pausa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'chord',\n              onClick: () => setSelectedTool('chord'),\n              children: \"\\uD83C\\uDFB9 Acorde\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u23F1\\uFE0F Dura\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: durations.map(duration => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedDuration === duration,\n              onClick: () => setSelectedDuration(duration),\n              children: duration === 'whole' ? '𝅝' : duration === 'half' ? '𝅗𝅥' : duration === 'quarter' ? '♩' : duration === 'eighth' ? '♫' : '♬'\n            }, duration, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFBC Notas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: notes.map(note => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedNote === note,\n              onClick: () => setSelectedNote(note),\n              children: note\n            }, note, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u266F\\u266D Acidentes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: [/*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === 'sharp',\n              onClick: () => setSelectedAccidental(selectedAccidental === 'sharp' ? null : 'sharp'),\n              children: \"\\u266F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === 'flat',\n              onClick: () => setSelectedAccidental(selectedAccidental === 'flat' ? null : 'flat'),\n              children: \"\\u266D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === 'natural',\n              onClick: () => setSelectedAccidental(selectedAccidental === 'natural' ? null : 'natural'),\n              children: \"\\u266E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === null,\n              onClick: () => setSelectedAccidental(null),\n              children: \"\\u2014\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCCA Estat\\xEDsticas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.9rem',\n              color: '#666',\n              lineHeight: '1.4'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83C\\uDFBC Instrumento: \", getInstrumentTemplate(selectedInstrument).name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83D\\uDCDD Notas: \", placedNotes.filter(n => !n.isRest).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u23F8\\uFE0F Pausas: \", placedNotes.filter(n => n.isRest).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83C\\uDFB5 Total: \", placedNotes.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83D\\uDCCF Compassos: \", Math.max(0, ...placedNotes.map(n => n.position.measure), 0)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u266F Sustenidos: \", placedNotes.filter(n => n.accidental === 'sharp').length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u266D Bem\\xF3is: \", placedNotes.filter(n => n.accidental === 'flat').length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83C\\uDFA4 Letras: \", lyrics.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83D\\uDD0D Zoom: \", Math.round(zoomLevel * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScoreCanvas, {\n        children: showChords ? /*#__PURE__*/_jsxDEV(ChordView, {\n          notes: placedNotes,\n          title: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 13\n        }, this) : showLyrics ? /*#__PURE__*/_jsxDEV(LyricsEditor, {\n          lyrics: lyrics,\n          notes: placedNotes,\n          onLyricsChange: setLyrics,\n          title: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(StaffContainer, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                color: '#495057'\n              },\n              children: [\"Partitura: \", title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.9rem',\n                color: '#666'\n              },\n              children: [\"Zoom: \", Math.round(zoomLevel * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Staff, {\n            viewBox: \"0 0 800 200\",\n            style: {\n              transform: `scale(${zoomLevel})`,\n              transformOrigin: 'top left'\n            },\n            children: [[0, 1, 2, 3, 4].map(line => /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"50\",\n              y1: 50 + line * 20,\n              x2: \"750\",\n              y2: 50 + line * 20,\n              stroke: \"#333\",\n              strokeWidth: \"1\"\n            }, line, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"20\",\n              y: \"90\",\n              fontSize: \"40\",\n              fill: \"#333\",\n              children: getInstrumentTemplate(selectedInstrument).clef === 'treble' ? '𝄞' : getInstrumentTemplate(selectedInstrument).clef === 'bass' ? '𝄢' : getInstrumentTemplate(selectedInstrument).clef === 'alto' ? '𝄡' : '𝄞'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"75\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 637,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"95\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"110\",\n              y1: \"50\",\n              x2: \"110\",\n              y2: \"130\",\n              stroke: \"#333\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this), [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: 120 + measure * 150,\n              y1: \"50\",\n              x2: 120 + measure * 150,\n              y2: \"130\",\n              stroke: \"#999\",\n              strokeWidth: \"1\"\n            }, measure, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 17\n            }, this)), [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"text\", {\n              x: 120 + (measure - 1) * 150 + 75,\n              y: \"35\",\n              fontSize: \"12\",\n              fill: \"#666\",\n              textAnchor: \"middle\",\n              children: measure\n            }, measure, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"120\",\n              y: \"40\",\n              width: \"600\",\n              height: \"100\",\n              fill: \"transparent\",\n              style: {\n                cursor: 'crosshair'\n              },\n              onClick: handleStaffClick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 15\n            }, this), placedNotes.map(note => {\n              const x = 120 + (note.position.measure - 1) * 150 + note.position.beat * 30;\n              const notePositions = {\n                'A5': 40,\n                'G5': 50,\n                'F5': 60,\n                'E5': 70,\n                'D5': 80,\n                'C5': 90,\n                'B4': 100,\n                'A4': 110,\n                'G4': 120,\n                'F4': 130,\n                'E4': 140\n              };\n              const y = notePositions[`${note.name}${note.octave}`] || 90;\n              return /*#__PURE__*/_jsxDEV(MusicalNoteComponent, {\n                note: note,\n                x: x,\n                y: y,\n                onRemove: () => setPlacedNotes(prev => prev.filter(n => n.id !== note.id))\n              }, note.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 19\n              }, this);\n            }), placedNotes.length === 0 && /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"100\",\n              fontSize: \"14\",\n              fill: \"#999\",\n              textAnchor: \"middle\",\n              children: \"Clique na pauta para adicionar notas \\u2022 Clique em uma nota para remov\\xEA-la\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 406,\n    columnNumber: 5\n  }, this);\n};\n_s(ScoreEditor, \"Qi45ndtwZen9EQBQqKs5NEwxAoM=\", false, function () {\n  return [useAuth];\n});\n_c13 = ScoreEditor;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"EditorContainer\");\n$RefreshReg$(_c2, \"EditorHeader\");\n$RefreshReg$(_c3, \"EditorTitle\");\n$RefreshReg$(_c4, \"EditorActions\");\n$RefreshReg$(_c5, \"ActionButton\");\n$RefreshReg$(_c6, \"EditorContent\");\n$RefreshReg$(_c7, \"ToolPanel\");\n$RefreshReg$(_c8, \"ToolSection\");\n$RefreshReg$(_c9, \"ToolGrid\");\n$RefreshReg$(_c0, \"ToolButton\");\n$RefreshReg$(_c1, \"ScoreCanvas\");\n$RefreshReg$(_c10, \"StaffContainer\");\n$RefreshReg$(_c11, \"Staff\");\n$RefreshReg$(_c12, \"GuestWarning\");\n$RefreshReg$(_c13, \"ScoreEditor\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "ScoreService", "ChordView", "MusicalNote", "MusicalNoteComponent", "LyricsEditor", "InstrumentSelector", "getInstrumentTemplate", "v4", "uuidv4", "jsxDEV", "_jsxDEV", "EditorC<PERSON><PERSON>", "div", "_c", "Editor<PERSON><PERSON>er", "_c2", "EditorT<PERSON>le", "_c3", "EditorActions", "_c4", "ActionButton", "button", "props", "variant", "_c5", "Editor<PERSON><PERSON><PERSON>", "_c6", "ToolPanel", "_c7", "ToolSection", "_c8", "ToolGrid", "_c9", "<PERSON><PERSON><PERSON><PERSON>on", "active", "_c0", "ScoreCanvas", "_c1", "StaffC<PERSON>r", "_c10", "Staff", "svg", "_c11", "Guest<PERSON><PERSON>ning", "_c12", "ScoreEditor", "scoreId", "initialConfig", "_s", "currentUser", "title", "setTitle", "selectedTool", "setSelectedTool", "selectedDuration", "setSelectedDuration", "selected<PERSON><PERSON>", "setSelectedNote", "selectedAccidental", "setSelectedAccidental", "isPlaying", "setIsPlaying", "isSaving", "setIsSaving", "placedNotes", "setPlacedNotes", "isLoading", "setIsLoading", "showChords", "setShowChords", "showLyrics", "setShowLyrics", "lastSaved", "setLastSaved", "zoomLevel", "setZoomLevel", "lyrics", "setLyrics", "selectedInstrument", "setSelectedInstrument", "instrument", "useEffect", "loadScore", "length", "autoSaveTimer", "setTimeout", "handleSave", "clearTimeout", "score", "getScore", "userId", "uid", "allNotes", "staffs", "for<PERSON>ach", "staff", "measures", "measure", "push", "notes", "error", "console", "isAutoSave", "alert", "measureMap", "Map", "note", "position", "has", "set", "get", "Array", "from", "entries", "map", "measureNumber", "id", "number", "timeSignature", "numerator", "denominator", "sort", "a", "b", "beat", "chords", "scoreData", "composer", "key", "mode", "keyMode", "timeSignatureNum", "timeSignatureDen", "tempo", "clef", "updateData", "updateScore", "createScore", "Date", "handlePlay", "handleStaffClick", "event", "_event$currentTarget$", "rect", "currentTarget", "getBoundingClientRect", "svgRect", "closest", "x", "clientX", "left", "y", "clientY", "top", "staffLines", "notePositions", "octave", "closestPosition", "reduce", "current", "Math", "abs", "measureWidth", "startX", "relativeX", "max", "floor", "beatPosition", "newNote", "name", "duration", "accidental", "undefined", "round", "isRest", "prev", "durations", "children", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "type", "value", "onChange", "e", "target", "placeholder", "toLocaleTimeString", "onClick", "disabled", "min", "onInstrumentChange", "compact", "lineHeight", "filter", "n", "onLyricsChange", "marginBottom", "margin", "viewBox", "transform", "transform<PERSON><PERSON>in", "line", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "fill", "textAnchor", "width", "cursor", "onRemove", "_c13", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ScoreEditor/ScoreEditor.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Score, MusicalNote, NoteName, NoteDuration, ClefType, InstrumentType, Octave, Lyrics } from '../../types/music';\nimport { ScoreService } from '../../services/scoreService';\nimport { ChordView } from '../ChordView/ChordView';\nimport { MusicalNote as MusicalNoteComponent } from '../MusicalNote/MusicalNote';\nimport { LyricsEditor } from '../LyricsEditor/LyricsEditor';\nimport { InstrumentSelector } from '../InstrumentSelector/InstrumentSelector';\nimport { getInstrumentTemplate } from '../../utils/instrumentTemplates';\nimport { ScoreConfig } from '../../pages/NewScore/NewScorePage';\nimport { v4 as uuidv4 } from 'uuid';\n\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n\nconst ToolButton = styled.button<{ active?: boolean }>`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n\ninterface ScoreEditorProps {\n  scoreId?: string;\n  initialConfig?: ScoreConfig;\n}\n\nexport const ScoreEditor: React.FC<ScoreEditorProps> = ({ scoreId, initialConfig }) => {\n  const { currentUser } = useAuth();\n  const [title, setTitle] = useState(initialConfig?.title || 'Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState<'note' | 'rest' | 'chord'>('note');\n  const [selectedDuration, setSelectedDuration] = useState<NoteDuration>('quarter');\n  const [selectedNote, setSelectedNote] = useState<NoteName>('C');\n  const [selectedAccidental, setSelectedAccidental] = useState<'sharp' | 'flat' | 'natural' | null>(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [placedNotes, setPlacedNotes] = useState<MusicalNote[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showChords, setShowChords] = useState(false);\n  const [showLyrics, setShowLyrics] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\n  const [zoomLevel, setZoomLevel] = useState(1);\n  const [lyrics, setLyrics] = useState<Lyrics[]>([]);\n  const [selectedInstrument, setSelectedInstrument] = useState<InstrumentType>(initialConfig?.instrument || 'piano');\n\n  // Carregar partitura existente\n  React.useEffect(() => {\n    if (scoreId && currentUser) {\n      loadScore();\n    }\n  }, [scoreId, currentUser]);\n\n  // Salvamento automático\n  React.useEffect(() => {\n    if (!currentUser || !scoreId || placedNotes.length === 0) return;\n\n    const autoSaveTimer = setTimeout(() => {\n      handleSave(true); // true indica salvamento automático\n    }, 2000); // Salvar após 2 segundos de inatividade\n\n    return () => clearTimeout(autoSaveTimer);\n  }, [placedNotes, lyrics, title, currentUser, scoreId]);\n\n  const loadScore = async () => {\n    if (!scoreId || !currentUser) return;\n\n    setIsLoading(true);\n    try {\n      const score = await ScoreService.getScore(scoreId);\n      if (score && score.userId === currentUser.uid) {\n        setTitle(score.title);\n\n        // Extrair notas de todos os compassos\n        const allNotes: MusicalNote[] = [];\n        score.staffs.forEach(staff => {\n          staff.measures.forEach(measure => {\n            allNotes.push(...measure.notes);\n          });\n        });\n\n        setPlacedNotes(allNotes);\n        setLyrics(score.lyrics || []);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar partitura:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSave = async (isAutoSave = false) => {\n    if (!currentUser) {\n      if (!isAutoSave) {\n        alert('Você precisa estar logado para salvar partituras!');\n      }\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      // Organizar notas por compasso\n      const measureMap = new Map<number, MusicalNote[]>();\n      placedNotes.forEach(note => {\n        const measure = note.position.measure;\n        if (!measureMap.has(measure)) {\n          measureMap.set(measure, []);\n        }\n        measureMap.get(measure)!.push(note);\n      });\n\n      // Criar compassos com as notas\n      const measures = Array.from(measureMap.entries()).map(([measureNumber, notes]) => ({\n        id: uuidv4(),\n        number: measureNumber,\n        timeSignature: { numerator: 4, denominator: 4 },\n        notes: notes.sort((a, b) => a.position.beat - b.position.beat),\n        chords: []\n      }));\n\n      // Criar dados da partitura\n      const scoreData = {\n        title,\n        composer: initialConfig?.composer,\n        key: {\n          note: initialConfig?.key || 'C' as NoteName,\n          mode: initialConfig?.keyMode || 'major' as const\n        },\n        timeSignature: {\n          numerator: initialConfig?.timeSignatureNum || 4,\n          denominator: initialConfig?.timeSignatureDen || 4\n        },\n        tempo: initialConfig?.tempo || 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: getInstrumentTemplate(selectedInstrument).clef,\n          instrument: selectedInstrument,\n          measures\n        }],\n        lyrics,\n        userId: currentUser.uid\n      };\n\n      if (scoreId) {\n        // Para update, removemos o userId pois não deve ser alterado\n        const { userId, ...updateData } = scoreData;\n        await ScoreService.updateScore(scoreId, updateData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n\n      setLastSaved(new Date());\n      if (!isAutoSave) {\n        alert('Partitura salva com sucesso!');\n      }\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      if (!isAutoSave) {\n        alert('Erro ao salvar partitura');\n      }\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n\n  const handleStaffClick = (event: React.MouseEvent<SVGRectElement>) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const svgRect = event.currentTarget.closest('svg')?.getBoundingClientRect();\n\n    if (!svgRect) return;\n\n    const x = event.clientX - svgRect.left;\n    const y = event.clientY - svgRect.top;\n\n    // Calcular a linha da pauta baseada na posição Y\n    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta\n    const notePositions = [\n      { y: 40, note: 'A' as NoteName, octave: 5 as Octave }, // Acima da pauta\n      { y: 50, note: 'G' as NoteName, octave: 5 as Octave }, // 5ª linha\n      { y: 60, note: 'F' as NoteName, octave: 5 as Octave }, // Entre 4ª e 5ª\n      { y: 70, note: 'E' as NoteName, octave: 5 as Octave }, // 4ª linha\n      { y: 80, note: 'D' as NoteName, octave: 5 as Octave }, // Entre 3ª e 4ª\n      { y: 90, note: 'C' as NoteName, octave: 5 as Octave }, // 3ª linha (Dó central)\n      { y: 100, note: 'B' as NoteName, octave: 4 as Octave }, // Entre 2ª e 3ª\n      { y: 110, note: 'A' as NoteName, octave: 4 as Octave }, // 2ª linha\n      { y: 120, note: 'G' as NoteName, octave: 4 as Octave }, // Entre 1ª e 2ª\n      { y: 130, note: 'F' as NoteName, octave: 4 as Octave }, // 1ª linha\n      { y: 140, note: 'E' as NoteName, octave: 4 as Octave }, // Abaixo da pauta\n    ];\n\n    // Encontrar a posição mais próxima\n    const closestPosition = notePositions.reduce((closest, current) => {\n      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;\n    });\n\n    // Calcular a posição horizontal (compasso e tempo)\n    const measureWidth = 150; // Largura aproximada de um compasso\n    const startX = 120; // Início da área de notas\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = ((relativeX % measureWidth) / measureWidth) * 4; // 4 tempos por compasso\n\n    // Criar nova nota\n    const newNote: MusicalNote = {\n      id: uuidv4(),\n      name: selectedNote, // Usar a nota selecionada no painel\n      octave: closestPosition.octave,\n      duration: selectedDuration,\n      accidental: selectedAccidental || undefined,\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4, // Arredondar para 1/4 de tempo\n        staff: 0\n      },\n      isRest: selectedTool === 'rest'\n    };\n\n    setPlacedNotes(prev => [...prev, newNote]);\n  };\n\n  const durations: NoteDuration[] = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes: NoteName[] = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n\n  if (isLoading) {\n    return (\n      <EditorContainer>\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '400px',\n          fontSize: '1.2rem',\n          color: '#666'\n        }}>\n          🎼 Carregando partitura...\n        </div>\n      </EditorContainer>\n    );\n  }\n\n  return (\n    <EditorContainer>\n      <EditorHeader>\n        <EditorTitle>\n          <h2>Editor de Partituras</h2>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n            <input\n              type=\"text\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              placeholder=\"Nome da partitura...\"\n            />\n            {currentUser && lastSaved && (\n              <div style={{ fontSize: '0.8rem', color: 'rgba(255,255,255,0.8)' }}>\n                💾 Salvo às {lastSaved.toLocaleTimeString()}\n              </div>\n            )}\n          </div>\n        </EditorTitle>\n        <EditorActions>\n          <ActionButton\n            onClick={() => setZoomLevel(prev => Math.max(0.5, prev - 0.1))}\n            variant=\"primary\"\n            disabled={zoomLevel <= 0.5}\n          >\n            🔍➖\n          </ActionButton>\n          <ActionButton\n            onClick={() => setZoomLevel(prev => Math.min(2, prev + 0.1))}\n            variant=\"primary\"\n            disabled={zoomLevel >= 2}\n          >\n            🔍➕\n          </ActionButton>\n          <ActionButton\n            onClick={() => {\n              setShowChords(!showChords);\n              setShowLyrics(false);\n            }}\n            variant=\"primary\"\n          >\n            {showChords ? '🎼 Partitura' : '🎸 Cifras'}\n          </ActionButton>\n          <ActionButton\n            onClick={() => {\n              setShowLyrics(!showLyrics);\n              setShowChords(false);\n            }}\n            variant=\"primary\"\n          >\n            {showLyrics ? '🎼 Partitura' : '🎤 Letras'}\n          </ActionButton>\n          <ActionButton\n            onClick={() => setPlacedNotes([])}\n            variant=\"primary\"\n            disabled={placedNotes.length === 0}\n          >\n            🗑️ Limpar\n          </ActionButton>\n          <ActionButton onClick={handlePlay} variant=\"primary\">\n            {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}\n          </ActionButton>\n          <ActionButton\n            onClick={handleSave}\n            variant=\"secondary\"\n            disabled={!currentUser || isSaving}\n          >\n            {isSaving ? '💾 Salvando...' : '💾 Salvar'}\n          </ActionButton>\n        </EditorActions>\n      </EditorHeader>\n\n      <EditorContent>\n        <ToolPanel>\n          {!currentUser && (\n            <GuestWarning>\n              ⚠️ Modo visitante: suas alterações não serão salvas\n            </GuestWarning>\n          )}\n\n          <InstrumentSelector\n            selectedInstrument={selectedInstrument}\n            onInstrumentChange={setSelectedInstrument}\n            compact={true}\n          />\n          \n          <ToolSection>\n            <h3>🎵 Ferramentas</h3>\n            <ToolGrid>\n              <ToolButton \n                active={selectedTool === 'note'}\n                onClick={() => setSelectedTool('note')}\n              >\n                🎵 Nota\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'rest'}\n                onClick={() => setSelectedTool('rest')}\n              >\n                🎼 Pausa\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'chord'}\n                onClick={() => setSelectedTool('chord')}\n              >\n                🎹 Acorde\n              </ToolButton>\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>⏱️ Duração</h3>\n            <ToolGrid>\n              {durations.map(duration => (\n                <ToolButton\n                  key={duration}\n                  active={selectedDuration === duration}\n                  onClick={() => setSelectedDuration(duration)}\n                >\n                  {duration === 'whole' ? '𝅝' : \n                   duration === 'half' ? '𝅗𝅥' :\n                   duration === 'quarter' ? '♩' :\n                   duration === 'eighth' ? '♫' : '♬'}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>🎼 Notas</h3>\n            <ToolGrid>\n              {notes.map(note => (\n                <ToolButton\n                  key={note}\n                  active={selectedNote === note}\n                  onClick={() => setSelectedNote(note)}\n                >\n                  {note}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>♯♭ Acidentes</h3>\n            <ToolGrid>\n              <ToolButton\n                active={selectedAccidental === 'sharp'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'sharp' ? null : 'sharp')}\n              >\n                ♯\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === 'flat'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'flat' ? null : 'flat')}\n              >\n                ♭\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === 'natural'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'natural' ? null : 'natural')}\n              >\n                ♮\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === null}\n                onClick={() => setSelectedAccidental(null)}\n              >\n                —\n              </ToolButton>\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>📊 Estatísticas</h3>\n            <div style={{ fontSize: '0.9rem', color: '#666', lineHeight: '1.4' }}>\n              <div>🎼 Instrumento: {getInstrumentTemplate(selectedInstrument).name}</div>\n              <div>📝 Notas: {placedNotes.filter(n => !n.isRest).length}</div>\n              <div>⏸️ Pausas: {placedNotes.filter(n => n.isRest).length}</div>\n              <div>🎵 Total: {placedNotes.length}</div>\n              <div>📏 Compassos: {Math.max(0, ...placedNotes.map(n => n.position.measure), 0)}</div>\n              <div>♯ Sustenidos: {placedNotes.filter(n => n.accidental === 'sharp').length}</div>\n              <div>♭ Bemóis: {placedNotes.filter(n => n.accidental === 'flat').length}</div>\n              <div>🎤 Letras: {lyrics.length}</div>\n              <div>🔍 Zoom: {Math.round(zoomLevel * 100)}%</div>\n            </div>\n          </ToolSection>\n        </ToolPanel>\n\n        <ScoreCanvas>\n          {showChords ? (\n            <ChordView notes={placedNotes} title={title} />\n          ) : showLyrics ? (\n            <LyricsEditor\n              lyrics={lyrics}\n              notes={placedNotes}\n              onLyricsChange={setLyrics}\n              title={title}\n            />\n          ) : (\n            <StaffContainer>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>\n                <h3 style={{ margin: 0, color: '#495057' }}>Partitura: {title}</h3>\n                <div style={{ fontSize: '0.9rem', color: '#666' }}>\n                  Zoom: {Math.round(zoomLevel * 100)}%\n                </div>\n              </div>\n              <Staff\n                viewBox=\"0 0 800 200\"\n                style={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}\n              >\n              {/* Linhas da pauta */}\n              {[0, 1, 2, 3, 4].map(line => (\n                <line\n                  key={line}\n                  x1=\"50\"\n                  y1={50 + line * 20}\n                  x2=\"750\"\n                  y2={50 + line * 20}\n                  stroke=\"#333\"\n                  strokeWidth=\"1\"\n                />\n              ))}\n              \n              {/* Clave baseada no instrumento */}\n              <text x=\"20\" y=\"90\" fontSize=\"40\" fill=\"#333\">\n                {getInstrumentTemplate(selectedInstrument).clef === 'treble' ? '𝄞' :\n                 getInstrumentTemplate(selectedInstrument).clef === 'bass' ? '𝄢' :\n                 getInstrumentTemplate(selectedInstrument).clef === 'alto' ? '𝄡' : '𝄞'}\n              </text>\n              \n              {/* Compasso 4/4 */}\n              <text x=\"80\" y=\"75\" fontSize=\"16\" fill=\"#333\">4</text>\n              <text x=\"80\" y=\"95\" fontSize=\"16\" fill=\"#333\">4</text>\n              \n              {/* Linha divisória inicial */}\n              <line x1=\"110\" y1=\"50\" x2=\"110\" y2=\"130\" stroke=\"#333\" strokeWidth=\"2\"/>\n\n              {/* Divisões de compasso */}\n              {[1, 2, 3, 4].map(measure => (\n                <line\n                  key={measure}\n                  x1={120 + (measure * 150)}\n                  y1=\"50\"\n                  x2={120 + (measure * 150)}\n                  y2=\"130\"\n                  stroke=\"#999\"\n                  strokeWidth=\"1\"\n                />\n              ))}\n\n              {/* Números dos compassos */}\n              {[1, 2, 3, 4].map(measure => (\n                <text\n                  key={measure}\n                  x={120 + ((measure - 1) * 150) + 75}\n                  y=\"35\"\n                  fontSize=\"12\"\n                  fill=\"#666\"\n                  textAnchor=\"middle\"\n                >\n                  {measure}\n                </text>\n              ))}\n              \n              {/* Área clicável para adicionar notas */}\n              <rect\n                x=\"120\"\n                y=\"40\"\n                width=\"600\"\n                height=\"100\"\n                fill=\"transparent\"\n                style={{ cursor: 'crosshair' }}\n                onClick={handleStaffClick}\n              />\n\n              {/* Renderizar notas colocadas */}\n              {placedNotes.map((note) => {\n                const x = 120 + ((note.position.measure - 1) * 150) + (note.position.beat * 30);\n                const notePositions: { [key: string]: number } = {\n                  'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,\n                  'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140\n                };\n                const y = notePositions[`${note.name}${note.octave}`] || 90;\n\n                return (\n                  <MusicalNoteComponent\n                    key={note.id}\n                    note={note}\n                    x={x}\n                    y={y}\n                    onRemove={() => setPlacedNotes(prev => prev.filter(n => n.id !== note.id))}\n                  />\n                );\n              })}\n\n              {/* Instruções */}\n              {placedNotes.length === 0 && (\n                <text x=\"400\" y=\"100\" fontSize=\"14\" fill=\"#999\" textAnchor=\"middle\">\n                  Clique na pauta para adicionar notas • Clique em uma nota para removê-la\n                </text>\n              )}\n            </Staff>\n          </StaffContainer>\n          )}\n        </ScoreCanvas>\n      </EditorContent>\n    </EditorContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAgB,OAAO;AAC/C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AAEpD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,WAAW,IAAIC,oBAAoB,QAAQ,4BAA4B;AAChF,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,qBAAqB,QAAQ,iCAAiC;AAEvE,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGb,MAAM,CAACc,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,eAAe;AAUrB,MAAMG,YAAY,GAAGhB,MAAM,CAACc,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,YAAY;AASlB,MAAME,WAAW,GAAGlB,MAAM,CAACc,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GA1BID,WAAW;AA4BjB,MAAME,aAAa,GAAGpB,MAAM,CAACc,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGtB,MAAM,CAACuB,MAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhCIJ,YAAY;AAkClB,MAAMK,aAAa,GAAG3B,MAAM,CAACc,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,aAAa;AAMnB,MAAME,SAAS,GAAG7B,MAAM,CAACc,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GANID,SAAS;AAQf,MAAME,WAAW,GAAG/B,MAAM,CAACc,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GATID,WAAW;AAWjB,MAAME,QAAQ,GAAGjC,MAAM,CAACc,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,QAAQ;AAMd,MAAME,UAAU,GAAGnC,MAAM,CAACuB,MAA4B;AACtD;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D,WAAWZ,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,OAAO,GAAG,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMZ,KAAK,IAAI,CAACA,KAAK,CAACY,MAAM,IAAI,sBAAsB;AACtD;AACA,CAAC;AAACC,GAAA,GAdIF,UAAU;AAgBhB,MAAMG,WAAW,GAAGtC,MAAM,CAACc,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GARID,WAAW;AAUjB,MAAME,cAAc,GAAGxC,MAAM,CAACc,GAAG;AACjC;AACA;AACA,CAAC;AAAC2B,IAAA,GAHID,cAAc;AAKpB,MAAME,KAAK,GAAG1C,MAAM,CAAC2C,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAPIF,KAAK;AASX,MAAMG,YAAY,GAAG7C,MAAM,CAACc,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GATID,YAAY;AAgBlB,OAAO,MAAME,WAAuC,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM;IAAEC;EAAY,CAAC,GAAGlD,OAAO,CAAC,CAAC;EACjC,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,CAAAkD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEG,KAAK,KAAI,gBAAgB,CAAC;EAC5E,MAAM,CAACE,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAA4B,MAAM,CAAC;EACnF,MAAM,CAACyD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1D,QAAQ,CAAe,SAAS,CAAC;EACjF,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAW,GAAG,CAAC;EAC/D,MAAM,CAAC6D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9D,QAAQ,CAAsC,IAAI,CAAC;EACvG,MAAM,CAAC+D,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiE,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAgB,EAAE,CAAC;EACjE,MAAM,CAACqE,SAAS,EAAEC,YAAY,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuE,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAc,IAAI,CAAC;EAC7D,MAAM,CAAC6E,SAAS,EAAEC,YAAY,CAAC,GAAG9E,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC+E,MAAM,EAAEC,SAAS,CAAC,GAAGhF,QAAQ,CAAW,EAAE,CAAC;EAClD,MAAM,CAACiF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlF,QAAQ,CAAiB,CAAAkD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiC,UAAU,KAAI,OAAO,CAAC;;EAElH;EACApF,KAAK,CAACqF,SAAS,CAAC,MAAM;IACpB,IAAInC,OAAO,IAAIG,WAAW,EAAE;MAC1BiC,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACpC,OAAO,EAAEG,WAAW,CAAC,CAAC;;EAE1B;EACArD,KAAK,CAACqF,SAAS,CAAC,MAAM;IACpB,IAAI,CAAChC,WAAW,IAAI,CAACH,OAAO,IAAIkB,WAAW,CAACmB,MAAM,KAAK,CAAC,EAAE;IAE1D,MAAMC,aAAa,GAAGC,UAAU,CAAC,MAAM;MACrCC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMC,YAAY,CAACH,aAAa,CAAC;EAC1C,CAAC,EAAE,CAACpB,WAAW,EAAEY,MAAM,EAAE1B,KAAK,EAAED,WAAW,EAAEH,OAAO,CAAC,CAAC;EAEtD,MAAMoC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAACpC,OAAO,IAAI,CAACG,WAAW,EAAE;IAE9BkB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMqB,KAAK,GAAG,MAAMxF,YAAY,CAACyF,QAAQ,CAAC3C,OAAO,CAAC;MAClD,IAAI0C,KAAK,IAAIA,KAAK,CAACE,MAAM,KAAKzC,WAAW,CAAC0C,GAAG,EAAE;QAC7CxC,QAAQ,CAACqC,KAAK,CAACtC,KAAK,CAAC;;QAErB;QACA,MAAM0C,QAAuB,GAAG,EAAE;QAClCJ,KAAK,CAACK,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;UAC5BA,KAAK,CAACC,QAAQ,CAACF,OAAO,CAACG,OAAO,IAAI;YAChCL,QAAQ,CAACM,IAAI,CAAC,GAAGD,OAAO,CAACE,KAAK,CAAC;UACjC,CAAC,CAAC;QACJ,CAAC,CAAC;QAEFlC,cAAc,CAAC2B,QAAQ,CAAC;QACxBf,SAAS,CAACW,KAAK,CAACZ,MAAM,IAAI,EAAE,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACRjC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmB,UAAU,GAAG,MAAAA,CAAOgB,UAAU,GAAG,KAAK,KAAK;IAC/C,IAAI,CAACrD,WAAW,EAAE;MAChB,IAAI,CAACqD,UAAU,EAAE;QACfC,KAAK,CAAC,mDAAmD,CAAC;MAC5D;MACA;IACF;IAEAxC,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF;MACA,MAAMyC,UAAU,GAAG,IAAIC,GAAG,CAAwB,CAAC;MACnDzC,WAAW,CAAC8B,OAAO,CAACY,IAAI,IAAI;QAC1B,MAAMT,OAAO,GAAGS,IAAI,CAACC,QAAQ,CAACV,OAAO;QACrC,IAAI,CAACO,UAAU,CAACI,GAAG,CAACX,OAAO,CAAC,EAAE;UAC5BO,UAAU,CAACK,GAAG,CAACZ,OAAO,EAAE,EAAE,CAAC;QAC7B;QACAO,UAAU,CAACM,GAAG,CAACb,OAAO,CAAC,CAAEC,IAAI,CAACQ,IAAI,CAAC;MACrC,CAAC,CAAC;;MAEF;MACA,MAAMV,QAAQ,GAAGe,KAAK,CAACC,IAAI,CAACR,UAAU,CAACS,OAAO,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,aAAa,EAAEhB,KAAK,CAAC,MAAM;QACjFiB,EAAE,EAAE5G,MAAM,CAAC,CAAC;QACZ6G,MAAM,EAAEF,aAAa;QACrBG,aAAa,EAAE;UAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;QAC/CrB,KAAK,EAAEA,KAAK,CAACsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACf,QAAQ,CAACiB,IAAI,GAAGD,CAAC,CAAChB,QAAQ,CAACiB,IAAI,CAAC;QAC9DC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMC,SAAS,GAAG;QAChB5E,KAAK;QACL6E,QAAQ,EAAEhF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgF,QAAQ;QACjCC,GAAG,EAAE;UACHtB,IAAI,EAAE,CAAA3D,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiF,GAAG,KAAI,GAAe;UAC3CC,IAAI,EAAE,CAAAlF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEmF,OAAO,KAAI;QAClC,CAAC;QACDZ,aAAa,EAAE;UACbC,SAAS,EAAE,CAAAxE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoF,gBAAgB,KAAI,CAAC;UAC/CX,WAAW,EAAE,CAAAzE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqF,gBAAgB,KAAI;QAClD,CAAC;QACDC,KAAK,EAAE,CAAAtF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsF,KAAK,KAAI,GAAG;QAClCxC,MAAM,EAAE,CAAC;UACPuB,EAAE,EAAE5G,MAAM,CAAC,CAAC;UACZ8H,IAAI,EAAEhI,qBAAqB,CAACwE,kBAAkB,CAAC,CAACwD,IAAI;UACpDtD,UAAU,EAAEF,kBAAkB;UAC9BkB;QACF,CAAC,CAAC;QACFpB,MAAM;QACNc,MAAM,EAAEzC,WAAW,CAAC0C;MACtB,CAAC;MAED,IAAI7C,OAAO,EAAE;QACX;QACA,MAAM;UAAE4C,MAAM;UAAE,GAAG6C;QAAW,CAAC,GAAGT,SAAS;QAC3C,MAAM9H,YAAY,CAACwI,WAAW,CAAC1F,OAAO,EAAEyF,UAAU,CAAC;MACrD,CAAC,MAAM;QACL,MAAMvI,YAAY,CAACyI,WAAW,CAACX,SAAS,EAAE7E,WAAW,CAAC0C,GAAG,CAAC;MAC5D;MAEAlB,YAAY,CAAC,IAAIiE,IAAI,CAAC,CAAC,CAAC;MACxB,IAAI,CAACpC,UAAU,EAAE;QACfC,KAAK,CAAC,8BAA8B,CAAC;MACvC;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,IAAI,CAACE,UAAU,EAAE;QACfC,KAAK,CAAC,0BAA0B,CAAC;MACnC;IACF,CAAC,SAAS;MACRxC,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM4E,UAAU,GAAGA,CAAA,KAAM;IACvB9E,YAAY,CAAC,CAACD,SAAS,CAAC;IACxB;EACF,CAAC;EAED,MAAMgF,gBAAgB,GAAIC,KAAuC,IAAK;IAAA,IAAAC,qBAAA;IACpE,MAAMC,IAAI,GAAGF,KAAK,CAACG,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,OAAO,IAAAJ,qBAAA,GAAGD,KAAK,CAACG,aAAa,CAACG,OAAO,CAAC,KAAK,CAAC,cAAAL,qBAAA,uBAAlCA,qBAAA,CAAoCG,qBAAqB,CAAC,CAAC;IAE3E,IAAI,CAACC,OAAO,EAAE;IAEd,MAAME,CAAC,GAAGP,KAAK,CAACQ,OAAO,GAAGH,OAAO,CAACI,IAAI;IACtC,MAAMC,CAAC,GAAGV,KAAK,CAACW,OAAO,GAAGN,OAAO,CAACO,GAAG;;IAErC;IACA,MAAMC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3C,MAAMC,aAAa,GAAG,CACpB;MAAEJ,CAAC,EAAE,EAAE;MAAE7C,IAAI,EAAE,GAAe;MAAEkD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE7C,IAAI,EAAE,GAAe;MAAEkD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE7C,IAAI,EAAE,GAAe;MAAEkD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE7C,IAAI,EAAE,GAAe;MAAEkD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE7C,IAAI,EAAE,GAAe;MAAEkD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE7C,IAAI,EAAE,GAAe;MAAEkD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,GAAG;MAAE7C,IAAI,EAAE,GAAe;MAAEkD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE7C,IAAI,EAAE,GAAe;MAAEkD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE7C,IAAI,EAAE,GAAe;MAAEkD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE7C,IAAI,EAAE,GAAe;MAAEkD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE7C,IAAI,EAAE,GAAe;MAAEkD,MAAM,EAAE;IAAY,CAAC,CAAE;IAAA,CACzD;;IAED;IACA,MAAMC,eAAe,GAAGF,aAAa,CAACG,MAAM,CAAC,CAACX,OAAO,EAAEY,OAAO,KAAK;MACjE,OAAOC,IAAI,CAACC,GAAG,CAACF,OAAO,CAACR,CAAC,GAAGA,CAAC,CAAC,GAAGS,IAAI,CAACC,GAAG,CAACd,OAAO,CAACI,CAAC,GAAGA,CAAC,CAAC,GAAGQ,OAAO,GAAGZ,OAAO;IAC9E,CAAC,CAAC;;IAEF;IACA,MAAMe,YAAY,GAAG,GAAG,CAAC,CAAC;IAC1B,MAAMC,MAAM,GAAG,GAAG,CAAC,CAAC;IACpB,MAAMC,SAAS,GAAGhB,CAAC,GAAGe,MAAM;IAC5B,MAAMlE,OAAO,GAAG+D,IAAI,CAACK,GAAG,CAAC,CAAC,EAAEL,IAAI,CAACM,KAAK,CAACF,SAAS,GAAGF,YAAY,CAAC,GAAG,CAAC,CAAC;IACrE,MAAMK,YAAY,GAAKH,SAAS,GAAGF,YAAY,GAAIA,YAAY,GAAI,CAAC,CAAC,CAAC;;IAEtE;IACA,MAAMM,OAAoB,GAAG;MAC3BpD,EAAE,EAAE5G,MAAM,CAAC,CAAC;MACZiK,IAAI,EAAEjH,YAAY;MAAE;MACpBoG,MAAM,EAAEC,eAAe,CAACD,MAAM;MAC9Bc,QAAQ,EAAEpH,gBAAgB;MAC1BqH,UAAU,EAAEjH,kBAAkB,IAAIkH,SAAS;MAC3CjE,QAAQ,EAAE;QACRV,OAAO;QACP2B,IAAI,EAAEoC,IAAI,CAACa,KAAK,CAACN,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;QAAE;QACxCxE,KAAK,EAAE;MACT,CAAC;MACD+E,MAAM,EAAE1H,YAAY,KAAK;IAC3B,CAAC;IAEDa,cAAc,CAAC8G,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEP,OAAO,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMQ,SAAyB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC;EACrF,MAAM7E,KAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAE7D,IAAIjC,SAAS,EAAE;IACb,oBACExD,OAAA,CAACC,eAAe;MAAAsK,QAAA,eACdvK,OAAA;QAAKwK,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE,OAAO;UACfC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;QACT,CAAE;QAAAP,QAAA,EAAC;MAEH;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC;EAEtB;EAEA,oBACElL,OAAA,CAACC,eAAe;IAAAsK,QAAA,gBACdvK,OAAA,CAACI,YAAY;MAAAmK,QAAA,gBACXvK,OAAA,CAACM,WAAW;QAAAiK,QAAA,gBACVvK,OAAA;UAAAuK,QAAA,EAAI;QAAoB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BlL,OAAA;UAAKwK,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEQ,GAAG,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBACjEvK,OAAA;YACEoL,IAAI,EAAC,MAAM;YACXC,KAAK,EAAE7I,KAAM;YACb8I,QAAQ,EAAGC,CAAC,IAAK9I,QAAQ,CAAC8I,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC1CI,WAAW,EAAC;UAAsB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,EACD3I,WAAW,IAAIuB,SAAS,iBACvB9D,OAAA;YAAKwK,KAAK,EAAE;cAAEK,QAAQ,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAwB,CAAE;YAAAP,QAAA,GAAC,2BACtD,EAACzG,SAAS,CAAC4H,kBAAkB,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdlL,OAAA,CAACQ,aAAa;QAAA+J,QAAA,gBACZvK,OAAA,CAACU,YAAY;UACXiL,OAAO,EAAEA,CAAA,KAAM1H,YAAY,CAACoG,IAAI,IAAIf,IAAI,CAACK,GAAG,CAAC,GAAG,EAAEU,IAAI,GAAG,GAAG,CAAC,CAAE;UAC/DxJ,OAAO,EAAC,SAAS;UACjB+K,QAAQ,EAAE5H,SAAS,IAAI,GAAI;UAAAuG,QAAA,EAC5B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACflL,OAAA,CAACU,YAAY;UACXiL,OAAO,EAAEA,CAAA,KAAM1H,YAAY,CAACoG,IAAI,IAAIf,IAAI,CAACuC,GAAG,CAAC,CAAC,EAAExB,IAAI,GAAG,GAAG,CAAC,CAAE;UAC7DxJ,OAAO,EAAC,SAAS;UACjB+K,QAAQ,EAAE5H,SAAS,IAAI,CAAE;UAAAuG,QAAA,EAC1B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACflL,OAAA,CAACU,YAAY;UACXiL,OAAO,EAAEA,CAAA,KAAM;YACbhI,aAAa,CAAC,CAACD,UAAU,CAAC;YAC1BG,aAAa,CAAC,KAAK,CAAC;UACtB,CAAE;UACFhD,OAAO,EAAC,SAAS;UAAA0J,QAAA,EAEhB7G,UAAU,GAAG,cAAc,GAAG;QAAW;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACflL,OAAA,CAACU,YAAY;UACXiL,OAAO,EAAEA,CAAA,KAAM;YACb9H,aAAa,CAAC,CAACD,UAAU,CAAC;YAC1BD,aAAa,CAAC,KAAK,CAAC;UACtB,CAAE;UACF9C,OAAO,EAAC,SAAS;UAAA0J,QAAA,EAEhB3G,UAAU,GAAG,cAAc,GAAG;QAAW;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACflL,OAAA,CAACU,YAAY;UACXiL,OAAO,EAAEA,CAAA,KAAMpI,cAAc,CAAC,EAAE,CAAE;UAClC1C,OAAO,EAAC,SAAS;UACjB+K,QAAQ,EAAEtI,WAAW,CAACmB,MAAM,KAAK,CAAE;UAAA8F,QAAA,EACpC;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACflL,OAAA,CAACU,YAAY;UAACiL,OAAO,EAAE1D,UAAW;UAACpH,OAAO,EAAC,SAAS;UAAA0J,QAAA,EACjDrH,SAAS,GAAG,WAAW,GAAG;QAAe;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACflL,OAAA,CAACU,YAAY;UACXiL,OAAO,EAAE/G,UAAW;UACpB/D,OAAO,EAAC,WAAW;UACnB+K,QAAQ,EAAE,CAACrJ,WAAW,IAAIa,QAAS;UAAAmH,QAAA,EAElCnH,QAAQ,GAAG,gBAAgB,GAAG;QAAW;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEflL,OAAA,CAACe,aAAa;MAAAwJ,QAAA,gBACZvK,OAAA,CAACiB,SAAS;QAAAsJ,QAAA,GACP,CAAChI,WAAW,iBACXvC,OAAA,CAACiC,YAAY;UAAAsI,QAAA,EAAC;QAEd;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CACf,eAEDlL,OAAA,CAACL,kBAAkB;UACjByE,kBAAkB,EAAEA,kBAAmB;UACvC0H,kBAAkB,EAAEzH,qBAAsB;UAC1C0H,OAAO,EAAE;QAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAEFlL,OAAA,CAACmB,WAAW;UAAAoJ,QAAA,gBACVvK,OAAA;YAAAuK,QAAA,EAAI;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBlL,OAAA,CAACqB,QAAQ;YAAAkJ,QAAA,gBACPvK,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEkB,YAAY,KAAK,MAAO;cAChCiJ,OAAO,EAAEA,CAAA,KAAMhJ,eAAe,CAAC,MAAM,CAAE;cAAA4H,QAAA,EACxC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblL,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEkB,YAAY,KAAK,MAAO;cAChCiJ,OAAO,EAAEA,CAAA,KAAMhJ,eAAe,CAAC,MAAM,CAAE;cAAA4H,QAAA,EACxC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblL,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEkB,YAAY,KAAK,OAAQ;cACjCiJ,OAAO,EAAEA,CAAA,KAAMhJ,eAAe,CAAC,OAAO,CAAE;cAAA4H,QAAA,EACzC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdlL,OAAA,CAACmB,WAAW;UAAAoJ,QAAA,gBACVvK,OAAA;YAAAuK,QAAA,EAAI;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBlL,OAAA,CAACqB,QAAQ;YAAAkJ,QAAA,EACND,SAAS,CAAC9D,GAAG,CAACwD,QAAQ,iBACrBhK,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEoB,gBAAgB,KAAKoH,QAAS;cACtC2B,OAAO,EAAEA,CAAA,KAAM9I,mBAAmB,CAACmH,QAAQ,CAAE;cAAAO,QAAA,EAE5CP,QAAQ,KAAK,OAAO,GAAG,IAAI,GAC3BA,QAAQ,KAAK,MAAM,GAAG,MAAM,GAC5BA,QAAQ,KAAK,SAAS,GAAG,GAAG,GAC5BA,QAAQ,KAAK,QAAQ,GAAG,GAAG,GAAG;YAAG,GAP7BA,QAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQH,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdlL,OAAA,CAACmB,WAAW;UAAAoJ,QAAA,gBACVvK,OAAA;YAAAuK,QAAA,EAAI;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBlL,OAAA,CAACqB,QAAQ;YAAAkJ,QAAA,EACN9E,KAAK,CAACe,GAAG,CAACR,IAAI,iBACbhG,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEsB,YAAY,KAAKkD,IAAK;cAC9B2F,OAAO,EAAEA,CAAA,KAAM5I,eAAe,CAACiD,IAAI,CAAE;cAAAuE,QAAA,EAEpCvE;YAAI,GAJAA,IAAI;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKC,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdlL,OAAA,CAACmB,WAAW;UAAAoJ,QAAA,gBACVvK,OAAA;YAAAuK,QAAA,EAAI;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBlL,OAAA,CAACqB,QAAQ;YAAAkJ,QAAA,gBACPvK,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEwB,kBAAkB,KAAK,OAAQ;cACvC2I,OAAO,EAAEA,CAAA,KAAM1I,qBAAqB,CAACD,kBAAkB,KAAK,OAAO,GAAG,IAAI,GAAG,OAAO,CAAE;cAAAuH,QAAA,EACvF;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblL,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEwB,kBAAkB,KAAK,MAAO;cACtC2I,OAAO,EAAEA,CAAA,KAAM1I,qBAAqB,CAACD,kBAAkB,KAAK,MAAM,GAAG,IAAI,GAAG,MAAM,CAAE;cAAAuH,QAAA,EACrF;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblL,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEwB,kBAAkB,KAAK,SAAU;cACzC2I,OAAO,EAAEA,CAAA,KAAM1I,qBAAqB,CAACD,kBAAkB,KAAK,SAAS,GAAG,IAAI,GAAG,SAAS,CAAE;cAAAuH,QAAA,EAC3F;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblL,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEwB,kBAAkB,KAAK,IAAK;cACpC2I,OAAO,EAAEA,CAAA,KAAM1I,qBAAqB,CAAC,IAAI,CAAE;cAAAsH,QAAA,EAC5C;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdlL,OAAA,CAACmB,WAAW;UAAAoJ,QAAA,gBACVvK,OAAA;YAAAuK,QAAA,EAAI;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBlL,OAAA;YAAKwK,KAAK,EAAE;cAAEK,QAAQ,EAAE,QAAQ;cAAEC,KAAK,EAAE,MAAM;cAAEkB,UAAU,EAAE;YAAM,CAAE;YAAAzB,QAAA,gBACnEvK,OAAA;cAAAuK,QAAA,GAAK,4BAAgB,EAAC3K,qBAAqB,CAACwE,kBAAkB,CAAC,CAAC2F,IAAI;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3ElL,OAAA;cAAAuK,QAAA,GAAK,sBAAU,EAACjH,WAAW,CAAC2I,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC9B,MAAM,CAAC,CAAC3F,MAAM;YAAA;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChElL,OAAA;cAAAuK,QAAA,GAAK,uBAAW,EAACjH,WAAW,CAAC2I,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,MAAM,CAAC,CAAC3F,MAAM;YAAA;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChElL,OAAA;cAAAuK,QAAA,GAAK,sBAAU,EAACjH,WAAW,CAACmB,MAAM;YAAA;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzClL,OAAA;cAAAuK,QAAA,GAAK,0BAAc,EAACjB,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE,GAAGrG,WAAW,CAACkD,GAAG,CAAC0F,CAAC,IAAIA,CAAC,CAACjG,QAAQ,CAACV,OAAO,CAAC,EAAE,CAAC,CAAC;YAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtFlL,OAAA;cAAAuK,QAAA,GAAK,qBAAc,EAACjH,WAAW,CAAC2I,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjC,UAAU,KAAK,OAAO,CAAC,CAACxF,MAAM;YAAA;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnFlL,OAAA;cAAAuK,QAAA,GAAK,oBAAU,EAACjH,WAAW,CAAC2I,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjC,UAAU,KAAK,MAAM,CAAC,CAACxF,MAAM;YAAA;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9ElL,OAAA;cAAAuK,QAAA,GAAK,uBAAW,EAACrG,MAAM,CAACO,MAAM;YAAA;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrClL,OAAA;cAAAuK,QAAA,GAAK,qBAAS,EAACjB,IAAI,CAACa,KAAK,CAACnG,SAAS,GAAG,GAAG,CAAC,EAAC,GAAC;YAAA;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEZlL,OAAA,CAAC0B,WAAW;QAAA6I,QAAA,EACT7G,UAAU,gBACT1D,OAAA,CAACT,SAAS;UAACkG,KAAK,EAAEnC,WAAY;UAACd,KAAK,EAAEA;QAAM;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAC7CtH,UAAU,gBACZ5D,OAAA,CAACN,YAAY;UACXwE,MAAM,EAAEA,MAAO;UACfuB,KAAK,EAAEnC,WAAY;UACnB6I,cAAc,EAAEhI,SAAU;UAC1B3B,KAAK,EAAEA;QAAM;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,gBAEFlL,OAAA,CAAC4B,cAAc;UAAA2I,QAAA,gBACbvK,OAAA;YAAKwK,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE,QAAQ;cAAEyB,YAAY,EAAE;YAAO,CAAE;YAAA7B,QAAA,gBAC3GvK,OAAA;cAAIwK,KAAK,EAAE;gBAAE6B,MAAM,EAAE,CAAC;gBAAEvB,KAAK,EAAE;cAAU,CAAE;cAAAP,QAAA,GAAC,aAAW,EAAC/H,KAAK;YAAA;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnElL,OAAA;cAAKwK,KAAK,EAAE;gBAAEK,QAAQ,EAAE,QAAQ;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAP,QAAA,GAAC,QAC3C,EAACjB,IAAI,CAACa,KAAK,CAACnG,SAAS,GAAG,GAAG,CAAC,EAAC,GACrC;YAAA;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlL,OAAA,CAAC8B,KAAK;YACJwK,OAAO,EAAC,aAAa;YACrB9B,KAAK,EAAE;cAAE+B,SAAS,EAAE,SAASvI,SAAS,GAAG;cAAEwI,eAAe,EAAE;YAAW,CAAE;YAAAjC,QAAA,GAG1E,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC/D,GAAG,CAACiG,IAAI,iBACvBzM,OAAA;cAEE0M,EAAE,EAAC,IAAI;cACPC,EAAE,EAAE,EAAE,GAAGF,IAAI,GAAG,EAAG;cACnBG,EAAE,EAAC,KAAK;cACRC,EAAE,EAAE,EAAE,GAAGJ,IAAI,GAAG,EAAG;cACnBK,MAAM,EAAC,MAAM;cACbC,WAAW,EAAC;YAAG,GANVN,IAAI;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV,CACF,CAAC,eAGFlL,OAAA;cAAM0I,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAACgC,QAAQ,EAAC,IAAI;cAACmC,IAAI,EAAC,MAAM;cAAAzC,QAAA,EAC1C3K,qBAAqB,CAACwE,kBAAkB,CAAC,CAACwD,IAAI,KAAK,QAAQ,GAAG,IAAI,GAClEhI,qBAAqB,CAACwE,kBAAkB,CAAC,CAACwD,IAAI,KAAK,MAAM,GAAG,IAAI,GAChEhI,qBAAqB,CAACwE,kBAAkB,CAAC,CAACwD,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG;YAAI;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eAGPlL,OAAA;cAAM0I,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAACgC,QAAQ,EAAC,IAAI;cAACmC,IAAI,EAAC,MAAM;cAAAzC,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDlL,OAAA;cAAM0I,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAACgC,QAAQ,EAAC,IAAI;cAACmC,IAAI,EAAC,MAAM;cAAAzC,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGtDlL,OAAA;cAAM0M,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,KAAK;cAACC,MAAM,EAAC,MAAM;cAACC,WAAW,EAAC;YAAG;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,EAGvE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC1E,GAAG,CAACjB,OAAO,iBACvBvF,OAAA;cAEE0M,EAAE,EAAE,GAAG,GAAInH,OAAO,GAAG,GAAK;cAC1BoH,EAAE,EAAC,IAAI;cACPC,EAAE,EAAE,GAAG,GAAIrH,OAAO,GAAG,GAAK;cAC1BsH,EAAE,EAAC,KAAK;cACRC,MAAM,EAAC,MAAM;cACbC,WAAW,EAAC;YAAG,GANVxH,OAAO;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOb,CACF,CAAC,EAGD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC1E,GAAG,CAACjB,OAAO,iBACvBvF,OAAA;cAEE0I,CAAC,EAAE,GAAG,GAAI,CAACnD,OAAO,GAAG,CAAC,IAAI,GAAI,GAAG,EAAG;cACpCsD,CAAC,EAAC,IAAI;cACNgC,QAAQ,EAAC,IAAI;cACbmC,IAAI,EAAC,MAAM;cACXC,UAAU,EAAC,QAAQ;cAAA1C,QAAA,EAElBhF;YAAO,GAPHA,OAAO;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQR,CACP,CAAC,eAGFlL,OAAA;cACE0I,CAAC,EAAC,KAAK;cACPG,CAAC,EAAC,IAAI;cACNqE,KAAK,EAAC,KAAK;cACXtC,MAAM,EAAC,KAAK;cACZoC,IAAI,EAAC,aAAa;cAClBxC,KAAK,EAAE;gBAAE2C,MAAM,EAAE;cAAY,CAAE;cAC/BxB,OAAO,EAAEzD;YAAiB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,EAGD5H,WAAW,CAACkD,GAAG,CAAER,IAAI,IAAK;cACzB,MAAM0C,CAAC,GAAG,GAAG,GAAI,CAAC1C,IAAI,CAACC,QAAQ,CAACV,OAAO,GAAG,CAAC,IAAI,GAAI,GAAIS,IAAI,CAACC,QAAQ,CAACiB,IAAI,GAAG,EAAG;cAC/E,MAAM+B,aAAwC,GAAG;gBAC/C,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAChD,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE;cAC9D,CAAC;cACD,MAAMJ,CAAC,GAAGI,aAAa,CAAC,GAAGjD,IAAI,CAAC+D,IAAI,GAAG/D,IAAI,CAACkD,MAAM,EAAE,CAAC,IAAI,EAAE;cAE3D,oBACElJ,OAAA,CAACP,oBAAoB;gBAEnBuG,IAAI,EAAEA,IAAK;gBACX0C,CAAC,EAAEA,CAAE;gBACLG,CAAC,EAAEA,CAAE;gBACLuE,QAAQ,EAAEA,CAAA,KAAM7J,cAAc,CAAC8G,IAAI,IAAIA,IAAI,CAAC4B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxF,EAAE,KAAKV,IAAI,CAACU,EAAE,CAAC;cAAE,GAJtEV,IAAI,CAACU,EAAE;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKb,CAAC;YAEN,CAAC,CAAC,EAGD5H,WAAW,CAACmB,MAAM,KAAK,CAAC,iBACvBzE,OAAA;cAAM0I,CAAC,EAAC,KAAK;cAACG,CAAC,EAAC,KAAK;cAACgC,QAAQ,EAAC,IAAI;cAACmC,IAAI,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAA1C,QAAA,EAAC;YAEpE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MACf;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB,CAAC;AAAC5I,EAAA,CA9gBWH,WAAuC;EAAA,QAC1B9C,OAAO;AAAA;AAAAgO,IAAA,GADpBlL,WAAuC;AAAA,IAAAhC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAmL,IAAA;AAAAC,YAAA,CAAAnN,EAAA;AAAAmN,YAAA,CAAAjN,GAAA;AAAAiN,YAAA,CAAA/M,GAAA;AAAA+M,YAAA,CAAA7M,GAAA;AAAA6M,YAAA,CAAAxM,GAAA;AAAAwM,YAAA,CAAAtM,GAAA;AAAAsM,YAAA,CAAApM,GAAA;AAAAoM,YAAA,CAAAlM,GAAA;AAAAkM,YAAA,CAAAhM,GAAA;AAAAgM,YAAA,CAAA7L,GAAA;AAAA6L,YAAA,CAAA3L,GAAA;AAAA2L,YAAA,CAAAzL,IAAA;AAAAyL,YAAA,CAAAtL,IAAA;AAAAsL,YAAA,CAAApL,IAAA;AAAAoL,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}