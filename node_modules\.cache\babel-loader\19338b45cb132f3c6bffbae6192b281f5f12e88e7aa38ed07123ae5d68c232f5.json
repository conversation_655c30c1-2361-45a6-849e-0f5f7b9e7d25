{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\pages\\\\Auth\\\\RegisterPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { FiMusic, FiMail, FiLock, FiUser, FiEye, FiEyeOff } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 1rem;\n`;\n_c = AuthContainer;\nconst AuthCard = styled.div`\n  background: white;\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  width: 100%;\n  max-width: 400px;\n`;\n_c2 = AuthCard;\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  font-size: 2rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n`;\n_c3 = Logo;\nconst Title = styled.h2`\n  text-align: center;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n  font-weight: 600;\n`;\n_c4 = Title;\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\n_c5 = Form;\nconst InputGroup = styled.div`\n  position: relative;\n`;\n_c6 = InputGroup;\nconst InputIcon = styled.div`\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #6b7280;\n`;\n_c7 = InputIcon;\nconst Input = styled.input`\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 3rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n  box-sizing: border-box;\n\n  &:focus {\n    outline: none;\n    border-color: #3b82f6;\n  }\n\n  &::placeholder {\n    color: #9ca3af;\n  }\n`;\n_c8 = Input;\nconst PasswordToggle = styled.button`\n  position: absolute;\n  right: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 0.25rem;\n\n  &:hover {\n    color: #374151;\n  }\n`;\n_c9 = PasswordToggle;\nconst SubmitButton = styled.button`\n  width: 100%;\n  padding: 0.75rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: transform 0.2s;\n  margin-top: 1rem;\n\n  &:hover {\n    transform: translateY(-1px);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c0 = SubmitButton;\nconst SwitchAuth = styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  color: #6b7280;\n`;\n_c1 = SwitchAuth;\nconst SwitchLink = styled.button`\n  background: none;\n  border: none;\n  color: #3b82f6;\n  cursor: pointer;\n  font-weight: 600;\n  text-decoration: underline;\n\n  &:hover {\n    color: #2563eb;\n  }\n`;\n_c10 = SwitchLink;\nconst ErrorMessage = styled.div`\n  background-color: #fee2e2;\n  color: #dc2626;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;\n_c11 = ErrorMessage;\nconst SuccessMessage = styled.div`\n  background-color: #d1fae5;\n  color: #065f46;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;\n_c12 = SuccessMessage;\nexport const RegisterPage = ({\n  onSwitchToLogin\n}) => {\n  _s();\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const {\n    register\n  } = useAuth();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!name || !email || !password || !confirmPassword) {\n      setError('Por favor, preencha todos os campos');\n      return;\n    }\n    if (password !== confirmPassword) {\n      setError('As senhas não coincidem');\n      return;\n    }\n    if (password.length < 6) {\n      setError('A senha deve ter pelo menos 6 caracteres');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      await register(email, password, name);\n      setSuccess('Conta criada com sucesso! Você será redirecionado...');\n    } catch (error) {\n      console.error('Erro no registro:', error);\n      if (error.code === 'auth/email-already-in-use') {\n        setError('Este email já está em uso');\n      } else if (error.code === 'auth/weak-password') {\n        setError('A senha é muito fraca');\n      } else if (error.code === 'auth/invalid-email') {\n        setError('Email inválido');\n      } else {\n        setError('Erro ao criar conta. Tente novamente.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContainer, {\n    children: /*#__PURE__*/_jsxDEV(AuthCard, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        children: [/*#__PURE__*/_jsxDEV(FiMusic, {\n          size: 32\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), \"Partitura Digital\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Title, {\n        children: \"Criar nova conta\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 19\n      }, this), success && /*#__PURE__*/_jsxDEV(SuccessMessage, {\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputIcon, {\n            children: /*#__PURE__*/_jsxDEV(FiUser, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"text\",\n            placeholder: \"Seu nome\",\n            value: name,\n            onChange: e => setName(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputIcon, {\n            children: /*#__PURE__*/_jsxDEV(FiMail, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: \"email\",\n            placeholder: \"Seu email\",\n            value: email,\n            onChange: e => setEmail(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputIcon, {\n            children: /*#__PURE__*/_jsxDEV(FiLock, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: showPassword ? 'text' : 'password',\n            placeholder: \"Sua senha\",\n            value: password,\n            onChange: e => setPassword(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PasswordToggle, {\n            type: \"button\",\n            onClick: () => setShowPassword(!showPassword),\n            children: showPassword ? /*#__PURE__*/_jsxDEV(FiEyeOff, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 31\n            }, this) : /*#__PURE__*/_jsxDEV(FiEye, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 56\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputIcon, {\n            children: /*#__PURE__*/_jsxDEV(FiLock, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            type: showConfirmPassword ? 'text' : 'password',\n            placeholder: \"Confirme sua senha\",\n            value: confirmPassword,\n            onChange: e => setConfirmPassword(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PasswordToggle, {\n            type: \"button\",\n            onClick: () => setShowConfirmPassword(!showConfirmPassword),\n            children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(FiEyeOff, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(FiEye, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 63\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SubmitButton, {\n          type: \"submit\",\n          disabled: loading,\n          children: loading ? 'Criando conta...' : 'Criar conta'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SwitchAuth, {\n        children: [\"J\\xE1 tem uma conta?\", ' ', /*#__PURE__*/_jsxDEV(SwitchLink, {\n          onClick: onSwitchToLogin,\n          children: \"Fazer login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 5\n  }, this);\n};\n_s(RegisterPage, \"Ekg6Qhg0CXUkt1wdqFrZGK5Z6fs=\", false, function () {\n  return [useAuth];\n});\n_c13 = RegisterPage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"AuthContainer\");\n$RefreshReg$(_c2, \"AuthCard\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"Form\");\n$RefreshReg$(_c6, \"InputGroup\");\n$RefreshReg$(_c7, \"InputIcon\");\n$RefreshReg$(_c8, \"Input\");\n$RefreshReg$(_c9, \"PasswordToggle\");\n$RefreshReg$(_c0, \"SubmitButton\");\n$RefreshReg$(_c1, \"SwitchAuth\");\n$RefreshReg$(_c10, \"SwitchLink\");\n$RefreshReg$(_c11, \"ErrorMessage\");\n$RefreshReg$(_c12, \"SuccessMessage\");\n$RefreshReg$(_c13, \"RegisterPage\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "FiMusic", "FiMail", "FiLock", "FiUser", "FiEye", "Fi<PERSON>ye<PERSON>ff", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "AuthCard", "_c2", "Logo", "_c3", "Title", "h2", "_c4", "Form", "form", "_c5", "InputGroup", "_c6", "InputIcon", "_c7", "Input", "input", "_c8", "PasswordToggle", "button", "_c9", "SubmitButton", "_c0", "SwitchAuth", "_c1", "SwitchLink", "_c10", "ErrorMessage", "_c11", "SuccessMessage", "_c12", "RegisterPage", "onSwitchToLogin", "_s", "name", "setName", "email", "setEmail", "password", "setPassword", "confirmPassword", "setConfirmPassword", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "loading", "setLoading", "error", "setError", "success", "setSuccess", "register", "handleSubmit", "e", "preventDefault", "length", "console", "code", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "value", "onChange", "target", "required", "onClick", "disabled", "_c13", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/pages/Auth/RegisterPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { FiMusic, FiMail, FiLock, FiUser, <PERSON>Eye, FiEyeOff } from 'react-icons/fi';\n\nconst AuthContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 1rem;\n`;\n\nconst AuthCard = styled.div`\n  background: white;\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  width: 100%;\n  max-width: 400px;\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  font-size: 2rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n`;\n\nconst Title = styled.h2`\n  text-align: center;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n  font-weight: 600;\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\n\nconst InputGroup = styled.div`\n  position: relative;\n`;\n\nconst InputIcon = styled.div`\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #6b7280;\n`;\n\nconst Input = styled.input`\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 3rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n  box-sizing: border-box;\n\n  &:focus {\n    outline: none;\n    border-color: #3b82f6;\n  }\n\n  &::placeholder {\n    color: #9ca3af;\n  }\n`;\n\nconst PasswordToggle = styled.button`\n  position: absolute;\n  right: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 0.25rem;\n\n  &:hover {\n    color: #374151;\n  }\n`;\n\nconst SubmitButton = styled.button`\n  width: 100%;\n  padding: 0.75rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: transform 0.2s;\n  margin-top: 1rem;\n\n  &:hover {\n    transform: translateY(-1px);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst SwitchAuth = styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  color: #6b7280;\n`;\n\nconst SwitchLink = styled.button`\n  background: none;\n  border: none;\n  color: #3b82f6;\n  cursor: pointer;\n  font-weight: 600;\n  text-decoration: underline;\n\n  &:hover {\n    color: #2563eb;\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background-color: #fee2e2;\n  color: #dc2626;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;\n\nconst SuccessMessage = styled.div`\n  background-color: #d1fae5;\n  color: #065f46;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;\n\ninterface RegisterPageProps {\n  onSwitchToLogin: () => void;\n}\n\nexport const RegisterPage: React.FC<RegisterPageProps> = ({ onSwitchToLogin }) => {\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const { register } = useAuth();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!name || !email || !password || !confirmPassword) {\n      setError('Por favor, preencha todos os campos');\n      return;\n    }\n\n    if (password !== confirmPassword) {\n      setError('As senhas não coincidem');\n      return;\n    }\n\n    if (password.length < 6) {\n      setError('A senha deve ter pelo menos 6 caracteres');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      await register(email, password, name);\n      setSuccess('Conta criada com sucesso! Você será redirecionado...');\n    } catch (error: any) {\n      console.error('Erro no registro:', error);\n      if (error.code === 'auth/email-already-in-use') {\n        setError('Este email já está em uso');\n      } else if (error.code === 'auth/weak-password') {\n        setError('A senha é muito fraca');\n      } else if (error.code === 'auth/invalid-email') {\n        setError('Email inválido');\n      } else {\n        setError('Erro ao criar conta. Tente novamente.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <AuthContainer>\n      <AuthCard>\n        <Logo>\n          <FiMusic size={32} />\n          Partitura Digital\n        </Logo>\n        \n        <Title>Criar nova conta</Title>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n        {success && <SuccessMessage>{success}</SuccessMessage>}\n\n        <Form onSubmit={handleSubmit}>\n          <InputGroup>\n            <InputIcon>\n              <FiUser size={20} />\n            </InputIcon>\n            <Input\n              type=\"text\"\n              placeholder=\"Seu nome\"\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              required\n            />\n          </InputGroup>\n\n          <InputGroup>\n            <InputIcon>\n              <FiMail size={20} />\n            </InputIcon>\n            <Input\n              type=\"email\"\n              placeholder=\"Seu email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n            />\n          </InputGroup>\n\n          <InputGroup>\n            <InputIcon>\n              <FiLock size={20} />\n            </InputIcon>\n            <Input\n              type={showPassword ? 'text' : 'password'}\n              placeholder=\"Sua senha\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n            />\n            <PasswordToggle\n              type=\"button\"\n              onClick={() => setShowPassword(!showPassword)}\n            >\n              {showPassword ? <FiEyeOff size={20} /> : <FiEye size={20} />}\n            </PasswordToggle>\n          </InputGroup>\n\n          <InputGroup>\n            <InputIcon>\n              <FiLock size={20} />\n            </InputIcon>\n            <Input\n              type={showConfirmPassword ? 'text' : 'password'}\n              placeholder=\"Confirme sua senha\"\n              value={confirmPassword}\n              onChange={(e) => setConfirmPassword(e.target.value)}\n              required\n            />\n            <PasswordToggle\n              type=\"button\"\n              onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n            >\n              {showConfirmPassword ? <FiEyeOff size={20} /> : <FiEye size={20} />}\n            </PasswordToggle>\n          </InputGroup>\n\n          <SubmitButton type=\"submit\" disabled={loading}>\n            {loading ? 'Criando conta...' : 'Criar conta'}\n          </SubmitButton>\n        </Form>\n\n        <SwitchAuth>\n          Já tem uma conta?{' '}\n          <SwitchLink onClick={onSwitchToLogin}>\n            Fazer login\n          </SwitchLink>\n        </SwitchAuth>\n      </AuthCard>\n    </AuthContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElF,MAAMC,aAAa,GAAGV,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,aAAa;AASnB,MAAMG,QAAQ,GAAGb,MAAM,CAACW,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,QAAQ;AASd,MAAME,IAAI,GAAGf,MAAM,CAACW,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GATID,IAAI;AAWV,MAAME,KAAK,GAAGjB,MAAM,CAACkB,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,IAAI,GAAGpB,MAAM,CAACqB,IAAI;AACxB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,IAAI;AAMV,MAAMG,UAAU,GAAGvB,MAAM,CAACW,GAAG;AAC7B;AACA,CAAC;AAACa,GAAA,GAFID,UAAU;AAIhB,MAAME,SAAS,GAAGzB,MAAM,CAACW,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GANID,SAAS;AAQf,MAAME,KAAK,GAAG3B,MAAM,CAAC4B,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAjBIF,KAAK;AAmBX,MAAMG,cAAc,GAAG9B,MAAM,CAAC+B,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIF,cAAc;AAgBpB,MAAMG,YAAY,GAAGjC,MAAM,CAAC+B,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAtBID,YAAY;AAwBlB,MAAME,UAAU,GAAGnC,MAAM,CAACW,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GAJID,UAAU;AAMhB,MAAME,UAAU,GAAGrC,MAAM,CAAC+B,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,IAAA,GAXID,UAAU;AAahB,MAAME,YAAY,GAAGvC,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6B,IAAA,GARID,YAAY;AAUlB,MAAME,cAAc,GAAGzC,MAAM,CAACW,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GARID,cAAc;AAcpB,OAAO,MAAME,YAAyC,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiD,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6D,KAAK,EAAEC,QAAQ,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+D,OAAO,EAAEC,UAAU,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAM;IAAEiE;EAAS,CAAC,GAAG/D,OAAO,CAAC,CAAC;EAE9B,MAAMgE,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACrB,IAAI,IAAI,CAACE,KAAK,IAAI,CAACE,QAAQ,IAAI,CAACE,eAAe,EAAE;MACpDS,QAAQ,CAAC,qCAAqC,CAAC;MAC/C;IACF;IAEA,IAAIX,QAAQ,KAAKE,eAAe,EAAE;MAChCS,QAAQ,CAAC,yBAAyB,CAAC;MACnC;IACF;IAEA,IAAIX,QAAQ,CAACkB,MAAM,GAAG,CAAC,EAAE;MACvBP,QAAQ,CAAC,0CAA0C,CAAC;MACpD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMC,QAAQ,CAAChB,KAAK,EAAEE,QAAQ,EAAEJ,IAAI,CAAC;MACrCiB,UAAU,CAAC,sDAAsD,CAAC;IACpE,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnBS,OAAO,CAACT,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,IAAIA,KAAK,CAACU,IAAI,KAAK,2BAA2B,EAAE;QAC9CT,QAAQ,CAAC,2BAA2B,CAAC;MACvC,CAAC,MAAM,IAAID,KAAK,CAACU,IAAI,KAAK,oBAAoB,EAAE;QAC9CT,QAAQ,CAAC,uBAAuB,CAAC;MACnC,CAAC,MAAM,IAAID,KAAK,CAACU,IAAI,KAAK,oBAAoB,EAAE;QAC9CT,QAAQ,CAAC,gBAAgB,CAAC;MAC5B,CAAC,MAAM;QACLA,QAAQ,CAAC,uCAAuC,CAAC;MACnD;IACF,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACElD,OAAA,CAACC,aAAa;IAAA6D,QAAA,eACZ9D,OAAA,CAACI,QAAQ;MAAA0D,QAAA,gBACP9D,OAAA,CAACM,IAAI;QAAAwD,QAAA,gBACH9D,OAAA,CAACP,OAAO;UAACsE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,qBAEvB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEPnE,OAAA,CAACQ,KAAK;QAAAsD,QAAA,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAE9BhB,KAAK,iBAAInD,OAAA,CAAC8B,YAAY;QAAAgC,QAAA,EAAEX;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,EAC7Cd,OAAO,iBAAIrD,OAAA,CAACgC,cAAc;QAAA8B,QAAA,EAAET;MAAO;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC,eAEtDnE,OAAA,CAACW,IAAI;QAACyD,QAAQ,EAAEZ,YAAa;QAAAM,QAAA,gBAC3B9D,OAAA,CAACc,UAAU;UAAAgD,QAAA,gBACT9D,OAAA,CAACgB,SAAS;YAAA8C,QAAA,eACR9D,OAAA,CAACJ,MAAM;cAACmE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACZnE,OAAA,CAACkB,KAAK;YACJmD,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,UAAU;YACtBC,KAAK,EAAElC,IAAK;YACZmC,QAAQ,EAAGf,CAAC,IAAKnB,OAAO,CAACmB,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;YACzCG,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAEbnE,OAAA,CAACc,UAAU;UAAAgD,QAAA,gBACT9D,OAAA,CAACgB,SAAS;YAAA8C,QAAA,eACR9D,OAAA,CAACN,MAAM;cAACqE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACZnE,OAAA,CAACkB,KAAK;YACJmD,IAAI,EAAC,OAAO;YACZC,WAAW,EAAC,WAAW;YACvBC,KAAK,EAAEhC,KAAM;YACbiC,QAAQ,EAAGf,CAAC,IAAKjB,QAAQ,CAACiB,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;YAC1CG,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eAEbnE,OAAA,CAACc,UAAU;UAAAgD,QAAA,gBACT9D,OAAA,CAACgB,SAAS;YAAA8C,QAAA,eACR9D,OAAA,CAACL,MAAM;cAACoE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACZnE,OAAA,CAACkB,KAAK;YACJmD,IAAI,EAAExB,YAAY,GAAG,MAAM,GAAG,UAAW;YACzCyB,WAAW,EAAC,WAAW;YACvBC,KAAK,EAAE9B,QAAS;YAChB+B,QAAQ,EAAGf,CAAC,IAAKf,WAAW,CAACe,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;YAC7CG,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFnE,OAAA,CAACqB,cAAc;YACbgD,IAAI,EAAC,QAAQ;YACbM,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAAC,CAACD,YAAY,CAAE;YAAAiB,QAAA,EAE7CjB,YAAY,gBAAG7C,OAAA,CAACF,QAAQ;cAACiE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGnE,OAAA,CAACH,KAAK;cAACkE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEbnE,OAAA,CAACc,UAAU;UAAAgD,QAAA,gBACT9D,OAAA,CAACgB,SAAS;YAAA8C,QAAA,eACR9D,OAAA,CAACL,MAAM;cAACoE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACZnE,OAAA,CAACkB,KAAK;YACJmD,IAAI,EAAEtB,mBAAmB,GAAG,MAAM,GAAG,UAAW;YAChDuB,WAAW,EAAC,oBAAoB;YAChCC,KAAK,EAAE5B,eAAgB;YACvB6B,QAAQ,EAAGf,CAAC,IAAKb,kBAAkB,CAACa,CAAC,CAACgB,MAAM,CAACF,KAAK,CAAE;YACpDG,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACFnE,OAAA,CAACqB,cAAc;YACbgD,IAAI,EAAC,QAAQ;YACbM,OAAO,EAAEA,CAAA,KAAM3B,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;YAAAe,QAAA,EAE3Df,mBAAmB,gBAAG/C,OAAA,CAACF,QAAQ;cAACiE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGnE,OAAA,CAACH,KAAK;cAACkE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEbnE,OAAA,CAACwB,YAAY;UAAC6C,IAAI,EAAC,QAAQ;UAACO,QAAQ,EAAE3B,OAAQ;UAAAa,QAAA,EAC3Cb,OAAO,GAAG,kBAAkB,GAAG;QAAa;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eAEPnE,OAAA,CAAC0B,UAAU;QAAAoC,QAAA,GAAC,sBACO,EAAC,GAAG,eACrB9D,OAAA,CAAC4B,UAAU;UAAC+C,OAAO,EAAExC,eAAgB;UAAA2B,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEpB,CAAC;AAAC/B,EAAA,CAlJWF,YAAyC;EAAA,QAW/B1C,OAAO;AAAA;AAAAqF,IAAA,GAXjB3C,YAAyC;AAAA,IAAA/B,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA4C,IAAA;AAAAC,YAAA,CAAA3E,EAAA;AAAA2E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAApE,GAAA;AAAAoE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAvD,GAAA;AAAAuD,YAAA,CAAArD,GAAA;AAAAqD,YAAA,CAAAnD,GAAA;AAAAmD,YAAA,CAAAjD,IAAA;AAAAiD,YAAA,CAAA/C,IAAA;AAAA+C,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}