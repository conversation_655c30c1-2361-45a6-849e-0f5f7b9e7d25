{"ast": null, "code": "import React from'react';// Componentes de ícones simples usando Unicode\nimport{jsx as _jsx}from\"react/jsx-runtime\";export const Icons={Music:_ref=>{let{size=20}=_ref;return/*#__PURE__*/_jsx(\"span\",{style:{fontSize:size,display:'inline-block'},children:\"\\uD83C\\uDFB5\"});},User:_ref2=>{let{size=20}=_ref2;return/*#__PURE__*/_jsx(\"span\",{style:{fontSize:size,display:'inline-block'},children:\"\\uD83D\\uDC64\"});},LogOut:_ref3=>{let{size=20}=_ref3;return/*#__PURE__*/_jsx(\"span\",{style:{fontSize:size,display:'inline-block'},children:\"\\uD83D\\uDEAA\"});},Home:_ref4=>{let{size=20}=_ref4;return/*#__PURE__*/_jsx(\"span\",{style:{fontSize:size,display:'inline-block'},children:\"\\uD83C\\uDFE0\"});},Plus:_ref5=>{let{size=20}=_ref5;return/*#__PURE__*/_jsx(\"span\",{style:{fontSize:size,display:'inline-block'},children:\"\\u2795\"});},Mail:_ref6=>{let{size=20}=_ref6;return/*#__PURE__*/_jsx(\"span\",{style:{fontSize:size,display:'inline-block'},children:\"\\u2709\\uFE0F\"});},Lock:_ref7=>{let{size=20}=_ref7;return/*#__PURE__*/_jsx(\"span\",{style:{fontSize:size,display:'inline-block'},children:\"\\uD83D\\uDD12\"});},Eye:_ref8=>{let{size=20}=_ref8;return/*#__PURE__*/_jsx(\"span\",{style:{fontSize:size,display:'inline-block'},children:\"\\uD83D\\uDC41\\uFE0F\"});},EyeOff:_ref9=>{let{size=20}=_ref9;return/*#__PURE__*/_jsx(\"span\",{style:{fontSize:size,display:'inline-block'},children:\"\\uD83D\\uDE48\"});}};// Tipo para os ícones", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "Icons", "Music", "_ref", "size", "style", "fontSize", "display", "children", "User", "_ref2", "LogOut", "_ref3", "Home", "_ref4", "Plus", "_ref5", "Mail", "_ref6", "Lock", "_ref7", "Eye", "_ref8", "Eye<PERSON>ff", "_ref9"], "sources": ["D:/Dev/partitura_digital/src/utils/icons.tsx"], "sourcesContent": ["import React from 'react';\n\n// Componentes de ícones simples usando Unicode\nexport const Icons = {\n  Music: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>🎵</span>\n  ),\n  User: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>👤</span>\n  ),\n  LogOut: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>🚪</span>\n  ),\n  Home: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>🏠</span>\n  ),\n  Plus: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>➕</span>\n  ),\n  Mail: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>✉️</span>\n  ),\n  Lock: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>🔒</span>\n  ),\n  Eye: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>👁️</span>\n  ),\n  EyeOff: ({ size = 20 }: { size?: number }) => (\n    <span style={{ fontSize: size, display: 'inline-block' }}>🙈</span>\n  ),\n};\n\n// Tipo para os ícones\nexport type IconComponent = React.FC<{ size?: number }>;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBACA,MAAO,MAAM,CAAAC,KAAK,CAAG,CACnBC,KAAK,CAAEC,IAAA,MAAC,CAAEC,IAAI,CAAG,EAAsB,CAAC,CAAAD,IAAA,oBACtCH,IAAA,SAAMK,KAAK,CAAE,CAAEC,QAAQ,CAAEF,IAAI,CAAEG,OAAO,CAAE,cAAe,CAAE,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,EACpE,CACDC,IAAI,CAAEC,KAAA,MAAC,CAAEN,IAAI,CAAG,EAAsB,CAAC,CAAAM,KAAA,oBACrCV,IAAA,SAAMK,KAAK,CAAE,CAAEC,QAAQ,CAAEF,IAAI,CAAEG,OAAO,CAAE,cAAe,CAAE,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,EACpE,CACDG,MAAM,CAAEC,KAAA,MAAC,CAAER,IAAI,CAAG,EAAsB,CAAC,CAAAQ,KAAA,oBACvCZ,IAAA,SAAMK,KAAK,CAAE,CAAEC,QAAQ,CAAEF,IAAI,CAAEG,OAAO,CAAE,cAAe,CAAE,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,EACpE,CACDK,IAAI,CAAEC,KAAA,MAAC,CAAEV,IAAI,CAAG,EAAsB,CAAC,CAAAU,KAAA,oBACrCd,IAAA,SAAMK,KAAK,CAAE,CAAEC,QAAQ,CAAEF,IAAI,CAAEG,OAAO,CAAE,cAAe,CAAE,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,EACpE,CACDO,IAAI,CAAEC,KAAA,MAAC,CAAEZ,IAAI,CAAG,EAAsB,CAAC,CAAAY,KAAA,oBACrChB,IAAA,SAAMK,KAAK,CAAE,CAAEC,QAAQ,CAAEF,IAAI,CAAEG,OAAO,CAAE,cAAe,CAAE,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,EACnE,CACDS,IAAI,CAAEC,KAAA,MAAC,CAAEd,IAAI,CAAG,EAAsB,CAAC,CAAAc,KAAA,oBACrClB,IAAA,SAAMK,KAAK,CAAE,CAAEC,QAAQ,CAAEF,IAAI,CAAEG,OAAO,CAAE,cAAe,CAAE,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,EACpE,CACDW,IAAI,CAAEC,KAAA,MAAC,CAAEhB,IAAI,CAAG,EAAsB,CAAC,CAAAgB,KAAA,oBACrCpB,IAAA,SAAMK,KAAK,CAAE,CAAEC,QAAQ,CAAEF,IAAI,CAAEG,OAAO,CAAE,cAAe,CAAE,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,EACpE,CACDa,GAAG,CAAEC,KAAA,MAAC,CAAElB,IAAI,CAAG,EAAsB,CAAC,CAAAkB,KAAA,oBACpCtB,IAAA,SAAMK,KAAK,CAAE,CAAEC,QAAQ,CAAEF,IAAI,CAAEG,OAAO,CAAE,cAAe,CAAE,CAAAC,QAAA,CAAC,oBAAG,CAAM,CAAC,EACrE,CACDe,MAAM,CAAEC,KAAA,MAAC,CAAEpB,IAAI,CAAG,EAAsB,CAAC,CAAAoB,KAAA,oBACvCxB,IAAA,SAAMK,KAAK,CAAE,CAAEC,QAAQ,CAAEF,IAAI,CAAEG,OAAO,CAAE,cAAe,CAAE,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,EAEvE,CAAC,CAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}