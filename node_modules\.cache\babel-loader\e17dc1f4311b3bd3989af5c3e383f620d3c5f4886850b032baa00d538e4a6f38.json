{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\pages\\\\NewScore\\\\NewScorePage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { InstrumentSelector } from '../../components/InstrumentSelector/InstrumentSelector';\nimport { getInstrumentTemplate, suggestKeyForInstrument } from '../../utils/instrumentTemplates';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageContainer = styled.div`\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n_c = PageContainer;\nconst PageHeader = styled.div`\n  text-align: center;\n  margin-bottom: 3rem;\n`;\n_c2 = PageHeader;\nconst PageTitle = styled.h1`\n  color: white;\n  margin: 0 0 1rem 0;\n  font-size: 2.5rem;\n  font-weight: 700;\n`;\n_c3 = PageTitle;\nconst PageSubtitle = styled.p`\n  color: rgba(255,255,255,0.8);\n  font-size: 1.2rem;\n  margin: 0;\n`;\n_c4 = PageSubtitle;\nconst SetupForm = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  margin-bottom: 2rem;\n`;\n_c5 = SetupForm;\nconst FormSection = styled.div`\n  margin-bottom: 2rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n_c6 = FormSection;\nconst SectionTitle = styled.h3`\n  color: #2c3e50;\n  margin: 0 0 1rem 0;\n  font-size: 1.3rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n_c7 = SectionTitle;\nconst FormRow = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1rem;\n`;\n_c8 = FormRow;\nconst FormGroup = styled.div`\n  label {\n    display: block;\n    margin-bottom: 0.5rem;\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input, select {\n    width: 100%;\n    padding: 0.75rem;\n    border: 2px solid #e9ecef;\n    border-radius: 8px;\n    font-size: 1rem;\n    transition: border-color 0.2s;\n    box-sizing: border-box;\n    \n    &:focus {\n      outline: none;\n      border-color: #667eea;\n    }\n  }\n`;\n_c9 = FormGroup;\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-top: 2rem;\n`;\n_c0 = ActionButtons;\nconst ActionButton = styled.button`\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n    }\n  ` : `\n    background: #6c757d;\n    color: white;\n    \n    &:hover {\n      background: #5a6268;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c1 = ActionButton;\nconst PreviewCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  border: 2px solid #e9ecef;\n  margin-top: 1rem;\n`;\n_c10 = PreviewCard;\nconst PreviewTitle = styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;\n_c11 = PreviewTitle;\nconst PreviewDetail = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n_c12 = PreviewDetail;\nconst PreviewLabel = styled.span`\n  font-weight: 600;\n  color: #495057;\n`;\n_c13 = PreviewLabel;\nconst PreviewValue = styled.span`\n  color: #666;\n`;\n_c14 = PreviewValue;\nexport const NewScorePage = ({\n  onCreateScore,\n  onCancel\n}) => {\n  _s();\n  const [config, setConfig] = useState({\n    title: 'Nova Partitura',\n    composer: '',\n    instrument: 'piano',\n    key: 'C',\n    keyMode: 'major',\n    timeSignatureNum: 4,\n    timeSignatureDen: 4,\n    tempo: 120\n  });\n  const handleInstrumentChange = instrument => {\n    const suggestedKey = suggestKeyForInstrument(instrument);\n    setConfig(prev => ({\n      ...prev,\n      instrument,\n      key: suggestedKey\n    }));\n  };\n  const handleSubmit = () => {\n    if (!config.title.trim()) {\n      alert('Por favor, digite um título para a partitura');\n      return;\n    }\n    onCreateScore(config);\n  };\n  const instrumentTemplate = getInstrumentTemplate(config.instrument);\n  const notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];\n  return /*#__PURE__*/_jsxDEV(PageContainer, {\n    children: [/*#__PURE__*/_jsxDEV(PageHeader, {\n      children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n        children: \"\\uD83C\\uDFBC Nova Partitura\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageSubtitle, {\n        children: \"Configure sua nova composi\\xE7\\xE3o musical\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SetupForm, {\n      children: [/*#__PURE__*/_jsxDEV(FormSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"\\uD83D\\uDCDD Informa\\xE7\\xF5es B\\xE1sicas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormRow, {\n          children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"T\\xEDtulo da Partitura:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: config.title,\n              onChange: e => setConfig(prev => ({\n                ...prev,\n                title: e.target.value\n              })),\n              placeholder: \"Digite o t\\xEDtulo...\",\n              autoFocus: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Compositor (opcional):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: config.composer,\n              onChange: e => setConfig(prev => ({\n                ...prev,\n                composer: e.target.value\n              })),\n              placeholder: \"Nome do compositor...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"\\uD83C\\uDFB9 Instrumento\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InstrumentSelector, {\n          selectedInstrument: config.instrument,\n          onInstrumentChange: handleInstrumentChange,\n          compact: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: \"\\uD83C\\uDFB5 Configura\\xE7\\xF5es Musicais\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormRow, {\n          children: [/*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Tonalidade:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: config.key,\n              onChange: e => setConfig(prev => ({\n                ...prev,\n                key: e.target.value\n              })),\n              children: notes.map(note => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: note,\n                children: note\n              }, note, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Modo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: config.keyMode,\n              onChange: e => setConfig(prev => ({\n                ...prev,\n                keyMode: e.target.value\n              })),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"major\",\n                children: \"Maior\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"minor\",\n                children: \"Menor\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Compasso:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '0.5rem',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: config.timeSignatureNum,\n                onChange: e => setConfig(prev => ({\n                  ...prev,\n                  timeSignatureNum: parseInt(e.target.value)\n                })),\n                style: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 2,\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 3,\n                  children: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 4,\n                  children: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 6,\n                  children: \"6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 9,\n                  children: \"9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 12,\n                  children: \"12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"/\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: config.timeSignatureDen,\n                onChange: e => setConfig(prev => ({\n                  ...prev,\n                  timeSignatureDen: parseInt(e.target.value)\n                })),\n                style: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 2,\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 4,\n                  children: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 8,\n                  children: \"8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: 16,\n                  children: \"16\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Tempo (BPM):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              min: \"40\",\n              max: \"200\",\n              value: config.tempo,\n              onChange: e => setConfig(prev => ({\n                ...prev,\n                tempo: parseInt(e.target.value) || 120\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PreviewCard, {\n        children: [/*#__PURE__*/_jsxDEV(PreviewTitle, {\n          children: \"\\uD83D\\uDCCB Resumo da Configura\\xE7\\xE3o\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PreviewDetail, {\n          children: [/*#__PURE__*/_jsxDEV(PreviewLabel, {\n            children: \"T\\xEDtulo:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PreviewValue, {\n            children: config.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), config.composer && /*#__PURE__*/_jsxDEV(PreviewDetail, {\n          children: [/*#__PURE__*/_jsxDEV(PreviewLabel, {\n            children: \"Compositor:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(PreviewValue, {\n            children: config.composer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(PreviewDetail, {\n          children: [/*#__PURE__*/_jsxDEV(PreviewLabel, {\n            children: \"Instrumento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PreviewValue, {\n            children: [instrumentTemplate.emoji, \" \", instrumentTemplate.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PreviewDetail, {\n          children: [/*#__PURE__*/_jsxDEV(PreviewLabel, {\n            children: \"Tonalidade:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PreviewValue, {\n            children: [config.key, \" \", config.keyMode === 'major' ? 'Maior' : 'Menor']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PreviewDetail, {\n          children: [/*#__PURE__*/_jsxDEV(PreviewLabel, {\n            children: \"Compasso:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PreviewValue, {\n            children: [config.timeSignatureNum, \"/\", config.timeSignatureDen]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PreviewDetail, {\n          children: [/*#__PURE__*/_jsxDEV(PreviewLabel, {\n            children: \"Tempo:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PreviewValue, {\n            children: [config.tempo, \" BPM\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ActionButtons, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          variant: \"secondary\",\n          onClick: onCancel,\n          children: \"\\u274C Cancelar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          variant: \"primary\",\n          onClick: handleSubmit,\n          children: \"\\u2705 Criar Partitura\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n};\n_s(NewScorePage, \"u+wYRrGFT2mVDnGt46tCU1Tnmm8=\");\n_c15 = NewScorePage;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"PageHeader\");\n$RefreshReg$(_c3, \"PageTitle\");\n$RefreshReg$(_c4, \"PageSubtitle\");\n$RefreshReg$(_c5, \"SetupForm\");\n$RefreshReg$(_c6, \"FormSection\");\n$RefreshReg$(_c7, \"SectionTitle\");\n$RefreshReg$(_c8, \"FormRow\");\n$RefreshReg$(_c9, \"FormGroup\");\n$RefreshReg$(_c0, \"ActionButtons\");\n$RefreshReg$(_c1, \"ActionButton\");\n$RefreshReg$(_c10, \"PreviewCard\");\n$RefreshReg$(_c11, \"PreviewTitle\");\n$RefreshReg$(_c12, \"PreviewDetail\");\n$RefreshReg$(_c13, \"PreviewLabel\");\n$RefreshReg$(_c14, \"PreviewValue\");\n$RefreshReg$(_c15, \"NewScorePage\");", "map": {"version": 3, "names": ["React", "useState", "styled", "InstrumentSelector", "getInstrumentTemplate", "suggestKeyForInstrument", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "<PERSON><PERSON><PERSON><PERSON>", "_c2", "Page<PERSON><PERSON>le", "h1", "_c3", "PageSubtitle", "p", "_c4", "SetupForm", "_c5", "FormSection", "_c6", "SectionTitle", "h3", "_c7", "FormRow", "_c8", "FormGroup", "_c9", "ActionButtons", "_c0", "ActionButton", "button", "props", "variant", "_c1", "PreviewCard", "_c10", "PreviewTitle", "h4", "_c11", "PreviewDetail", "_c12", "PreviewLabel", "span", "_c13", "PreviewValue", "_c14", "NewScorePage", "onCreateScore", "onCancel", "_s", "config", "setConfig", "title", "composer", "instrument", "key", "keyMode", "timeSignatureNum", "timeSignatureDen", "tempo", "handleInstrumentChange", "<PERSON><PERSON><PERSON>", "prev", "handleSubmit", "trim", "alert", "instrumentTemplate", "notes", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "e", "target", "placeholder", "autoFocus", "selectedInstrument", "onInstrumentChange", "compact", "map", "note", "style", "display", "gap", "alignItems", "parseInt", "flex", "min", "max", "emoji", "name", "onClick", "_c15", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/pages/NewScore/NewScorePage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { InstrumentType, NoteName } from '../../types/music';\nimport { InstrumentSelector } from '../../components/InstrumentSelector/InstrumentSelector';\nimport { getInstrumentTemplate, suggestKeyForInstrument } from '../../utils/instrumentTemplates';\n\nconst PageContainer = styled.div`\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n\nconst PageHeader = styled.div`\n  text-align: center;\n  margin-bottom: 3rem;\n`;\n\nconst PageTitle = styled.h1`\n  color: white;\n  margin: 0 0 1rem 0;\n  font-size: 2.5rem;\n  font-weight: 700;\n`;\n\nconst PageSubtitle = styled.p`\n  color: rgba(255,255,255,0.8);\n  font-size: 1.2rem;\n  margin: 0;\n`;\n\nconst SetupForm = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  margin-bottom: 2rem;\n`;\n\nconst FormSection = styled.div`\n  margin-bottom: 2rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\nconst SectionTitle = styled.h3`\n  color: #2c3e50;\n  margin: 0 0 1rem 0;\n  font-size: 1.3rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n`;\n\nconst FormRow = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1rem;\n`;\n\nconst FormGroup = styled.div`\n  label {\n    display: block;\n    margin-bottom: 0.5rem;\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input, select {\n    width: 100%;\n    padding: 0.75rem;\n    border: 2px solid #e9ecef;\n    border-radius: 8px;\n    font-size: 1rem;\n    transition: border-color 0.2s;\n    box-sizing: border-box;\n    \n    &:focus {\n      outline: none;\n      border-color: #667eea;\n    }\n  }\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-top: 2rem;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n    }\n  ` : `\n    background: #6c757d;\n    color: white;\n    \n    &:hover {\n      background: #5a6268;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst PreviewCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  border: 2px solid #e9ecef;\n  margin-top: 1rem;\n`;\n\nconst PreviewTitle = styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;\n\nconst PreviewDetail = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\nconst PreviewLabel = styled.span`\n  font-weight: 600;\n  color: #495057;\n`;\n\nconst PreviewValue = styled.span`\n  color: #666;\n`;\n\ninterface NewScorePageProps {\n  onCreateScore: (config: ScoreConfig) => void;\n  onCancel: () => void;\n}\n\nexport interface ScoreConfig {\n  title: string;\n  composer: string;\n  instrument: InstrumentType;\n  key: NoteName;\n  keyMode: 'major' | 'minor';\n  timeSignatureNum: number;\n  timeSignatureDen: number;\n  tempo: number;\n}\n\nexport const NewScorePage: React.FC<NewScorePageProps> = ({ onCreateScore, onCancel }) => {\n  const [config, setConfig] = useState<ScoreConfig>({\n    title: 'Nova Partitura',\n    composer: '',\n    instrument: 'piano',\n    key: 'C' as NoteName,\n    keyMode: 'major',\n    timeSignatureNum: 4,\n    timeSignatureDen: 4,\n    tempo: 120\n  });\n\n  const handleInstrumentChange = (instrument: InstrumentType) => {\n    const suggestedKey = suggestKeyForInstrument(instrument);\n    setConfig(prev => ({\n      ...prev,\n      instrument,\n      key: suggestedKey as NoteName\n    }));\n  };\n\n  const handleSubmit = () => {\n    if (!config.title.trim()) {\n      alert('Por favor, digite um título para a partitura');\n      return;\n    }\n    \n    onCreateScore(config);\n  };\n\n  const instrumentTemplate = getInstrumentTemplate(config.instrument);\n  const notes: NoteName[] = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];\n\n  return (\n    <PageContainer>\n      <PageHeader>\n        <PageTitle>🎼 Nova Partitura</PageTitle>\n        <PageSubtitle>Configure sua nova composição musical</PageSubtitle>\n      </PageHeader>\n\n      <SetupForm>\n        <FormSection>\n          <SectionTitle>📝 Informações Básicas</SectionTitle>\n          <FormRow>\n            <FormGroup>\n              <label>Título da Partitura:</label>\n              <input\n                type=\"text\"\n                value={config.title}\n                onChange={(e) => setConfig(prev => ({ ...prev, title: e.target.value }))}\n                placeholder=\"Digite o título...\"\n                autoFocus\n              />\n            </FormGroup>\n            <FormGroup>\n              <label>Compositor (opcional):</label>\n              <input\n                type=\"text\"\n                value={config.composer}\n                onChange={(e) => setConfig(prev => ({ ...prev, composer: e.target.value }))}\n                placeholder=\"Nome do compositor...\"\n              />\n            </FormGroup>\n          </FormRow>\n        </FormSection>\n\n        <FormSection>\n          <SectionTitle>🎹 Instrumento</SectionTitle>\n          <InstrumentSelector\n            selectedInstrument={config.instrument}\n            onInstrumentChange={handleInstrumentChange}\n            compact={false}\n          />\n        </FormSection>\n\n        <FormSection>\n          <SectionTitle>🎵 Configurações Musicais</SectionTitle>\n          <FormRow>\n            <FormGroup>\n              <label>Tonalidade:</label>\n              <select\n                value={config.key}\n                onChange={(e) => setConfig(prev => ({ ...prev, key: e.target.value as NoteName }))}\n              >\n                {notes.map(note => (\n                  <option key={note} value={note}>{note}</option>\n                ))}\n              </select>\n            </FormGroup>\n            <FormGroup>\n              <label>Modo:</label>\n              <select\n                value={config.keyMode}\n                onChange={(e) => setConfig(prev => ({ ...prev, keyMode: e.target.value as 'major' | 'minor' }))}\n              >\n                <option value=\"major\">Maior</option>\n                <option value=\"minor\">Menor</option>\n              </select>\n            </FormGroup>\n            <FormGroup>\n              <label>Compasso:</label>\n              <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>\n                <select\n                  value={config.timeSignatureNum}\n                  onChange={(e) => setConfig(prev => ({ ...prev, timeSignatureNum: parseInt(e.target.value) }))}\n                  style={{ flex: 1 }}\n                >\n                  <option value={2}>2</option>\n                  <option value={3}>3</option>\n                  <option value={4}>4</option>\n                  <option value={6}>6</option>\n                  <option value={9}>9</option>\n                  <option value={12}>12</option>\n                </select>\n                <span>/</span>\n                <select\n                  value={config.timeSignatureDen}\n                  onChange={(e) => setConfig(prev => ({ ...prev, timeSignatureDen: parseInt(e.target.value) }))}\n                  style={{ flex: 1 }}\n                >\n                  <option value={2}>2</option>\n                  <option value={4}>4</option>\n                  <option value={8}>8</option>\n                  <option value={16}>16</option>\n                </select>\n              </div>\n            </FormGroup>\n            <FormGroup>\n              <label>Tempo (BPM):</label>\n              <input\n                type=\"number\"\n                min=\"40\"\n                max=\"200\"\n                value={config.tempo}\n                onChange={(e) => setConfig(prev => ({ ...prev, tempo: parseInt(e.target.value) || 120 }))}\n              />\n            </FormGroup>\n          </FormRow>\n        </FormSection>\n\n        <PreviewCard>\n          <PreviewTitle>📋 Resumo da Configuração</PreviewTitle>\n          <PreviewDetail>\n            <PreviewLabel>Título:</PreviewLabel>\n            <PreviewValue>{config.title}</PreviewValue>\n          </PreviewDetail>\n          {config.composer && (\n            <PreviewDetail>\n              <PreviewLabel>Compositor:</PreviewLabel>\n              <PreviewValue>{config.composer}</PreviewValue>\n            </PreviewDetail>\n          )}\n          <PreviewDetail>\n            <PreviewLabel>Instrumento:</PreviewLabel>\n            <PreviewValue>{instrumentTemplate.emoji} {instrumentTemplate.name}</PreviewValue>\n          </PreviewDetail>\n          <PreviewDetail>\n            <PreviewLabel>Tonalidade:</PreviewLabel>\n            <PreviewValue>{config.key} {config.keyMode === 'major' ? 'Maior' : 'Menor'}</PreviewValue>\n          </PreviewDetail>\n          <PreviewDetail>\n            <PreviewLabel>Compasso:</PreviewLabel>\n            <PreviewValue>{config.timeSignatureNum}/{config.timeSignatureDen}</PreviewValue>\n          </PreviewDetail>\n          <PreviewDetail>\n            <PreviewLabel>Tempo:</PreviewLabel>\n            <PreviewValue>{config.tempo} BPM</PreviewValue>\n          </PreviewDetail>\n        </PreviewCard>\n\n        <ActionButtons>\n          <ActionButton variant=\"secondary\" onClick={onCancel}>\n            ❌ Cancelar\n          </ActionButton>\n          <ActionButton variant=\"primary\" onClick={handleSubmit}>\n            ✅ Criar Partitura\n          </ActionButton>\n        </ActionButtons>\n      </SetupForm>\n    </PageContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AAEtC,SAASC,kBAAkB,QAAQ,wDAAwD;AAC3F,SAASC,qBAAqB,EAAEC,uBAAuB,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,MAAMC,aAAa,GAAGN,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,aAAa;AAMnB,MAAMG,UAAU,GAAGT,MAAM,CAACO,GAAG;AAC7B;AACA;AACA,CAAC;AAACG,GAAA,GAHID,UAAU;AAKhB,MAAME,SAAS,GAAGX,MAAM,CAACY,EAAE;AAC3B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,SAAS;AAOf,MAAMG,YAAY,GAAGd,MAAM,CAACe,CAAC;AAC7B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,YAAY;AAMlB,MAAMG,SAAS,GAAGjB,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GANID,SAAS;AAQf,MAAME,WAAW,GAAGnB,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GANID,WAAW;AAQjB,MAAME,YAAY,GAAGrB,MAAM,CAACsB,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAPIF,YAAY;AASlB,MAAMG,OAAO,GAAGxB,MAAM,CAACO,GAAG;AAC1B;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GALID,OAAO;AAOb,MAAME,SAAS,GAAG1B,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAtBID,SAAS;AAwBf,MAAME,aAAa,GAAG5B,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GALID,aAAa;AAOnB,MAAME,YAAY,GAAG9B,MAAM,CAAC+B,MAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhCIJ,YAAY;AAkClB,MAAMK,WAAW,GAAGnC,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6B,IAAA,GANID,WAAW;AAQjB,MAAME,YAAY,GAAGrC,MAAM,CAACsC,EAAE;AAC9B;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAJIF,YAAY;AAMlB,MAAMG,aAAa,GAAGxC,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GATID,aAAa;AAWnB,MAAME,YAAY,GAAG1C,MAAM,CAAC2C,IAAI;AAChC;AACA;AACA,CAAC;AAACC,IAAA,GAHIF,YAAY;AAKlB,MAAMG,YAAY,GAAG7C,MAAM,CAAC2C,IAAI;AAChC;AACA,CAAC;AAACG,IAAA,GAFID,YAAY;AAoBlB,OAAO,MAAME,YAAyC,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrD,QAAQ,CAAc;IAChDsD,KAAK,EAAE,gBAAgB;IACvBC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,OAAO;IACnBC,GAAG,EAAE,GAAe;IACpBC,OAAO,EAAE,OAAO;IAChBC,gBAAgB,EAAE,CAAC;IACnBC,gBAAgB,EAAE,CAAC;IACnBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,sBAAsB,GAAIN,UAA0B,IAAK;IAC7D,MAAMO,YAAY,GAAG3D,uBAAuB,CAACoD,UAAU,CAAC;IACxDH,SAAS,CAACW,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPR,UAAU;MACVC,GAAG,EAAEM;IACP,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACb,MAAM,CAACE,KAAK,CAACY,IAAI,CAAC,CAAC,EAAE;MACxBC,KAAK,CAAC,8CAA8C,CAAC;MACrD;IACF;IAEAlB,aAAa,CAACG,MAAM,CAAC;EACvB,CAAC;EAED,MAAMgB,kBAAkB,GAAGjE,qBAAqB,CAACiD,MAAM,CAACI,UAAU,CAAC;EACnE,MAAMa,KAAiB,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;EAE3F,oBACE/D,OAAA,CAACC,aAAa;IAAA+D,QAAA,gBACZhE,OAAA,CAACI,UAAU;MAAA4D,QAAA,gBACThE,OAAA,CAACM,SAAS;QAAA0D,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACxCpE,OAAA,CAACS,YAAY;QAAAuD,QAAA,EAAC;MAAqC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,eAEbpE,OAAA,CAACY,SAAS;MAAAoD,QAAA,gBACRhE,OAAA,CAACc,WAAW;QAAAkD,QAAA,gBACVhE,OAAA,CAACgB,YAAY;UAAAgD,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACnDpE,OAAA,CAACmB,OAAO;UAAA6C,QAAA,gBACNhE,OAAA,CAACqB,SAAS;YAAA2C,QAAA,gBACRhE,OAAA;cAAAgE,QAAA,EAAO;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnCpE,OAAA;cACEqE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAExB,MAAM,CAACE,KAAM;cACpBuB,QAAQ,EAAGC,CAAC,IAAKzB,SAAS,CAACW,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEV,KAAK,EAAEwB,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAC,CAAE;cACzEI,WAAW,EAAC,uBAAoB;cAChCC,SAAS;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZpE,OAAA,CAACqB,SAAS;YAAA2C,QAAA,gBACRhE,OAAA;cAAAgE,QAAA,EAAO;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrCpE,OAAA;cACEqE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAExB,MAAM,CAACG,QAAS;cACvBsB,QAAQ,EAAGC,CAAC,IAAKzB,SAAS,CAACW,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAET,QAAQ,EAAEuB,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAC,CAAE;cAC5EI,WAAW,EAAC;YAAuB;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEdpE,OAAA,CAACc,WAAW;QAAAkD,QAAA,gBACVhE,OAAA,CAACgB,YAAY;UAAAgD,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAC3CpE,OAAA,CAACJ,kBAAkB;UACjBgF,kBAAkB,EAAE9B,MAAM,CAACI,UAAW;UACtC2B,kBAAkB,EAAErB,sBAAuB;UAC3CsB,OAAO,EAAE;QAAM;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAEdpE,OAAA,CAACc,WAAW;QAAAkD,QAAA,gBACVhE,OAAA,CAACgB,YAAY;UAAAgD,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACtDpE,OAAA,CAACmB,OAAO;UAAA6C,QAAA,gBACNhE,OAAA,CAACqB,SAAS;YAAA2C,QAAA,gBACRhE,OAAA;cAAAgE,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BpE,OAAA;cACEsE,KAAK,EAAExB,MAAM,CAACK,GAAI;cAClBoB,QAAQ,EAAGC,CAAC,IAAKzB,SAAS,CAACW,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEP,GAAG,EAAEqB,CAAC,CAACC,MAAM,CAACH;cAAkB,CAAC,CAAC,CAAE;cAAAN,QAAA,EAElFD,KAAK,CAACgB,GAAG,CAACC,IAAI,iBACbhF,OAAA;gBAAmBsE,KAAK,EAAEU,IAAK;gBAAAhB,QAAA,EAAEgB;cAAI,GAAxBA,IAAI;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACZpE,OAAA,CAACqB,SAAS;YAAA2C,QAAA,gBACRhE,OAAA;cAAAgE,QAAA,EAAO;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpBpE,OAAA;cACEsE,KAAK,EAAExB,MAAM,CAACM,OAAQ;cACtBmB,QAAQ,EAAGC,CAAC,IAAKzB,SAAS,CAACW,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEN,OAAO,EAAEoB,CAAC,CAACC,MAAM,CAACH;cAA2B,CAAC,CAAC,CAAE;cAAAN,QAAA,gBAEhGhE,OAAA;gBAAQsE,KAAK,EAAC,OAAO;gBAAAN,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCpE,OAAA;gBAAQsE,KAAK,EAAC,OAAO;gBAAAN,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACZpE,OAAA,CAACqB,SAAS;YAAA2C,QAAA,gBACRhE,OAAA;cAAAgE,QAAA,EAAO;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACxBpE,OAAA;cAAKiF,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,GAAG,EAAE,QAAQ;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAApB,QAAA,gBACnEhE,OAAA;gBACEsE,KAAK,EAAExB,MAAM,CAACO,gBAAiB;gBAC/BkB,QAAQ,EAAGC,CAAC,IAAKzB,SAAS,CAACW,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEL,gBAAgB,EAAEgC,QAAQ,CAACb,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE,CAAC,CAAC,CAAE;gBAC9FW,KAAK,EAAE;kBAAEK,IAAI,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,gBAEnBhE,OAAA;kBAAQsE,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BpE,OAAA;kBAAQsE,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BpE,OAAA;kBAAQsE,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BpE,OAAA;kBAAQsE,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BpE,OAAA;kBAAQsE,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BpE,OAAA;kBAAQsE,KAAK,EAAE,EAAG;kBAAAN,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACTpE,OAAA;gBAAAgE,QAAA,EAAM;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACdpE,OAAA;gBACEsE,KAAK,EAAExB,MAAM,CAACQ,gBAAiB;gBAC/BiB,QAAQ,EAAGC,CAAC,IAAKzB,SAAS,CAACW,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEJ,gBAAgB,EAAE+B,QAAQ,CAACb,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE,CAAC,CAAC,CAAE;gBAC9FW,KAAK,EAAE;kBAAEK,IAAI,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,gBAEnBhE,OAAA;kBAAQsE,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BpE,OAAA;kBAAQsE,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BpE,OAAA;kBAAQsE,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BpE,OAAA;kBAAQsE,KAAK,EAAE,EAAG;kBAAAN,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACZpE,OAAA,CAACqB,SAAS;YAAA2C,QAAA,gBACRhE,OAAA;cAAAgE,QAAA,EAAO;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3BpE,OAAA;cACEqE,IAAI,EAAC,QAAQ;cACbkB,GAAG,EAAC,IAAI;cACRC,GAAG,EAAC,KAAK;cACTlB,KAAK,EAAExB,MAAM,CAACS,KAAM;cACpBgB,QAAQ,EAAGC,CAAC,IAAKzB,SAAS,CAACW,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEH,KAAK,EAAE8B,QAAQ,CAACb,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;cAAI,CAAC,CAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEdpE,OAAA,CAAC8B,WAAW;QAAAkC,QAAA,gBACVhE,OAAA,CAACgC,YAAY;UAAAgC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACtDpE,OAAA,CAACmC,aAAa;UAAA6B,QAAA,gBACZhE,OAAA,CAACqC,YAAY;YAAA2B,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACpCpE,OAAA,CAACwC,YAAY;YAAAwB,QAAA,EAAElB,MAAM,CAACE;UAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,EACftB,MAAM,CAACG,QAAQ,iBACdjD,OAAA,CAACmC,aAAa;UAAA6B,QAAA,gBACZhE,OAAA,CAACqC,YAAY;YAAA2B,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACxCpE,OAAA,CAACwC,YAAY;YAAAwB,QAAA,EAAElB,MAAM,CAACG;UAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAChB,eACDpE,OAAA,CAACmC,aAAa;UAAA6B,QAAA,gBACZhE,OAAA,CAACqC,YAAY;YAAA2B,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACzCpE,OAAA,CAACwC,YAAY;YAAAwB,QAAA,GAAEF,kBAAkB,CAAC2B,KAAK,EAAC,GAAC,EAAC3B,kBAAkB,CAAC4B,IAAI;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAChBpE,OAAA,CAACmC,aAAa;UAAA6B,QAAA,gBACZhE,OAAA,CAACqC,YAAY;YAAA2B,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACxCpE,OAAA,CAACwC,YAAY;YAAAwB,QAAA,GAAElB,MAAM,CAACK,GAAG,EAAC,GAAC,EAACL,MAAM,CAACM,OAAO,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO;UAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eAChBpE,OAAA,CAACmC,aAAa;UAAA6B,QAAA,gBACZhE,OAAA,CAACqC,YAAY;YAAA2B,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACtCpE,OAAA,CAACwC,YAAY;YAAAwB,QAAA,GAAElB,MAAM,CAACO,gBAAgB,EAAC,GAAC,EAACP,MAAM,CAACQ,gBAAgB;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eAChBpE,OAAA,CAACmC,aAAa;UAAA6B,QAAA,gBACZhE,OAAA,CAACqC,YAAY;YAAA2B,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACnCpE,OAAA,CAACwC,YAAY;YAAAwB,QAAA,GAAElB,MAAM,CAACS,KAAK,EAAC,MAAI;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEdpE,OAAA,CAACuB,aAAa;QAAAyC,QAAA,gBACZhE,OAAA,CAACyB,YAAY;UAACG,OAAO,EAAC,WAAW;UAAC+D,OAAO,EAAE/C,QAAS;UAAAoB,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACfpE,OAAA,CAACyB,YAAY;UAACG,OAAO,EAAC,SAAS;UAAC+D,OAAO,EAAEhC,YAAa;UAAAK,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEpB,CAAC;AAACvB,EAAA,CArLWH,YAAyC;AAAAkD,IAAA,GAAzClD,YAAyC;AAAA,IAAAvC,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAmD,IAAA;AAAAC,YAAA,CAAA1F,EAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}