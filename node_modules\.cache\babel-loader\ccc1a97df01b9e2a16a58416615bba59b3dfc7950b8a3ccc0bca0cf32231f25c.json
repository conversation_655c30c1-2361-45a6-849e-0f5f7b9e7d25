{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\utils\\\\icons.tsx\";\nimport React from 'react';\nimport { FiMusic, FiUser, FiLogOut, FiHome, FiPlus, FiMail, FiLock, FiEye, FiEyeOff } from 'react-icons/fi';\n\n// Wrapper para os ícones para resolver problemas de TypeScript\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Icons = {\n  Music: props => /*#__PURE__*/_jsxDEV(FiMusic, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 60\n  }, this),\n  User: props => /*#__PURE__*/_jsxDEV(FiUser, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 59\n  }, this),\n  LogOut: props => /*#__PURE__*/_jsxDEV(FiLogOut, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 61\n  }, this),\n  Home: props => /*#__PURE__*/_jsxDEV(FiHome, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 59\n  }, this),\n  Plus: props => /*#__PURE__*/_jsxDEV(FiPlus, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 59\n  }, this),\n  Mail: props => /*#__PURE__*/_jsxDEV(FiMail, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 59\n  }, this),\n  Lock: props => /*#__PURE__*/_jsxDEV(FiLock, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 59\n  }, this),\n  Eye: props => /*#__PURE__*/_jsxDEV(FiEye, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 58\n  }, this),\n  EyeOff: props => /*#__PURE__*/_jsxDEV(FiEyeOff, {\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 61\n  }, this)\n};\n\n// Tipo para os ícones", "map": {"version": 3, "names": ["React", "FiMusic", "FiUser", "FiLogOut", "FiHome", "FiPlus", "FiMail", "FiLock", "FiEye", "Fi<PERSON>ye<PERSON>ff", "jsxDEV", "_jsxDEV", "Icons", "Music", "props", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "User", "LogOut", "Home", "Plus", "Mail", "Lock", "Eye", "Eye<PERSON>ff"], "sources": ["D:/Dev/partitura_digital/src/utils/icons.tsx"], "sourcesContent": ["import React from 'react';\nimport { \n  FiMusic, \n  FiUser, \n  FiLogOut, \n  FiHome, \n  FiPlus, \n  FiMail, \n  FiLock, \n  FiEye, \n  FiEyeOff \n} from 'react-icons/fi';\n\n// Wrapper para os ícones para resolver problemas de TypeScript\nexport const Icons = {\n  Music: (props: { size?: number; className?: string }) => <FiMusic {...props} />,\n  User: (props: { size?: number; className?: string }) => <FiUser {...props} />,\n  LogOut: (props: { size?: number; className?: string }) => <FiLogOut {...props} />,\n  Home: (props: { size?: number; className?: string }) => <FiHome {...props} />,\n  Plus: (props: { size?: number; className?: string }) => <FiPlus {...props} />,\n  Mail: (props: { size?: number; className?: string }) => <FiMail {...props} />,\n  Lock: (props: { size?: number; className?: string }) => <FiLock {...props} />,\n  Eye: (props: { size?: number; className?: string }) => <FiEye {...props} />,\n  EyeOff: (props: { size?: number; className?: string }) => <FiEyeOff {...props} />,\n};\n\n// Tipo para os ícones\nexport type IconComponent = React.FC<{ size?: number; className?: string }>;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,QAAQ,QACH,gBAAgB;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,KAAK,GAAG;EACnBC,KAAK,EAAGC,KAA4C,iBAAKH,OAAA,CAACV,OAAO;IAAA,GAAKa;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAC/EC,IAAI,EAAGL,KAA4C,iBAAKH,OAAA,CAACT,MAAM;IAAA,GAAKY;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAC7EE,MAAM,EAAGN,KAA4C,iBAAKH,OAAA,CAACR,QAAQ;IAAA,GAAKW;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACjFG,IAAI,EAAGP,KAA4C,iBAAKH,OAAA,CAACP,MAAM;IAAA,GAAKU;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAC7EI,IAAI,EAAGR,KAA4C,iBAAKH,OAAA,CAACN,MAAM;IAAA,GAAKS;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAC7EK,IAAI,EAAGT,KAA4C,iBAAKH,OAAA,CAACL,MAAM;IAAA,GAAKQ;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAC7EM,IAAI,EAAGV,KAA4C,iBAAKH,OAAA,CAACJ,MAAM;IAAA,GAAKO;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAC7EO,GAAG,EAAGX,KAA4C,iBAAKH,OAAA,CAACH,KAAK;IAAA,GAAKM;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAC3EQ,MAAM,EAAGZ,KAA4C,iBAAKH,OAAA,CAACF,QAAQ;IAAA,GAAKK;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG;AAClF,CAAC;;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}