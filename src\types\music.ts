// Tipos para notas musicais
export type NoteName = 'C' | 'C#' | 'D' | 'D#' | 'E' | 'F' | 'F#' | 'G' | 'G#' | 'A' | 'A#' | 'B';
export type Octave = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;
export type NoteDuration = 'whole' | 'half' | 'quarter' | 'eighth' | 'sixteenth' | 'thirty-second';

// Tipos para instrumentos
export type InstrumentType = 'piano' | 'guitar' | 'violin' | 'flute' | 'trumpet' | 'drums' | 'bass' | 'other';

// Tipos para claves
export type ClefType = 'treble' | 'bass' | 'alto' | 'tenor';

// Tipos para acidentes
export type Accidental = 'sharp' | 'flat' | 'natural' | 'double-sharp' | 'double-flat';

// Interface para uma nota musical
export interface MusicalNote {
  id: string;
  name: NoteName;
  octave: Octave;
  duration: NoteDuration;
  accidental?: Accidental;
  position: {
    measure: number;
    beat: number;
    staff: number; // Para partituras com múltiplas pautas
  };
  isRest?: boolean; // Para pausas
}

// Interface para acordes (cifras)
export interface Chord {
  id: string;
  root: NoteName;
  quality: 'major' | 'minor' | 'diminished' | 'augmented' | 'dominant7' | 'major7' | 'minor7' | 'sus2' | 'sus4';
  bass?: NoteName; // Para inversões
  position: {
    measure: number;
    beat: number;
  };
}

// Interface para compasso
export interface Measure {
  id: string;
  number: number;
  timeSignature: {
    numerator: number;
    denominator: number;
  };
  notes: MusicalNote[];
  chords: Chord[];
}

// Interface para uma pauta
export interface Staff {
  id: string;
  clef: ClefType;
  instrument: InstrumentType;
  measures: Measure[];
}

// Interface para letras da música
export interface Lyrics {
  id?: string;
  text: string;
  position: {
    measure: number;
    beat: number;
    staff?: number;
  };
  syllable?: boolean; // Se é uma sílaba de uma palavra maior
}

// Interface principal para uma partitura
export interface Score {
  id: string;
  title: string;
  composer?: string;
  key: {
    note: NoteName;
    mode: 'major' | 'minor';
  };
  timeSignature: {
    numerator: number;
    denominator: number;
  };
  tempo?: number; // BPM
  staffs: Staff[];
  lyrics: Lyrics[];
  createdAt: Date;
  updatedAt: Date;
  userId: string; // ID do usuário que criou
}

// Interface para configurações de visualização
export interface ViewSettings {
  showChords: boolean;
  showLyrics: boolean;
  showStaffNotation: boolean;
  showChordSymbols: boolean;
  zoom: number;
  pageLayout: 'single' | 'continuous';
}

// Interface para configurações do editor
export interface EditorSettings {
  snapToGrid: boolean;
  playbackEnabled: boolean;
  metronomeEnabled: boolean;
  autoSave: boolean;
  defaultNoteDuration: NoteDuration;
  defaultInstrument: InstrumentType;
}
