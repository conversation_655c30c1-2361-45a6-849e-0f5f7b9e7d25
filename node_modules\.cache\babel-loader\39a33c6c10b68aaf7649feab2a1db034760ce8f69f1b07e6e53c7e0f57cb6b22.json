{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ProfessionalScore\\\\ProfessionalScore.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport styled from 'styled-components';\n// import { Factory, Renderer, Stave, StaveNote, Voice, Formatter, Accidental } from 'vexflow';\nimport * as Tone from 'tone';\nimport { getInstrumentTemplate } from '../../utils/instrumentTemplates';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ScoreContainer = styled.div`\n  background: white;\n  border-radius: 16px;\n  padding: 2rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  margin: 1rem 0;\n  overflow-x: auto;\n`;\n_c = ScoreContainer;\nconst ScoreHeader = styled.div`\n  text-align: center;\n  margin-bottom: 2rem;\n  border-bottom: 2px solid #e9ecef;\n  padding-bottom: 1rem;\n`;\n_c2 = ScoreHeader;\nconst ScoreTitle = styled.h1`\n  font-size: 2rem;\n  color: #2c3e50;\n  margin: 0 0 0.5rem 0;\n  font-family: 'Times New Roman', serif;\n`;\n_c3 = ScoreTitle;\nconst ScoreSubtitle = styled.div`\n  font-size: 1.1rem;\n  color: #666;\n  font-style: italic;\n`;\n_c4 = ScoreSubtitle;\nconst ScoreCanvas = styled.div`\n  min-height: 400px;\n  width: 100%;\n  \n  svg {\n    width: 100%;\n    height: auto;\n  }\n`;\n_c5 = ScoreCanvas;\nconst PlaybackControls = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 1rem;\n  margin: 1rem 0;\n  padding: 1rem;\n  background: #f8f9fa;\n  border-radius: 12px;\n`;\n_c6 = PlaybackControls;\nconst PlayButton = styled.button`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 50px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.isPlaying ? `\n    background: #e74c3c;\n    color: white;\n    &:hover { background: #c0392b; }\n  ` : `\n    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\n    color: white;\n    &:hover { \n      background: linear-gradient(135deg, #229954 0%, #27ae60 100%);\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);\n    }\n  `}\n`;\n_c7 = PlayButton;\nconst TempoControl = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  \n  label {\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input {\n    width: 80px;\n    padding: 0.5rem;\n    border: 1px solid #dee2e6;\n    border-radius: 6px;\n    text-align: center;\n  }\n`;\n_c8 = TempoControl;\nconst ProgressBar = styled.div`\n  flex: 1;\n  height: 6px;\n  background: #e9ecef;\n  border-radius: 3px;\n  overflow: hidden;\n  position: relative;\n`;\n_c9 = ProgressBar;\nconst ProgressFill = styled.div`\n  height: 100%;\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\n  width: ${props => props.progress}%;\n  transition: width 0.1s ease;\n`;\n_c0 = ProgressFill;\nconst PlaybackLine = styled.line`\n  stroke: #e74c3c;\n  stroke-width: 2;\n  opacity: ${props => props.visible ? 1 : 0};\n  transition: opacity 0.2s ease;\n`;\nexport const ProfessionalScore = ({\n  title,\n  composer,\n  notes,\n  lyrics,\n  instruments,\n  tempo,\n  onTempoChange,\n  onNoteClick\n}) => {\n  _s();\n  const scoreRef = useRef(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [playbackProgress, setPlaybackProgress] = useState(0);\n  const [playbackPosition, setPlaybackPosition] = useState(0);\n  const [synth, setSynth] = useState(null);\n  const [renderer, setRenderer] = useState(null);\n\n  // Inicializar Tone.js\n  useEffect(() => {\n    const synthInstance = new Tone.PolySynth(Tone.Synth).toDestination();\n    setSynth(synthInstance);\n    return () => {\n      synthInstance.dispose();\n    };\n  }, []);\n\n  // Renderizar partitura com VexFlow\n  useEffect(() => {\n    if (!scoreRef.current) return;\n\n    // Limpar canvas anterior\n    scoreRef.current.innerHTML = '';\n    const div = scoreRef.current;\n\n    // Criar renderer\n    const rendererInstance = new Renderer(div, Renderer.Backends.SVG);\n    rendererInstance.resize(800, 600);\n    setRenderer(rendererInstance);\n    const context = rendererInstance.getContext();\n    context.setFont('Arial', 10);\n\n    // Criar sistema de pautas para múltiplos instrumentos\n    let yPosition = 40;\n    const systemHeight = 120;\n    instruments.forEach((instrument, instrumentIndex) => {\n      const template = getInstrumentTemplate(instrument);\n\n      // Criar pauta\n      const stave = new Stave(10, yPosition, 750);\n\n      // Adicionar clave\n      if (template.clef === 'treble') {\n        stave.addClef('treble');\n      } else if (template.clef === 'bass') {\n        stave.addClef('bass');\n      }\n\n      // Adicionar compasso\n      stave.addTimeSignature('4/4');\n\n      // Adicionar nome do instrumento\n      stave.setText(template.name, VF.Modifier.Position.LEFT);\n      stave.setContext(context).draw();\n\n      // Filtrar notas deste instrumento\n      const instrumentNotes = notes.filter(note => note.position.staff === instrumentIndex);\n\n      // Agrupar notas por compasso\n      const measureGroups = new Map();\n      instrumentNotes.forEach(note => {\n        const measure = note.position.measure;\n        if (!measureGroups.has(measure)) {\n          measureGroups.set(measure, []);\n        }\n        measureGroups.get(measure).push(note);\n      });\n\n      // Renderizar notas por compasso\n      Array.from(measureGroups.entries()).forEach(([measureNum, measureNotes]) => {\n        if (measureNotes.length === 0) return;\n\n        // Converter notas para formato VexFlow\n        const vexNotes = measureNotes.sort((a, b) => a.position.beat - b.position.beat).map(note => {\n          const noteName = note.name.toLowerCase();\n          const octave = note.octave;\n          const duration = convertDurationToVex(note.duration);\n          let vexNote;\n          if (note.isRest) {\n            vexNote = new StaveNote({\n              clef: template.clef,\n              keys: ['r/4'],\n              duration: duration + 'r'\n            });\n          } else {\n            const key = `${noteName}/${octave}`;\n            vexNote = new StaveNote({\n              clef: template.clef,\n              keys: [key],\n              duration: duration\n            });\n\n            // Adicionar acidentes\n            if (note.accidental) {\n              if (note.accidental === 'sharp') {\n                vexNote.addModifier(new Accidental('#'), 0);\n              } else if (note.accidental === 'flat') {\n                vexNote.addModifier(new Accidental('b'), 0);\n              } else if (note.accidental === 'natural') {\n                vexNote.addModifier(new Accidental('n'), 0);\n              }\n            }\n          }\n          return vexNote;\n        });\n        if (vexNotes.length > 0) {\n          // Criar voice e adicionar notas\n          const voice = new Voice({\n            num_beats: 4,\n            beat_value: 4\n          });\n          voice.addTickables(vexNotes);\n\n          // Formatar e desenhar\n          new Formatter().joinVoices([voice]).format([voice], 700);\n          voice.draw(context, stave);\n\n          // Adicionar letras se existirem\n          const measureLyrics = lyrics.filter(lyric => lyric.position.measure === measureNum);\n          measureLyrics.forEach((lyric, lyricIndex) => {\n            const x = stave.getX() + (lyric.position.beat - 1) * (stave.getWidth() / 4);\n            const y = yPosition + systemHeight - 10;\n            context.fillText(lyric.text, x, y);\n          });\n        }\n      });\n      yPosition += systemHeight;\n    });\n  }, [notes, lyrics, instruments]);\n\n  // Função para converter duração para formato VexFlow\n  const convertDurationToVex = duration => {\n    const durationMap = {\n      'whole': 'w',\n      'half': 'h',\n      'quarter': 'q',\n      'eighth': '8',\n      'sixteenth': '16'\n    };\n    return durationMap[duration] || 'q';\n  };\n\n  // Função para reproduzir partitura\n  const handlePlay = async () => {\n    if (!synth) return;\n    if (isPlaying) {\n      // Parar reprodução\n      Tone.Transport.stop();\n      Tone.Transport.cancel();\n      setIsPlaying(false);\n      setPlaybackProgress(0);\n      setPlaybackPosition(0);\n      return;\n    }\n\n    // Iniciar Tone.js se necessário\n    if (Tone.context.state !== 'running') {\n      await Tone.start();\n    }\n\n    // Configurar tempo\n    Tone.Transport.bpm.value = tempo;\n\n    // Agendar notas para reprodução\n    const sortedNotes = [...notes].filter(note => !note.isRest).sort((a, b) => {\n      if (a.position.measure !== b.position.measure) {\n        return a.position.measure - b.position.measure;\n      }\n      return a.position.beat - b.position.beat;\n    });\n    sortedNotes.forEach(note => {\n      const time = ((note.position.measure - 1) * 4 + (note.position.beat - 1)) * (60 / tempo);\n      const noteName = `${note.name}${note.octave}`;\n      const duration = getDurationInSeconds(note.duration, tempo);\n      Tone.Transport.schedule(time => {\n        synth.triggerAttackRelease(noteName, duration);\n      }, time);\n    });\n\n    // Calcular duração total\n    const totalDuration = sortedNotes.length > 0 ? ((sortedNotes[sortedNotes.length - 1].position.measure - 1) * 4 + sortedNotes[sortedNotes.length - 1].position.beat) * (60 / tempo) : 4 * (60 / tempo);\n\n    // Atualizar progresso\n    const updateProgress = () => {\n      if (Tone.Transport.state === 'started') {\n        const currentTime = Tone.Transport.seconds;\n        const progress = currentTime / totalDuration * 100;\n        setPlaybackProgress(Math.min(progress, 100));\n        setPlaybackPosition(currentTime);\n        if (progress < 100) {\n          requestAnimationFrame(updateProgress);\n        } else {\n          setIsPlaying(false);\n          setPlaybackProgress(0);\n          setPlaybackPosition(0);\n        }\n      }\n    };\n\n    // Iniciar reprodução\n    Tone.Transport.start();\n    setIsPlaying(true);\n    updateProgress();\n  };\n\n  // Função para calcular duração em segundos\n  const getDurationInSeconds = (duration, bpm) => {\n    const beatDuration = 60 / bpm;\n    const durationMap = {\n      'whole': beatDuration * 4,\n      'half': beatDuration * 2,\n      'quarter': beatDuration,\n      'eighth': beatDuration / 2,\n      'sixteenth': beatDuration / 4\n    };\n    return durationMap[duration] || beatDuration;\n  };\n\n  // Função para lidar com cliques na partitura\n  const handleScoreClick = event => {\n    var _scoreRef$current;\n    if (!onNoteClick || !renderer) return;\n    const rect = (_scoreRef$current = scoreRef.current) === null || _scoreRef$current === void 0 ? void 0 : _scoreRef$current.getBoundingClientRect();\n    if (!rect) return;\n    const x = event.clientX - rect.left;\n    const y = event.clientY - rect.top;\n\n    // Determinar qual pauta foi clicada\n    const staffIndex = Math.floor((y - 40) / 120);\n    if (staffIndex >= 0 && staffIndex < instruments.length) {\n      onNoteClick(x, y, staffIndex);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(ScoreContainer, {\n    children: [/*#__PURE__*/_jsxDEV(ScoreHeader, {\n      children: [/*#__PURE__*/_jsxDEV(ScoreTitle, {\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this), composer && /*#__PURE__*/_jsxDEV(ScoreSubtitle, {\n        children: [\"por \", composer]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 22\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PlaybackControls, {\n      children: [/*#__PURE__*/_jsxDEV(PlayButton, {\n        isPlaying: isPlaying,\n        onClick: handlePlay,\n        children: isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TempoControl, {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Tempo:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          min: \"60\",\n          max: \"200\",\n          value: tempo,\n          onChange: e => onTempoChange(parseInt(e.target.value) || 120)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"BPM\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProgressBar, {\n        children: /*#__PURE__*/_jsxDEV(ProgressFill, {\n          progress: playbackProgress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ScoreCanvas, {\n      ref: scoreRef,\n      onClick: handleScoreClick,\n      style: {\n        cursor: onNoteClick ? 'crosshair' : 'default'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 405,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfessionalScore, \"KWXRS/9Xx0zlUkq31CWesRsXKj8=\");\n_c1 = ProfessionalScore;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"ScoreContainer\");\n$RefreshReg$(_c2, \"ScoreHeader\");\n$RefreshReg$(_c3, \"ScoreTitle\");\n$RefreshReg$(_c4, \"ScoreSubtitle\");\n$RefreshReg$(_c5, \"ScoreCanvas\");\n$RefreshReg$(_c6, \"PlaybackControls\");\n$RefreshReg$(_c7, \"PlayButton\");\n$RefreshReg$(_c8, \"TempoControl\");\n$RefreshReg$(_c9, \"ProgressBar\");\n$RefreshReg$(_c0, \"ProgressFill\");\n$RefreshReg$(_c1, \"ProfessionalScore\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "styled", "<PERSON><PERSON>", "getInstrumentTemplate", "jsxDEV", "_jsxDEV", "ScoreContainer", "div", "_c", "ScoreHeader", "_c2", "ScoreTitle", "h1", "_c3", "ScoreSubtitle", "_c4", "ScoreCanvas", "_c5", "PlaybackControls", "_c6", "PlayButton", "button", "props", "isPlaying", "_c7", "TempoControl", "_c8", "ProgressBar", "_c9", "ProgressFill", "progress", "_c0", "PlaybackLine", "line", "visible", "ProfessionalScore", "title", "composer", "notes", "lyrics", "instruments", "tempo", "onTempoChange", "onNoteClick", "_s", "scoreRef", "setIsPlaying", "playbackProgress", "setPlaybackProgress", "playbackPosition", "setPlaybackPosition", "synth", "setSynth", "renderer", "<PERSON><PERSON><PERSON><PERSON>", "synthInstance", "PolySynth", "Synth", "toDestination", "dispose", "current", "innerHTML", "rendererInstance", "<PERSON><PERSON><PERSON>", "Backends", "SVG", "resize", "context", "getContext", "setFont", "yPosition", "systemHeight", "for<PERSON>ach", "instrument", "instrumentIndex", "template", "stave", "Stave", "clef", "addClef", "addTimeSignature", "setText", "name", "VF", "Modifier", "Position", "LEFT", "setContext", "draw", "instrumentNotes", "filter", "note", "position", "staff", "measureGroups", "Map", "measure", "has", "set", "get", "push", "Array", "from", "entries", "measureNum", "measureNotes", "length", "vexNotes", "sort", "a", "b", "beat", "map", "noteName", "toLowerCase", "octave", "duration", "convertDurationToVex", "vexNote", "isRest", "StaveNote", "keys", "key", "accidental", "addModifier", "Accidental", "voice", "Voice", "num_beats", "beat_value", "addTickables", "<PERSON><PERSON><PERSON>", "joinVoices", "format", "measureLyrics", "lyric", "lyricIndex", "x", "getX", "getWidth", "y", "fillText", "text", "durationMap", "handlePlay", "Transport", "stop", "cancel", "state", "start", "bpm", "value", "sortedNotes", "time", "getDurationInSeconds", "schedule", "triggerAttackRelease", "totalDuration", "updateProgress", "currentTime", "seconds", "Math", "min", "requestAnimationFrame", "beatDuration", "handleScoreClick", "event", "_scoreRef$current", "rect", "getBoundingClientRect", "clientX", "left", "clientY", "top", "staffIndex", "floor", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "max", "onChange", "e", "parseInt", "target", "ref", "style", "cursor", "_c1", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ProfessionalScore/ProfessionalScore.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport styled from 'styled-components';\n// import { Factory, Renderer, Stave, StaveNote, Voice, Formatter, Accidental } from 'vexflow';\nimport * as Tone from 'tone';\nimport { MusicalNote, Lyrics, InstrumentType } from '../../types/music';\nimport { getInstrumentTemplate } from '../../utils/instrumentTemplates';\n\nconst ScoreContainer = styled.div`\n  background: white;\n  border-radius: 16px;\n  padding: 2rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n  margin: 1rem 0;\n  overflow-x: auto;\n`;\n\nconst ScoreHeader = styled.div`\n  text-align: center;\n  margin-bottom: 2rem;\n  border-bottom: 2px solid #e9ecef;\n  padding-bottom: 1rem;\n`;\n\nconst ScoreTitle = styled.h1`\n  font-size: 2rem;\n  color: #2c3e50;\n  margin: 0 0 0.5rem 0;\n  font-family: 'Times New Roman', serif;\n`;\n\nconst ScoreSubtitle = styled.div`\n  font-size: 1.1rem;\n  color: #666;\n  font-style: italic;\n`;\n\nconst ScoreCanvas = styled.div`\n  min-height: 400px;\n  width: 100%;\n  \n  svg {\n    width: 100%;\n    height: auto;\n  }\n`;\n\nconst PlaybackControls = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 1rem;\n  margin: 1rem 0;\n  padding: 1rem;\n  background: #f8f9fa;\n  border-radius: 12px;\n`;\n\nconst PlayButton = styled.button<{ isPlaying?: boolean }>`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 50px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.isPlaying ? `\n    background: #e74c3c;\n    color: white;\n    &:hover { background: #c0392b; }\n  ` : `\n    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\n    color: white;\n    &:hover { \n      background: linear-gradient(135deg, #229954 0%, #27ae60 100%);\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);\n    }\n  `}\n`;\n\nconst TempoControl = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  \n  label {\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input {\n    width: 80px;\n    padding: 0.5rem;\n    border: 1px solid #dee2e6;\n    border-radius: 6px;\n    text-align: center;\n  }\n`;\n\nconst ProgressBar = styled.div`\n  flex: 1;\n  height: 6px;\n  background: #e9ecef;\n  border-radius: 3px;\n  overflow: hidden;\n  position: relative;\n`;\n\nconst ProgressFill = styled.div<{ progress: number }>`\n  height: 100%;\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\n  width: ${props => props.progress}%;\n  transition: width 0.1s ease;\n`;\n\nconst PlaybackLine = styled.line<{ visible: boolean }>`\n  stroke: #e74c3c;\n  stroke-width: 2;\n  opacity: ${props => props.visible ? 1 : 0};\n  transition: opacity 0.2s ease;\n`;\n\ninterface ProfessionalScoreProps {\n  title: string;\n  composer?: string;\n  notes: MusicalNote[];\n  lyrics: Lyrics[];\n  instruments: InstrumentType[];\n  tempo: number;\n  onTempoChange: (tempo: number) => void;\n  onNoteClick?: (x: number, y: number, staffIndex: number) => void;\n}\n\nexport const ProfessionalScore: React.FC<ProfessionalScoreProps> = ({\n  title,\n  composer,\n  notes,\n  lyrics,\n  instruments,\n  tempo,\n  onTempoChange,\n  onNoteClick\n}) => {\n  const scoreRef = useRef<HTMLDivElement>(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [playbackProgress, setPlaybackProgress] = useState(0);\n  const [playbackPosition, setPlaybackPosition] = useState(0);\n  const [synth, setSynth] = useState<Tone.PolySynth | null>(null);\n  const [renderer, setRenderer] = useState<any>(null);\n\n  // Inicializar Tone.js\n  useEffect(() => {\n    const synthInstance = new Tone.PolySynth(Tone.Synth).toDestination();\n    setSynth(synthInstance);\n    \n    return () => {\n      synthInstance.dispose();\n    };\n  }, []);\n\n  // Renderizar partitura com VexFlow\n  useEffect(() => {\n    if (!scoreRef.current) return;\n\n    // Limpar canvas anterior\n    scoreRef.current.innerHTML = '';\n\n    const div = scoreRef.current;\n\n    // Criar renderer\n    const rendererInstance = new Renderer(div, Renderer.Backends.SVG);\n    rendererInstance.resize(800, 600);\n    setRenderer(rendererInstance);\n    \n    const context = rendererInstance.getContext();\n    context.setFont('Arial', 10);\n\n    // Criar sistema de pautas para múltiplos instrumentos\n    let yPosition = 40;\n    const systemHeight = 120;\n    \n    instruments.forEach((instrument, instrumentIndex) => {\n      const template = getInstrumentTemplate(instrument);\n      \n      // Criar pauta\n      const stave = new Stave(10, yPosition, 750);\n      \n      // Adicionar clave\n      if (template.clef === 'treble') {\n        stave.addClef('treble');\n      } else if (template.clef === 'bass') {\n        stave.addClef('bass');\n      }\n      \n      // Adicionar compasso\n      stave.addTimeSignature('4/4');\n      \n      // Adicionar nome do instrumento\n      stave.setText(template.name, VF.Modifier.Position.LEFT);\n      \n      stave.setContext(context).draw();\n\n      // Filtrar notas deste instrumento\n      const instrumentNotes = notes.filter(note => \n        note.position.staff === instrumentIndex\n      );\n\n      // Agrupar notas por compasso\n      const measureGroups = new Map<number, MusicalNote[]>();\n      instrumentNotes.forEach(note => {\n        const measure = note.position.measure;\n        if (!measureGroups.has(measure)) {\n          measureGroups.set(measure, []);\n        }\n        measureGroups.get(measure)!.push(note);\n      });\n\n      // Renderizar notas por compasso\n      Array.from(measureGroups.entries()).forEach(([measureNum, measureNotes]) => {\n        if (measureNotes.length === 0) return;\n\n        // Converter notas para formato VexFlow\n        const vexNotes = measureNotes\n          .sort((a, b) => a.position.beat - b.position.beat)\n          .map(note => {\n            const noteName = note.name.toLowerCase();\n            const octave = note.octave;\n            const duration = convertDurationToVex(note.duration);\n\n            let vexNote;\n            if (note.isRest) {\n              vexNote = new StaveNote({\n                clef: template.clef,\n                keys: ['r/4'],\n                duration: duration + 'r'\n              });\n            } else {\n              const key = `${noteName}/${octave}`;\n              vexNote = new StaveNote({\n                clef: template.clef,\n                keys: [key],\n                duration: duration\n              });\n\n              // Adicionar acidentes\n              if (note.accidental) {\n                if (note.accidental === 'sharp') {\n                  vexNote.addModifier(new Accidental('#'), 0);\n                } else if (note.accidental === 'flat') {\n                  vexNote.addModifier(new Accidental('b'), 0);\n                } else if (note.accidental === 'natural') {\n                  vexNote.addModifier(new Accidental('n'), 0);\n                }\n              }\n            }\n\n            return vexNote;\n          });\n\n        if (vexNotes.length > 0) {\n          // Criar voice e adicionar notas\n          const voice = new Voice({ num_beats: 4, beat_value: 4 });\n          voice.addTickables(vexNotes);\n\n          // Formatar e desenhar\n          new Formatter().joinVoices([voice]).format([voice], 700);\n          voice.draw(context, stave);\n\n          // Adicionar letras se existirem\n          const measureLyrics = lyrics.filter(lyric => \n            lyric.position.measure === measureNum\n          );\n          \n          measureLyrics.forEach((lyric, lyricIndex) => {\n            const x = stave.getX() + (lyric.position.beat - 1) * (stave.getWidth() / 4);\n            const y = yPosition + systemHeight - 10;\n            \n            context.fillText(lyric.text, x, y);\n          });\n        }\n      });\n\n      yPosition += systemHeight;\n    });\n\n  }, [notes, lyrics, instruments]);\n\n  // Função para converter duração para formato VexFlow\n  const convertDurationToVex = (duration: string): string => {\n    const durationMap: { [key: string]: string } = {\n      'whole': 'w',\n      'half': 'h',\n      'quarter': 'q',\n      'eighth': '8',\n      'sixteenth': '16'\n    };\n    return durationMap[duration] || 'q';\n  };\n\n  // Função para reproduzir partitura\n  const handlePlay = async () => {\n    if (!synth) return;\n\n    if (isPlaying) {\n      // Parar reprodução\n      Tone.Transport.stop();\n      Tone.Transport.cancel();\n      setIsPlaying(false);\n      setPlaybackProgress(0);\n      setPlaybackPosition(0);\n      return;\n    }\n\n    // Iniciar Tone.js se necessário\n    if (Tone.context.state !== 'running') {\n      await Tone.start();\n    }\n\n    // Configurar tempo\n    Tone.Transport.bpm.value = tempo;\n\n    // Agendar notas para reprodução\n    const sortedNotes = [...notes]\n      .filter(note => !note.isRest)\n      .sort((a, b) => {\n        if (a.position.measure !== b.position.measure) {\n          return a.position.measure - b.position.measure;\n        }\n        return a.position.beat - b.position.beat;\n      });\n\n    sortedNotes.forEach(note => {\n      const time = ((note.position.measure - 1) * 4 + (note.position.beat - 1)) * (60 / tempo);\n      const noteName = `${note.name}${note.octave}`;\n      const duration = getDurationInSeconds(note.duration, tempo);\n\n      Tone.Transport.schedule((time) => {\n        synth.triggerAttackRelease(noteName, duration);\n      }, time);\n    });\n\n    // Calcular duração total\n    const totalDuration = sortedNotes.length > 0 \n      ? ((sortedNotes[sortedNotes.length - 1].position.measure - 1) * 4 + \n         sortedNotes[sortedNotes.length - 1].position.beat) * (60 / tempo)\n      : 4 * (60 / tempo);\n\n    // Atualizar progresso\n    const updateProgress = () => {\n      if (Tone.Transport.state === 'started') {\n        const currentTime = Tone.Transport.seconds;\n        const progress = (currentTime / totalDuration) * 100;\n        setPlaybackProgress(Math.min(progress, 100));\n        setPlaybackPosition(currentTime);\n        \n        if (progress < 100) {\n          requestAnimationFrame(updateProgress);\n        } else {\n          setIsPlaying(false);\n          setPlaybackProgress(0);\n          setPlaybackPosition(0);\n        }\n      }\n    };\n\n    // Iniciar reprodução\n    Tone.Transport.start();\n    setIsPlaying(true);\n    updateProgress();\n  };\n\n  // Função para calcular duração em segundos\n  const getDurationInSeconds = (duration: string, bpm: number): number => {\n    const beatDuration = 60 / bpm;\n    const durationMap: { [key: string]: number } = {\n      'whole': beatDuration * 4,\n      'half': beatDuration * 2,\n      'quarter': beatDuration,\n      'eighth': beatDuration / 2,\n      'sixteenth': beatDuration / 4\n    };\n    return durationMap[duration] || beatDuration;\n  };\n\n  // Função para lidar com cliques na partitura\n  const handleScoreClick = (event: React.MouseEvent) => {\n    if (!onNoteClick || !renderer) return;\n\n    const rect = scoreRef.current?.getBoundingClientRect();\n    if (!rect) return;\n\n    const x = event.clientX - rect.left;\n    const y = event.clientY - rect.top;\n\n    // Determinar qual pauta foi clicada\n    const staffIndex = Math.floor((y - 40) / 120);\n    \n    if (staffIndex >= 0 && staffIndex < instruments.length) {\n      onNoteClick(x, y, staffIndex);\n    }\n  };\n\n  return (\n    <ScoreContainer>\n      <ScoreHeader>\n        <ScoreTitle>{title}</ScoreTitle>\n        {composer && <ScoreSubtitle>por {composer}</ScoreSubtitle>}\n      </ScoreHeader>\n\n      <PlaybackControls>\n        <PlayButton isPlaying={isPlaying} onClick={handlePlay}>\n          {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}\n        </PlayButton>\n        \n        <TempoControl>\n          <label>Tempo:</label>\n          <input\n            type=\"number\"\n            min=\"60\"\n            max=\"200\"\n            value={tempo}\n            onChange={(e) => onTempoChange(parseInt(e.target.value) || 120)}\n          />\n          <span>BPM</span>\n        </TempoControl>\n\n        <ProgressBar>\n          <ProgressFill progress={playbackProgress} />\n        </ProgressBar>\n      </PlaybackControls>\n\n      <ScoreCanvas \n        ref={scoreRef} \n        onClick={handleScoreClick}\n        style={{ cursor: onNoteClick ? 'crosshair' : 'default' }}\n      />\n    </ScoreContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,MAAM,MAAM,mBAAmB;AACtC;AACA,OAAO,KAAKC,IAAI,MAAM,MAAM;AAE5B,SAASC,qBAAqB,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,MAAMC,cAAc,GAAGL,MAAM,CAACM,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,cAAc;AASpB,MAAMG,WAAW,GAAGR,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,WAAW;AAOjB,MAAME,UAAU,GAAGV,MAAM,CAACW,EAAE;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,UAAU;AAOhB,MAAMG,aAAa,GAAGb,MAAM,CAACM,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAJID,aAAa;AAMnB,MAAME,WAAW,GAAGf,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GARID,WAAW;AAUjB,MAAME,gBAAgB,GAAGjB,MAAM,CAACM,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GATID,gBAAgB;AAWtB,MAAME,UAAU,GAAGnB,MAAM,CAACoB,MAA+B;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAG;AAC/B;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACC,GAAA,GAtBIJ,UAAU;AAwBhB,MAAMK,YAAY,GAAGxB,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GAjBID,YAAY;AAmBlB,MAAME,WAAW,GAAG1B,MAAM,CAACM,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GAPID,WAAW;AASjB,MAAME,YAAY,GAAG5B,MAAM,CAACM,GAAyB;AACrD;AACA;AACA,WAAWe,KAAK,IAAIA,KAAK,CAACQ,QAAQ;AAClC;AACA,CAAC;AAACC,GAAA,GALIF,YAAY;AAOlB,MAAMG,YAAY,GAAG/B,MAAM,CAACgC,IAA0B;AACtD;AACA;AACA,aAAaX,KAAK,IAAIA,KAAK,CAACY,OAAO,GAAG,CAAC,GAAG,CAAC;AAC3C;AACA,CAAC;AAaD,OAAO,MAAMC,iBAAmD,GAAGA,CAAC;EAClEC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC,MAAM;EACNC,WAAW;EACXC,KAAK;EACLC,aAAa;EACbC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAG9C,MAAM,CAAiB,IAAI,CAAC;EAC7C,MAAM,CAACwB,SAAS,EAAEuB,YAAY,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAwB,IAAI,CAAC;EAC/D,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAM,IAAI,CAAC;;EAEnD;EACAF,SAAS,CAAC,MAAM;IACd,MAAMyD,aAAa,GAAG,IAAIrD,IAAI,CAACsD,SAAS,CAACtD,IAAI,CAACuD,KAAK,CAAC,CAACC,aAAa,CAAC,CAAC;IACpEN,QAAQ,CAACG,aAAa,CAAC;IAEvB,OAAO,MAAM;MACXA,aAAa,CAACI,OAAO,CAAC,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7D,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+C,QAAQ,CAACe,OAAO,EAAE;;IAEvB;IACAf,QAAQ,CAACe,OAAO,CAACC,SAAS,GAAG,EAAE;IAE/B,MAAMtD,GAAG,GAAGsC,QAAQ,CAACe,OAAO;;IAE5B;IACA,MAAME,gBAAgB,GAAG,IAAIC,QAAQ,CAACxD,GAAG,EAAEwD,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC;IACjEH,gBAAgB,CAACI,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;IACjCZ,WAAW,CAACQ,gBAAgB,CAAC;IAE7B,MAAMK,OAAO,GAAGL,gBAAgB,CAACM,UAAU,CAAC,CAAC;IAC7CD,OAAO,CAACE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;;IAE5B;IACA,IAAIC,SAAS,GAAG,EAAE;IAClB,MAAMC,YAAY,GAAG,GAAG;IAExB/B,WAAW,CAACgC,OAAO,CAAC,CAACC,UAAU,EAAEC,eAAe,KAAK;MACnD,MAAMC,QAAQ,GAAGxE,qBAAqB,CAACsE,UAAU,CAAC;;MAElD;MACA,MAAMG,KAAK,GAAG,IAAIC,KAAK,CAAC,EAAE,EAAEP,SAAS,EAAE,GAAG,CAAC;;MAE3C;MACA,IAAIK,QAAQ,CAACG,IAAI,KAAK,QAAQ,EAAE;QAC9BF,KAAK,CAACG,OAAO,CAAC,QAAQ,CAAC;MACzB,CAAC,MAAM,IAAIJ,QAAQ,CAACG,IAAI,KAAK,MAAM,EAAE;QACnCF,KAAK,CAACG,OAAO,CAAC,MAAM,CAAC;MACvB;;MAEA;MACAH,KAAK,CAACI,gBAAgB,CAAC,KAAK,CAAC;;MAE7B;MACAJ,KAAK,CAACK,OAAO,CAACN,QAAQ,CAACO,IAAI,EAAEC,EAAE,CAACC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAC;MAEvDV,KAAK,CAACW,UAAU,CAACpB,OAAO,CAAC,CAACqB,IAAI,CAAC,CAAC;;MAEhC;MACA,MAAMC,eAAe,GAAGnD,KAAK,CAACoD,MAAM,CAACC,IAAI,IACvCA,IAAI,CAACC,QAAQ,CAACC,KAAK,KAAKnB,eAC1B,CAAC;;MAED;MACA,MAAMoB,aAAa,GAAG,IAAIC,GAAG,CAAwB,CAAC;MACtDN,eAAe,CAACjB,OAAO,CAACmB,IAAI,IAAI;QAC9B,MAAMK,OAAO,GAAGL,IAAI,CAACC,QAAQ,CAACI,OAAO;QACrC,IAAI,CAACF,aAAa,CAACG,GAAG,CAACD,OAAO,CAAC,EAAE;UAC/BF,aAAa,CAACI,GAAG,CAACF,OAAO,EAAE,EAAE,CAAC;QAChC;QACAF,aAAa,CAACK,GAAG,CAACH,OAAO,CAAC,CAAEI,IAAI,CAACT,IAAI,CAAC;MACxC,CAAC,CAAC;;MAEF;MACAU,KAAK,CAACC,IAAI,CAACR,aAAa,CAACS,OAAO,CAAC,CAAC,CAAC,CAAC/B,OAAO,CAAC,CAAC,CAACgC,UAAU,EAAEC,YAAY,CAAC,KAAK;QAC1E,IAAIA,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE;;QAE/B;QACA,MAAMC,QAAQ,GAAGF,YAAY,CAC1BG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACjB,QAAQ,CAACmB,IAAI,GAAGD,CAAC,CAAClB,QAAQ,CAACmB,IAAI,CAAC,CACjDC,GAAG,CAACrB,IAAI,IAAI;UACX,MAAMsB,QAAQ,GAAGtB,IAAI,CAACT,IAAI,CAACgC,WAAW,CAAC,CAAC;UACxC,MAAMC,MAAM,GAAGxB,IAAI,CAACwB,MAAM;UAC1B,MAAMC,QAAQ,GAAGC,oBAAoB,CAAC1B,IAAI,CAACyB,QAAQ,CAAC;UAEpD,IAAIE,OAAO;UACX,IAAI3B,IAAI,CAAC4B,MAAM,EAAE;YACfD,OAAO,GAAG,IAAIE,SAAS,CAAC;cACtB1C,IAAI,EAAEH,QAAQ,CAACG,IAAI;cACnB2C,IAAI,EAAE,CAAC,KAAK,CAAC;cACbL,QAAQ,EAAEA,QAAQ,GAAG;YACvB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,MAAMM,GAAG,GAAG,GAAGT,QAAQ,IAAIE,MAAM,EAAE;YACnCG,OAAO,GAAG,IAAIE,SAAS,CAAC;cACtB1C,IAAI,EAAEH,QAAQ,CAACG,IAAI;cACnB2C,IAAI,EAAE,CAACC,GAAG,CAAC;cACXN,QAAQ,EAAEA;YACZ,CAAC,CAAC;;YAEF;YACA,IAAIzB,IAAI,CAACgC,UAAU,EAAE;cACnB,IAAIhC,IAAI,CAACgC,UAAU,KAAK,OAAO,EAAE;gBAC/BL,OAAO,CAACM,WAAW,CAAC,IAAIC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;cAC7C,CAAC,MAAM,IAAIlC,IAAI,CAACgC,UAAU,KAAK,MAAM,EAAE;gBACrCL,OAAO,CAACM,WAAW,CAAC,IAAIC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;cAC7C,CAAC,MAAM,IAAIlC,IAAI,CAACgC,UAAU,KAAK,SAAS,EAAE;gBACxCL,OAAO,CAACM,WAAW,CAAC,IAAIC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;cAC7C;YACF;UACF;UAEA,OAAOP,OAAO;QAChB,CAAC,CAAC;QAEJ,IAAIX,QAAQ,CAACD,MAAM,GAAG,CAAC,EAAE;UACvB;UACA,MAAMoB,KAAK,GAAG,IAAIC,KAAK,CAAC;YAAEC,SAAS,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAE,CAAC,CAAC;UACxDH,KAAK,CAACI,YAAY,CAACvB,QAAQ,CAAC;;UAE5B;UACA,IAAIwB,SAAS,CAAC,CAAC,CAACC,UAAU,CAAC,CAACN,KAAK,CAAC,CAAC,CAACO,MAAM,CAAC,CAACP,KAAK,CAAC,EAAE,GAAG,CAAC;UACxDA,KAAK,CAACtC,IAAI,CAACrB,OAAO,EAAES,KAAK,CAAC;;UAE1B;UACA,MAAM0D,aAAa,GAAG/F,MAAM,CAACmD,MAAM,CAAC6C,KAAK,IACvCA,KAAK,CAAC3C,QAAQ,CAACI,OAAO,KAAKQ,UAC7B,CAAC;UAED8B,aAAa,CAAC9D,OAAO,CAAC,CAAC+D,KAAK,EAAEC,UAAU,KAAK;YAC3C,MAAMC,CAAC,GAAG7D,KAAK,CAAC8D,IAAI,CAAC,CAAC,GAAG,CAACH,KAAK,CAAC3C,QAAQ,CAACmB,IAAI,GAAG,CAAC,KAAKnC,KAAK,CAAC+D,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3E,MAAMC,CAAC,GAAGtE,SAAS,GAAGC,YAAY,GAAG,EAAE;YAEvCJ,OAAO,CAAC0E,QAAQ,CAACN,KAAK,CAACO,IAAI,EAAEL,CAAC,EAAEG,CAAC,CAAC;UACpC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEFtE,SAAS,IAAIC,YAAY;IAC3B,CAAC,CAAC;EAEJ,CAAC,EAAE,CAACjC,KAAK,EAAEC,MAAM,EAAEC,WAAW,CAAC,CAAC;;EAEhC;EACA,MAAM6E,oBAAoB,GAAID,QAAgB,IAAa;IACzD,MAAM2B,WAAsC,GAAG;MAC7C,OAAO,EAAE,GAAG;MACZ,MAAM,EAAE,GAAG;MACX,SAAS,EAAE,GAAG;MACd,QAAQ,EAAE,GAAG;MACb,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,WAAW,CAAC3B,QAAQ,CAAC,IAAI,GAAG;EACrC,CAAC;;EAED;EACA,MAAM4B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAAC7F,KAAK,EAAE;IAEZ,IAAI5B,SAAS,EAAE;MACb;MACArB,IAAI,CAAC+I,SAAS,CAACC,IAAI,CAAC,CAAC;MACrBhJ,IAAI,CAAC+I,SAAS,CAACE,MAAM,CAAC,CAAC;MACvBrG,YAAY,CAAC,KAAK,CAAC;MACnBE,mBAAmB,CAAC,CAAC,CAAC;MACtBE,mBAAmB,CAAC,CAAC,CAAC;MACtB;IACF;;IAEA;IACA,IAAIhD,IAAI,CAACiE,OAAO,CAACiF,KAAK,KAAK,SAAS,EAAE;MACpC,MAAMlJ,IAAI,CAACmJ,KAAK,CAAC,CAAC;IACpB;;IAEA;IACAnJ,IAAI,CAAC+I,SAAS,CAACK,GAAG,CAACC,KAAK,GAAG9G,KAAK;;IAEhC;IACA,MAAM+G,WAAW,GAAG,CAAC,GAAGlH,KAAK,CAAC,CAC3BoD,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAAC4B,MAAM,CAAC,CAC5BX,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACd,IAAID,CAAC,CAACjB,QAAQ,CAACI,OAAO,KAAKc,CAAC,CAAClB,QAAQ,CAACI,OAAO,EAAE;QAC7C,OAAOa,CAAC,CAACjB,QAAQ,CAACI,OAAO,GAAGc,CAAC,CAAClB,QAAQ,CAACI,OAAO;MAChD;MACA,OAAOa,CAAC,CAACjB,QAAQ,CAACmB,IAAI,GAAGD,CAAC,CAAClB,QAAQ,CAACmB,IAAI;IAC1C,CAAC,CAAC;IAEJyC,WAAW,CAAChF,OAAO,CAACmB,IAAI,IAAI;MAC1B,MAAM8D,IAAI,GAAG,CAAC,CAAC9D,IAAI,CAACC,QAAQ,CAACI,OAAO,GAAG,CAAC,IAAI,CAAC,IAAIL,IAAI,CAACC,QAAQ,CAACmB,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,GAAGtE,KAAK,CAAC;MACxF,MAAMwE,QAAQ,GAAG,GAAGtB,IAAI,CAACT,IAAI,GAAGS,IAAI,CAACwB,MAAM,EAAE;MAC7C,MAAMC,QAAQ,GAAGsC,oBAAoB,CAAC/D,IAAI,CAACyB,QAAQ,EAAE3E,KAAK,CAAC;MAE3DvC,IAAI,CAAC+I,SAAS,CAACU,QAAQ,CAAEF,IAAI,IAAK;QAChCtG,KAAK,CAACyG,oBAAoB,CAAC3C,QAAQ,EAAEG,QAAQ,CAAC;MAChD,CAAC,EAAEqC,IAAI,CAAC;IACV,CAAC,CAAC;;IAEF;IACA,MAAMI,aAAa,GAAGL,WAAW,CAAC9C,MAAM,GAAG,CAAC,GACxC,CAAC,CAAC8C,WAAW,CAACA,WAAW,CAAC9C,MAAM,GAAG,CAAC,CAAC,CAACd,QAAQ,CAACI,OAAO,GAAG,CAAC,IAAI,CAAC,GAC9DwD,WAAW,CAACA,WAAW,CAAC9C,MAAM,GAAG,CAAC,CAAC,CAACd,QAAQ,CAACmB,IAAI,KAAK,EAAE,GAAGtE,KAAK,CAAC,GAClE,CAAC,IAAI,EAAE,GAAGA,KAAK,CAAC;;IAEpB;IACA,MAAMqH,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAI5J,IAAI,CAAC+I,SAAS,CAACG,KAAK,KAAK,SAAS,EAAE;QACtC,MAAMW,WAAW,GAAG7J,IAAI,CAAC+I,SAAS,CAACe,OAAO;QAC1C,MAAMlI,QAAQ,GAAIiI,WAAW,GAAGF,aAAa,GAAI,GAAG;QACpD7G,mBAAmB,CAACiH,IAAI,CAACC,GAAG,CAACpI,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC5CoB,mBAAmB,CAAC6G,WAAW,CAAC;QAEhC,IAAIjI,QAAQ,GAAG,GAAG,EAAE;UAClBqI,qBAAqB,CAACL,cAAc,CAAC;QACvC,CAAC,MAAM;UACLhH,YAAY,CAAC,KAAK,CAAC;UACnBE,mBAAmB,CAAC,CAAC,CAAC;UACtBE,mBAAmB,CAAC,CAAC,CAAC;QACxB;MACF;IACF,CAAC;;IAED;IACAhD,IAAI,CAAC+I,SAAS,CAACI,KAAK,CAAC,CAAC;IACtBvG,YAAY,CAAC,IAAI,CAAC;IAClBgH,cAAc,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMJ,oBAAoB,GAAGA,CAACtC,QAAgB,EAAEkC,GAAW,KAAa;IACtE,MAAMc,YAAY,GAAG,EAAE,GAAGd,GAAG;IAC7B,MAAMP,WAAsC,GAAG;MAC7C,OAAO,EAAEqB,YAAY,GAAG,CAAC;MACzB,MAAM,EAAEA,YAAY,GAAG,CAAC;MACxB,SAAS,EAAEA,YAAY;MACvB,QAAQ,EAAEA,YAAY,GAAG,CAAC;MAC1B,WAAW,EAAEA,YAAY,GAAG;IAC9B,CAAC;IACD,OAAOrB,WAAW,CAAC3B,QAAQ,CAAC,IAAIgD,YAAY;EAC9C,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,KAAuB,IAAK;IAAA,IAAAC,iBAAA;IACpD,IAAI,CAAC5H,WAAW,IAAI,CAACU,QAAQ,EAAE;IAE/B,MAAMmH,IAAI,IAAAD,iBAAA,GAAG1H,QAAQ,CAACe,OAAO,cAAA2G,iBAAA,uBAAhBA,iBAAA,CAAkBE,qBAAqB,CAAC,CAAC;IACtD,IAAI,CAACD,IAAI,EAAE;IAEX,MAAM/B,CAAC,GAAG6B,KAAK,CAACI,OAAO,GAAGF,IAAI,CAACG,IAAI;IACnC,MAAM/B,CAAC,GAAG0B,KAAK,CAACM,OAAO,GAAGJ,IAAI,CAACK,GAAG;;IAElC;IACA,MAAMC,UAAU,GAAGb,IAAI,CAACc,KAAK,CAAC,CAACnC,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;IAE7C,IAAIkC,UAAU,IAAI,CAAC,IAAIA,UAAU,GAAGtI,WAAW,CAACkE,MAAM,EAAE;MACtD/D,WAAW,CAAC8F,CAAC,EAAEG,CAAC,EAAEkC,UAAU,CAAC;IAC/B;EACF,CAAC;EAED,oBACEzK,OAAA,CAACC,cAAc;IAAA0K,QAAA,gBACb3K,OAAA,CAACI,WAAW;MAAAuK,QAAA,gBACV3K,OAAA,CAACM,UAAU;QAAAqK,QAAA,EAAE5I;MAAK;QAAA6I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,EAC/B/I,QAAQ,iBAAIhC,OAAA,CAACS,aAAa;QAAAkK,QAAA,GAAC,MAAI,EAAC3I,QAAQ;MAAA;QAAA4I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAgB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eAEd/K,OAAA,CAACa,gBAAgB;MAAA8J,QAAA,gBACf3K,OAAA,CAACe,UAAU;QAACG,SAAS,EAAEA,SAAU;QAAC8J,OAAO,EAAErC,UAAW;QAAAgC,QAAA,EACnDzJ,SAAS,GAAG,WAAW,GAAG;MAAe;QAAA0J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAEb/K,OAAA,CAACoB,YAAY;QAAAuJ,QAAA,gBACX3K,OAAA;UAAA2K,QAAA,EAAO;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrB/K,OAAA;UACEiL,IAAI,EAAC,QAAQ;UACbpB,GAAG,EAAC,IAAI;UACRqB,GAAG,EAAC,KAAK;UACThC,KAAK,EAAE9G,KAAM;UACb+I,QAAQ,EAAGC,CAAC,IAAK/I,aAAa,CAACgJ,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACpC,KAAK,CAAC,IAAI,GAAG;QAAE;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eACF/K,OAAA;UAAA2K,QAAA,EAAM;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEf/K,OAAA,CAACsB,WAAW;QAAAqJ,QAAA,eACV3K,OAAA,CAACwB,YAAY;UAACC,QAAQ,EAAEiB;QAAiB;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEnB/K,OAAA,CAACW,WAAW;MACV4K,GAAG,EAAE/I,QAAS;MACdwI,OAAO,EAAEhB,gBAAiB;MAC1BwB,KAAK,EAAE;QAAEC,MAAM,EAAEnJ,WAAW,GAAG,WAAW,GAAG;MAAU;IAAE;MAAAsI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAErB,CAAC;AAACxI,EAAA,CAjTWT,iBAAmD;AAAA4J,GAAA,GAAnD5J,iBAAmD;AAAA,IAAA3B,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAgK,GAAA;AAAAC,YAAA,CAAAxL,EAAA;AAAAwL,YAAA,CAAAtL,GAAA;AAAAsL,YAAA,CAAAnL,GAAA;AAAAmL,YAAA,CAAAjL,GAAA;AAAAiL,YAAA,CAAA/K,GAAA;AAAA+K,YAAA,CAAA7K,GAAA;AAAA6K,YAAA,CAAAxK,GAAA;AAAAwK,YAAA,CAAAtK,GAAA;AAAAsK,YAAA,CAAApK,GAAA;AAAAoK,YAAA,CAAAjK,GAAA;AAAAiK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}