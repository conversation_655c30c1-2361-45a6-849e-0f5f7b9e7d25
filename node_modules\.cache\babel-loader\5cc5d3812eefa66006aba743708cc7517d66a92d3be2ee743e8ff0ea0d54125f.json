{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { LoginPage } from './pages/Auth/LoginPage';\nimport { RegisterPage } from './pages/Auth/RegisterPage';\nimport { Layout } from './components/Layout/Layout';\nimport { ScoreEditor } from './components/ScoreEditor/ScoreEditor';\nimport { ScoresList } from './pages/ScoresList/ScoresList';\nimport { NewScorePage } from './pages/NewScore/NewScorePage';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  font-size: 1.2rem;\n  color: #666;\n`;\n_c = LoadingContainer;\nconst HomePage = styled.div`\n  text-align: center;\n  padding: 2rem;\n`;\n_c2 = HomePage;\nconst WelcomeTitle = styled.h1`\n  color: #2c3e50;\n  margin-bottom: 1rem;\n`;\n_c3 = WelcomeTitle;\nconst WelcomeText = styled.p`\n  color: #666;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  max-width: 600px;\n  margin: 0 auto 2rem auto;\n`;\n_c4 = WelcomeText;\nconst GuestNotice = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin: 2rem auto;\n  max-width: 500px;\n  text-align: center;\n  box-shadow: 0 4px 6px rgba(0,0,0,0.1);\n\n  p {\n    margin: 0.5rem 0;\n    color: #856404;\n  }\n\n  strong {\n    color: #533f03;\n  }\n`;\n\n// Componente principal da aplicação\n_c5 = GuestNotice;\nconst MainApp = () => {\n  _s();\n  const [currentPage, setCurrentPage] = useState('home');\n  const [authMode, setAuthMode] = useState('login');\n  const [editingScoreId, setEditingScoreId] = useState(null);\n  const [scoreConfig, setScoreConfig] = useState(null);\n  const {\n    currentUser\n  } = useAuth();\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return /*#__PURE__*/_jsxDEV(HomePage, {\n          children: [/*#__PURE__*/_jsxDEV(WelcomeTitle, {\n            children: \"\\uD83C\\uDFB5 Bem-vindo ao Partitura Digital!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(WelcomeText, {\n            children: \"Crie e edite suas partituras musicais de forma intuitiva. Adicione notas, acordes, letras e muito mais. Visualize suas composi\\xE7\\xF5es tanto em nota\\xE7\\xE3o tradicional quanto em cifras.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), !currentUser && /*#__PURE__*/_jsxDEV(GuestNotice, {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"\\uD83D\\uDCA1 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Modo Visitante:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 23\n              }, this), \" Voc\\xEA pode usar o editor, mas suas partituras n\\xE3o ser\\xE3o salvas.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Fa\\xE7a login para salvar suas cria\\xE7\\xF5es!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this);\n      case 'scores':\n        return /*#__PURE__*/_jsxDEV(ScoresList, {\n          onCreateNew: () => {\n            setEditingScoreId(null);\n            setScoreConfig(null);\n            setCurrentPage('new-score-setup');\n          },\n          onEditScore: scoreId => {\n            setEditingScoreId(scoreId);\n            setScoreConfig(null);\n            setCurrentPage('score-editor');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this);\n      case 'new-score':\n        // Redirecionar para setup se não há configuração\n        if (!scoreConfig) {\n          setCurrentPage('new-score-setup');\n          return null;\n        }\n        return /*#__PURE__*/_jsxDEV(ScoreEditor, {\n          scoreId: editingScoreId || undefined,\n          initialConfig: scoreConfig\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 16\n        }, this);\n      case 'new-score-setup':\n        return /*#__PURE__*/_jsxDEV(NewScorePage, {\n          onCreateScore: config => {\n            setScoreConfig(config);\n            setCurrentPage('new-score');\n          },\n          onCancel: () => setCurrentPage('home')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this);\n      case 'score-editor':\n        return /*#__PURE__*/_jsxDEV(ScoreEditor, {\n          scoreId: editingScoreId || undefined\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 16\n        }, this);\n      case 'auth':\n        return authMode === 'login' ? /*#__PURE__*/_jsxDEV(LoginPage, {\n          onSwitchToRegister: () => setAuthMode('register')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(RegisterPage, {\n          onSwitchToLogin: () => setAuthMode('login')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"P\\xE1gina n\\xE3o encontrada\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const handleNavigate = page => {\n    if (page === 'login') {\n      setAuthMode('login');\n      setCurrentPage('auth');\n    } else if (page === 'register') {\n      setAuthMode('register');\n      setCurrentPage('auth');\n    } else {\n      setCurrentPage(page);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    currentPage: currentPage,\n    onNavigate: handleNavigate,\n    showAuthInSidebar: !currentUser,\n    children: renderPage()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 143,\n    columnNumber: 5\n  }, this);\n};\n\n// Componente principal que gerencia o estado de loading\n_s(MainApp, \"DpHWhhekucSfwoExUYOooAKOgS4=\", false, function () {\n  return [useAuth];\n});\n_c6 = MainApp;\nconst AppContent = () => {\n  _s2();\n  const {\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingContainer, {\n      children: \"Carregando...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(MainApp, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 10\n  }, this);\n};\n\n// App principal com provider\n_s2(AppContent, \"XG23gq19gO+cpdyphzuFQHtaS84=\", false, function () {\n  return [useAuth];\n});\n_c7 = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n}\n_c8 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"LoadingContainer\");\n$RefreshReg$(_c2, \"HomePage\");\n$RefreshReg$(_c3, \"WelcomeTitle\");\n$RefreshReg$(_c4, \"WelcomeText\");\n$RefreshReg$(_c5, \"GuestNotice\");\n$RefreshReg$(_c6, \"MainApp\");\n$RefreshReg$(_c7, \"AppContent\");\n$RefreshReg$(_c8, \"App\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "LoginPage", "RegisterPage", "Layout", "ScoreEditor", "ScoresList", "NewScorePage", "styled", "jsxDEV", "_jsxDEV", "LoadingContainer", "div", "_c", "HomePage", "_c2", "WelcomeTitle", "h1", "_c3", "WelcomeText", "p", "_c4", "GuestNotice", "_c5", "MainApp", "_s", "currentPage", "setCurrentPage", "authMode", "setAuthMode", "editingScoreId", "setEditingScoreId", "scoreConfig", "setScoreConfig", "currentUser", "renderPage", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onCreateNew", "onEditScore", "scoreId", "undefined", "initialConfig", "onCreateScore", "config", "onCancel", "onSwitchToRegister", "onSwitchToLogin", "handleNavigate", "page", "onNavigate", "showAuthInSidebar", "_c6", "A<PERSON><PERSON><PERSON>nt", "_s2", "loading", "_c7", "App", "_c8", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { LoginPage } from './pages/Auth/LoginPage';\nimport { RegisterPage } from './pages/Auth/RegisterPage';\nimport { Layout } from './components/Layout/Layout';\nimport { ScoreEditor } from './components/ScoreEditor/ScoreEditor';\nimport { ScoresList } from './pages/ScoresList/ScoresList';\nimport { NewScorePage, ScoreConfig } from './pages/NewScore/NewScorePage';\nimport styled from 'styled-components';\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  font-size: 1.2rem;\n  color: #666;\n`;\n\nconst HomePage = styled.div`\n  text-align: center;\n  padding: 2rem;\n`;\n\nconst WelcomeTitle = styled.h1`\n  color: #2c3e50;\n  margin-bottom: 1rem;\n`;\n\nconst WelcomeText = styled.p`\n  color: #666;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  max-width: 600px;\n  margin: 0 auto 2rem auto;\n`;\n\nconst GuestNotice = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin: 2rem auto;\n  max-width: 500px;\n  text-align: center;\n  box-shadow: 0 4px 6px rgba(0,0,0,0.1);\n\n  p {\n    margin: 0.5rem 0;\n    color: #856404;\n  }\n\n  strong {\n    color: #533f03;\n  }\n`;\n\n// Componente principal da aplicação\nconst MainApp: React.FC = () => {\n  const [currentPage, setCurrentPage] = useState('home');\n  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');\n  const [editingScoreId, setEditingScoreId] = useState<string | null>(null);\n  const [scoreConfig, setScoreConfig] = useState<ScoreConfig | null>(null);\n  const { currentUser } = useAuth();\n\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return (\n          <HomePage>\n            <WelcomeTitle>🎵 Bem-vindo ao Partitura Digital!</WelcomeTitle>\n            <WelcomeText>\n              Crie e edite suas partituras musicais de forma intuitiva.\n              Adicione notas, acordes, letras e muito mais.\n              Visualize suas composições tanto em notação tradicional quanto em cifras.\n            </WelcomeText>\n            {!currentUser && (\n              <GuestNotice>\n                <p>💡 <strong>Modo Visitante:</strong> Você pode usar o editor, mas suas partituras não serão salvas.</p>\n                <p>Faça login para salvar suas criações!</p>\n              </GuestNotice>\n            )}\n          </HomePage>\n        );\n      case 'scores':\n        return (\n          <ScoresList\n            onCreateNew={() => {\n              setEditingScoreId(null);\n              setScoreConfig(null);\n              setCurrentPage('new-score-setup');\n            }}\n            onEditScore={(scoreId) => {\n              setEditingScoreId(scoreId);\n              setScoreConfig(null);\n              setCurrentPage('score-editor');\n            }}\n          />\n        );\n      case 'new-score':\n        // Redirecionar para setup se não há configuração\n        if (!scoreConfig) {\n          setCurrentPage('new-score-setup');\n          return null;\n        }\n        return <ScoreEditor scoreId={editingScoreId || undefined} initialConfig={scoreConfig} />;\n      case 'new-score-setup':\n        return (\n          <NewScorePage\n            onCreateScore={(config) => {\n              setScoreConfig(config);\n              setCurrentPage('new-score');\n            }}\n            onCancel={() => setCurrentPage('home')}\n          />\n        );\n      case 'score-editor':\n        return <ScoreEditor scoreId={editingScoreId || undefined} />;\n      case 'auth':\n        return authMode === 'login' ? (\n          <LoginPage onSwitchToRegister={() => setAuthMode('register')} />\n        ) : (\n          <RegisterPage onSwitchToLogin={() => setAuthMode('login')} />\n        );\n      default:\n        return <div>Página não encontrada</div>;\n    }\n  };\n\n  const handleNavigate = (page: string) => {\n    if (page === 'login') {\n      setAuthMode('login');\n      setCurrentPage('auth');\n    } else if (page === 'register') {\n      setAuthMode('register');\n      setCurrentPage('auth');\n    } else {\n      setCurrentPage(page);\n    }\n  };\n\n  return (\n    <Layout\n      currentPage={currentPage}\n      onNavigate={handleNavigate}\n      showAuthInSidebar={!currentUser}\n    >\n      {renderPage()}\n    </Layout>\n  );\n};\n\n// Componente principal que gerencia o estado de loading\nconst AppContent: React.FC = () => {\n  const { loading } = useAuth();\n\n  if (loading) {\n    return <LoadingContainer>Carregando...</LoadingContainer>;\n  }\n\n  return <MainApp />;\n};\n\n// App principal com provider\nfunction App() {\n  return (\n    <AuthProvider>\n      <AppContent />\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,MAAM,QAAQ,4BAA4B;AACnD,SAASC,WAAW,QAAQ,sCAAsC;AAClE,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,YAAY,QAAqB,+BAA+B;AACzE,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,gBAAgB,GAAGH,MAAM,CAACI,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,gBAAgB;AAStB,MAAMG,QAAQ,GAAGN,MAAM,CAACI,GAAG;AAC3B;AACA;AACA,CAAC;AAACG,GAAA,GAHID,QAAQ;AAKd,MAAME,YAAY,GAAGR,MAAM,CAACS,EAAE;AAC9B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,YAAY;AAKlB,MAAMG,WAAW,GAAGX,MAAM,CAACY,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,WAAW;AAQjB,MAAMG,WAAW,GAAGd,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAW,GAAA,GApBMD,WAAW;AAqBjB,MAAME,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,MAAM,CAAC;EACtD,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAuB,OAAO,CAAC;EACvE,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAqB,IAAI,CAAC;EACxE,MAAM;IAAEmC;EAAY,CAAC,GAAGjC,OAAO,CAAC,CAAC;EAEjC,MAAMkC,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQT,WAAW;MACjB,KAAK,MAAM;QACT,oBACEhB,OAAA,CAACI,QAAQ;UAAAsB,QAAA,gBACP1B,OAAA,CAACM,YAAY;YAAAoB,QAAA,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAC/D9B,OAAA,CAACS,WAAW;YAAAiB,QAAA,EAAC;UAIb;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,EACb,CAACN,WAAW,iBACXxB,OAAA,CAACY,WAAW;YAAAc,QAAA,gBACV1B,OAAA;cAAA0B,QAAA,GAAG,eAAG,eAAA1B,OAAA;gBAAA0B,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,4EAA+D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzG9B,OAAA;cAAA0B,QAAA,EAAG;YAAqC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CACd;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAEf,KAAK,QAAQ;QACX,oBACE9B,OAAA,CAACJ,UAAU;UACTmC,WAAW,EAAEA,CAAA,KAAM;YACjBV,iBAAiB,CAAC,IAAI,CAAC;YACvBE,cAAc,CAAC,IAAI,CAAC;YACpBN,cAAc,CAAC,iBAAiB,CAAC;UACnC,CAAE;UACFe,WAAW,EAAGC,OAAO,IAAK;YACxBZ,iBAAiB,CAACY,OAAO,CAAC;YAC1BV,cAAc,CAAC,IAAI,CAAC;YACpBN,cAAc,CAAC,cAAc,CAAC;UAChC;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEN,KAAK,WAAW;QACd;QACA,IAAI,CAACR,WAAW,EAAE;UAChBL,cAAc,CAAC,iBAAiB,CAAC;UACjC,OAAO,IAAI;QACb;QACA,oBAAOjB,OAAA,CAACL,WAAW;UAACsC,OAAO,EAAEb,cAAc,IAAIc,SAAU;UAACC,aAAa,EAAEb;QAAY;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1F,KAAK,iBAAiB;QACpB,oBACE9B,OAAA,CAACH,YAAY;UACXuC,aAAa,EAAGC,MAAM,IAAK;YACzBd,cAAc,CAACc,MAAM,CAAC;YACtBpB,cAAc,CAAC,WAAW,CAAC;UAC7B,CAAE;UACFqB,QAAQ,EAAEA,CAAA,KAAMrB,cAAc,CAAC,MAAM;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAEN,KAAK,cAAc;QACjB,oBAAO9B,OAAA,CAACL,WAAW;UAACsC,OAAO,EAAEb,cAAc,IAAIc;QAAU;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9D,KAAK,MAAM;QACT,OAAOZ,QAAQ,KAAK,OAAO,gBACzBlB,OAAA,CAACR,SAAS;UAAC+C,kBAAkB,EAAEA,CAAA,KAAMpB,WAAW,CAAC,UAAU;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEhE9B,OAAA,CAACP,YAAY;UAAC+C,eAAe,EAAEA,CAAA,KAAMrB,WAAW,CAAC,OAAO;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC7D;MACH;QACE,oBAAO9B,OAAA;UAAA0B,QAAA,EAAK;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IAC3C;EACF,CAAC;EAED,MAAMW,cAAc,GAAIC,IAAY,IAAK;IACvC,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpBvB,WAAW,CAAC,OAAO,CAAC;MACpBF,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,MAAM,IAAIyB,IAAI,KAAK,UAAU,EAAE;MAC9BvB,WAAW,CAAC,UAAU,CAAC;MACvBF,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,MAAM;MACLA,cAAc,CAACyB,IAAI,CAAC;IACtB;EACF,CAAC;EAED,oBACE1C,OAAA,CAACN,MAAM;IACLsB,WAAW,EAAEA,WAAY;IACzB2B,UAAU,EAAEF,cAAe;IAC3BG,iBAAiB,EAAE,CAACpB,WAAY;IAAAE,QAAA,EAE/BD,UAAU,CAAC;EAAC;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEb,CAAC;;AAED;AAAAf,EAAA,CA9FMD,OAAiB;EAAA,QAKGvB,OAAO;AAAA;AAAAsD,GAAA,GAL3B/B,OAAiB;AA+FvB,MAAMgC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAM;IAAEC;EAAQ,CAAC,GAAGzD,OAAO,CAAC,CAAC;EAE7B,IAAIyD,OAAO,EAAE;IACX,oBAAOhD,OAAA,CAACC,gBAAgB;MAAAyB,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAkB,CAAC;EAC3D;EAEA,oBAAO9B,OAAA,CAACc,OAAO;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACpB,CAAC;;AAED;AAAAiB,GAAA,CAVMD,UAAoB;EAAA,QACJvD,OAAO;AAAA;AAAA0D,GAAA,GADvBH,UAAoB;AAW1B,SAASI,GAAGA,CAAA,EAAG;EACb,oBACElD,OAAA,CAACV,YAAY;IAAAoC,QAAA,eACX1B,OAAA,CAAC8C,UAAU;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;AAACqB,GAAA,GANQD,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAA/C,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAgC,GAAA,EAAAI,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAjD,EAAA;AAAAiD,YAAA,CAAA/C,GAAA;AAAA+C,YAAA,CAAA5C,GAAA;AAAA4C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAvC,GAAA;AAAAuC,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}