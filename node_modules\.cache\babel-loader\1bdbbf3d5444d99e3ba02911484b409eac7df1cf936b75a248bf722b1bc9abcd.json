{"ast": null, "code": "import React from'react';import{MusicalNote as MusicalNoteComponent}from'../MusicalNote/MusicalNote';import{getInstrumentTemplate}from'../../utils/instrumentTemplates';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";export const StaffSystem=_ref=>{let{instrument,notes,onStaffClick,onNoteRemove,zoomLevel}=_ref;const template=getInstrumentTemplate(instrument);const staffCount=template.staffCount;// Configurações para diferentes tipos de pauta\nconst getStaffConfig=staffIndex=>{if(instrument==='piano'&&staffCount===2){return{clef:staffIndex===0?'treble':'bass',yOffset:staffIndex*175,notePositions:staffIndex===0?{// Clav<PERSON> de <PERSON> (pauta superior)\n'A5':40,'G5':50,'F5':60,'E5':70,'D5':80,'C5':90,'B4':100,'A4':110,'G4':120,'F4':130,'E4':140}:{// Clave de Fá (pauta inferior)\n'C4':215,'B3':225,'A3':235,'G3':245,'F3':255,'E3':265,'D3':275,'C3':285,'B2':295,'A2':305,'G2':315}};}// Configuração padrão para instrumentos de pauta única\nreturn{clef:template.clef,yOffset:0,notePositions:{'A5':40,'G5':50,'F5':60,'E5':70,'D5':80,'C5':90,'B4':100,'A4':110,'G4':120,'F4':130,'E4':140}};};const renderStaff=staffIndex=>{const config=getStaffConfig(staffIndex);const staffY=config.yOffset;return/*#__PURE__*/_jsxs(\"g\",{children:[[0,1,2,3,4].map(line=>/*#__PURE__*/_jsx(\"line\",{x1:\"50\",y1:50+staffY+line*20,x2:\"750\",y2:50+staffY+line*20,stroke:\"#333\",strokeWidth:\"1\"},line)),/*#__PURE__*/_jsx(\"text\",{x:\"20\",y:90+staffY,fontSize:\"40\",fill:\"#333\",children:config.clef==='treble'?'𝄞':config.clef==='bass'?'𝄢':config.clef==='alto'?'𝄡':'𝄞'}),staffIndex===0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"text\",{x:\"80\",y:75+staffY,fontSize:\"16\",fill:\"#333\",children:\"4\"}),/*#__PURE__*/_jsx(\"text\",{x:\"80\",y:95+staffY,fontSize:\"16\",fill:\"#333\",children:\"4\"})]}),/*#__PURE__*/_jsx(\"line\",{x1:\"110\",y1:50+staffY,x2:\"110\",y2:130+staffY,stroke:\"#333\",strokeWidth:\"2\"}),[1,2,3,4].map(measure=>/*#__PURE__*/_jsx(\"line\",{x1:120+measure*150,y1:50+staffY,x2:120+measure*150,y2:130+staffY,stroke:\"#999\",strokeWidth:\"1\"},measure)),staffIndex===0&&[1,2,3,4].map(measure=>/*#__PURE__*/_jsx(\"text\",{x:120+(measure-1)*150+75,y:\"35\",fontSize:\"12\",fill:\"#666\",textAnchor:\"middle\",children:measure},measure)),/*#__PURE__*/_jsx(\"rect\",{x:\"120\",y:40+staffY,width:\"600\",height:\"100\",fill:\"transparent\",style:{cursor:'crosshair'},onClick:e=>onStaffClick(e,staffIndex)}),notes.filter(note=>note.position.staff===staffIndex).map(note=>{const x=120+(note.position.measure-1)*150+note.position.beat*30;const noteKey=`${note.name}${note.octave}`;const y=config.notePositions[noteKey]||90+staffY;return/*#__PURE__*/_jsx(MusicalNoteComponent,{note:note,x:x,y:y,onRemove:()=>onNoteRemove(note.id)},note.id);}),notes.filter(n=>n.position.staff===staffIndex).length===0&&/*#__PURE__*/_jsx(\"text\",{x:\"400\",y:100+staffY,fontSize:\"14\",fill:\"#999\",textAnchor:\"middle\",children:staffIndex===0?'Clique na pauta para adicionar notas':staffCount===2?'Pauta inferior (mão esquerda)':''}),instrument==='piano'&&staffCount===2&&staffIndex===0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"line\",{x1:\"50\",y1:\"50\",x2:\"50\",y2:\"305\",stroke:\"#333\",strokeWidth:\"3\"}),/*#__PURE__*/_jsx(\"line\",{x1:\"750\",y1:\"50\",x2:\"750\",y2:\"305\",stroke:\"#333\",strokeWidth:\"1\"})]})]},staffIndex);};const svgHeight=staffCount===2?350:200;return/*#__PURE__*/_jsxs(\"svg\",{viewBox:`0 0 800 ${svgHeight}`,style:{width:'100%',height:`${svgHeight}px`,border:'1px solid #e9ecef',borderRadius:'8px',background:'white',marginBottom:'2rem',transform:`scale(${zoomLevel})`,transformOrigin:'top left'},children:[Array.from({length:staffCount},(_,index)=>renderStaff(index)),notes.length===0&&/*#__PURE__*/_jsx(\"text\",{x:\"400\",y:svgHeight-20,fontSize:\"12\",fill:\"#999\",textAnchor:\"middle\",children:\"Clique em uma nota para remov\\xEA-la \\u2022 Use o painel lateral para selecionar ferramentas\"})]});};", "map": {"version": 3, "names": ["React", "MusicalNote", "MusicalNoteComponent", "getInstrumentTemplate", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "StaffSystem", "_ref", "instrument", "notes", "onStaffClick", "onNoteRemove", "zoomLevel", "template", "staffCount", "getStaffConfig", "staffIndex", "clef", "yOffset", "notePositions", "renderStaff", "config", "staffY", "children", "map", "line", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "x", "y", "fontSize", "fill", "measure", "textAnchor", "width", "height", "style", "cursor", "onClick", "e", "filter", "note", "position", "staff", "beat", "note<PERSON>ey", "name", "octave", "onRemove", "id", "n", "length", "svgHeight", "viewBox", "border", "borderRadius", "background", "marginBottom", "transform", "transform<PERSON><PERSON>in", "Array", "from", "_", "index"], "sources": ["D:/Dev/partitura_digital/src/components/StaffSystem/StaffSystem.tsx"], "sourcesContent": ["import React from 'react';\nimport { MusicalNote, InstrumentType, ClefType } from '../../types/music';\nimport { MusicalNote as MusicalNoteComponent } from '../MusicalNote/MusicalNote';\nimport { getInstrumentTemplate } from '../../utils/instrumentTemplates';\n\ninterface StaffSystemProps {\n  instrument: InstrumentType;\n  notes: MusicalNote[];\n  onStaffClick: (event: React.MouseEvent<SVGRectElement>, staffIndex: number) => void;\n  onNoteRemove: (noteId: string) => void;\n  zoomLevel: number;\n}\n\nexport const StaffSystem: React.FC<StaffSystemProps> = ({\n  instrument,\n  notes,\n  onStaffClick,\n  onNoteRemove,\n  zoomLevel\n}) => {\n  const template = getInstrumentTemplate(instrument);\n  const staffCount = template.staffCount;\n  \n  // Configurações para diferentes tipos de pauta\n  const getStaffConfig = (staffIndex: number) => {\n    if (instrument === 'piano' && staffCount === 2) {\n      return {\n        clef: staffIndex === 0 ? 'treble' : 'bass',\n        yOffset: staffIndex * 175,\n        notePositions: staffIndex === 0 ? {\n          // Clave de Sol (pauta superior)\n          'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,\n          'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140\n        } : {\n          // Clave de Fá (pauta inferior)\n          'C4': 215, 'B3': 225, 'A3': 235, 'G3': 245, 'F3': 255,\n          'E3': 265, 'D3': 275, 'C3': 285, 'B2': 295, 'A2': 305, 'G2': 315\n        }\n      };\n    }\n    \n    // Configuração padrão para instrumentos de pauta única\n    return {\n      clef: template.clef,\n      yOffset: 0,\n      notePositions: {\n        'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,\n        'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140\n      }\n    };\n  };\n\n  const renderStaff = (staffIndex: number) => {\n    const config = getStaffConfig(staffIndex);\n    const staffY = config.yOffset;\n    \n    return (\n      <g key={staffIndex}>\n        {/* Linhas da pauta */}\n        {[0, 1, 2, 3, 4].map(line => (\n          <line\n            key={line}\n            x1=\"50\"\n            y1={50 + staffY + line * 20}\n            x2=\"750\"\n            y2={50 + staffY + line * 20}\n            stroke=\"#333\"\n            strokeWidth=\"1\"\n          />\n        ))}\n        \n        {/* Clave */}\n        <text x=\"20\" y={90 + staffY} fontSize=\"40\" fill=\"#333\">\n          {config.clef === 'treble' ? '𝄞' : \n           config.clef === 'bass' ? '𝄢' : \n           config.clef === 'alto' ? '𝄡' : '𝄞'}\n        </text>\n        \n        {/* Compasso (apenas na primeira pauta) */}\n        {staffIndex === 0 && (\n          <>\n            <text x=\"80\" y={75 + staffY} fontSize=\"16\" fill=\"#333\">4</text>\n            <text x=\"80\" y={95 + staffY} fontSize=\"16\" fill=\"#333\">4</text>\n          </>\n        )}\n        \n        {/* Linha divisória inicial */}\n        <line \n          x1=\"110\" \n          y1={50 + staffY} \n          x2=\"110\" \n          y2={130 + staffY} \n          stroke=\"#333\" \n          strokeWidth=\"2\"\n        />\n        \n        {/* Divisões de compasso */}\n        {[1, 2, 3, 4].map(measure => (\n          <line\n            key={measure}\n            x1={120 + (measure * 150)}\n            y1={50 + staffY}\n            x2={120 + (measure * 150)}\n            y2={130 + staffY}\n            stroke=\"#999\"\n            strokeWidth=\"1\"\n          />\n        ))}\n        \n        {/* Números dos compassos (apenas na primeira pauta) */}\n        {staffIndex === 0 && [1, 2, 3, 4].map(measure => (\n          <text\n            key={measure}\n            x={120 + ((measure - 1) * 150) + 75}\n            y=\"35\"\n            fontSize=\"12\"\n            fill=\"#666\"\n            textAnchor=\"middle\"\n          >\n            {measure}\n          </text>\n        ))}\n        \n        {/* Área clicável para adicionar notas */}\n        <rect \n          x=\"120\" \n          y={40 + staffY} \n          width=\"600\" \n          height=\"100\" \n          fill=\"transparent\" \n          style={{ cursor: 'crosshair' }}\n          onClick={(e) => onStaffClick(e, staffIndex)}\n        />\n        \n        {/* Renderizar notas desta pauta */}\n        {notes\n          .filter(note => note.position.staff === staffIndex)\n          .map((note) => {\n            const x = 120 + ((note.position.measure - 1) * 150) + (note.position.beat * 30);\n            const noteKey = `${note.name}${note.octave}`;\n            const y = (config.notePositions as any)[noteKey] || (90 + staffY);\n\n            return (\n              <MusicalNoteComponent\n                key={note.id}\n                note={note}\n                x={x}\n                y={y}\n                onRemove={() => onNoteRemove(note.id)}\n              />\n            );\n          })}\n        \n        {/* Instruções (apenas se não há notas) */}\n        {notes.filter(n => n.position.staff === staffIndex).length === 0 && (\n          <text \n            x=\"400\" \n            y={100 + staffY} \n            fontSize=\"14\" \n            fill=\"#999\" \n            textAnchor=\"middle\"\n          >\n            {staffIndex === 0 ? 'Clique na pauta para adicionar notas' : \n             staffCount === 2 ? 'Pauta inferior (mão esquerda)' : ''}\n          </text>\n        )}\n        \n        {/* Conectores entre pautas (para piano) */}\n        {instrument === 'piano' && staffCount === 2 && staffIndex === 0 && (\n          <>\n            {/* Linha conectora esquerda */}\n            <line\n              x1=\"50\"\n              y1=\"50\"\n              x2=\"50\"\n              y2=\"305\"\n              stroke=\"#333\"\n              strokeWidth=\"3\"\n            />\n            {/* Linha conectora direita */}\n            <line\n              x1=\"750\"\n              y1=\"50\"\n              x2=\"750\"\n              y2=\"305\"\n              stroke=\"#333\"\n              strokeWidth=\"1\"\n            />\n          </>\n        )}\n      </g>\n    );\n  };\n\n  const svgHeight = staffCount === 2 ? 350 : 200;\n\n  return (\n    <svg \n      viewBox={`0 0 800 ${svgHeight}`}\n      style={{ \n        width: '100%',\n        height: `${svgHeight}px`,\n        border: '1px solid #e9ecef',\n        borderRadius: '8px',\n        background: 'white',\n        marginBottom: '2rem',\n        transform: `scale(${zoomLevel})`,\n        transformOrigin: 'top left'\n      }}\n    >\n      {Array.from({ length: staffCount }, (_, index) => renderStaff(index))}\n      \n      {/* Instruções gerais */}\n      {notes.length === 0 && (\n        <text x=\"400\" y={svgHeight - 20} fontSize=\"12\" fill=\"#999\" textAnchor=\"middle\">\n          Clique em uma nota para removê-la • Use o painel lateral para selecionar ferramentas\n        </text>\n      )}\n    </svg>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,OAASC,WAAW,GAAI,CAAAC,oBAAoB,KAAQ,4BAA4B,CAChF,OAASC,qBAAqB,KAAQ,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAUxE,MAAO,MAAM,CAAAC,WAAuC,CAAGC,IAAA,EAMjD,IANkD,CACtDC,UAAU,CACVC,KAAK,CACLC,YAAY,CACZC,YAAY,CACZC,SACF,CAAC,CAAAL,IAAA,CACC,KAAM,CAAAM,QAAQ,CAAGd,qBAAqB,CAACS,UAAU,CAAC,CAClD,KAAM,CAAAM,UAAU,CAAGD,QAAQ,CAACC,UAAU,CAEtC;AACA,KAAM,CAAAC,cAAc,CAAIC,UAAkB,EAAK,CAC7C,GAAIR,UAAU,GAAK,OAAO,EAAIM,UAAU,GAAK,CAAC,CAAE,CAC9C,MAAO,CACLG,IAAI,CAAED,UAAU,GAAK,CAAC,CAAG,QAAQ,CAAG,MAAM,CAC1CE,OAAO,CAAEF,UAAU,CAAG,GAAG,CACzBG,aAAa,CAAEH,UAAU,GAAK,CAAC,CAAG,CAChC;AACA,IAAI,CAAE,EAAE,CAAE,IAAI,CAAE,EAAE,CAAE,IAAI,CAAE,EAAE,CAAE,IAAI,CAAE,EAAE,CAAE,IAAI,CAAE,EAAE,CAChD,IAAI,CAAE,EAAE,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAC9D,CAAC,CAAG,CACF;AACA,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CACrD,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAC/D,CACF,CAAC,CACH,CAEA;AACA,MAAO,CACLC,IAAI,CAAEJ,QAAQ,CAACI,IAAI,CACnBC,OAAO,CAAE,CAAC,CACVC,aAAa,CAAE,CACb,IAAI,CAAE,EAAE,CAAE,IAAI,CAAE,EAAE,CAAE,IAAI,CAAE,EAAE,CAAE,IAAI,CAAE,EAAE,CAAE,IAAI,CAAE,EAAE,CAChD,IAAI,CAAE,EAAE,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAC9D,CACF,CAAC,CACH,CAAC,CAED,KAAM,CAAAC,WAAW,CAAIJ,UAAkB,EAAK,CAC1C,KAAM,CAAAK,MAAM,CAAGN,cAAc,CAACC,UAAU,CAAC,CACzC,KAAM,CAAAM,MAAM,CAAGD,MAAM,CAACH,OAAO,CAE7B,mBACEb,KAAA,MAAAkB,QAAA,EAEG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACC,GAAG,CAACC,IAAI,eACvBxB,IAAA,SAEEyB,EAAE,CAAC,IAAI,CACPC,EAAE,CAAE,EAAE,CAAGL,MAAM,CAAGG,IAAI,CAAG,EAAG,CAC5BG,EAAE,CAAC,KAAK,CACRC,EAAE,CAAE,EAAE,CAAGP,MAAM,CAAGG,IAAI,CAAG,EAAG,CAC5BK,MAAM,CAAC,MAAM,CACbC,WAAW,CAAC,GAAG,EANVN,IAON,CACF,CAAC,cAGFxB,IAAA,SAAM+B,CAAC,CAAC,IAAI,CAACC,CAAC,CAAE,EAAE,CAAGX,MAAO,CAACY,QAAQ,CAAC,IAAI,CAACC,IAAI,CAAC,MAAM,CAAAZ,QAAA,CACnDF,MAAM,CAACJ,IAAI,GAAK,QAAQ,CAAG,IAAI,CAC/BI,MAAM,CAACJ,IAAI,GAAK,MAAM,CAAG,IAAI,CAC7BI,MAAM,CAACJ,IAAI,GAAK,MAAM,CAAG,IAAI,CAAG,IAAI,CACjC,CAAC,CAGND,UAAU,GAAK,CAAC,eACfX,KAAA,CAAAF,SAAA,EAAAoB,QAAA,eACEtB,IAAA,SAAM+B,CAAC,CAAC,IAAI,CAACC,CAAC,CAAE,EAAE,CAAGX,MAAO,CAACY,QAAQ,CAAC,IAAI,CAACC,IAAI,CAAC,MAAM,CAAAZ,QAAA,CAAC,GAAC,CAAM,CAAC,cAC/DtB,IAAA,SAAM+B,CAAC,CAAC,IAAI,CAACC,CAAC,CAAE,EAAE,CAAGX,MAAO,CAACY,QAAQ,CAAC,IAAI,CAACC,IAAI,CAAC,MAAM,CAAAZ,QAAA,CAAC,GAAC,CAAM,CAAC,EAC/D,CACH,cAGDtB,IAAA,SACEyB,EAAE,CAAC,KAAK,CACRC,EAAE,CAAE,EAAE,CAAGL,MAAO,CAChBM,EAAE,CAAC,KAAK,CACRC,EAAE,CAAE,GAAG,CAAGP,MAAO,CACjBQ,MAAM,CAAC,MAAM,CACbC,WAAW,CAAC,GAAG,CAChB,CAAC,CAGD,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACP,GAAG,CAACY,OAAO,eACvBnC,IAAA,SAEEyB,EAAE,CAAE,GAAG,CAAIU,OAAO,CAAG,GAAK,CAC1BT,EAAE,CAAE,EAAE,CAAGL,MAAO,CAChBM,EAAE,CAAE,GAAG,CAAIQ,OAAO,CAAG,GAAK,CAC1BP,EAAE,CAAE,GAAG,CAAGP,MAAO,CACjBQ,MAAM,CAAC,MAAM,CACbC,WAAW,CAAC,GAAG,EANVK,OAON,CACF,CAAC,CAGDpB,UAAU,GAAK,CAAC,EAAI,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACQ,GAAG,CAACY,OAAO,eAC3CnC,IAAA,SAEE+B,CAAC,CAAE,GAAG,CAAI,CAACI,OAAO,CAAG,CAAC,EAAI,GAAI,CAAG,EAAG,CACpCH,CAAC,CAAC,IAAI,CACNC,QAAQ,CAAC,IAAI,CACbC,IAAI,CAAC,MAAM,CACXE,UAAU,CAAC,QAAQ,CAAAd,QAAA,CAElBa,OAAO,EAPHA,OAQD,CACP,CAAC,cAGFnC,IAAA,SACE+B,CAAC,CAAC,KAAK,CACPC,CAAC,CAAE,EAAE,CAAGX,MAAO,CACfgB,KAAK,CAAC,KAAK,CACXC,MAAM,CAAC,KAAK,CACZJ,IAAI,CAAC,aAAa,CAClBK,KAAK,CAAE,CAAEC,MAAM,CAAE,WAAY,CAAE,CAC/BC,OAAO,CAAGC,CAAC,EAAKjC,YAAY,CAACiC,CAAC,CAAE3B,UAAU,CAAE,CAC7C,CAAC,CAGDP,KAAK,CACHmC,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,QAAQ,CAACC,KAAK,GAAK/B,UAAU,CAAC,CAClDQ,GAAG,CAAEqB,IAAI,EAAK,CACb,KAAM,CAAAb,CAAC,CAAG,GAAG,CAAI,CAACa,IAAI,CAACC,QAAQ,CAACV,OAAO,CAAG,CAAC,EAAI,GAAI,CAAIS,IAAI,CAACC,QAAQ,CAACE,IAAI,CAAG,EAAG,CAC/E,KAAM,CAAAC,OAAO,CAAG,GAAGJ,IAAI,CAACK,IAAI,GAAGL,IAAI,CAACM,MAAM,EAAE,CAC5C,KAAM,CAAAlB,CAAC,CAAIZ,MAAM,CAACF,aAAa,CAAS8B,OAAO,CAAC,EAAK,EAAE,CAAG3B,MAAO,CAEjE,mBACErB,IAAA,CAACH,oBAAoB,EAEnB+C,IAAI,CAAEA,IAAK,CACXb,CAAC,CAAEA,CAAE,CACLC,CAAC,CAAEA,CAAE,CACLmB,QAAQ,CAAEA,CAAA,GAAMzC,YAAY,CAACkC,IAAI,CAACQ,EAAE,CAAE,EAJjCR,IAAI,CAACQ,EAKX,CAAC,CAEN,CAAC,CAAC,CAGH5C,KAAK,CAACmC,MAAM,CAACU,CAAC,EAAIA,CAAC,CAACR,QAAQ,CAACC,KAAK,GAAK/B,UAAU,CAAC,CAACuC,MAAM,GAAK,CAAC,eAC9DtD,IAAA,SACE+B,CAAC,CAAC,KAAK,CACPC,CAAC,CAAE,GAAG,CAAGX,MAAO,CAChBY,QAAQ,CAAC,IAAI,CACbC,IAAI,CAAC,MAAM,CACXE,UAAU,CAAC,QAAQ,CAAAd,QAAA,CAElBP,UAAU,GAAK,CAAC,CAAG,sCAAsC,CACzDF,UAAU,GAAK,CAAC,CAAG,+BAA+B,CAAG,EAAE,CACpD,CACP,CAGAN,UAAU,GAAK,OAAO,EAAIM,UAAU,GAAK,CAAC,EAAIE,UAAU,GAAK,CAAC,eAC7DX,KAAA,CAAAF,SAAA,EAAAoB,QAAA,eAEEtB,IAAA,SACEyB,EAAE,CAAC,IAAI,CACPC,EAAE,CAAC,IAAI,CACPC,EAAE,CAAC,IAAI,CACPC,EAAE,CAAC,KAAK,CACRC,MAAM,CAAC,MAAM,CACbC,WAAW,CAAC,GAAG,CAChB,CAAC,cAEF9B,IAAA,SACEyB,EAAE,CAAC,KAAK,CACRC,EAAE,CAAC,IAAI,CACPC,EAAE,CAAC,KAAK,CACRC,EAAE,CAAC,KAAK,CACRC,MAAM,CAAC,MAAM,CACbC,WAAW,CAAC,GAAG,CAChB,CAAC,EACF,CACH,GApIKf,UAqIL,CAAC,CAER,CAAC,CAED,KAAM,CAAAwC,SAAS,CAAG1C,UAAU,GAAK,CAAC,CAAG,GAAG,CAAG,GAAG,CAE9C,mBACET,KAAA,QACEoD,OAAO,CAAE,WAAWD,SAAS,EAAG,CAChChB,KAAK,CAAE,CACLF,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,GAAGiB,SAAS,IAAI,CACxBE,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,OAAO,CACnBC,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,SAASlD,SAAS,GAAG,CAChCmD,eAAe,CAAE,UACnB,CAAE,CAAAxC,QAAA,EAEDyC,KAAK,CAACC,IAAI,CAAC,CAAEV,MAAM,CAAEzC,UAAW,CAAC,CAAE,CAACoD,CAAC,CAAEC,KAAK,GAAK/C,WAAW,CAAC+C,KAAK,CAAC,CAAC,CAGpE1D,KAAK,CAAC8C,MAAM,GAAK,CAAC,eACjBtD,IAAA,SAAM+B,CAAC,CAAC,KAAK,CAACC,CAAC,CAAEuB,SAAS,CAAG,EAAG,CAACtB,QAAQ,CAAC,IAAI,CAACC,IAAI,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAAAd,QAAA,CAAC,8FAE/E,CAAM,CACP,EACE,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}