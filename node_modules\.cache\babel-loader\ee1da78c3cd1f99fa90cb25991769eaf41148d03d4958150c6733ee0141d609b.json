{"ast": null, "code": "import React from'react';import styled from'styled-components';import{jsxs as _jsxs,jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";const ChordContainer=styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;const ChordHeader=styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n`;const ChordTitle=styled.h3`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n`;const ExportButton=styled.button`\n  padding: 0.5rem 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n  }\n`;const ChordGrid=styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n`;const MeasureCard=styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n`;const MeasureNumber=styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;const ChordSymbol=styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n  text-align: center;\n`;const ChordNotes=styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  text-align: center;\n`;const EmptyState=styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;const ProgressionView=styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  border: 2px solid #e9ecef;\n`;const ProgressionTitle=styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;const ProgressionChords=styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  align-items: center;\n`;const ProgressionChord=styled.span`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 8px;\n  font-weight: 600;\n  font-size: 1.1rem;\n`;const ProgressionArrow=styled.span`\n  color: #666;\n  font-size: 1.2rem;\n`;// Função para determinar o acorde baseado nas notas\nconst analyzeChord=notes=>{if(notes.length===0)return'';// Filtrar apenas notas (não pausas) e aplicar acidentes\nconst processedNotes=notes.filter(note=>!note.isRest).map(note=>{let noteName=note.name;// Aplicar acidentes\nif(note.accidental==='sharp'){const sharpMap={'C':'C#','D':'D#','F':'F#','G':'G#','A':'A#'};noteName=sharpMap[noteName]||noteName;}else if(note.accidental==='flat'){const flatMap={'D':'Db','E':'Eb','G':'Gb','A':'Ab','B':'Bb'};noteName=flatMap[noteName]||noteName;}return noteName;}).filter((name,index,arr)=>arr.indexOf(name)===index);// Remover duplicatas\nif(processedNotes.length===0)return'';if(processedNotes.length===1)return processedNotes[0];// Normalizar notas para análise (converter tudo para sustenidos)\nconst normalizeNote=note=>{const enharmonics={'Db':'C#','Eb':'D#','Gb':'F#','Ab':'G#','Bb':'A#'};return enharmonics[note]||note;};const normalizedNotes=processedNotes.map(normalizeNote).sort();// Definir acordes conhecidos\nconst chordPatterns={// Acordes maiores\n'C,E,G':'C','C#,F,G#':'C#','D,F#,A':'D','D#,G,A#':'D#','E,G#,B':'E','F,A,C':'F','F#,A#,C#':'F#','G,B,D':'G','G#,C,D#':'G#','A,C#,E':'A','A#,D,F':'A#','B,D#,F#':'B',// Acordes menores\n'C,D#,G':'Cm','C#,E,G#':'C#m','D,F,A':'Dm','D#,F#,A#':'D#m','E,G,B':'Em','F,G#,C':'Fm','F#,A,C#':'F#m','G,A#,D':'Gm','G#,B,D#':'G#m','A,C,E':'Am','A#,C#,F':'A#m','B,D,F#':'Bm',// Acordes de sétima\n'C,E,G,A#':'C7','D,F#,A,C':'D7','E,G#,B,D':'E7','F,A,C,D#':'F7','G,B,D,F':'G7','A,C#,E,G':'A7','B,D#,F#,A':'B7'};const noteString=normalizedNotes.join(',');// Verificar padrões conhecidos\nif(chordPatterns[noteString]){return chordPatterns[noteString];}// Se não encontrou um padrão, tentar identificar pela fundamental\nconst root=normalizedNotes[0];const intervals=normalizedNotes.slice(1);if(intervals.length>=2){// Tentar identificar como acorde básico\nreturn`${root}?`;// Indica acorde não identificado com fundamental\n}// Retornar notas individuais\nreturn processedNotes.join(' - ');};export const ChordView=_ref=>{let{notes,title}=_ref;// Agrupar notas por compasso\nconst measureMap=new Map();notes.forEach(note=>{const measure=note.position.measure;if(!measureMap.has(measure)){measureMap.set(measure,[]);}measureMap.get(measure).push(note);});// Converter para array ordenado\nconst measures=Array.from(measureMap.entries()).sort((_ref2,_ref3)=>{let[a]=_ref2;let[b]=_ref3;return a-b;}).map(_ref4=>{let[measureNumber,measureNotes]=_ref4;return{number:measureNumber,notes:measureNotes.sort((a,b)=>a.position.beat-b.position.beat),chord:analyzeChord(measureNotes)};});// Função para exportar cifras como texto\nconst exportChords=()=>{const progression=measures.filter(m=>m.chord&&m.chord!=='—').map(m=>m.chord);const exportText=`\nPartitura: ${title}\nProgressão Harmônica: ${progression.join(' - ')}\n\nDetalhes por Compasso:\n${measures.map(m=>`Compasso ${m.number}: ${m.chord||'—'}`).join('\\n')}\n    `.trim();// Copiar para clipboard\nnavigator.clipboard.writeText(exportText).then(()=>{alert('Cifras copiadas para a área de transferência!');}).catch(()=>{// Fallback: mostrar em alert\nalert(exportText);});};// Criar progressão harmônica\nconst progression=measures.filter(m=>m.chord&&m.chord!=='—').map(m=>m.chord);return/*#__PURE__*/_jsxs(ChordContainer,{children:[/*#__PURE__*/_jsxs(ChordHeader,{children:[/*#__PURE__*/_jsxs(ChordTitle,{children:[\"\\uD83C\\uDFB8 Cifras - \",title]}),measures.length>0&&/*#__PURE__*/_jsx(ExportButton,{onClick:exportChords,children:\"\\uD83D\\uDCCB Exportar Cifras\"})]}),measures.length===0?/*#__PURE__*/_jsx(EmptyState,{children:\"Adicione algumas notas na partitura para ver as cifras aqui\"}):/*#__PURE__*/_jsxs(_Fragment,{children:[progression.length>0&&/*#__PURE__*/_jsxs(ProgressionView,{children:[/*#__PURE__*/_jsx(ProgressionTitle,{children:\"\\uD83C\\uDFBC Progress\\xE3o Harm\\xF4nica\"}),/*#__PURE__*/_jsx(ProgressionChords,{children:progression.map((chord,index)=>/*#__PURE__*/_jsxs(React.Fragment,{children:[/*#__PURE__*/_jsx(ProgressionChord,{children:chord}),index<progression.length-1&&/*#__PURE__*/_jsx(ProgressionArrow,{children:\"\\u2192\"})]},index))})]}),/*#__PURE__*/_jsx(ChordGrid,{children:measures.map(measure=>/*#__PURE__*/_jsxs(MeasureCard,{children:[/*#__PURE__*/_jsxs(MeasureNumber,{children:[\"Compasso \",measure.number]}),/*#__PURE__*/_jsx(ChordSymbol,{children:measure.chord||'—'}),/*#__PURE__*/_jsx(ChordNotes,{children:measure.notes.filter(note=>!note.isRest).map(note=>{let noteName=note.name;if(note.accidental==='sharp')noteName+='♯';if(note.accidental==='flat')noteName+='♭';if(note.accidental==='natural')noteName+='♮';return`${noteName}${note.octave}`;}).join(', ')||'Pausas'})]},measure.number))})]})]});};", "map": {"version": 3, "names": ["React", "styled", "jsxs", "_jsxs", "jsx", "_jsx", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChordTitle", "h3", "ExportButton", "button", "ChordGrid", "MeasureCard", "MeasureNumber", "ChordSymbol", "ChordNotes", "EmptyState", "ProgressionView", "ProgressionTitle", "h4", "ProgressionChords", "ProgressionChord", "span", "ProgressionArrow", "analyzeChord", "notes", "length", "processedNotes", "filter", "note", "isRest", "map", "noteName", "name", "accidental", "sharpMap", "flatMap", "index", "arr", "indexOf", "normalizeNote", "enharmonics", "normalizedNotes", "sort", "chordPatterns", "noteString", "join", "root", "intervals", "slice", "ChordView", "_ref", "title", "measureMap", "Map", "for<PERSON>ach", "measure", "position", "has", "set", "get", "push", "measures", "Array", "from", "entries", "_ref2", "_ref3", "a", "b", "_ref4", "measureNumber", "measureNotes", "number", "beat", "chord", "exportChords", "progression", "m", "exportText", "trim", "navigator", "clipboard", "writeText", "then", "alert", "catch", "children", "onClick", "octave"], "sources": ["D:/Dev/partitura_digital/src/components/ChordView/ChordView.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { MusicalNote, NoteName } from '../../types/music';\n\nconst ChordContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst ChordHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n`;\n\nconst ChordTitle = styled.h3`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n`;\n\nconst ExportButton = styled.button`\n  padding: 0.5rem 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n  }\n`;\n\nconst ChordGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n`;\n\nconst MeasureCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n`;\n\nconst MeasureNumber = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;\n\nconst ChordSymbol = styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n  text-align: center;\n`;\n\nconst ChordNotes = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  text-align: center;\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;\n\nconst ProgressionView = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  border: 2px solid #e9ecef;\n`;\n\nconst ProgressionTitle = styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;\n\nconst ProgressionChords = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  align-items: center;\n`;\n\nconst ProgressionChord = styled.span`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 8px;\n  font-weight: 600;\n  font-size: 1.1rem;\n`;\n\nconst ProgressionArrow = styled.span`\n  color: #666;\n  font-size: 1.2rem;\n`;\n\ninterface ChordViewProps {\n  notes: MusicalNote[];\n  title: string;\n}\n\n// Função para determinar o acorde baseado nas notas\nconst analyzeChord = (notes: MusicalNote[]): string => {\n  if (notes.length === 0) return '';\n\n  // Filtrar apenas notas (não pausas) e aplicar acidentes\n  const processedNotes = notes\n    .filter(note => !note.isRest)\n    .map(note => {\n      let noteName: string = note.name;\n\n      // Aplicar acidentes\n      if (note.accidental === 'sharp') {\n        const sharpMap: { [key: string]: string } = {\n          'C': 'C#', 'D': 'D#', 'F': 'F#', 'G': 'G#', 'A': 'A#'\n        };\n        noteName = sharpMap[noteName] || noteName;\n      } else if (note.accidental === 'flat') {\n        const flatMap: { [key: string]: string } = {\n          'D': 'Db', 'E': 'Eb', 'G': 'Gb', 'A': 'Ab', 'B': 'Bb'\n        };\n        noteName = flatMap[noteName] || noteName;\n      }\n\n      return noteName;\n    })\n    .filter((name, index, arr) => arr.indexOf(name) === index); // Remover duplicatas\n\n  if (processedNotes.length === 0) return '';\n  if (processedNotes.length === 1) return processedNotes[0];\n\n  // Normalizar notas para análise (converter tudo para sustenidos)\n  const normalizeNote = (note: string): string => {\n    const enharmonics: { [key: string]: string } = {\n      'Db': 'C#', 'Eb': 'D#', 'Gb': 'F#', 'Ab': 'G#', 'Bb': 'A#'\n    };\n    return enharmonics[note] || note;\n  };\n\n  const normalizedNotes = processedNotes.map(normalizeNote).sort();\n\n  // Definir acordes conhecidos\n  const chordPatterns: { [key: string]: string } = {\n    // Acordes maiores\n    'C,E,G': 'C',\n    'C#,F,G#': 'C#',\n    'D,F#,A': 'D',\n    'D#,G,A#': 'D#',\n    'E,G#,B': 'E',\n    'F,A,C': 'F',\n    'F#,A#,C#': 'F#',\n    'G,B,D': 'G',\n    'G#,C,D#': 'G#',\n    'A,C#,E': 'A',\n    'A#,D,F': 'A#',\n    'B,D#,F#': 'B',\n\n    // Acordes menores\n    'C,D#,G': 'Cm',\n    'C#,E,G#': 'C#m',\n    'D,F,A': 'Dm',\n    'D#,F#,A#': 'D#m',\n    'E,G,B': 'Em',\n    'F,G#,C': 'Fm',\n    'F#,A,C#': 'F#m',\n    'G,A#,D': 'Gm',\n    'G#,B,D#': 'G#m',\n    'A,C,E': 'Am',\n    'A#,C#,F': 'A#m',\n    'B,D,F#': 'Bm',\n\n    // Acordes de sétima\n    'C,E,G,A#': 'C7',\n    'D,F#,A,C': 'D7',\n    'E,G#,B,D': 'E7',\n    'F,A,C,D#': 'F7',\n    'G,B,D,F': 'G7',\n    'A,C#,E,G': 'A7',\n    'B,D#,F#,A': 'B7'\n  };\n\n  const noteString = normalizedNotes.join(',');\n\n  // Verificar padrões conhecidos\n  if (chordPatterns[noteString]) {\n    return chordPatterns[noteString];\n  }\n\n  // Se não encontrou um padrão, tentar identificar pela fundamental\n  const root = normalizedNotes[0];\n  const intervals = normalizedNotes.slice(1);\n\n  if (intervals.length >= 2) {\n    // Tentar identificar como acorde básico\n    return `${root}?`; // Indica acorde não identificado com fundamental\n  }\n\n  // Retornar notas individuais\n  return processedNotes.join(' - ');\n};\n\nexport const ChordView: React.FC<ChordViewProps> = ({ notes, title }) => {\n  // Agrupar notas por compasso\n  const measureMap = new Map<number, MusicalNote[]>();\n\n  notes.forEach(note => {\n    const measure = note.position.measure;\n    if (!measureMap.has(measure)) {\n      measureMap.set(measure, []);\n    }\n    measureMap.get(measure)!.push(note);\n  });\n\n  // Converter para array ordenado\n  const measures = Array.from(measureMap.entries())\n    .sort(([a], [b]) => a - b)\n    .map(([measureNumber, measureNotes]) => ({\n      number: measureNumber,\n      notes: measureNotes.sort((a, b) => a.position.beat - b.position.beat),\n      chord: analyzeChord(measureNotes)\n    }));\n\n  // Função para exportar cifras como texto\n  const exportChords = () => {\n    const progression = measures\n      .filter(m => m.chord && m.chord !== '—')\n      .map(m => m.chord);\n\n    const exportText = `\nPartitura: ${title}\nProgressão Harmônica: ${progression.join(' - ')}\n\nDetalhes por Compasso:\n${measures.map(m => `Compasso ${m.number}: ${m.chord || '—'}`).join('\\n')}\n    `.trim();\n\n    // Copiar para clipboard\n    navigator.clipboard.writeText(exportText).then(() => {\n      alert('Cifras copiadas para a área de transferência!');\n    }).catch(() => {\n      // Fallback: mostrar em alert\n      alert(exportText);\n    });\n  };\n\n  // Criar progressão harmônica\n  const progression = measures\n    .filter(m => m.chord && m.chord !== '—')\n    .map(m => m.chord);\n\n  return (\n    <ChordContainer>\n      <ChordHeader>\n        <ChordTitle>🎸 Cifras - {title}</ChordTitle>\n        {measures.length > 0 && (\n          <ExportButton onClick={exportChords}>\n            📋 Exportar Cifras\n          </ExportButton>\n        )}\n      </ChordHeader>\n\n      {measures.length === 0 ? (\n        <EmptyState>\n          Adicione algumas notas na partitura para ver as cifras aqui\n        </EmptyState>\n      ) : (\n        <>\n          {/* Progressão Harmônica */}\n          {progression.length > 0 && (\n            <ProgressionView>\n              <ProgressionTitle>🎼 Progressão Harmônica</ProgressionTitle>\n              <ProgressionChords>\n                {progression.map((chord, index) => (\n                  <React.Fragment key={index}>\n                    <ProgressionChord>{chord}</ProgressionChord>\n                    {index < progression.length - 1 && (\n                      <ProgressionArrow>→</ProgressionArrow>\n                    )}\n                  </React.Fragment>\n                ))}\n              </ProgressionChords>\n            </ProgressionView>\n          )}\n\n          {/* Detalhes por Compasso */}\n          <ChordGrid>\n            {measures.map(measure => (\n              <MeasureCard key={measure.number}>\n                <MeasureNumber>Compasso {measure.number}</MeasureNumber>\n                <ChordSymbol>\n                  {measure.chord || '—'}\n                </ChordSymbol>\n                <ChordNotes>\n                  {measure.notes\n                    .filter(note => !note.isRest)\n                    .map(note => {\n                      let noteName = note.name;\n                      if (note.accidental === 'sharp') noteName += '♯';\n                      if (note.accidental === 'flat') noteName += '♭';\n                      if (note.accidental === 'natural') noteName += '♮';\n                      return `${noteName}${note.octave}`;\n                    })\n                    .join(', ') || 'Pausas'}\n                </ChordNotes>\n              </MeasureCard>\n            ))}\n          </ChordGrid>\n        </>\n      )}\n    </ChordContainer>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBAGvC,KAAM,CAAAC,cAAc,CAAGP,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGT,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAE,UAAU,CAAGV,MAAM,CAACW,EAAE;AAC5B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGZ,MAAM,CAACa,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,SAAS,CAAGd,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAO,WAAW,CAAGf,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAQ,aAAa,CAAGhB,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAS,WAAW,CAAGjB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAU,UAAU,CAAGlB,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAW,UAAU,CAAGnB,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAY,eAAe,CAAGpB,MAAM,CAACQ,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAa,gBAAgB,CAAGrB,MAAM,CAACsB,EAAE;AAClC;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,iBAAiB,CAAGvB,MAAM,CAACQ,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAgB,gBAAgB,CAAGxB,MAAM,CAACyB,IAAI;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAG1B,MAAM,CAACyB,IAAI;AACpC;AACA;AACA,CAAC,CAOD;AACA,KAAM,CAAAE,YAAY,CAAIC,KAAoB,EAAa,CACrD,GAAIA,KAAK,CAACC,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAEjC;AACA,KAAM,CAAAC,cAAc,CAAGF,KAAK,CACzBG,MAAM,CAACC,IAAI,EAAI,CAACA,IAAI,CAACC,MAAM,CAAC,CAC5BC,GAAG,CAACF,IAAI,EAAI,CACX,GAAI,CAAAG,QAAgB,CAAGH,IAAI,CAACI,IAAI,CAEhC;AACA,GAAIJ,IAAI,CAACK,UAAU,GAAK,OAAO,CAAE,CAC/B,KAAM,CAAAC,QAAmC,CAAG,CAC1C,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IACnD,CAAC,CACDH,QAAQ,CAAGG,QAAQ,CAACH,QAAQ,CAAC,EAAIA,QAAQ,CAC3C,CAAC,IAAM,IAAIH,IAAI,CAACK,UAAU,GAAK,MAAM,CAAE,CACrC,KAAM,CAAAE,OAAkC,CAAG,CACzC,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IAAI,CAAE,GAAG,CAAE,IACnD,CAAC,CACDJ,QAAQ,CAAGI,OAAO,CAACJ,QAAQ,CAAC,EAAIA,QAAQ,CAC1C,CAEA,MAAO,CAAAA,QAAQ,CACjB,CAAC,CAAC,CACDJ,MAAM,CAAC,CAACK,IAAI,CAAEI,KAAK,CAAEC,GAAG,GAAKA,GAAG,CAACC,OAAO,CAACN,IAAI,CAAC,GAAKI,KAAK,CAAC,CAAE;AAE9D,GAAIV,cAAc,CAACD,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAC1C,GAAIC,cAAc,CAACD,MAAM,GAAK,CAAC,CAAE,MAAO,CAAAC,cAAc,CAAC,CAAC,CAAC,CAEzD;AACA,KAAM,CAAAa,aAAa,CAAIX,IAAY,EAAa,CAC9C,KAAM,CAAAY,WAAsC,CAAG,CAC7C,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IACxD,CAAC,CACD,MAAO,CAAAA,WAAW,CAACZ,IAAI,CAAC,EAAIA,IAAI,CAClC,CAAC,CAED,KAAM,CAAAa,eAAe,CAAGf,cAAc,CAACI,GAAG,CAACS,aAAa,CAAC,CAACG,IAAI,CAAC,CAAC,CAEhE;AACA,KAAM,CAAAC,aAAwC,CAAG,CAC/C;AACA,OAAO,CAAE,GAAG,CACZ,SAAS,CAAE,IAAI,CACf,QAAQ,CAAE,GAAG,CACb,SAAS,CAAE,IAAI,CACf,QAAQ,CAAE,GAAG,CACb,OAAO,CAAE,GAAG,CACZ,UAAU,CAAE,IAAI,CAChB,OAAO,CAAE,GAAG,CACZ,SAAS,CAAE,IAAI,CACf,QAAQ,CAAE,GAAG,CACb,QAAQ,CAAE,IAAI,CACd,SAAS,CAAE,GAAG,CAEd;AACA,QAAQ,CAAE,IAAI,CACd,SAAS,CAAE,KAAK,CAChB,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,IAAI,CACd,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,IAAI,CACd,SAAS,CAAE,KAAK,CAChB,OAAO,CAAE,IAAI,CACb,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,IAAI,CAEd;AACA,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,IAAI,CAChB,SAAS,CAAE,IAAI,CACf,UAAU,CAAE,IAAI,CAChB,WAAW,CAAE,IACf,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGH,eAAe,CAACI,IAAI,CAAC,GAAG,CAAC,CAE5C;AACA,GAAIF,aAAa,CAACC,UAAU,CAAC,CAAE,CAC7B,MAAO,CAAAD,aAAa,CAACC,UAAU,CAAC,CAClC,CAEA;AACA,KAAM,CAAAE,IAAI,CAAGL,eAAe,CAAC,CAAC,CAAC,CAC/B,KAAM,CAAAM,SAAS,CAAGN,eAAe,CAACO,KAAK,CAAC,CAAC,CAAC,CAE1C,GAAID,SAAS,CAACtB,MAAM,EAAI,CAAC,CAAE,CACzB;AACA,MAAO,GAAGqB,IAAI,GAAG,CAAE;AACrB,CAEA;AACA,MAAO,CAAApB,cAAc,CAACmB,IAAI,CAAC,KAAK,CAAC,CACnC,CAAC,CAED,MAAO,MAAM,CAAAI,SAAmC,CAAGC,IAAA,EAAsB,IAArB,CAAE1B,KAAK,CAAE2B,KAAM,CAAC,CAAAD,IAAA,CAClE;AACA,KAAM,CAAAE,UAAU,CAAG,GAAI,CAAAC,GAAG,CAAwB,CAAC,CAEnD7B,KAAK,CAAC8B,OAAO,CAAC1B,IAAI,EAAI,CACpB,KAAM,CAAA2B,OAAO,CAAG3B,IAAI,CAAC4B,QAAQ,CAACD,OAAO,CACrC,GAAI,CAACH,UAAU,CAACK,GAAG,CAACF,OAAO,CAAC,CAAE,CAC5BH,UAAU,CAACM,GAAG,CAACH,OAAO,CAAE,EAAE,CAAC,CAC7B,CACAH,UAAU,CAACO,GAAG,CAACJ,OAAO,CAAC,CAAEK,IAAI,CAAChC,IAAI,CAAC,CACrC,CAAC,CAAC,CAEF;AACA,KAAM,CAAAiC,QAAQ,CAAGC,KAAK,CAACC,IAAI,CAACX,UAAU,CAACY,OAAO,CAAC,CAAC,CAAC,CAC9CtB,IAAI,CAAC,CAAAuB,KAAA,CAAAC,KAAA,OAAC,CAACC,CAAC,CAAC,CAAAF,KAAA,IAAE,CAACG,CAAC,CAAC,CAAAF,KAAA,OAAK,CAAAC,CAAC,CAAGC,CAAC,GAAC,CACzBtC,GAAG,CAACuC,KAAA,MAAC,CAACC,aAAa,CAAEC,YAAY,CAAC,CAAAF,KAAA,OAAM,CACvCG,MAAM,CAAEF,aAAa,CACrB9C,KAAK,CAAE+C,YAAY,CAAC7B,IAAI,CAAC,CAACyB,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAACX,QAAQ,CAACiB,IAAI,CAAGL,CAAC,CAACZ,QAAQ,CAACiB,IAAI,CAAC,CACrEC,KAAK,CAAEnD,YAAY,CAACgD,YAAY,CAClC,CAAC,EAAC,CAAC,CAEL;AACA,KAAM,CAAAI,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,WAAW,CAAGf,QAAQ,CACzBlC,MAAM,CAACkD,CAAC,EAAIA,CAAC,CAACH,KAAK,EAAIG,CAAC,CAACH,KAAK,GAAK,GAAG,CAAC,CACvC5C,GAAG,CAAC+C,CAAC,EAAIA,CAAC,CAACH,KAAK,CAAC,CAEpB,KAAM,CAAAI,UAAU,CAAG;AACvB,aAAa3B,KAAK;AAClB,wBAAwByB,WAAW,CAAC/B,IAAI,CAAC,KAAK,CAAC;AAC/C;AACA;AACA,EAAEgB,QAAQ,CAAC/B,GAAG,CAAC+C,CAAC,EAAI,YAAYA,CAAC,CAACL,MAAM,KAAKK,CAAC,CAACH,KAAK,EAAI,GAAG,EAAE,CAAC,CAAC7B,IAAI,CAAC,IAAI,CAAC;AACzE,KAAK,CAACkC,IAAI,CAAC,CAAC,CAER;AACAC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACJ,UAAU,CAAC,CAACK,IAAI,CAAC,IAAM,CACnDC,KAAK,CAAC,+CAA+C,CAAC,CACxD,CAAC,CAAC,CAACC,KAAK,CAAC,IAAM,CACb;AACAD,KAAK,CAACN,UAAU,CAAC,CACnB,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAF,WAAW,CAAGf,QAAQ,CACzBlC,MAAM,CAACkD,CAAC,EAAIA,CAAC,CAACH,KAAK,EAAIG,CAAC,CAACH,KAAK,GAAK,GAAG,CAAC,CACvC5C,GAAG,CAAC+C,CAAC,EAAIA,CAAC,CAACH,KAAK,CAAC,CAEpB,mBACE5E,KAAA,CAACK,cAAc,EAAAmF,QAAA,eACbxF,KAAA,CAACO,WAAW,EAAAiF,QAAA,eACVxF,KAAA,CAACQ,UAAU,EAAAgF,QAAA,EAAC,wBAAY,CAACnC,KAAK,EAAa,CAAC,CAC3CU,QAAQ,CAACpC,MAAM,CAAG,CAAC,eAClBzB,IAAA,CAACQ,YAAY,EAAC+E,OAAO,CAAEZ,YAAa,CAAAW,QAAA,CAAC,8BAErC,CAAc,CACf,EACU,CAAC,CAEbzB,QAAQ,CAACpC,MAAM,GAAK,CAAC,cACpBzB,IAAA,CAACe,UAAU,EAAAuE,QAAA,CAAC,6DAEZ,CAAY,CAAC,cAEbxF,KAAA,CAAAI,SAAA,EAAAoF,QAAA,EAEGV,WAAW,CAACnD,MAAM,CAAG,CAAC,eACrB3B,KAAA,CAACkB,eAAe,EAAAsE,QAAA,eACdtF,IAAA,CAACiB,gBAAgB,EAAAqE,QAAA,CAAC,yCAAuB,CAAkB,CAAC,cAC5DtF,IAAA,CAACmB,iBAAiB,EAAAmE,QAAA,CACfV,WAAW,CAAC9C,GAAG,CAAC,CAAC4C,KAAK,CAAEtC,KAAK,gBAC5BtC,KAAA,CAACH,KAAK,CAACM,QAAQ,EAAAqF,QAAA,eACbtF,IAAA,CAACoB,gBAAgB,EAAAkE,QAAA,CAAEZ,KAAK,CAAmB,CAAC,CAC3CtC,KAAK,CAAGwC,WAAW,CAACnD,MAAM,CAAG,CAAC,eAC7BzB,IAAA,CAACsB,gBAAgB,EAAAgE,QAAA,CAAC,QAAC,CAAkB,CACtC,GAJkBlD,KAKL,CACjB,CAAC,CACe,CAAC,EACL,CAClB,cAGDpC,IAAA,CAACU,SAAS,EAAA4E,QAAA,CACPzB,QAAQ,CAAC/B,GAAG,CAACyB,OAAO,eACnBzD,KAAA,CAACa,WAAW,EAAA2E,QAAA,eACVxF,KAAA,CAACc,aAAa,EAAA0E,QAAA,EAAC,WAAS,CAAC/B,OAAO,CAACiB,MAAM,EAAgB,CAAC,cACxDxE,IAAA,CAACa,WAAW,EAAAyE,QAAA,CACT/B,OAAO,CAACmB,KAAK,EAAI,GAAG,CACV,CAAC,cACd1E,IAAA,CAACc,UAAU,EAAAwE,QAAA,CACR/B,OAAO,CAAC/B,KAAK,CACXG,MAAM,CAACC,IAAI,EAAI,CAACA,IAAI,CAACC,MAAM,CAAC,CAC5BC,GAAG,CAACF,IAAI,EAAI,CACX,GAAI,CAAAG,QAAQ,CAAGH,IAAI,CAACI,IAAI,CACxB,GAAIJ,IAAI,CAACK,UAAU,GAAK,OAAO,CAAEF,QAAQ,EAAI,GAAG,CAChD,GAAIH,IAAI,CAACK,UAAU,GAAK,MAAM,CAAEF,QAAQ,EAAI,GAAG,CAC/C,GAAIH,IAAI,CAACK,UAAU,GAAK,SAAS,CAAEF,QAAQ,EAAI,GAAG,CAClD,MAAO,GAAGA,QAAQ,GAAGH,IAAI,CAAC4D,MAAM,EAAE,CACpC,CAAC,CAAC,CACD3C,IAAI,CAAC,IAAI,CAAC,EAAI,QAAQ,CACf,CAAC,GAhBGU,OAAO,CAACiB,MAiBb,CACd,CAAC,CACO,CAAC,EACZ,CACH,EACa,CAAC,CAErB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}