{"ast": null, "code": "export const INSTRUMENT_TEMPLATES = [{\n  id: 'piano',\n  name: 'Piano',\n  emoji: '🎹',\n  clef: 'treble',\n  range: {\n    lowest: {\n      note: 'A',\n      octave: 0\n    },\n    highest: {\n      note: 'C',\n      octave: 8\n    }\n  },\n  description: 'Instrumento de teclas com ampla extensão e capacidade polifônica',\n  commonKeys: ['C', 'G', 'D', 'A', 'E', 'F', 'Bb', 'Eb', 'Ab'],\n  staffCount: 2 // Clave de sol e fá\n}, {\n  id: 'guitar',\n  name: 'Viol<PERSON>/Guitarra',\n  emoji: '🎸',\n  clef: 'treble',\n  range: {\n    lowest: {\n      note: 'E',\n      octave: 2\n    },\n    highest: {\n      note: 'E',\n      octave: 6\n    }\n  },\n  transposition: -12,\n  // Soa uma oitava abaixo do escrito\n  tuning: ['E2', 'A2', 'D3', 'G3', 'B3', 'E4'],\n  description: 'Instrumento de cordas dedilhadas, versátil para diversos estilos',\n  commonKeys: ['E', 'A', 'D', 'G', 'C', 'F', 'B', 'Em', 'Am', 'Dm'],\n  staffCount: 1\n}, {\n  id: 'violin',\n  name: 'Violino',\n  emoji: '🎻',\n  clef: 'treble',\n  range: {\n    lowest: {\n      note: 'G',\n      octave: 3\n    },\n    highest: {\n      note: 'E',\n      octave: 7\n    }\n  },\n  tuning: ['G3', 'D4', 'A4', 'E5'],\n  description: 'Instrumento de cordas friccionadas, líder da família das cordas',\n  commonKeys: ['G', 'D', 'A', 'E', 'C', 'F', 'Bb'],\n  staffCount: 1\n}, {\n  id: 'flute',\n  name: 'Flauta',\n  emoji: '🪈',\n  clef: 'treble',\n  range: {\n    lowest: {\n      note: 'C',\n      octave: 4\n    },\n    highest: {\n      note: 'D',\n      octave: 7\n    }\n  },\n  description: 'Instrumento de sopro da família das madeiras, som brilhante',\n  commonKeys: ['C', 'G', 'D', 'A', 'F', 'Bb'],\n  staffCount: 1\n}, {\n  id: 'trumpet',\n  name: 'Trompete',\n  emoji: '🎺',\n  clef: 'treble',\n  range: {\n    lowest: {\n      note: 'F#',\n      octave: 3\n    },\n    highest: {\n      note: 'C',\n      octave: 6\n    }\n  },\n  transposition: -2,\n  // Instrumento em Bb\n  description: 'Instrumento de sopro da família dos metais, som brilhante e penetrante',\n  commonKeys: ['Bb', 'F', 'C', 'G', 'D', 'Eb'],\n  staffCount: 1\n}, {\n  id: 'drums',\n  name: 'Bateria',\n  emoji: '🥁',\n  clef: 'treble',\n  // Usa clave de sol mas com notação especial\n  range: {\n    lowest: {\n      note: 'C',\n      octave: 3\n    },\n    highest: {\n      note: 'C',\n      octave: 6\n    }\n  },\n  description: 'Conjunto de instrumentos de percussão para ritmo e acompanhamento',\n  commonKeys: ['C'],\n  // Bateria não tem tonalidade específica\n  staffCount: 1\n}, {\n  id: 'bass',\n  name: 'Baixo',\n  emoji: '🎸',\n  clef: 'bass',\n  range: {\n    lowest: {\n      note: 'E',\n      octave: 1\n    },\n    highest: {\n      note: 'G',\n      octave: 4\n    }\n  },\n  transposition: -12,\n  // Soa uma oitava abaixo\n  tuning: ['E1', 'A1', 'D2', 'G2'],\n  description: 'Instrumento de cordas graves, base harmônica e rítmica',\n  commonKeys: ['E', 'A', 'D', 'G', 'C', 'F', 'B'],\n  staffCount: 1\n}, {\n  id: 'other',\n  name: 'Outro',\n  emoji: '🎵',\n  clef: 'treble',\n  range: {\n    lowest: {\n      note: 'C',\n      octave: 3\n    },\n    highest: {\n      note: 'C',\n      octave: 6\n    }\n  },\n  description: 'Configuração genérica para outros instrumentos',\n  commonKeys: ['C', 'G', 'D', 'A', 'E', 'F', 'Bb'],\n  staffCount: 1\n}];\n\n// Função para obter template por ID\nexport const getInstrumentTemplate = id => {\n  return INSTRUMENT_TEMPLATES.find(template => template.id === id) || INSTRUMENT_TEMPLATES[INSTRUMENT_TEMPLATES.length - 1];\n};\n\n// Função para obter todas as categorias de instrumentos\nexport const getInstrumentCategories = () => {\n  return {\n    keyboard: ['piano'],\n    strings: ['guitar', 'violin', 'bass'],\n    winds: ['flute', 'trumpet'],\n    percussion: ['drums'],\n    other: ['other']\n  };\n};\n\n// Função para verificar se uma nota está no range do instrumento\nexport const isNoteInRange = (note, octave, instrument) => {\n  const template = getInstrumentTemplate(instrument);\n  const {\n    lowest,\n    highest\n  } = template.range;\n\n  // Converter notas para números para comparação\n  const noteToNumber = (noteName, oct) => {\n    const noteValues = {\n      C: 0,\n      D: 2,\n      E: 4,\n      F: 5,\n      G: 7,\n      A: 9,\n      B: 11\n    };\n    return oct * 12 + noteValues[noteName];\n  };\n  const noteNumber = noteToNumber(note, octave);\n  const lowestNumber = noteToNumber(lowest.note, lowest.octave);\n  const highestNumber = noteToNumber(highest.note, highest.octave);\n  return noteNumber >= lowestNumber && noteNumber <= highestNumber;\n};\n\n// Função para sugerir tonalidade baseada no instrumento\nexport const suggestKeyForInstrument = instrument => {\n  const template = getInstrumentTemplate(instrument);\n  return template.commonKeys[0]; // Retorna a tonalidade mais comum\n};\n\n// Função para obter informações de afinação\nexport const getTuningInfo = instrument => {\n  const template = getInstrumentTemplate(instrument);\n  return template.tuning || null;\n};\n\n// Função para aplicar transposição do instrumento\nexport const applyInstrumentTransposition = (note, octave, instrument) => {\n  const template = getInstrumentTemplate(instrument);\n  if (!template.transposition) {\n    return {\n      note,\n      octave\n    };\n  }\n\n  // Lógica de transposição (simplificada)\n  const noteValues = {\n    C: 0,\n    D: 2,\n    E: 4,\n    F: 5,\n    G: 7,\n    A: 9,\n    B: 11\n  };\n  const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];\n  let noteNumber = octave * 12 + noteValues[note];\n  noteNumber += template.transposition;\n  const newOctave = Math.floor(noteNumber / 12);\n  const newNoteIndex = noteNumber % 12;\n  const newNote = noteNames[newNoteIndex];\n  return {\n    note: newNote,\n    octave: newOctave\n  };\n};", "map": {"version": 3, "names": ["INSTRUMENT_TEMPLATES", "id", "name", "emoji", "clef", "range", "lowest", "note", "octave", "highest", "description", "commonKeys", "staffCount", "transposition", "tuning", "getInstrumentTemplate", "find", "template", "length", "getInstrumentCategories", "keyboard", "strings", "winds", "percussion", "other", "isNoteInRange", "instrument", "noteToNumber", "noteName", "oct", "noteValues", "C", "D", "E", "F", "G", "A", "B", "noteNumber", "lowestNumber", "highestNumber", "suggestKeyForInstrument", "getTuningInfo", "applyInstrumentTransposition", "noteNames", "newOctave", "Math", "floor", "newNoteIndex", "newNote"], "sources": ["D:/Dev/partitura_digital/src/utils/instrumentTemplates.ts"], "sourcesContent": ["import { InstrumentType, ClefType, NoteName } from '../types/music';\n\nexport interface InstrumentTemplate {\n  id: InstrumentType;\n  name: string;\n  emoji: string;\n  clef: ClefType;\n  range: {\n    lowest: { note: NoteName; octave: number };\n    highest: { note: NoteName; octave: number };\n  };\n  transposition?: number; // Semitons para transposição\n  tuning?: string[]; // Para instrumentos de corda\n  description: string;\n  commonKeys: string[]; // Tonalidades mais comuns\n  staffCount: number; // Número de pautas (1 para maioria, 2 para piano)\n}\n\nexport const INSTRUMENT_TEMPLATES: InstrumentTemplate[] = [\n  {\n    id: 'piano',\n    name: 'Piano',\n    emoji: '🎹',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'A', octave: 0 },\n      highest: { note: 'C', octave: 8 }\n    },\n    description: 'Instrumento de teclas com ampla extensão e capacidade polifônica',\n    commonKeys: ['C', 'G', 'D', 'A', 'E', 'F', 'Bb', 'Eb', 'Ab'],\n    staffCount: 2 // Clave de sol e fá\n  },\n  {\n    id: 'guitar',\n    name: 'Violão/Guitarra',\n    emoji: '🎸',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'E', octave: 2 },\n      highest: { note: 'E', octave: 6 }\n    },\n    transposition: -12, // Soa uma oitava abaixo do escrito\n    tuning: ['E2', 'A2', 'D3', 'G3', 'B3', 'E4'],\n    description: 'Instrumento de cordas dedilhadas, versátil para diversos estilos',\n    commonKeys: ['E', 'A', 'D', 'G', 'C', 'F', 'B', 'Em', 'Am', 'Dm'],\n    staffCount: 1\n  },\n  {\n    id: 'violin',\n    name: 'Violino',\n    emoji: '🎻',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'G', octave: 3 },\n      highest: { note: 'E', octave: 7 }\n    },\n    tuning: ['G3', 'D4', 'A4', 'E5'],\n    description: 'Instrumento de cordas friccionadas, líder da família das cordas',\n    commonKeys: ['G', 'D', 'A', 'E', 'C', 'F', 'Bb'],\n    staffCount: 1\n  },\n  {\n    id: 'flute',\n    name: 'Flauta',\n    emoji: '🪈',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'C', octave: 4 },\n      highest: { note: 'D', octave: 7 }\n    },\n    description: 'Instrumento de sopro da família das madeiras, som brilhante',\n    commonKeys: ['C', 'G', 'D', 'A', 'F', 'Bb'],\n    staffCount: 1\n  },\n  {\n    id: 'trumpet',\n    name: 'Trompete',\n    emoji: '🎺',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'F#', octave: 3 },\n      highest: { note: 'C', octave: 6 }\n    },\n    transposition: -2, // Instrumento em Bb\n    description: 'Instrumento de sopro da família dos metais, som brilhante e penetrante',\n    commonKeys: ['Bb', 'F', 'C', 'G', 'D', 'Eb'],\n    staffCount: 1\n  },\n  {\n    id: 'drums',\n    name: 'Bateria',\n    emoji: '🥁',\n    clef: 'treble', // Usa clave de sol mas com notação especial\n    range: {\n      lowest: { note: 'C', octave: 3 },\n      highest: { note: 'C', octave: 6 }\n    },\n    description: 'Conjunto de instrumentos de percussão para ritmo e acompanhamento',\n    commonKeys: ['C'], // Bateria não tem tonalidade específica\n    staffCount: 1\n  },\n  {\n    id: 'bass',\n    name: 'Baixo',\n    emoji: '🎸',\n    clef: 'bass',\n    range: {\n      lowest: { note: 'E', octave: 1 },\n      highest: { note: 'G', octave: 4 }\n    },\n    transposition: -12, // Soa uma oitava abaixo\n    tuning: ['E1', 'A1', 'D2', 'G2'],\n    description: 'Instrumento de cordas graves, base harmônica e rítmica',\n    commonKeys: ['E', 'A', 'D', 'G', 'C', 'F', 'B'],\n    staffCount: 1\n  },\n  {\n    id: 'other',\n    name: 'Outro',\n    emoji: '🎵',\n    clef: 'treble',\n    range: {\n      lowest: { note: 'C', octave: 3 },\n      highest: { note: 'C', octave: 6 }\n    },\n    description: 'Configuração genérica para outros instrumentos',\n    commonKeys: ['C', 'G', 'D', 'A', 'E', 'F', 'Bb'],\n    staffCount: 1\n  }\n];\n\n// Função para obter template por ID\nexport const getInstrumentTemplate = (id: InstrumentType): InstrumentTemplate => {\n  return INSTRUMENT_TEMPLATES.find(template => template.id === id) || INSTRUMENT_TEMPLATES[INSTRUMENT_TEMPLATES.length - 1];\n};\n\n// Função para obter todas as categorias de instrumentos\nexport const getInstrumentCategories = () => {\n  return {\n    keyboard: ['piano'],\n    strings: ['guitar', 'violin', 'bass'],\n    winds: ['flute', 'trumpet'],\n    percussion: ['drums'],\n    other: ['other']\n  };\n};\n\n// Função para verificar se uma nota está no range do instrumento\nexport const isNoteInRange = (\n  note: NoteName, \n  octave: number, \n  instrument: InstrumentType\n): boolean => {\n  const template = getInstrumentTemplate(instrument);\n  const { lowest, highest } = template.range;\n  \n  // Converter notas para números para comparação\n  const noteToNumber = (noteName: NoteName, oct: number): number => {\n    const noteValues = { C: 0, D: 2, E: 4, F: 5, G: 7, A: 9, B: 11 };\n    return oct * 12 + noteValues[noteName];\n  };\n  \n  const noteNumber = noteToNumber(note, octave);\n  const lowestNumber = noteToNumber(lowest.note, lowest.octave);\n  const highestNumber = noteToNumber(highest.note, highest.octave);\n  \n  return noteNumber >= lowestNumber && noteNumber <= highestNumber;\n};\n\n// Função para sugerir tonalidade baseada no instrumento\nexport const suggestKeyForInstrument = (instrument: InstrumentType): string => {\n  const template = getInstrumentTemplate(instrument);\n  return template.commonKeys[0]; // Retorna a tonalidade mais comum\n};\n\n// Função para obter informações de afinação\nexport const getTuningInfo = (instrument: InstrumentType): string[] | null => {\n  const template = getInstrumentTemplate(instrument);\n  return template.tuning || null;\n};\n\n// Função para aplicar transposição do instrumento\nexport const applyInstrumentTransposition = (\n  note: NoteName, \n  octave: number, \n  instrument: InstrumentType\n): { note: NoteName; octave: number } => {\n  const template = getInstrumentTemplate(instrument);\n  \n  if (!template.transposition) {\n    return { note, octave };\n  }\n  \n  // Lógica de transposição (simplificada)\n  const noteValues = { C: 0, D: 2, E: 4, F: 5, G: 7, A: 9, B: 11 };\n  const noteNames: NoteName[] = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];\n  \n  let noteNumber = octave * 12 + noteValues[note];\n  noteNumber += template.transposition;\n  \n  const newOctave = Math.floor(noteNumber / 12);\n  const newNoteIndex = noteNumber % 12;\n  const newNote = noteNames[newNoteIndex] as NoteName;\n  \n  return { note: newNote, octave: newOctave };\n};\n"], "mappings": "AAkBA,OAAO,MAAMA,oBAA0C,GAAG,CACxD;EACEC,EAAE,EAAE,OAAO;EACXC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE;IACLC,MAAM,EAAE;MAAEC,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE,CAAC;IAChCC,OAAO,EAAE;MAAEF,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE;EAClC,CAAC;EACDE,WAAW,EAAE,kEAAkE;EAC/EC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC5DC,UAAU,EAAE,CAAC,CAAC;AAChB,CAAC,EACD;EACEX,EAAE,EAAE,QAAQ;EACZC,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE;IACLC,MAAM,EAAE;MAAEC,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE,CAAC;IAChCC,OAAO,EAAE;MAAEF,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE;EAClC,CAAC;EACDK,aAAa,EAAE,CAAC,EAAE;EAAE;EACpBC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC5CJ,WAAW,EAAE,kEAAkE;EAC/EC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjEC,UAAU,EAAE;AACd,CAAC,EACD;EACEX,EAAE,EAAE,QAAQ;EACZC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE;IACLC,MAAM,EAAE;MAAEC,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE,CAAC;IAChCC,OAAO,EAAE;MAAEF,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE;EAClC,CAAC;EACDM,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAChCJ,WAAW,EAAE,iEAAiE;EAC9EC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAChDC,UAAU,EAAE;AACd,CAAC,EACD;EACEX,EAAE,EAAE,OAAO;EACXC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE;IACLC,MAAM,EAAE;MAAEC,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE,CAAC;IAChCC,OAAO,EAAE;MAAEF,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE;EAClC,CAAC;EACDE,WAAW,EAAE,6DAA6D;EAC1EC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAC3CC,UAAU,EAAE;AACd,CAAC,EACD;EACEX,EAAE,EAAE,SAAS;EACbC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE;IACLC,MAAM,EAAE;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAE,CAAC;IACjCC,OAAO,EAAE;MAAEF,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE;EAClC,CAAC;EACDK,aAAa,EAAE,CAAC,CAAC;EAAE;EACnBH,WAAW,EAAE,wEAAwE;EACrFC,UAAU,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAC5CC,UAAU,EAAE;AACd,CAAC,EACD;EACEX,EAAE,EAAE,OAAO;EACXC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,QAAQ;EAAE;EAChBC,KAAK,EAAE;IACLC,MAAM,EAAE;MAAEC,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE,CAAC;IAChCC,OAAO,EAAE;MAAEF,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE;EAClC,CAAC;EACDE,WAAW,EAAE,mEAAmE;EAChFC,UAAU,EAAE,CAAC,GAAG,CAAC;EAAE;EACnBC,UAAU,EAAE;AACd,CAAC,EACD;EACEX,EAAE,EAAE,MAAM;EACVC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE;IACLC,MAAM,EAAE;MAAEC,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE,CAAC;IAChCC,OAAO,EAAE;MAAEF,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE;EAClC,CAAC;EACDK,aAAa,EAAE,CAAC,EAAE;EAAE;EACpBC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAChCJ,WAAW,EAAE,wDAAwD;EACrEC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/CC,UAAU,EAAE;AACd,CAAC,EACD;EACEX,EAAE,EAAE,OAAO;EACXC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE;IACLC,MAAM,EAAE;MAAEC,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE,CAAC;IAChCC,OAAO,EAAE;MAAEF,IAAI,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAE;EAClC,CAAC;EACDE,WAAW,EAAE,gDAAgD;EAC7DC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAChDC,UAAU,EAAE;AACd,CAAC,CACF;;AAED;AACA,OAAO,MAAMG,qBAAqB,GAAId,EAAkB,IAAyB;EAC/E,OAAOD,oBAAoB,CAACgB,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAAChB,EAAE,KAAKA,EAAE,CAAC,IAAID,oBAAoB,CAACA,oBAAoB,CAACkB,MAAM,GAAG,CAAC,CAAC;AAC3H,CAAC;;AAED;AACA,OAAO,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EAC3C,OAAO;IACLC,QAAQ,EAAE,CAAC,OAAO,CAAC;IACnBC,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC;IACrCC,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;IAC3BC,UAAU,EAAE,CAAC,OAAO,CAAC;IACrBC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;AACH,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAGA,CAC3BlB,IAAc,EACdC,MAAc,EACdkB,UAA0B,KACd;EACZ,MAAMT,QAAQ,GAAGF,qBAAqB,CAACW,UAAU,CAAC;EAClD,MAAM;IAAEpB,MAAM;IAAEG;EAAQ,CAAC,GAAGQ,QAAQ,CAACZ,KAAK;;EAE1C;EACA,MAAMsB,YAAY,GAAGA,CAACC,QAAkB,EAAEC,GAAW,KAAa;IAChE,MAAMC,UAAU,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAC;IAChE,OAAOR,GAAG,GAAG,EAAE,GAAGC,UAAU,CAACF,QAAQ,CAAC;EACxC,CAAC;EAED,MAAMU,UAAU,GAAGX,YAAY,CAACpB,IAAI,EAAEC,MAAM,CAAC;EAC7C,MAAM+B,YAAY,GAAGZ,YAAY,CAACrB,MAAM,CAACC,IAAI,EAAED,MAAM,CAACE,MAAM,CAAC;EAC7D,MAAMgC,aAAa,GAAGb,YAAY,CAAClB,OAAO,CAACF,IAAI,EAAEE,OAAO,CAACD,MAAM,CAAC;EAEhE,OAAO8B,UAAU,IAAIC,YAAY,IAAID,UAAU,IAAIE,aAAa;AAClE,CAAC;;AAED;AACA,OAAO,MAAMC,uBAAuB,GAAIf,UAA0B,IAAa;EAC7E,MAAMT,QAAQ,GAAGF,qBAAqB,CAACW,UAAU,CAAC;EAClD,OAAOT,QAAQ,CAACN,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC;;AAED;AACA,OAAO,MAAM+B,aAAa,GAAIhB,UAA0B,IAAsB;EAC5E,MAAMT,QAAQ,GAAGF,qBAAqB,CAACW,UAAU,CAAC;EAClD,OAAOT,QAAQ,CAACH,MAAM,IAAI,IAAI;AAChC,CAAC;;AAED;AACA,OAAO,MAAM6B,4BAA4B,GAAGA,CAC1CpC,IAAc,EACdC,MAAc,EACdkB,UAA0B,KACa;EACvC,MAAMT,QAAQ,GAAGF,qBAAqB,CAACW,UAAU,CAAC;EAElD,IAAI,CAACT,QAAQ,CAACJ,aAAa,EAAE;IAC3B,OAAO;MAAEN,IAAI;MAAEC;IAAO,CAAC;EACzB;;EAEA;EACA,MAAMsB,UAAU,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAG,CAAC;EAChE,MAAMO,SAAqB,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;EAE/F,IAAIN,UAAU,GAAG9B,MAAM,GAAG,EAAE,GAAGsB,UAAU,CAACvB,IAAI,CAAC;EAC/C+B,UAAU,IAAIrB,QAAQ,CAACJ,aAAa;EAEpC,MAAMgC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACT,UAAU,GAAG,EAAE,CAAC;EAC7C,MAAMU,YAAY,GAAGV,UAAU,GAAG,EAAE;EACpC,MAAMW,OAAO,GAAGL,SAAS,CAACI,YAAY,CAAa;EAEnD,OAAO;IAAEzC,IAAI,EAAE0C,OAAO;IAAEzC,MAAM,EAAEqC;EAAU,CAAC;AAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}