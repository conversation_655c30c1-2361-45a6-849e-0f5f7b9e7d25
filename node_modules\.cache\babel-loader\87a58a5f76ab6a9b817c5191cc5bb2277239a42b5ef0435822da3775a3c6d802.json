{"ast": null, "code": "import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, {\n  blocked,\n  upgrade,\n  blocking,\n  terminated\n} = {}) {\n  const request = indexedDB.open(name, version);\n  const openPromise = wrap(request);\n  if (upgrade) {\n    request.addEventListener('upgradeneeded', event => {\n      upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n    });\n  }\n  if (blocked) {\n    request.addEventListener('blocked', event => blocked(\n    // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n    event.oldVersion, event.newVersion, event));\n  }\n  openPromise.then(db => {\n    if (terminated) db.addEventListener('close', () => terminated());\n    if (blocking) {\n      db.addEventListener('versionchange', event => blocking(event.oldVersion, event.newVersion, event));\n    }\n  }).catch(() => {});\n  return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, {\n  blocked\n} = {}) {\n  const request = indexedDB.deleteDatabase(name);\n  if (blocked) {\n    request.addEventListener('blocked', event => blocked(\n    // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n    event.oldVersion, event));\n  }\n  return wrap(request).then(() => undefined);\n}\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n  if (!(target instanceof IDBDatabase && !(prop in target) && typeof prop === 'string')) {\n    return;\n  }\n  if (cachedMethods.get(prop)) return cachedMethods.get(prop);\n  const targetFuncName = prop.replace(/FromIndex$/, '');\n  const useIndex = prop !== targetFuncName;\n  const isWrite = writeMethods.includes(targetFuncName);\n  if (\n  // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n  !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) || !(isWrite || readMethods.includes(targetFuncName))) {\n    return;\n  }\n  const method = async function (storeName, ...args) {\n    // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n    const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n    let target = tx.store;\n    if (useIndex) target = target.index(args.shift());\n    // Must reject if op rejects.\n    // If it's a write operation, must reject if tx.done rejects.\n    // Must reject with op rejection first.\n    // Must resolve with op value.\n    // Must handle both promises (no unhandled rejections)\n    return (await Promise.all([target[targetFuncName](...args), isWrite && tx.done]))[0];\n  };\n  cachedMethods.set(prop, method);\n  return method;\n}\nreplaceTraps(oldTraps => ({\n  ...oldTraps,\n  get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n  has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop)\n}));\nexport { deleteDB, openDB };", "map": {"version": 3, "names": ["w", "wrap", "r", "replaceTraps", "u", "unwrap", "openDB", "name", "version", "blocked", "upgrade", "blocking", "terminated", "request", "indexedDB", "open", "openPromise", "addEventListener", "event", "result", "oldVersion", "newVersion", "transaction", "then", "db", "catch", "deleteDB", "deleteDatabase", "undefined", "readMethods", "writeMethods", "cachedMethods", "Map", "getMethod", "target", "prop", "IDBDatabase", "get", "targetFuncName", "replace", "useIndex", "isWrite", "includes", "IDBIndex", "IDBObjectStore", "prototype", "method", "storeName", "args", "tx", "store", "index", "shift", "Promise", "all", "done", "set", "oldTraps", "receiver", "has"], "sources": ["D:/Dev/partitura_digital/node_modules/idb/build/index.js"], "sourcesContent": ["import { w as wrap, r as replaceTraps } from './wrap-idb-value.js';\nexport { u as unwrap, w as wrap } from './wrap-idb-value.js';\n\n/**\n * Open a database.\n *\n * @param name Name of the database.\n * @param version Schema version.\n * @param callbacks Additional callbacks.\n */\nfunction openDB(name, version, { blocked, upgrade, blocking, terminated } = {}) {\n    const request = indexedDB.open(name, version);\n    const openPromise = wrap(request);\n    if (upgrade) {\n        request.addEventListener('upgradeneeded', (event) => {\n            upgrade(wrap(request.result), event.oldVersion, event.newVersion, wrap(request.transaction), event);\n        });\n    }\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event.newVersion, event));\n    }\n    openPromise\n        .then((db) => {\n        if (terminated)\n            db.addEventListener('close', () => terminated());\n        if (blocking) {\n            db.addEventListener('versionchange', (event) => blocking(event.oldVersion, event.newVersion, event));\n        }\n    })\n        .catch(() => { });\n    return openPromise;\n}\n/**\n * Delete a database.\n *\n * @param name Name of the database.\n */\nfunction deleteDB(name, { blocked } = {}) {\n    const request = indexedDB.deleteDatabase(name);\n    if (blocked) {\n        request.addEventListener('blocked', (event) => blocked(\n        // Casting due to https://github.com/microsoft/TypeScript-DOM-lib-generator/pull/1405\n        event.oldVersion, event));\n    }\n    return wrap(request).then(() => undefined);\n}\n\nconst readMethods = ['get', 'getKey', 'getAll', 'getAllKeys', 'count'];\nconst writeMethods = ['put', 'add', 'delete', 'clear'];\nconst cachedMethods = new Map();\nfunction getMethod(target, prop) {\n    if (!(target instanceof IDBDatabase &&\n        !(prop in target) &&\n        typeof prop === 'string')) {\n        return;\n    }\n    if (cachedMethods.get(prop))\n        return cachedMethods.get(prop);\n    const targetFuncName = prop.replace(/FromIndex$/, '');\n    const useIndex = prop !== targetFuncName;\n    const isWrite = writeMethods.includes(targetFuncName);\n    if (\n    // Bail if the target doesn't exist on the target. Eg, getAll isn't in Edge.\n    !(targetFuncName in (useIndex ? IDBIndex : IDBObjectStore).prototype) ||\n        !(isWrite || readMethods.includes(targetFuncName))) {\n        return;\n    }\n    const method = async function (storeName, ...args) {\n        // isWrite ? 'readwrite' : undefined gzipps better, but fails in Edge :(\n        const tx = this.transaction(storeName, isWrite ? 'readwrite' : 'readonly');\n        let target = tx.store;\n        if (useIndex)\n            target = target.index(args.shift());\n        // Must reject if op rejects.\n        // If it's a write operation, must reject if tx.done rejects.\n        // Must reject with op rejection first.\n        // Must resolve with op value.\n        // Must handle both promises (no unhandled rejections)\n        return (await Promise.all([\n            target[targetFuncName](...args),\n            isWrite && tx.done,\n        ]))[0];\n    };\n    cachedMethods.set(prop, method);\n    return method;\n}\nreplaceTraps((oldTraps) => ({\n    ...oldTraps,\n    get: (target, prop, receiver) => getMethod(target, prop) || oldTraps.get(target, prop, receiver),\n    has: (target, prop) => !!getMethod(target, prop) || oldTraps.has(target, prop),\n}));\n\nexport { deleteDB, openDB };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,YAAY,QAAQ,qBAAqB;AAClE,SAASC,CAAC,IAAIC,MAAM,EAAEL,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,MAAMA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAAEC,OAAO;EAAEC,OAAO;EAAEC,QAAQ;EAAEC;AAAW,CAAC,GAAG,CAAC,CAAC,EAAE;EAC5E,MAAMC,OAAO,GAAGC,SAAS,CAACC,IAAI,CAACR,IAAI,EAAEC,OAAO,CAAC;EAC7C,MAAMQ,WAAW,GAAGf,IAAI,CAACY,OAAO,CAAC;EACjC,IAAIH,OAAO,EAAE;IACTG,OAAO,CAACI,gBAAgB,CAAC,eAAe,EAAGC,KAAK,IAAK;MACjDR,OAAO,CAACT,IAAI,CAACY,OAAO,CAACM,MAAM,CAAC,EAAED,KAAK,CAACE,UAAU,EAAEF,KAAK,CAACG,UAAU,EAAEpB,IAAI,CAACY,OAAO,CAACS,WAAW,CAAC,EAAEJ,KAAK,CAAC;IACvG,CAAC,CAAC;EACN;EACA,IAAIT,OAAO,EAAE;IACTI,OAAO,CAACI,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAKT,OAAO;IACtD;IACAS,KAAK,CAACE,UAAU,EAAEF,KAAK,CAACG,UAAU,EAAEH,KAAK,CAAC,CAAC;EAC/C;EACAF,WAAW,CACNO,IAAI,CAAEC,EAAE,IAAK;IACd,IAAIZ,UAAU,EACVY,EAAE,CAACP,gBAAgB,CAAC,OAAO,EAAE,MAAML,UAAU,CAAC,CAAC,CAAC;IACpD,IAAID,QAAQ,EAAE;MACVa,EAAE,CAACP,gBAAgB,CAAC,eAAe,EAAGC,KAAK,IAAKP,QAAQ,CAACO,KAAK,CAACE,UAAU,EAAEF,KAAK,CAACG,UAAU,EAAEH,KAAK,CAAC,CAAC;IACxG;EACJ,CAAC,CAAC,CACGO,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;EACrB,OAAOT,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,QAAQA,CAACnB,IAAI,EAAE;EAAEE;AAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;EACtC,MAAMI,OAAO,GAAGC,SAAS,CAACa,cAAc,CAACpB,IAAI,CAAC;EAC9C,IAAIE,OAAO,EAAE;IACTI,OAAO,CAACI,gBAAgB,CAAC,SAAS,EAAGC,KAAK,IAAKT,OAAO;IACtD;IACAS,KAAK,CAACE,UAAU,EAAEF,KAAK,CAAC,CAAC;EAC7B;EACA,OAAOjB,IAAI,CAACY,OAAO,CAAC,CAACU,IAAI,CAAC,MAAMK,SAAS,CAAC;AAC9C;AAEA,MAAMC,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC;AACtE,MAAMC,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;AACtD,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC/B,SAASC,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC7B,IAAI,EAAED,MAAM,YAAYE,WAAW,IAC/B,EAAED,IAAI,IAAID,MAAM,CAAC,IACjB,OAAOC,IAAI,KAAK,QAAQ,CAAC,EAAE;IAC3B;EACJ;EACA,IAAIJ,aAAa,CAACM,GAAG,CAACF,IAAI,CAAC,EACvB,OAAOJ,aAAa,CAACM,GAAG,CAACF,IAAI,CAAC;EAClC,MAAMG,cAAc,GAAGH,IAAI,CAACI,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;EACrD,MAAMC,QAAQ,GAAGL,IAAI,KAAKG,cAAc;EACxC,MAAMG,OAAO,GAAGX,YAAY,CAACY,QAAQ,CAACJ,cAAc,CAAC;EACrD;EACA;EACA,EAAEA,cAAc,IAAI,CAACE,QAAQ,GAAGG,QAAQ,GAAGC,cAAc,EAAEC,SAAS,CAAC,IACjE,EAAEJ,OAAO,IAAIZ,WAAW,CAACa,QAAQ,CAACJ,cAAc,CAAC,CAAC,EAAE;IACpD;EACJ;EACA,MAAMQ,MAAM,GAAG,eAAAA,CAAgBC,SAAS,EAAE,GAAGC,IAAI,EAAE;IAC/C;IACA,MAAMC,EAAE,GAAG,IAAI,CAAC3B,WAAW,CAACyB,SAAS,EAAEN,OAAO,GAAG,WAAW,GAAG,UAAU,CAAC;IAC1E,IAAIP,MAAM,GAAGe,EAAE,CAACC,KAAK;IACrB,IAAIV,QAAQ,EACRN,MAAM,GAAGA,MAAM,CAACiB,KAAK,CAACH,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC;IACvC;IACA;IACA;IACA;IACA;IACA,OAAO,CAAC,MAAMC,OAAO,CAACC,GAAG,CAAC,CACtBpB,MAAM,CAACI,cAAc,CAAC,CAAC,GAAGU,IAAI,CAAC,EAC/BP,OAAO,IAAIQ,EAAE,CAACM,IAAI,CACrB,CAAC,EAAE,CAAC,CAAC;EACV,CAAC;EACDxB,aAAa,CAACyB,GAAG,CAACrB,IAAI,EAAEW,MAAM,CAAC;EAC/B,OAAOA,MAAM;AACjB;AACA3C,YAAY,CAAEsD,QAAQ,KAAM;EACxB,GAAGA,QAAQ;EACXpB,GAAG,EAAEA,CAACH,MAAM,EAAEC,IAAI,EAAEuB,QAAQ,KAAKzB,SAAS,CAACC,MAAM,EAAEC,IAAI,CAAC,IAAIsB,QAAQ,CAACpB,GAAG,CAACH,MAAM,EAAEC,IAAI,EAAEuB,QAAQ,CAAC;EAChGC,GAAG,EAAEA,CAACzB,MAAM,EAAEC,IAAI,KAAK,CAAC,CAACF,SAAS,CAACC,MAAM,EAAEC,IAAI,CAAC,IAAIsB,QAAQ,CAACE,GAAG,CAACzB,MAAM,EAAEC,IAAI;AACjF,CAAC,CAAC,CAAC;AAEH,SAAST,QAAQ,EAAEpB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}