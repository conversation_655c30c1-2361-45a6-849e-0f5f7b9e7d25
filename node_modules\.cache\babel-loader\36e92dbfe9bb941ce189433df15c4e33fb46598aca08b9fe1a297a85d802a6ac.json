{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\FirebaseTest.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { auth, db } from '../services/firebase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const FirebaseTest = () => {\n  _s();\n  const [status, setStatus] = useState('Testando conexão...');\n  useEffect(() => {\n    const testFirebase = async () => {\n      try {\n        // Testar se o Firebase foi inicializado corretamente\n        console.log('Auth instance:', auth);\n        console.log('Firestore instance:', db);\n\n        // Verificar se o auth está funcionando\n        if (auth) {\n          setStatus('✅ Firebase configurado corretamente!');\n        } else {\n          setStatus('❌ Erro na configuração do Firebase Auth');\n        }\n      } catch (error) {\n        console.error('Erro ao testar Firebase:', error);\n        setStatus(`❌ Erro: ${error}`);\n      }\n    };\n    testFirebase();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      margin: '20px',\n      border: '1px solid #ccc',\n      borderRadius: '8px',\n      backgroundColor: '#f9f9f9'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"\\uD83D\\uDD25 Teste de Conex\\xE3o Firebase\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Status:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 10\n      }, this), \" \", status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Project ID:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 10\n      }, this), \" \", auth.app.options.projectId]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Auth Domain:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 10\n      }, this), \" \", auth.app.options.authDomain]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(FirebaseTest, \"+6rUOJvLfK3dSwuIdjlh6yelbb4=\");\n_c = FirebaseTest;\nvar _c;\n$RefreshReg$(_c, \"FirebaseTest\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "auth", "db", "jsxDEV", "_jsxDEV", "FirebaseTest", "_s", "status", "setStatus", "testFirebase", "console", "log", "error", "style", "padding", "margin", "border", "borderRadius", "backgroundColor", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "app", "options", "projectId", "authDomain", "_c", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/FirebaseTest.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { auth, db } from '../services/firebase';\nimport { connectAuthEmulator } from 'firebase/auth';\nimport { connectFirestoreEmulator } from 'firebase/firestore';\n\nexport const FirebaseTest: React.FC = () => {\n  const [status, setStatus] = useState<string>('Testando conexão...');\n\n  useEffect(() => {\n    const testFirebase = async () => {\n      try {\n        // Testar se o Firebase foi inicializado corretamente\n        console.log('Auth instance:', auth);\n        console.log('Firestore instance:', db);\n        \n        // Verificar se o auth está funcionando\n        if (auth) {\n          setStatus('✅ Firebase configurado corretamente!');\n        } else {\n          setStatus('❌ Erro na configuração do Firebase Auth');\n        }\n      } catch (error) {\n        console.error('Erro ao testar Firebase:', error);\n        setStatus(`❌ Erro: ${error}`);\n      }\n    };\n\n    testFirebase();\n  }, []);\n\n  return (\n    <div style={{ \n      padding: '20px', \n      margin: '20px', \n      border: '1px solid #ccc', \n      borderRadius: '8px',\n      backgroundColor: '#f9f9f9'\n    }}>\n      <h3>🔥 Teste de Conexão Firebase</h3>\n      <p><strong>Status:</strong> {status}</p>\n      <p><strong>Project ID:</strong> {auth.app.options.projectId}</p>\n      <p><strong>Auth Domain:</strong> {auth.app.options.authDomain}</p>\n    </div>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,EAAE,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAIhD,OAAO,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAS,qBAAqB,CAAC;EAEnED,SAAS,CAAC,MAAM;IACd,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF;QACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEV,IAAI,CAAC;QACnCS,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAET,EAAE,CAAC;;QAEtC;QACA,IAAID,IAAI,EAAE;UACRO,SAAS,CAAC,sCAAsC,CAAC;QACnD,CAAC,MAAM;UACLA,SAAS,CAAC,yCAAyC,CAAC;QACtD;MACF,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDJ,SAAS,CAAC,WAAWI,KAAK,EAAE,CAAC;MAC/B;IACF,CAAC;IAEDH,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEL,OAAA;IAAKS,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE,gBAAgB;MACxBC,YAAY,EAAE,KAAK;MACnBC,eAAe,EAAE;IACnB,CAAE;IAAAC,QAAA,gBACAf,OAAA;MAAAe,QAAA,EAAI;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrCnB,OAAA;MAAAe,QAAA,gBAAGf,OAAA;QAAAe,QAAA,EAAQ;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAAChB,MAAM;IAAA;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxCnB,OAAA;MAAAe,QAAA,gBAAGf,OAAA;QAAAe,QAAA,EAAQ;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACtB,IAAI,CAACuB,GAAG,CAACC,OAAO,CAACC,SAAS;IAAA;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChEnB,OAAA;MAAAe,QAAA,gBAAGf,OAAA;QAAAe,QAAA,EAAQ;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,KAAC,EAACtB,IAAI,CAACuB,GAAG,CAACC,OAAO,CAACE,UAAU;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/D,CAAC;AAEV,CAAC;AAACjB,EAAA,CAvCWD,YAAsB;AAAAuB,EAAA,GAAtBvB,YAAsB;AAAA,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}