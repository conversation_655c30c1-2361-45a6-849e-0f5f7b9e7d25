import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc, 
  query, 
  where, 
  orderBy, 
  Timestamp 
} from 'firebase/firestore';
import { db } from './firebase';
import { Score } from '../types/music';

const SCORES_COLLECTION = 'scores';

// Converter Score para formato do Firestore
const scoreToFirestore = (score: Omit<Score, 'id'>) => ({
  ...score,
  createdAt: Timestamp.fromDate(score.createdAt),
  updatedAt: Timestamp.fromDate(score.updatedAt)
});

// Converter documento do Firestore para Score
const firestoreToScore = (doc: any): Score => ({
  id: doc.id,
  ...doc.data(),
  createdAt: doc.data().createdAt.toDate(),
  updatedAt: doc.data().updatedAt.toDate()
});

export class ScoreService {
  // Criar nova partitura
  static async createScore(scoreData: Omit<Score, 'id' | 'createdAt' | 'updatedAt'>, userId: string): Promise<string> {
    const now = new Date();
    const score: Omit<Score, 'id'> = {
      ...scoreData,
      userId,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(collection(db, SCORES_COLLECTION), scoreToFirestore(score));
    return docRef.id;
  }

  // Atualizar partitura existente
  static async updateScore(scoreId: string, updates: Partial<Omit<Score, 'id' | 'createdAt' | 'userId'>>): Promise<void> {
    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);
    const updateData = {
      ...updates,
      updatedAt: Timestamp.fromDate(new Date())
    };
    
    await updateDoc(scoreRef, updateData);
  }

  // Deletar partitura
  static async deleteScore(scoreId: string): Promise<void> {
    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);
    await deleteDoc(scoreRef);
  }

  // Buscar partitura por ID
  static async getScore(scoreId: string): Promise<Score | null> {
    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);
    const scoreSnap = await getDoc(scoreRef);
    
    if (scoreSnap.exists()) {
      return firestoreToScore(scoreSnap);
    }
    
    return null;
  }

  // Buscar todas as partituras de um usuário
  static async getUserScores(userId: string): Promise<Score[]> {
    const q = query(
      collection(db, SCORES_COLLECTION),
      where('userId', '==', userId),
      orderBy('updatedAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => firestoreToScore(doc));
  }

  // Buscar partituras por título (busca parcial)
  static async searchScoresByTitle(userId: string, searchTerm: string): Promise<Score[]> {
    const q = query(
      collection(db, SCORES_COLLECTION),
      where('userId', '==', userId),
      orderBy('title')
    );
    
    const querySnapshot = await getDocs(q);
    const scores = querySnapshot.docs.map(doc => firestoreToScore(doc));
    
    // Filtrar localmente por título (Firestore não suporta busca parcial nativa)
    return scores.filter(score => 
      score.title.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }

  // Duplicar partitura
  static async duplicateScore(scoreId: string, newTitle: string): Promise<string> {
    const originalScore = await this.getScore(scoreId);
    if (!originalScore) {
      throw new Error('Partitura não encontrada');
    }

    const duplicatedScore: Omit<Score, 'id' | 'createdAt' | 'updatedAt'> = {
      ...originalScore,
      title: newTitle
    };

    return await this.createScore(duplicatedScore, originalScore.userId);
  }
}
