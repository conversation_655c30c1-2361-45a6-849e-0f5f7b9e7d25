import React, { useState } from 'react';
import styled from 'styled-components';
import { Lyrics, MusicalNote } from '../../types/music';
import { v4 as uuidv4 } from 'uuid';

const LyricsContainer = styled.div`
  background: rgba(255,255,255,0.95);
  border-radius: 16px;
  padding: 2rem;
  margin: 1rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
`;

const LyricsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const LyricsTitle = styled.h3`
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
`;

const AddLyricButton = styled.button`
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }
`;

const LyricsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
`;

const LyricCard = styled.div`
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1rem;
  border: 2px solid #e9ecef;
  position: relative;
`;

const LyricPosition = styled.div`
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.5rem;
  font-weight: 600;
`;

const LyricText = styled.input`
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  box-sizing: border-box;
  
  &:focus {
    outline: none;
    border-color: #667eea;
  }
`;

const LyricActions = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const ActionButton = styled.button<{ variant?: 'danger' }>`
  padding: 0.25rem 0.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.variant === 'danger' ? `
    background: #e74c3c;
    color: white;
    &:hover { background: #c0392b; }
  ` : `
    background: #6c757d;
    color: white;
    &:hover { background: #5a6268; }
  `}
`;

const AddLyricForm = styled.div`
  background: #e3f2fd;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 2px solid #bbdefb;
`;

const FormRow = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: end;
`;

const FormGroup = styled.div`
  flex: 1;
  
  label {
    display: block;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
  }
  
  input, select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.9rem;
    box-sizing: border-box;
    
    &:focus {
      outline: none;
      border-color: #667eea;
    }
  }
`;

const FormActions = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const FormButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.variant === 'primary' ? `
    background: #667eea;
    color: white;
    &:hover { background: #5a67d8; }
  ` : `
    background: #6c757d;
    color: white;
    &:hover { background: #5a6268; }
  `}
`;

const EmptyState = styled.div`
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
`;

const LyricsPreview = styled.div`
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
  margin-top: 1rem;
`;

const PreviewTitle = styled.h4`
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
`;

const PreviewText = styled.div`
  line-height: 1.8;
  font-size: 1.1rem;
  color: #333;
  white-space: pre-wrap;
`;

interface LyricsEditorProps {
  lyrics: Lyrics[];
  notes: MusicalNote[];
  onLyricsChange: (lyrics: Lyrics[]) => void;
  title: string;
}

export const LyricsEditor: React.FC<LyricsEditorProps> = ({ 
  lyrics, 
  notes, 
  onLyricsChange, 
  title 
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newLyric, setNewLyric] = useState({
    text: '',
    measure: 1,
    beat: 1
  });

  const handleAddLyric = () => {
    if (!newLyric.text.trim()) return;
    
    const lyric: Lyrics = {
      id: uuidv4(),
      text: newLyric.text.trim(),
      position: {
        measure: newLyric.measure,
        beat: newLyric.beat
      }
    };
    
    onLyricsChange([...lyrics, lyric]);
    setNewLyric({ text: '', measure: 1, beat: 1 });
    setShowAddForm(false);
  };

  const handleUpdateLyric = (id: string, text: string) => {
    const updatedLyrics = lyrics.map(lyric =>
      lyric.id === id ? { ...lyric, text } : lyric
    );
    onLyricsChange(updatedLyrics);
  };

  const handleDeleteLyric = (id: string) => {
    const updatedLyrics = lyrics.filter(lyric => lyric.id !== id);
    onLyricsChange(updatedLyrics);
  };

  // Ordenar letras por posição
  const sortedLyrics = [...lyrics].sort((a, b) => {
    if (a.position.measure !== b.position.measure) {
      return a.position.measure - b.position.measure;
    }
    return a.position.beat - b.position.beat;
  });

  // Gerar preview das letras
  const generatePreview = () => {
    return sortedLyrics.map(lyric => lyric.text).join(' ');
  };

  // Obter compassos disponíveis baseado nas notas
  const availableMeasures = notes.length > 0 
    ? Array.from(new Set(notes.map(note => note.position.measure))).sort((a, b) => a - b)
    : [1];

  return (
    <LyricsContainer>
      <LyricsHeader>
        <LyricsTitle>🎤 Letras - {title}</LyricsTitle>
        <AddLyricButton onClick={() => setShowAddForm(!showAddForm)}>
          {showAddForm ? '❌ Cancelar' : '➕ Adicionar Letra'}
        </AddLyricButton>
      </LyricsHeader>

      {showAddForm && (
        <AddLyricForm>
          <FormRow>
            <FormGroup>
              <label>Texto da Letra:</label>
              <LyricText
                value={newLyric.text}
                onChange={(e) => setNewLyric({ ...newLyric, text: e.target.value })}
                placeholder="Digite a palavra ou frase..."
                autoFocus
              />
            </FormGroup>
            <FormGroup style={{ flex: '0 0 120px' }}>
              <label>Compasso:</label>
              <select
                value={newLyric.measure}
                onChange={(e) => setNewLyric({ ...newLyric, measure: parseInt(e.target.value) })}
              >
                {availableMeasures.map(measure => (
                  <option key={measure} value={measure}>
                    {measure}
                  </option>
                ))}
              </select>
            </FormGroup>
            <FormGroup style={{ flex: '0 0 100px' }}>
              <label>Tempo:</label>
              <select
                value={newLyric.beat}
                onChange={(e) => setNewLyric({ ...newLyric, beat: parseInt(e.target.value) })}
              >
                <option value={1}>1</option>
                <option value={2}>2</option>
                <option value={3}>3</option>
                <option value={4}>4</option>
              </select>
            </FormGroup>
          </FormRow>
          <FormActions>
            <FormButton variant="primary" onClick={handleAddLyric}>
              ✅ Adicionar
            </FormButton>
            <FormButton variant="secondary" onClick={() => setShowAddForm(false)}>
              ❌ Cancelar
            </FormButton>
          </FormActions>
        </AddLyricForm>
      )}

      {sortedLyrics.length === 0 ? (
        <EmptyState>
          Adicione letras para sincronizar com sua partitura
        </EmptyState>
      ) : (
        <>
          <LyricsGrid>
            {sortedLyrics.map(lyric => (
              <LyricCard key={lyric.id}>
                <LyricPosition>
                  📍 Compasso {lyric.position.measure}, Tempo {lyric.position.beat}
                </LyricPosition>
                <LyricText
                  value={lyric.text}
                  onChange={(e) => handleUpdateLyric(lyric.id, e.target.value)}
                />
                <LyricActions>
                  <ActionButton 
                    variant="danger"
                    onClick={() => handleDeleteLyric(lyric.id)}
                  >
                    🗑️ Excluir
                  </ActionButton>
                </LyricActions>
              </LyricCard>
            ))}
          </LyricsGrid>

          <LyricsPreview>
            <PreviewTitle>📝 Preview da Letra</PreviewTitle>
            <PreviewText>
              {generatePreview() || 'Nenhuma letra adicionada ainda...'}
            </PreviewText>
          </LyricsPreview>
        </>
      )}
    </LyricsContainer>
  );
};
