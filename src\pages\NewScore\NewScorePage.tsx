import React, { useState } from 'react';
import styled from 'styled-components';
import { InstrumentType, NoteName } from '../../types/music';
import { InstrumentSelector } from '../../components/InstrumentSelector/InstrumentSelector';
import { getInstrumentTemplate, suggestKeyForInstrument } from '../../utils/instrumentTemplates';

const PageContainer = styled.div`
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 3rem;
`;

const PageTitle = styled.h1`
  color: white;
  margin: 0 0 1rem 0;
  font-size: 2.5rem;
  font-weight: 700;
`;

const PageSubtitle = styled.p`
  color: rgba(255,255,255,0.8);
  font-size: 1.2rem;
  margin: 0;
`;

const SetupForm = styled.div`
  background: rgba(255,255,255,0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
`;

const FormSection = styled.div`
  margin-bottom: 2rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const SectionTitle = styled.h3`
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
`;

const FormGroup = styled.div`
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
  }
  
  input, select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s;
    box-sizing: border-box;
    
    &:focus {
      outline: none;
      border-color: #667eea;
    }
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => props.variant === 'primary' ? `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
  ` : `
    background: #6c757d;
    color: white;
    
    &:hover {
      background: #5a6268;
      transform: translateY(-1px);
    }
  `}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const PreviewCard = styled.div`
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
  margin-top: 1rem;
`;

const PreviewTitle = styled.h4`
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
`;

const PreviewDetail = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const PreviewLabel = styled.span`
  font-weight: 600;
  color: #495057;
`;

const PreviewValue = styled.span`
  color: #666;
`;

interface NewScorePageProps {
  onCreateScore: (config: ScoreConfig) => void;
  onCancel: () => void;
}

export interface ScoreConfig {
  title: string;
  composer: string;
  instrument: InstrumentType;
  key: NoteName;
  keyMode: 'major' | 'minor';
  timeSignatureNum: number;
  timeSignatureDen: number;
  tempo: number;
}

export const NewScorePage: React.FC<NewScorePageProps> = ({ onCreateScore, onCancel }) => {
  const [config, setConfig] = useState<ScoreConfig>({
    title: 'Nova Partitura',
    composer: '',
    instrument: 'piano',
    key: 'C' as NoteName,
    keyMode: 'major',
    timeSignatureNum: 4,
    timeSignatureDen: 4,
    tempo: 120
  });

  const handleInstrumentChange = (instrument: InstrumentType) => {
    const suggestedKey = suggestKeyForInstrument(instrument);
    setConfig(prev => ({
      ...prev,
      instrument,
      key: suggestedKey as NoteName
    }));
  };

  const handleSubmit = () => {
    if (!config.title.trim()) {
      alert('Por favor, digite um título para a partitura');
      return;
    }
    
    onCreateScore(config);
  };

  const instrumentTemplate = getInstrumentTemplate(config.instrument);
  const notes: NoteName[] = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>🎼 Nova Partitura</PageTitle>
        <PageSubtitle>Configure sua nova composição musical</PageSubtitle>
      </PageHeader>

      <SetupForm>
        <FormSection>
          <SectionTitle>📝 Informações Básicas</SectionTitle>
          <FormRow>
            <FormGroup>
              <label>Título da Partitura:</label>
              <input
                type="text"
                value={config.title}
                onChange={(e) => setConfig(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Digite o título..."
                autoFocus
              />
            </FormGroup>
            <FormGroup>
              <label>Compositor (opcional):</label>
              <input
                type="text"
                value={config.composer}
                onChange={(e) => setConfig(prev => ({ ...prev, composer: e.target.value }))}
                placeholder="Nome do compositor..."
              />
            </FormGroup>
          </FormRow>
        </FormSection>

        <FormSection>
          <SectionTitle>🎹 Instrumento</SectionTitle>
          <InstrumentSelector
            selectedInstrument={config.instrument}
            onInstrumentChange={handleInstrumentChange}
            compact={false}
          />
        </FormSection>

        <FormSection>
          <SectionTitle>🎵 Configurações Musicais</SectionTitle>
          <FormRow>
            <FormGroup>
              <label>Tonalidade:</label>
              <select
                value={config.key}
                onChange={(e) => setConfig(prev => ({ ...prev, key: e.target.value as NoteName }))}
              >
                {notes.map(note => (
                  <option key={note} value={note}>{note}</option>
                ))}
              </select>
            </FormGroup>
            <FormGroup>
              <label>Modo:</label>
              <select
                value={config.keyMode}
                onChange={(e) => setConfig(prev => ({ ...prev, keyMode: e.target.value as 'major' | 'minor' }))}
              >
                <option value="major">Maior</option>
                <option value="minor">Menor</option>
              </select>
            </FormGroup>
            <FormGroup>
              <label>Compasso:</label>
              <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                <select
                  value={config.timeSignatureNum}
                  onChange={(e) => setConfig(prev => ({ ...prev, timeSignatureNum: parseInt(e.target.value) }))}
                  style={{ flex: 1 }}
                >
                  <option value={2}>2</option>
                  <option value={3}>3</option>
                  <option value={4}>4</option>
                  <option value={6}>6</option>
                  <option value={9}>9</option>
                  <option value={12}>12</option>
                </select>
                <span>/</span>
                <select
                  value={config.timeSignatureDen}
                  onChange={(e) => setConfig(prev => ({ ...prev, timeSignatureDen: parseInt(e.target.value) }))}
                  style={{ flex: 1 }}
                >
                  <option value={2}>2</option>
                  <option value={4}>4</option>
                  <option value={8}>8</option>
                  <option value={16}>16</option>
                </select>
              </div>
            </FormGroup>
            <FormGroup>
              <label>Tempo (BPM):</label>
              <input
                type="number"
                min="40"
                max="200"
                value={config.tempo}
                onChange={(e) => setConfig(prev => ({ ...prev, tempo: parseInt(e.target.value) || 120 }))}
              />
            </FormGroup>
          </FormRow>
        </FormSection>

        <PreviewCard>
          <PreviewTitle>📋 Resumo da Configuração</PreviewTitle>
          <PreviewDetail>
            <PreviewLabel>Título:</PreviewLabel>
            <PreviewValue>{config.title}</PreviewValue>
          </PreviewDetail>
          {config.composer && (
            <PreviewDetail>
              <PreviewLabel>Compositor:</PreviewLabel>
              <PreviewValue>{config.composer}</PreviewValue>
            </PreviewDetail>
          )}
          <PreviewDetail>
            <PreviewLabel>Instrumento:</PreviewLabel>
            <PreviewValue>{instrumentTemplate.emoji} {instrumentTemplate.name}</PreviewValue>
          </PreviewDetail>
          <PreviewDetail>
            <PreviewLabel>Tonalidade:</PreviewLabel>
            <PreviewValue>{config.key} {config.keyMode === 'major' ? 'Maior' : 'Menor'}</PreviewValue>
          </PreviewDetail>
          <PreviewDetail>
            <PreviewLabel>Compasso:</PreviewLabel>
            <PreviewValue>{config.timeSignatureNum}/{config.timeSignatureDen}</PreviewValue>
          </PreviewDetail>
          <PreviewDetail>
            <PreviewLabel>Tempo:</PreviewLabel>
            <PreviewValue>{config.tempo} BPM</PreviewValue>
          </PreviewDetail>
        </PreviewCard>

        <ActionButtons>
          <ActionButton variant="secondary" onClick={onCancel}>
            ❌ Cancelar
          </ActionButton>
          <ActionButton variant="primary" onClick={handleSubmit}>
            ✅ Criar Partitura
          </ActionButton>
        </ActionButtons>
      </SetupForm>
    </PageContainer>
  );
};
