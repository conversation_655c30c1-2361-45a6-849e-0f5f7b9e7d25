{"ast": null, "code": "function f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n    case 1:\n      return x ^ y ^ z;\n    case 2:\n      return x & y ^ x & z ^ y & z;\n    case 3:\n      return x ^ y ^ z;\n  }\n}\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\nfunction sha1(bytes) {\n  const K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  const H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n  const newBytes = new Uint8Array(bytes.length + 1);\n  newBytes.set(bytes);\n  newBytes[bytes.length] = 0x80;\n  bytes = newBytes;\n  const l = bytes.length / 4 + 2;\n  const N = Math.ceil(l / 16);\n  const M = new Array(N);\n  for (let i = 0; i < N; ++i) {\n    const arr = new Uint32Array(16);\n    for (let j = 0; j < 16; ++j) {\n      arr[j] = bytes[i * 64 + j * 4] << 24 | bytes[i * 64 + j * 4 + 1] << 16 | bytes[i * 64 + j * 4 + 2] << 8 | bytes[i * 64 + j * 4 + 3];\n    }\n    M[i] = arr;\n  }\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n  for (let i = 0; i < N; ++i) {\n    const W = new Uint32Array(80);\n    for (let t = 0; t < 16; ++t) {\n      W[t] = M[i][t];\n    }\n    for (let t = 16; t < 80; ++t) {\n      W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n    }\n    let a = H[0];\n    let b = H[1];\n    let c = H[2];\n    let d = H[3];\n    let e = H[4];\n    for (let t = 0; t < 80; ++t) {\n      const s = Math.floor(t / 20);\n      const T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n  return Uint8Array.of(H[0] >> 24, H[0] >> 16, H[0] >> 8, H[0], H[1] >> 24, H[1] >> 16, H[1] >> 8, H[1], H[2] >> 24, H[2] >> 16, H[2] >> 8, H[2], H[3] >> 24, H[3] >> 16, H[3] >> 8, H[3], H[4] >> 24, H[4] >> 16, H[4] >> 8, H[4]);\n}\nexport default sha1;", "map": {"version": 3, "names": ["f", "s", "x", "y", "z", "ROTL", "n", "sha1", "bytes", "K", "H", "newBytes", "Uint8Array", "length", "set", "l", "N", "Math", "ceil", "M", "Array", "i", "arr", "Uint32Array", "j", "pow", "floor", "W", "t", "a", "b", "c", "d", "e", "T", "of"], "sources": ["D:/Dev/partitura_digital/node_modules/uuid/dist/esm-browser/sha1.js"], "sourcesContent": ["function f(s, x, y, z) {\n    switch (s) {\n        case 0:\n            return (x & y) ^ (~x & z);\n        case 1:\n            return x ^ y ^ z;\n        case 2:\n            return (x & y) ^ (x & z) ^ (y & z);\n        case 3:\n            return x ^ y ^ z;\n    }\n}\nfunction ROTL(x, n) {\n    return (x << n) | (x >>> (32 - n));\n}\nfunction sha1(bytes) {\n    const K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n    const H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n    const newBytes = new Uint8Array(bytes.length + 1);\n    newBytes.set(bytes);\n    newBytes[bytes.length] = 0x80;\n    bytes = newBytes;\n    const l = bytes.length / 4 + 2;\n    const N = Math.ceil(l / 16);\n    const M = new Array(N);\n    for (let i = 0; i < N; ++i) {\n        const arr = new Uint32Array(16);\n        for (let j = 0; j < 16; ++j) {\n            arr[j] =\n                (bytes[i * 64 + j * 4] << 24) |\n                    (bytes[i * 64 + j * 4 + 1] << 16) |\n                    (bytes[i * 64 + j * 4 + 2] << 8) |\n                    bytes[i * 64 + j * 4 + 3];\n        }\n        M[i] = arr;\n    }\n    M[N - 1][14] = ((bytes.length - 1) * 8) / Math.pow(2, 32);\n    M[N - 1][14] = Math.floor(M[N - 1][14]);\n    M[N - 1][15] = ((bytes.length - 1) * 8) & 0xffffffff;\n    for (let i = 0; i < N; ++i) {\n        const W = new Uint32Array(80);\n        for (let t = 0; t < 16; ++t) {\n            W[t] = M[i][t];\n        }\n        for (let t = 16; t < 80; ++t) {\n            W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n        }\n        let a = H[0];\n        let b = H[1];\n        let c = H[2];\n        let d = H[3];\n        let e = H[4];\n        for (let t = 0; t < 80; ++t) {\n            const s = Math.floor(t / 20);\n            const T = (ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t]) >>> 0;\n            e = d;\n            d = c;\n            c = ROTL(b, 30) >>> 0;\n            b = a;\n            a = T;\n        }\n        H[0] = (H[0] + a) >>> 0;\n        H[1] = (H[1] + b) >>> 0;\n        H[2] = (H[2] + c) >>> 0;\n        H[3] = (H[3] + d) >>> 0;\n        H[4] = (H[4] + e) >>> 0;\n    }\n    return Uint8Array.of(H[0] >> 24, H[0] >> 16, H[0] >> 8, H[0], H[1] >> 24, H[1] >> 16, H[1] >> 8, H[1], H[2] >> 24, H[2] >> 16, H[2] >> 8, H[2], H[3] >> 24, H[3] >> 16, H[3] >> 8, H[3], H[4] >> 24, H[4] >> 16, H[4] >> 8, H[4]);\n}\nexport default sha1;\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACnB,QAAQH,CAAC;IACL,KAAK,CAAC;MACF,OAAQC,CAAC,GAAGC,CAAC,GAAK,CAACD,CAAC,GAAGE,CAAE;IAC7B,KAAK,CAAC;MACF,OAAOF,CAAC,GAAGC,CAAC,GAAGC,CAAC;IACpB,KAAK,CAAC;MACF,OAAQF,CAAC,GAAGC,CAAC,GAAKD,CAAC,GAAGE,CAAE,GAAID,CAAC,GAAGC,CAAE;IACtC,KAAK,CAAC;MACF,OAAOF,CAAC,GAAGC,CAAC,GAAGC,CAAC;EACxB;AACJ;AACA,SAASC,IAAIA,CAACH,CAAC,EAAEI,CAAC,EAAE;EAChB,OAAQJ,CAAC,IAAII,CAAC,GAAKJ,CAAC,KAAM,EAAE,GAAGI,CAAG;AACtC;AACA,SAASC,IAAIA,CAACC,KAAK,EAAE;EACjB,MAAMC,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EAC1D,MAAMC,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EACtE,MAAMC,QAAQ,GAAG,IAAIC,UAAU,CAACJ,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;EACjDF,QAAQ,CAACG,GAAG,CAACN,KAAK,CAAC;EACnBG,QAAQ,CAACH,KAAK,CAACK,MAAM,CAAC,GAAG,IAAI;EAC7BL,KAAK,GAAGG,QAAQ;EAChB,MAAMI,CAAC,GAAGP,KAAK,CAACK,MAAM,GAAG,CAAC,GAAG,CAAC;EAC9B,MAAMG,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACH,CAAC,GAAG,EAAE,CAAC;EAC3B,MAAMI,CAAC,GAAG,IAAIC,KAAK,CAACJ,CAAC,CAAC;EACtB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAE,EAAEK,CAAC,EAAE;IACxB,MAAMC,GAAG,GAAG,IAAIC,WAAW,CAAC,EAAE,CAAC;IAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MACzBF,GAAG,CAACE,CAAC,CAAC,GACDhB,KAAK,CAACa,CAAC,GAAG,EAAE,GAAGG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,GACvBhB,KAAK,CAACa,CAAC,GAAG,EAAE,GAAGG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAG,GAChChB,KAAK,CAACa,CAAC,GAAG,EAAE,GAAGG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAE,GAChChB,KAAK,CAACa,CAAC,GAAG,EAAE,GAAGG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrC;IACAL,CAAC,CAACE,CAAC,CAAC,GAAGC,GAAG;EACd;EACAH,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAI,CAACR,KAAK,CAACK,MAAM,GAAG,CAAC,IAAI,CAAC,GAAII,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;EACzDN,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGC,IAAI,CAACS,KAAK,CAACP,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACvCG,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAI,CAACR,KAAK,CAACK,MAAM,GAAG,CAAC,IAAI,CAAC,GAAI,UAAU;EACpD,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAE,EAAEK,CAAC,EAAE;IACxB,MAAMM,CAAC,GAAG,IAAIJ,WAAW,CAAC,EAAE,CAAC;IAC7B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MACzBD,CAAC,CAACC,CAAC,CAAC,GAAGT,CAAC,CAACE,CAAC,CAAC,CAACO,CAAC,CAAC;IAClB;IACA,KAAK,IAAIA,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MAC1BD,CAAC,CAACC,CAAC,CAAC,GAAGvB,IAAI,CAACsB,CAAC,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,GAAG,EAAE,CAAC,GAAGD,CAAC,CAACC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/D;IACA,IAAIC,CAAC,GAAGnB,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIoB,CAAC,GAAGpB,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIqB,CAAC,GAAGrB,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIsB,CAAC,GAAGtB,CAAC,CAAC,CAAC,CAAC;IACZ,IAAIuB,CAAC,GAAGvB,CAAC,CAAC,CAAC,CAAC;IACZ,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MACzB,MAAM3B,CAAC,GAAGgB,IAAI,CAACS,KAAK,CAACE,CAAC,GAAG,EAAE,CAAC;MAC5B,MAAMM,CAAC,GAAI7B,IAAI,CAACwB,CAAC,EAAE,CAAC,CAAC,GAAG7B,CAAC,CAACC,CAAC,EAAE6B,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGC,CAAC,GAAGxB,CAAC,CAACR,CAAC,CAAC,GAAG0B,CAAC,CAACC,CAAC,CAAC,KAAM,CAAC;MAC9DK,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAG1B,IAAI,CAACyB,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC;MACrBA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGK,CAAC;IACT;IACAxB,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGmB,CAAC,KAAM,CAAC;IACvBnB,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGoB,CAAC,KAAM,CAAC;IACvBpB,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGqB,CAAC,KAAM,CAAC;IACvBrB,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGsB,CAAC,KAAM,CAAC;IACvBtB,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,KAAM,CAAC;EAC3B;EACA,OAAOrB,UAAU,CAACuB,EAAE,CAACzB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;AACrO;AACA,eAAeH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}