{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, onAuthStateChanged, updateProfile } from 'firebase/auth';\nimport { auth } from '../services/firebase';\n\n// Interface para o contexto de autenticação\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Criar o contexto\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Hook personalizado para usar o contexto\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth deve ser usado dentro de um AuthProvider');\n  }\n  return context;\n};\n\n// Provider do contexto de autenticação\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [currentUser, setCurrentUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Função para fazer login\n  const login = async (email, password) => {\n    await signInWithEmailAndPassword(auth, email, password);\n  };\n\n  // Função para registrar novo usuário\n  const register = async (email, password, displayName) => {\n    const {\n      user\n    } = await createUserWithEmailAndPassword(auth, email, password);\n    await updateProfile(user, {\n      displayName\n    });\n  };\n\n  // Função para fazer logout\n  const logout = async () => {\n    await signOut(auth);\n  };\n\n  // Efeito para monitorar mudanças no estado de autenticação\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, user => {\n      setCurrentUser(user);\n      setLoading(false);\n    });\n    return unsubscribe;\n  }, []);\n  const value = {\n    currentUser,\n    login,\n    register,\n    logout,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: !loading && children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"+Bt7EujFHjK6mRV3YX1iAtSqXvQ=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "signInWithEmailAndPassword", "createUserWithEmailAndPassword", "signOut", "onAuthStateChanged", "updateProfile", "auth", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "currentUser", "setCurrentUser", "loading", "setLoading", "login", "email", "password", "register", "displayName", "user", "logout", "unsubscribe", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { \n  User, \n  signInWithEmailAndPassword, \n  createUserWithEmailAndPassword, \n  signOut, \n  onAuthStateChanged,\n  updateProfile\n} from 'firebase/auth';\nimport { auth } from '../services/firebase';\n\n// Interface para o contexto de autenticação\ninterface AuthContextType {\n  currentUser: User | null;\n  login: (email: string, password: string) => Promise<void>;\n  register: (email: string, password: string, displayName: string) => Promise<void>;\n  logout: () => Promise<void>;\n  loading: boolean;\n}\n\n// Criar o contexto\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Hook personalizado para usar o contexto\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth deve ser usado dentro de um AuthProvider');\n  }\n  return context;\n};\n\n// Provider do contexto de autenticação\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Função para fazer login\n  const login = async (email: string, password: string): Promise<void> => {\n    await signInWithEmailAndPassword(auth, email, password);\n  };\n\n  // Função para registrar novo usuário\n  const register = async (email: string, password: string, displayName: string): Promise<void> => {\n    const { user } = await createUserWithEmailAndPassword(auth, email, password);\n    await updateProfile(user, { displayName });\n  };\n\n  // Função para fazer logout\n  const logout = async (): Promise<void> => {\n    await signOut(auth);\n  };\n\n  // Efeito para monitorar mudanças no estado de autenticação\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, (user) => {\n      setCurrentUser(user);\n      setLoading(false);\n    });\n\n    return unsubscribe;\n  }, []);\n\n  const value: AuthContextType = {\n    currentUser,\n    login,\n    register,\n    logout,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {!loading && children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC7E,SAEEC,0BAA0B,EAC1BC,8BAA8B,EAC9BC,OAAO,EACPC,kBAAkB,EAClBC,aAAa,QACR,eAAe;AACtB,SAASC,IAAI,QAAQ,sBAAsB;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AASA;AACA,MAAMC,WAAW,gBAAGZ,aAAa,CAA8Ba,SAAS,CAAC;;AAEzE;AACA,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGf,UAAU,CAACW,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,kDAAkD,CAAC;EACrE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CARaD,OAAO;AASpB,OAAO,MAAMI,YAAqD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACrF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMsB,KAAK,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAoB;IACtE,MAAMvB,0BAA0B,CAACK,IAAI,EAAEiB,KAAK,EAAEC,QAAQ,CAAC;EACzD,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAG,MAAAA,CAAOF,KAAa,EAAEC,QAAgB,EAAEE,WAAmB,KAAoB;IAC9F,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMzB,8BAA8B,CAACI,IAAI,EAAEiB,KAAK,EAAEC,QAAQ,CAAC;IAC5E,MAAMnB,aAAa,CAACsB,IAAI,EAAE;MAAED;IAAY,CAAC,CAAC;EAC5C,CAAC;;EAED;EACA,MAAME,MAAM,GAAG,MAAAA,CAAA,KAA2B;IACxC,MAAMzB,OAAO,CAACG,IAAI,CAAC;EACrB,CAAC;;EAED;EACAP,SAAS,CAAC,MAAM;IACd,MAAM8B,WAAW,GAAGzB,kBAAkB,CAACE,IAAI,EAAGqB,IAAI,IAAK;MACrDR,cAAc,CAACQ,IAAI,CAAC;MACpBN,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;IAEF,OAAOQ,WAAW;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,KAAsB,GAAG;IAC7BZ,WAAW;IACXI,KAAK;IACLG,QAAQ;IACRG,MAAM;IACNR;EACF,CAAC;EAED,oBACEZ,OAAA,CAACC,WAAW,CAACsB,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAd,QAAA,EAChC,CAACI,OAAO,IAAIJ;EAAQ;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAE3B,CAAC;AAAClB,GAAA,CA3CWF,YAAqD;AAAAqB,EAAA,GAArDrB,YAAqD;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}