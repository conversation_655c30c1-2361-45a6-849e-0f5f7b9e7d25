import React from 'react';
import { 
  FiMusic, 
  FiUser, 
  FiLogOut, 
  FiHome, 
  FiPlus, 
  FiMail, 
  FiLock, 
  FiEye, 
  FiEyeOff 
} from 'react-icons/fi';

// Wrapper para os ícones para resolver problemas de TypeScript
export const Icons = {
  Music: (props: { size?: number; className?: string }) => <FiMusic {...props} />,
  User: (props: { size?: number; className?: string }) => <FiUser {...props} />,
  LogOut: (props: { size?: number; className?: string }) => <FiLogOut {...props} />,
  Home: (props: { size?: number; className?: string }) => <FiHome {...props} />,
  Plus: (props: { size?: number; className?: string }) => <FiPlus {...props} />,
  Mail: (props: { size?: number; className?: string }) => <FiMail {...props} />,
  Lock: (props: { size?: number; className?: string }) => <FiLock {...props} />,
  Eye: (props: { size?: number; className?: string }) => <FiEye {...props} />,
  EyeOff: (props: { size?: number; className?: string }) => <FiEyeOff {...props} />,
};

// Tipo para os ícones
export type IconComponent = React.FC<{ size?: number; className?: string }>;
