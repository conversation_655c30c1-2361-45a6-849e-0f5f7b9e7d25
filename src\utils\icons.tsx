import React from 'react';

// Componentes de ícones simples usando Unicode
export const Icons = {
  Music: ({ size = 20 }: { size?: number }) => (
    <span style={{ fontSize: size, display: 'inline-block' }}>🎵</span>
  ),
  User: ({ size = 20 }: { size?: number }) => (
    <span style={{ fontSize: size, display: 'inline-block' }}>👤</span>
  ),
  LogOut: ({ size = 20 }: { size?: number }) => (
    <span style={{ fontSize: size, display: 'inline-block' }}>🚪</span>
  ),
  Home: ({ size = 20 }: { size?: number }) => (
    <span style={{ fontSize: size, display: 'inline-block' }}>🏠</span>
  ),
  Plus: ({ size = 20 }: { size?: number }) => (
    <span style={{ fontSize: size, display: 'inline-block' }}>➕</span>
  ),
  Mail: ({ size = 20 }: { size?: number }) => (
    <span style={{ fontSize: size, display: 'inline-block' }}>✉️</span>
  ),
  Lock: ({ size = 20 }: { size?: number }) => (
    <span style={{ fontSize: size, display: 'inline-block' }}>🔒</span>
  ),
  Eye: ({ size = 20 }: { size?: number }) => (
    <span style={{ fontSize: size, display: 'inline-block' }}>👁️</span>
  ),
  EyeOff: ({ size = 20 }: { size?: number }) => (
    <span style={{ fontSize: size, display: 'inline-block' }}>🙈</span>
  ),
};

// Tipo para os ícones
export type IconComponent = React.FC<{ size?: number }>;
