{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ScoreEditor\\\\ScoreEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { ScoreService } from '../../services/scoreService';\nimport { ChordView } from '../ChordView/ChordView';\nimport { MusicalNote as MusicalNoteComponent } from '../MusicalNote/MusicalNote';\nimport { LyricsEditor } from '../LyricsEditor/LyricsEditor';\nimport { v4 as uuidv4 } from 'uuid';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n_c = EditorContainer;\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c2 = EditorHeader;\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n_c3 = EditorTitle;\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n_c4 = EditorActions;\nconst ActionButton = styled.button`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c5 = ActionButton;\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n_c6 = EditorContent;\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n_c7 = ToolPanel;\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n_c8 = ToolSection;\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n_c9 = ToolGrid;\nconst ToolButton = styled.button`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n_c0 = ToolButton;\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n_c1 = ScoreCanvas;\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n_c10 = StaffContainer;\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n_c11 = Staff;\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n_c12 = GuestWarning;\nexport const ScoreEditor = ({\n  scoreId\n}) => {\n  _s();\n  const {\n    currentUser\n  } = useAuth();\n  const [title, setTitle] = useState('Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState('note');\n  const [selectedDuration, setSelectedDuration] = useState('quarter');\n  const [selectedNote, setSelectedNote] = useState('C');\n  const [selectedAccidental, setSelectedAccidental] = useState(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [placedNotes, setPlacedNotes] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showChords, setShowChords] = useState(false);\n  const [showLyrics, setShowLyrics] = useState(false);\n  const [lastSaved, setLastSaved] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(1);\n  const [lyrics, setLyrics] = useState([]);\n\n  // Carregar partitura existente\n  React.useEffect(() => {\n    if (scoreId && currentUser) {\n      loadScore();\n    }\n  }, [scoreId, currentUser]);\n\n  // Salvamento automático\n  React.useEffect(() => {\n    if (!currentUser || !scoreId || placedNotes.length === 0) return;\n    const autoSaveTimer = setTimeout(() => {\n      handleSave(true); // true indica salvamento automático\n    }, 2000); // Salvar após 2 segundos de inatividade\n\n    return () => clearTimeout(autoSaveTimer);\n  }, [placedNotes, title, currentUser, scoreId]);\n  const loadScore = async () => {\n    if (!scoreId || !currentUser) return;\n    setIsLoading(true);\n    try {\n      const score = await ScoreService.getScore(scoreId);\n      if (score && score.userId === currentUser.uid) {\n        setTitle(score.title);\n\n        // Extrair notas de todos os compassos\n        const allNotes = [];\n        score.staffs.forEach(staff => {\n          staff.measures.forEach(measure => {\n            allNotes.push(...measure.notes);\n          });\n        });\n        setPlacedNotes(allNotes);\n        setLyrics(score.lyrics || []);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar partitura:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSave = async (isAutoSave = false) => {\n    if (!currentUser) {\n      if (!isAutoSave) {\n        alert('Você precisa estar logado para salvar partituras!');\n      }\n      return;\n    }\n    setIsSaving(true);\n    try {\n      // Organizar notas por compasso\n      const measureMap = new Map();\n      placedNotes.forEach(note => {\n        const measure = note.position.measure;\n        if (!measureMap.has(measure)) {\n          measureMap.set(measure, []);\n        }\n        measureMap.get(measure).push(note);\n      });\n\n      // Criar compassos com as notas\n      const measures = Array.from(measureMap.entries()).map(([measureNumber, notes]) => ({\n        id: uuidv4(),\n        number: measureNumber,\n        timeSignature: {\n          numerator: 4,\n          denominator: 4\n        },\n        notes: notes.sort((a, b) => a.position.beat - b.position.beat),\n        chords: []\n      }));\n\n      // Criar dados da partitura\n      const scoreData = {\n        title,\n        key: {\n          note: 'C',\n          mode: 'major'\n        },\n        timeSignature: {\n          numerator: 4,\n          denominator: 4\n        },\n        tempo: 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: 'treble',\n          instrument: 'piano',\n          measures\n        }],\n        lyrics,\n        userId: currentUser.uid\n      };\n      if (scoreId) {\n        // Para update, removemos o userId pois não deve ser alterado\n        const {\n          userId,\n          ...updateData\n        } = scoreData;\n        await ScoreService.updateScore(scoreId, updateData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n      setLastSaved(new Date());\n      if (!isAutoSave) {\n        alert('Partitura salva com sucesso!');\n      }\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      if (!isAutoSave) {\n        alert('Erro ao salvar partitura');\n      }\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n  const handleStaffClick = event => {\n    var _event$currentTarget$;\n    const rect = event.currentTarget.getBoundingClientRect();\n    const svgRect = (_event$currentTarget$ = event.currentTarget.closest('svg')) === null || _event$currentTarget$ === void 0 ? void 0 : _event$currentTarget$.getBoundingClientRect();\n    if (!svgRect) return;\n    const x = event.clientX - svgRect.left;\n    const y = event.clientY - svgRect.top;\n\n    // Calcular a linha da pauta baseada na posição Y\n    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta\n    const notePositions = [{\n      y: 40,\n      note: 'A',\n      octave: 5\n    },\n    // Acima da pauta\n    {\n      y: 50,\n      note: 'G',\n      octave: 5\n    },\n    // 5ª linha\n    {\n      y: 60,\n      note: 'F',\n      octave: 5\n    },\n    // Entre 4ª e 5ª\n    {\n      y: 70,\n      note: 'E',\n      octave: 5\n    },\n    // 4ª linha\n    {\n      y: 80,\n      note: 'D',\n      octave: 5\n    },\n    // Entre 3ª e 4ª\n    {\n      y: 90,\n      note: 'C',\n      octave: 5\n    },\n    // 3ª linha (Dó central)\n    {\n      y: 100,\n      note: 'B',\n      octave: 4\n    },\n    // Entre 2ª e 3ª\n    {\n      y: 110,\n      note: 'A',\n      octave: 4\n    },\n    // 2ª linha\n    {\n      y: 120,\n      note: 'G',\n      octave: 4\n    },\n    // Entre 1ª e 2ª\n    {\n      y: 130,\n      note: 'F',\n      octave: 4\n    },\n    // 1ª linha\n    {\n      y: 140,\n      note: 'E',\n      octave: 4\n    } // Abaixo da pauta\n    ];\n\n    // Encontrar a posição mais próxima\n    const closestPosition = notePositions.reduce((closest, current) => {\n      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;\n    });\n\n    // Calcular a posição horizontal (compasso e tempo)\n    const measureWidth = 150; // Largura aproximada de um compasso\n    const startX = 120; // Início da área de notas\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = relativeX % measureWidth / measureWidth * 4; // 4 tempos por compasso\n\n    // Criar nova nota\n    const newNote = {\n      id: uuidv4(),\n      name: selectedNote,\n      // Usar a nota selecionada no painel\n      octave: closestPosition.octave,\n      duration: selectedDuration,\n      accidental: selectedAccidental || undefined,\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4,\n        // Arredondar para 1/4 de tempo\n        staff: 0\n      },\n      isRest: selectedTool === 'rest'\n    };\n    setPlacedNotes(prev => [...prev, newNote]);\n  };\n  const durations = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(EditorContainer, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '400px',\n          fontSize: '1.2rem',\n          color: '#666'\n        },\n        children: \"\\uD83C\\uDFBC Carregando partitura...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(EditorContainer, {\n    children: [/*#__PURE__*/_jsxDEV(EditorHeader, {\n      children: [/*#__PURE__*/_jsxDEV(EditorTitle, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Editor de Partituras\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: title,\n            onChange: e => setTitle(e.target.value),\n            placeholder: \"Nome da partitura...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), currentUser && lastSaved && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              color: 'rgba(255,255,255,0.8)'\n            },\n            children: [\"\\uD83D\\uDCBE Salvo \\xE0s \", lastSaved.toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorActions, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setZoomLevel(prev => Math.max(0.5, prev - 0.1)),\n          variant: \"primary\",\n          disabled: zoomLevel <= 0.5,\n          children: \"\\uD83D\\uDD0D\\u2796\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setZoomLevel(prev => Math.min(2, prev + 0.1)),\n          variant: \"primary\",\n          disabled: zoomLevel >= 2,\n          children: \"\\uD83D\\uDD0D\\u2795\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => {\n            setShowChords(!showChords);\n            setShowLyrics(false);\n          },\n          variant: \"primary\",\n          children: showChords ? '🎼 Partitura' : '🎸 Cifras'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => {\n            setShowLyrics(!showLyrics);\n            setShowChords(false);\n          },\n          variant: \"primary\",\n          children: showLyrics ? '🎼 Partitura' : '🎤 Letras'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setPlacedNotes([]),\n          variant: \"primary\",\n          disabled: placedNotes.length === 0,\n          children: \"\\uD83D\\uDDD1\\uFE0F Limpar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handlePlay,\n          variant: \"primary\",\n          children: isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handleSave,\n          variant: \"secondary\",\n          disabled: !currentUser || isSaving,\n          children: isSaving ? '💾 Salvando...' : '💾 Salvar'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditorContent, {\n      children: [/*#__PURE__*/_jsxDEV(ToolPanel, {\n        children: [!currentUser && /*#__PURE__*/_jsxDEV(GuestWarning, {\n          children: \"\\u26A0\\uFE0F Modo visitante: suas altera\\xE7\\xF5es n\\xE3o ser\\xE3o salvas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFB5 Ferramentas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: [/*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'note',\n              onClick: () => setSelectedTool('note'),\n              children: \"\\uD83C\\uDFB5 Nota\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'rest',\n              onClick: () => setSelectedTool('rest'),\n              children: \"\\uD83C\\uDFBC Pausa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'chord',\n              onClick: () => setSelectedTool('chord'),\n              children: \"\\uD83C\\uDFB9 Acorde\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u23F1\\uFE0F Dura\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: durations.map(duration => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedDuration === duration,\n              onClick: () => setSelectedDuration(duration),\n              children: duration === 'whole' ? '𝅝' : duration === 'half' ? '𝅗𝅥' : duration === 'quarter' ? '♩' : duration === 'eighth' ? '♫' : '♬'\n            }, duration, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFBC Notas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: notes.map(note => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedNote === note,\n              onClick: () => setSelectedNote(note),\n              children: note\n            }, note, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u266F\\u266D Acidentes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: [/*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === 'sharp',\n              onClick: () => setSelectedAccidental(selectedAccidental === 'sharp' ? null : 'sharp'),\n              children: \"\\u266F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === 'flat',\n              onClick: () => setSelectedAccidental(selectedAccidental === 'flat' ? null : 'flat'),\n              children: \"\\u266D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === 'natural',\n              onClick: () => setSelectedAccidental(selectedAccidental === 'natural' ? null : 'natural'),\n              children: \"\\u266E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === null,\n              onClick: () => setSelectedAccidental(null),\n              children: \"\\u2014\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCCA Estat\\xEDsticas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.9rem',\n              color: '#666',\n              lineHeight: '1.4'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83D\\uDCDD Notas: \", placedNotes.filter(n => !n.isRest).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u23F8\\uFE0F Pausas: \", placedNotes.filter(n => n.isRest).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83C\\uDFB5 Total: \", placedNotes.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83D\\uDCCF Compassos: \", Math.max(0, ...placedNotes.map(n => n.position.measure), 0)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u266F Sustenidos: \", placedNotes.filter(n => n.accidental === 'sharp').length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u266D Bem\\xF3is: \", placedNotes.filter(n => n.accidental === 'flat').length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83D\\uDD0D Zoom: \", Math.round(zoomLevel * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScoreCanvas, {\n        children: showChords ? /*#__PURE__*/_jsxDEV(ChordView, {\n          notes: placedNotes,\n          title: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 13\n        }, this) : showLyrics ? /*#__PURE__*/_jsxDEV(LyricsEditor, {\n          lyrics: lyrics,\n          notes: placedNotes,\n          onLyricsChange: setLyrics,\n          title: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(StaffContainer, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              marginBottom: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                color: '#495057'\n              },\n              children: [\"Partitura: \", title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.9rem',\n                color: '#666'\n              },\n              children: [\"Zoom: \", Math.round(zoomLevel * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Staff, {\n            viewBox: \"0 0 800 200\",\n            style: {\n              transform: `scale(${zoomLevel})`,\n              transformOrigin: 'top left'\n            },\n            children: [[0, 1, 2, 3, 4].map(line => /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"50\",\n              y1: 50 + line * 20,\n              x2: \"750\",\n              y2: 50 + line * 20,\n              stroke: \"#333\",\n              strokeWidth: \"1\"\n            }, line, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"20\",\n              y: \"90\",\n              fontSize: \"40\",\n              fill: \"#333\",\n              children: \"\\uD834\\uDD1E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"75\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"95\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"110\",\n              y1: \"50\",\n              x2: \"110\",\n              y2: \"130\",\n              stroke: \"#333\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 15\n            }, this), [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: 120 + measure * 150,\n              y1: \"50\",\n              x2: 120 + measure * 150,\n              y2: \"130\",\n              stroke: \"#999\",\n              strokeWidth: \"1\"\n            }, measure, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 17\n            }, this)), [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"text\", {\n              x: 120 + (measure - 1) * 150 + 75,\n              y: \"35\",\n              fontSize: \"12\",\n              fill: \"#666\",\n              textAnchor: \"middle\",\n              children: measure\n            }, measure, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"120\",\n              y: \"40\",\n              width: \"600\",\n              height: \"100\",\n              fill: \"transparent\",\n              style: {\n                cursor: 'crosshair'\n              },\n              onClick: handleStaffClick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this), placedNotes.map(note => {\n              const x = 120 + (note.position.measure - 1) * 150 + note.position.beat * 30;\n              const notePositions = {\n                'A5': 40,\n                'G5': 50,\n                'F5': 60,\n                'E5': 70,\n                'D5': 80,\n                'C5': 90,\n                'B4': 100,\n                'A4': 110,\n                'G4': 120,\n                'F4': 130,\n                'E4': 140\n              };\n              const y = notePositions[`${note.name}${note.octave}`] || 90;\n              return /*#__PURE__*/_jsxDEV(MusicalNoteComponent, {\n                note: note,\n                x: x,\n                y: y,\n                onRemove: () => setPlacedNotes(prev => prev.filter(n => n.id !== note.id))\n              }, note.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 19\n              }, this);\n            }), placedNotes.length === 0 && /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"100\",\n              fontSize: \"14\",\n              fill: \"#999\",\n              textAnchor: \"middle\",\n              children: \"Clique na pauta para adicionar notas \\u2022 Clique em uma nota para remov\\xEA-la\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 394,\n    columnNumber: 5\n  }, this);\n};\n_s(ScoreEditor, \"S0phs76jLcVAitf4fUW25D5OvE8=\", false, function () {\n  return [useAuth];\n});\n_c13 = ScoreEditor;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"EditorContainer\");\n$RefreshReg$(_c2, \"EditorHeader\");\n$RefreshReg$(_c3, \"EditorTitle\");\n$RefreshReg$(_c4, \"EditorActions\");\n$RefreshReg$(_c5, \"ActionButton\");\n$RefreshReg$(_c6, \"EditorContent\");\n$RefreshReg$(_c7, \"ToolPanel\");\n$RefreshReg$(_c8, \"ToolSection\");\n$RefreshReg$(_c9, \"ToolGrid\");\n$RefreshReg$(_c0, \"ToolButton\");\n$RefreshReg$(_c1, \"ScoreCanvas\");\n$RefreshReg$(_c10, \"StaffContainer\");\n$RefreshReg$(_c11, \"Staff\");\n$RefreshReg$(_c12, \"GuestWarning\");\n$RefreshReg$(_c13, \"ScoreEditor\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "ScoreService", "ChordView", "MusicalNote", "MusicalNoteComponent", "LyricsEditor", "v4", "uuidv4", "jsxDEV", "_jsxDEV", "EditorC<PERSON><PERSON>", "div", "_c", "Editor<PERSON><PERSON>er", "_c2", "EditorT<PERSON>le", "_c3", "EditorActions", "_c4", "ActionButton", "button", "props", "variant", "_c5", "Editor<PERSON><PERSON><PERSON>", "_c6", "ToolPanel", "_c7", "ToolSection", "_c8", "ToolGrid", "_c9", "<PERSON><PERSON><PERSON><PERSON>on", "active", "_c0", "ScoreCanvas", "_c1", "StaffC<PERSON>r", "_c10", "Staff", "svg", "_c11", "Guest<PERSON><PERSON>ning", "_c12", "ScoreEditor", "scoreId", "_s", "currentUser", "title", "setTitle", "selectedTool", "setSelectedTool", "selectedDuration", "setSelectedDuration", "selected<PERSON><PERSON>", "setSelectedNote", "selectedAccidental", "setSelectedAccidental", "isPlaying", "setIsPlaying", "isSaving", "setIsSaving", "placedNotes", "setPlacedNotes", "isLoading", "setIsLoading", "showChords", "setShowChords", "showLyrics", "setShowLyrics", "lastSaved", "setLastSaved", "zoomLevel", "setZoomLevel", "lyrics", "setLyrics", "useEffect", "loadScore", "length", "autoSaveTimer", "setTimeout", "handleSave", "clearTimeout", "score", "getScore", "userId", "uid", "allNotes", "staffs", "for<PERSON>ach", "staff", "measures", "measure", "push", "notes", "error", "console", "isAutoSave", "alert", "measureMap", "Map", "note", "position", "has", "set", "get", "Array", "from", "entries", "map", "measureNumber", "id", "number", "timeSignature", "numerator", "denominator", "sort", "a", "b", "beat", "chords", "scoreData", "key", "mode", "tempo", "clef", "instrument", "updateData", "updateScore", "createScore", "Date", "handlePlay", "handleStaffClick", "event", "_event$currentTarget$", "rect", "currentTarget", "getBoundingClientRect", "svgRect", "closest", "x", "clientX", "left", "y", "clientY", "top", "staffLines", "notePositions", "octave", "closestPosition", "reduce", "current", "Math", "abs", "measureWidth", "startX", "relativeX", "max", "floor", "beatPosition", "newNote", "name", "duration", "accidental", "undefined", "round", "isRest", "prev", "durations", "children", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "type", "value", "onChange", "e", "target", "placeholder", "toLocaleTimeString", "onClick", "disabled", "min", "lineHeight", "filter", "n", "onLyricsChange", "marginBottom", "margin", "viewBox", "transform", "transform<PERSON><PERSON>in", "line", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "fill", "textAnchor", "width", "cursor", "onRemove", "_c13", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ScoreEditor/ScoreEditor.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Score, MusicalNote, NoteName, NoteDuration, ClefType, InstrumentType, Octave, Lyrics } from '../../types/music';\nimport { ScoreService } from '../../services/scoreService';\nimport { ChordView } from '../ChordView/ChordView';\nimport { MusicalNote as MusicalNoteComponent } from '../MusicalNote/MusicalNote';\nimport { LyricsEditor } from '../LyricsEditor/LyricsEditor';\nimport { v4 as uuidv4 } from 'uuid';\n\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n\nconst ToolButton = styled.button<{ active?: boolean }>`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n\ninterface ScoreEditorProps {\n  scoreId?: string;\n}\n\nexport const ScoreEditor: React.FC<ScoreEditorProps> = ({ scoreId }) => {\n  const { currentUser } = useAuth();\n  const [title, setTitle] = useState('Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState<'note' | 'rest' | 'chord'>('note');\n  const [selectedDuration, setSelectedDuration] = useState<NoteDuration>('quarter');\n  const [selectedNote, setSelectedNote] = useState<NoteName>('C');\n  const [selectedAccidental, setSelectedAccidental] = useState<'sharp' | 'flat' | 'natural' | null>(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [placedNotes, setPlacedNotes] = useState<MusicalNote[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showChords, setShowChords] = useState(false);\n  const [showLyrics, setShowLyrics] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\n  const [zoomLevel, setZoomLevel] = useState(1);\n  const [lyrics, setLyrics] = useState<Lyrics[]>([]);\n\n  // Carregar partitura existente\n  React.useEffect(() => {\n    if (scoreId && currentUser) {\n      loadScore();\n    }\n  }, [scoreId, currentUser]);\n\n  // Salvamento automático\n  React.useEffect(() => {\n    if (!currentUser || !scoreId || placedNotes.length === 0) return;\n\n    const autoSaveTimer = setTimeout(() => {\n      handleSave(true); // true indica salvamento automático\n    }, 2000); // Salvar após 2 segundos de inatividade\n\n    return () => clearTimeout(autoSaveTimer);\n  }, [placedNotes, title, currentUser, scoreId]);\n\n  const loadScore = async () => {\n    if (!scoreId || !currentUser) return;\n\n    setIsLoading(true);\n    try {\n      const score = await ScoreService.getScore(scoreId);\n      if (score && score.userId === currentUser.uid) {\n        setTitle(score.title);\n\n        // Extrair notas de todos os compassos\n        const allNotes: MusicalNote[] = [];\n        score.staffs.forEach(staff => {\n          staff.measures.forEach(measure => {\n            allNotes.push(...measure.notes);\n          });\n        });\n\n        setPlacedNotes(allNotes);\n        setLyrics(score.lyrics || []);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar partitura:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSave = async (isAutoSave = false) => {\n    if (!currentUser) {\n      if (!isAutoSave) {\n        alert('Você precisa estar logado para salvar partituras!');\n      }\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      // Organizar notas por compasso\n      const measureMap = new Map<number, MusicalNote[]>();\n      placedNotes.forEach(note => {\n        const measure = note.position.measure;\n        if (!measureMap.has(measure)) {\n          measureMap.set(measure, []);\n        }\n        measureMap.get(measure)!.push(note);\n      });\n\n      // Criar compassos com as notas\n      const measures = Array.from(measureMap.entries()).map(([measureNumber, notes]) => ({\n        id: uuidv4(),\n        number: measureNumber,\n        timeSignature: { numerator: 4, denominator: 4 },\n        notes: notes.sort((a, b) => a.position.beat - b.position.beat),\n        chords: []\n      }));\n\n      // Criar dados da partitura\n      const scoreData = {\n        title,\n        key: { note: 'C' as NoteName, mode: 'major' as const },\n        timeSignature: { numerator: 4, denominator: 4 },\n        tempo: 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: 'treble' as ClefType,\n          instrument: 'piano' as InstrumentType,\n          measures\n        }],\n        lyrics,\n        userId: currentUser.uid\n      };\n\n      if (scoreId) {\n        // Para update, removemos o userId pois não deve ser alterado\n        const { userId, ...updateData } = scoreData;\n        await ScoreService.updateScore(scoreId, updateData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n\n      setLastSaved(new Date());\n      if (!isAutoSave) {\n        alert('Partitura salva com sucesso!');\n      }\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      if (!isAutoSave) {\n        alert('Erro ao salvar partitura');\n      }\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n\n  const handleStaffClick = (event: React.MouseEvent<SVGRectElement>) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const svgRect = event.currentTarget.closest('svg')?.getBoundingClientRect();\n\n    if (!svgRect) return;\n\n    const x = event.clientX - svgRect.left;\n    const y = event.clientY - svgRect.top;\n\n    // Calcular a linha da pauta baseada na posição Y\n    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta\n    const notePositions = [\n      { y: 40, note: 'A' as NoteName, octave: 5 as Octave }, // Acima da pauta\n      { y: 50, note: 'G' as NoteName, octave: 5 as Octave }, // 5ª linha\n      { y: 60, note: 'F' as NoteName, octave: 5 as Octave }, // Entre 4ª e 5ª\n      { y: 70, note: 'E' as NoteName, octave: 5 as Octave }, // 4ª linha\n      { y: 80, note: 'D' as NoteName, octave: 5 as Octave }, // Entre 3ª e 4ª\n      { y: 90, note: 'C' as NoteName, octave: 5 as Octave }, // 3ª linha (Dó central)\n      { y: 100, note: 'B' as NoteName, octave: 4 as Octave }, // Entre 2ª e 3ª\n      { y: 110, note: 'A' as NoteName, octave: 4 as Octave }, // 2ª linha\n      { y: 120, note: 'G' as NoteName, octave: 4 as Octave }, // Entre 1ª e 2ª\n      { y: 130, note: 'F' as NoteName, octave: 4 as Octave }, // 1ª linha\n      { y: 140, note: 'E' as NoteName, octave: 4 as Octave }, // Abaixo da pauta\n    ];\n\n    // Encontrar a posição mais próxima\n    const closestPosition = notePositions.reduce((closest, current) => {\n      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;\n    });\n\n    // Calcular a posição horizontal (compasso e tempo)\n    const measureWidth = 150; // Largura aproximada de um compasso\n    const startX = 120; // Início da área de notas\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = ((relativeX % measureWidth) / measureWidth) * 4; // 4 tempos por compasso\n\n    // Criar nova nota\n    const newNote: MusicalNote = {\n      id: uuidv4(),\n      name: selectedNote, // Usar a nota selecionada no painel\n      octave: closestPosition.octave,\n      duration: selectedDuration,\n      accidental: selectedAccidental || undefined,\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4, // Arredondar para 1/4 de tempo\n        staff: 0\n      },\n      isRest: selectedTool === 'rest'\n    };\n\n    setPlacedNotes(prev => [...prev, newNote]);\n  };\n\n  const durations: NoteDuration[] = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes: NoteName[] = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n\n  if (isLoading) {\n    return (\n      <EditorContainer>\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '400px',\n          fontSize: '1.2rem',\n          color: '#666'\n        }}>\n          🎼 Carregando partitura...\n        </div>\n      </EditorContainer>\n    );\n  }\n\n  return (\n    <EditorContainer>\n      <EditorHeader>\n        <EditorTitle>\n          <h2>Editor de Partituras</h2>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n            <input\n              type=\"text\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              placeholder=\"Nome da partitura...\"\n            />\n            {currentUser && lastSaved && (\n              <div style={{ fontSize: '0.8rem', color: 'rgba(255,255,255,0.8)' }}>\n                💾 Salvo às {lastSaved.toLocaleTimeString()}\n              </div>\n            )}\n          </div>\n        </EditorTitle>\n        <EditorActions>\n          <ActionButton\n            onClick={() => setZoomLevel(prev => Math.max(0.5, prev - 0.1))}\n            variant=\"primary\"\n            disabled={zoomLevel <= 0.5}\n          >\n            🔍➖\n          </ActionButton>\n          <ActionButton\n            onClick={() => setZoomLevel(prev => Math.min(2, prev + 0.1))}\n            variant=\"primary\"\n            disabled={zoomLevel >= 2}\n          >\n            🔍➕\n          </ActionButton>\n          <ActionButton\n            onClick={() => {\n              setShowChords(!showChords);\n              setShowLyrics(false);\n            }}\n            variant=\"primary\"\n          >\n            {showChords ? '🎼 Partitura' : '🎸 Cifras'}\n          </ActionButton>\n          <ActionButton\n            onClick={() => {\n              setShowLyrics(!showLyrics);\n              setShowChords(false);\n            }}\n            variant=\"primary\"\n          >\n            {showLyrics ? '🎼 Partitura' : '🎤 Letras'}\n          </ActionButton>\n          <ActionButton\n            onClick={() => setPlacedNotes([])}\n            variant=\"primary\"\n            disabled={placedNotes.length === 0}\n          >\n            🗑️ Limpar\n          </ActionButton>\n          <ActionButton onClick={handlePlay} variant=\"primary\">\n            {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}\n          </ActionButton>\n          <ActionButton\n            onClick={handleSave}\n            variant=\"secondary\"\n            disabled={!currentUser || isSaving}\n          >\n            {isSaving ? '💾 Salvando...' : '💾 Salvar'}\n          </ActionButton>\n        </EditorActions>\n      </EditorHeader>\n\n      <EditorContent>\n        <ToolPanel>\n          {!currentUser && (\n            <GuestWarning>\n              ⚠️ Modo visitante: suas alterações não serão salvas\n            </GuestWarning>\n          )}\n          \n          <ToolSection>\n            <h3>🎵 Ferramentas</h3>\n            <ToolGrid>\n              <ToolButton \n                active={selectedTool === 'note'}\n                onClick={() => setSelectedTool('note')}\n              >\n                🎵 Nota\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'rest'}\n                onClick={() => setSelectedTool('rest')}\n              >\n                🎼 Pausa\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'chord'}\n                onClick={() => setSelectedTool('chord')}\n              >\n                🎹 Acorde\n              </ToolButton>\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>⏱️ Duração</h3>\n            <ToolGrid>\n              {durations.map(duration => (\n                <ToolButton\n                  key={duration}\n                  active={selectedDuration === duration}\n                  onClick={() => setSelectedDuration(duration)}\n                >\n                  {duration === 'whole' ? '𝅝' : \n                   duration === 'half' ? '𝅗𝅥' :\n                   duration === 'quarter' ? '♩' :\n                   duration === 'eighth' ? '♫' : '♬'}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>🎼 Notas</h3>\n            <ToolGrid>\n              {notes.map(note => (\n                <ToolButton\n                  key={note}\n                  active={selectedNote === note}\n                  onClick={() => setSelectedNote(note)}\n                >\n                  {note}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>♯♭ Acidentes</h3>\n            <ToolGrid>\n              <ToolButton\n                active={selectedAccidental === 'sharp'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'sharp' ? null : 'sharp')}\n              >\n                ♯\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === 'flat'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'flat' ? null : 'flat')}\n              >\n                ♭\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === 'natural'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'natural' ? null : 'natural')}\n              >\n                ♮\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === null}\n                onClick={() => setSelectedAccidental(null)}\n              >\n                —\n              </ToolButton>\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>📊 Estatísticas</h3>\n            <div style={{ fontSize: '0.9rem', color: '#666', lineHeight: '1.4' }}>\n              <div>📝 Notas: {placedNotes.filter(n => !n.isRest).length}</div>\n              <div>⏸️ Pausas: {placedNotes.filter(n => n.isRest).length}</div>\n              <div>🎵 Total: {placedNotes.length}</div>\n              <div>📏 Compassos: {Math.max(0, ...placedNotes.map(n => n.position.measure), 0)}</div>\n              <div>♯ Sustenidos: {placedNotes.filter(n => n.accidental === 'sharp').length}</div>\n              <div>♭ Bemóis: {placedNotes.filter(n => n.accidental === 'flat').length}</div>\n              <div>🔍 Zoom: {Math.round(zoomLevel * 100)}%</div>\n            </div>\n          </ToolSection>\n        </ToolPanel>\n\n        <ScoreCanvas>\n          {showChords ? (\n            <ChordView notes={placedNotes} title={title} />\n          ) : showLyrics ? (\n            <LyricsEditor\n              lyrics={lyrics}\n              notes={placedNotes}\n              onLyricsChange={setLyrics}\n              title={title}\n            />\n          ) : (\n            <StaffContainer>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>\n                <h3 style={{ margin: 0, color: '#495057' }}>Partitura: {title}</h3>\n                <div style={{ fontSize: '0.9rem', color: '#666' }}>\n                  Zoom: {Math.round(zoomLevel * 100)}%\n                </div>\n              </div>\n              <Staff\n                viewBox=\"0 0 800 200\"\n                style={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}\n              >\n              {/* Linhas da pauta */}\n              {[0, 1, 2, 3, 4].map(line => (\n                <line\n                  key={line}\n                  x1=\"50\"\n                  y1={50 + line * 20}\n                  x2=\"750\"\n                  y2={50 + line * 20}\n                  stroke=\"#333\"\n                  strokeWidth=\"1\"\n                />\n              ))}\n              \n              {/* Clave de Sol */}\n              <text x=\"20\" y=\"90\" fontSize=\"40\" fill=\"#333\">𝄞</text>\n              \n              {/* Compasso 4/4 */}\n              <text x=\"80\" y=\"75\" fontSize=\"16\" fill=\"#333\">4</text>\n              <text x=\"80\" y=\"95\" fontSize=\"16\" fill=\"#333\">4</text>\n              \n              {/* Linha divisória inicial */}\n              <line x1=\"110\" y1=\"50\" x2=\"110\" y2=\"130\" stroke=\"#333\" strokeWidth=\"2\"/>\n\n              {/* Divisões de compasso */}\n              {[1, 2, 3, 4].map(measure => (\n                <line\n                  key={measure}\n                  x1={120 + (measure * 150)}\n                  y1=\"50\"\n                  x2={120 + (measure * 150)}\n                  y2=\"130\"\n                  stroke=\"#999\"\n                  strokeWidth=\"1\"\n                />\n              ))}\n\n              {/* Números dos compassos */}\n              {[1, 2, 3, 4].map(measure => (\n                <text\n                  key={measure}\n                  x={120 + ((measure - 1) * 150) + 75}\n                  y=\"35\"\n                  fontSize=\"12\"\n                  fill=\"#666\"\n                  textAnchor=\"middle\"\n                >\n                  {measure}\n                </text>\n              ))}\n              \n              {/* Área clicável para adicionar notas */}\n              <rect\n                x=\"120\"\n                y=\"40\"\n                width=\"600\"\n                height=\"100\"\n                fill=\"transparent\"\n                style={{ cursor: 'crosshair' }}\n                onClick={handleStaffClick}\n              />\n\n              {/* Renderizar notas colocadas */}\n              {placedNotes.map((note) => {\n                const x = 120 + ((note.position.measure - 1) * 150) + (note.position.beat * 30);\n                const notePositions: { [key: string]: number } = {\n                  'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,\n                  'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140\n                };\n                const y = notePositions[`${note.name}${note.octave}`] || 90;\n\n                return (\n                  <MusicalNoteComponent\n                    key={note.id}\n                    note={note}\n                    x={x}\n                    y={y}\n                    onRemove={() => setPlacedNotes(prev => prev.filter(n => n.id !== note.id))}\n                  />\n                );\n              })}\n\n              {/* Instruções */}\n              {placedNotes.length === 0 && (\n                <text x=\"400\" y=\"100\" fontSize=\"14\" fill=\"#999\" textAnchor=\"middle\">\n                  Clique na pauta para adicionar notas • Clique em uma nota para removê-la\n                </text>\n              )}\n            </Staff>\n          </StaffContainer>\n          )}\n        </ScoreCanvas>\n      </EditorContent>\n    </EditorContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAgB,OAAO;AAC/C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AAEpD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,WAAW,IAAIC,oBAAoB,QAAQ,4BAA4B;AAChF,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGX,MAAM,CAACY,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,eAAe;AAUrB,MAAMG,YAAY,GAAGd,MAAM,CAACY,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,YAAY;AASlB,MAAME,WAAW,GAAGhB,MAAM,CAACY,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GA1BID,WAAW;AA4BjB,MAAME,aAAa,GAAGlB,MAAM,CAACY,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGpB,MAAM,CAACqB,MAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhCIJ,YAAY;AAkClB,MAAMK,aAAa,GAAGzB,MAAM,CAACY,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,aAAa;AAMnB,MAAME,SAAS,GAAG3B,MAAM,CAACY,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GANID,SAAS;AAQf,MAAME,WAAW,GAAG7B,MAAM,CAACY,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GATID,WAAW;AAWjB,MAAME,QAAQ,GAAG/B,MAAM,CAACY,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,QAAQ;AAMd,MAAME,UAAU,GAAGjC,MAAM,CAACqB,MAA4B;AACtD;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D,WAAWZ,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,OAAO,GAAG,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMZ,KAAK,IAAI,CAACA,KAAK,CAACY,MAAM,IAAI,sBAAsB;AACtD;AACA,CAAC;AAACC,GAAA,GAdIF,UAAU;AAgBhB,MAAMG,WAAW,GAAGpC,MAAM,CAACY,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GARID,WAAW;AAUjB,MAAME,cAAc,GAAGtC,MAAM,CAACY,GAAG;AACjC;AACA;AACA,CAAC;AAAC2B,IAAA,GAHID,cAAc;AAKpB,MAAME,KAAK,GAAGxC,MAAM,CAACyC,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAPIF,KAAK;AASX,MAAMG,YAAY,GAAG3C,MAAM,CAACY,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GATID,YAAY;AAelB,OAAO,MAAME,WAAuC,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM;IAAEC;EAAY,CAAC,GAAG/C,OAAO,CAAC,CAAC;EACjC,MAAM,CAACgD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,gBAAgB,CAAC;EACpD,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAA4B,MAAM,CAAC;EACnF,MAAM,CAACsD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAe,SAAS,CAAC;EACjF,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAW,GAAG,CAAC;EAC/D,MAAM,CAAC0D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,QAAQ,CAAsC,IAAI,CAAC;EACvG,MAAM,CAAC4D,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8D,QAAQ,EAAEC,WAAW,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAgB,EAAE,CAAC;EACjE,MAAM,CAACkE,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwE,SAAS,EAAEC,YAAY,CAAC,GAAGzE,QAAQ,CAAc,IAAI,CAAC;EAC7D,MAAM,CAAC0E,SAAS,EAAEC,YAAY,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC4E,MAAM,EAAEC,SAAS,CAAC,GAAG7E,QAAQ,CAAW,EAAE,CAAC;;EAElD;EACAD,KAAK,CAAC+E,SAAS,CAAC,MAAM;IACpB,IAAI/B,OAAO,IAAIE,WAAW,EAAE;MAC1B8B,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAAChC,OAAO,EAAEE,WAAW,CAAC,CAAC;;EAE1B;EACAlD,KAAK,CAAC+E,SAAS,CAAC,MAAM;IACpB,IAAI,CAAC7B,WAAW,IAAI,CAACF,OAAO,IAAIiB,WAAW,CAACgB,MAAM,KAAK,CAAC,EAAE;IAE1D,MAAMC,aAAa,GAAGC,UAAU,CAAC,MAAM;MACrCC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMC,YAAY,CAACH,aAAa,CAAC;EAC1C,CAAC,EAAE,CAACjB,WAAW,EAAEd,KAAK,EAAED,WAAW,EAAEF,OAAO,CAAC,CAAC;EAE9C,MAAMgC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAAChC,OAAO,IAAI,CAACE,WAAW,EAAE;IAE9BkB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMkB,KAAK,GAAG,MAAMlF,YAAY,CAACmF,QAAQ,CAACvC,OAAO,CAAC;MAClD,IAAIsC,KAAK,IAAIA,KAAK,CAACE,MAAM,KAAKtC,WAAW,CAACuC,GAAG,EAAE;QAC7CrC,QAAQ,CAACkC,KAAK,CAACnC,KAAK,CAAC;;QAErB;QACA,MAAMuC,QAAuB,GAAG,EAAE;QAClCJ,KAAK,CAACK,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;UAC5BA,KAAK,CAACC,QAAQ,CAACF,OAAO,CAACG,OAAO,IAAI;YAChCL,QAAQ,CAACM,IAAI,CAAC,GAAGD,OAAO,CAACE,KAAK,CAAC;UACjC,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF/B,cAAc,CAACwB,QAAQ,CAAC;QACxBZ,SAAS,CAACQ,KAAK,CAACT,MAAM,IAAI,EAAE,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACR9B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMgB,UAAU,GAAG,MAAAA,CAAOgB,UAAU,GAAG,KAAK,KAAK;IAC/C,IAAI,CAAClD,WAAW,EAAE;MAChB,IAAI,CAACkD,UAAU,EAAE;QACfC,KAAK,CAAC,mDAAmD,CAAC;MAC5D;MACA;IACF;IAEArC,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF;MACA,MAAMsC,UAAU,GAAG,IAAIC,GAAG,CAAwB,CAAC;MACnDtC,WAAW,CAAC2B,OAAO,CAACY,IAAI,IAAI;QAC1B,MAAMT,OAAO,GAAGS,IAAI,CAACC,QAAQ,CAACV,OAAO;QACrC,IAAI,CAACO,UAAU,CAACI,GAAG,CAACX,OAAO,CAAC,EAAE;UAC5BO,UAAU,CAACK,GAAG,CAACZ,OAAO,EAAE,EAAE,CAAC;QAC7B;QACAO,UAAU,CAACM,GAAG,CAACb,OAAO,CAAC,CAAEC,IAAI,CAACQ,IAAI,CAAC;MACrC,CAAC,CAAC;;MAEF;MACA,MAAMV,QAAQ,GAAGe,KAAK,CAACC,IAAI,CAACR,UAAU,CAACS,OAAO,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,aAAa,EAAEhB,KAAK,CAAC,MAAM;QACjFiB,EAAE,EAAExG,MAAM,CAAC,CAAC;QACZyG,MAAM,EAAEF,aAAa;QACrBG,aAAa,EAAE;UAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;QAC/CrB,KAAK,EAAEA,KAAK,CAACsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACf,QAAQ,CAACiB,IAAI,GAAGD,CAAC,CAAChB,QAAQ,CAACiB,IAAI,CAAC;QAC9DC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMC,SAAS,GAAG;QAChBzE,KAAK;QACL0E,GAAG,EAAE;UAAErB,IAAI,EAAE,GAAe;UAAEsB,IAAI,EAAE;QAAiB,CAAC;QACtDV,aAAa,EAAE;UAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;QAC/CS,KAAK,EAAE,GAAG;QACVpC,MAAM,EAAE,CAAC;UACPuB,EAAE,EAAExG,MAAM,CAAC,CAAC;UACZsH,IAAI,EAAE,QAAoB;UAC1BC,UAAU,EAAE,OAAyB;UACrCnC;QACF,CAAC,CAAC;QACFjB,MAAM;QACNW,MAAM,EAAEtC,WAAW,CAACuC;MACtB,CAAC;MAED,IAAIzC,OAAO,EAAE;QACX;QACA,MAAM;UAAEwC,MAAM;UAAE,GAAG0C;QAAW,CAAC,GAAGN,SAAS;QAC3C,MAAMxH,YAAY,CAAC+H,WAAW,CAACnF,OAAO,EAAEkF,UAAU,CAAC;MACrD,CAAC,MAAM;QACL,MAAM9H,YAAY,CAACgI,WAAW,CAACR,SAAS,EAAE1E,WAAW,CAACuC,GAAG,CAAC;MAC5D;MAEAf,YAAY,CAAC,IAAI2D,IAAI,CAAC,CAAC,CAAC;MACxB,IAAI,CAACjC,UAAU,EAAE;QACfC,KAAK,CAAC,8BAA8B,CAAC;MACvC;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,IAAI,CAACE,UAAU,EAAE;QACfC,KAAK,CAAC,0BAA0B,CAAC;MACnC;IACF,CAAC,SAAS;MACRrC,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMsE,UAAU,GAAGA,CAAA,KAAM;IACvBxE,YAAY,CAAC,CAACD,SAAS,CAAC;IACxB;EACF,CAAC;EAED,MAAM0E,gBAAgB,GAAIC,KAAuC,IAAK;IAAA,IAAAC,qBAAA;IACpE,MAAMC,IAAI,GAAGF,KAAK,CAACG,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,OAAO,IAAAJ,qBAAA,GAAGD,KAAK,CAACG,aAAa,CAACG,OAAO,CAAC,KAAK,CAAC,cAAAL,qBAAA,uBAAlCA,qBAAA,CAAoCG,qBAAqB,CAAC,CAAC;IAE3E,IAAI,CAACC,OAAO,EAAE;IAEd,MAAME,CAAC,GAAGP,KAAK,CAACQ,OAAO,GAAGH,OAAO,CAACI,IAAI;IACtC,MAAMC,CAAC,GAAGV,KAAK,CAACW,OAAO,GAAGN,OAAO,CAACO,GAAG;;IAErC;IACA,MAAMC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3C,MAAMC,aAAa,GAAG,CACpB;MAAEJ,CAAC,EAAE,EAAE;MAAE1C,IAAI,EAAE,GAAe;MAAE+C,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE1C,IAAI,EAAE,GAAe;MAAE+C,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE1C,IAAI,EAAE,GAAe;MAAE+C,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE1C,IAAI,EAAE,GAAe;MAAE+C,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE1C,IAAI,EAAE,GAAe;MAAE+C,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE1C,IAAI,EAAE,GAAe;MAAE+C,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,GAAG;MAAE1C,IAAI,EAAE,GAAe;MAAE+C,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE1C,IAAI,EAAE,GAAe;MAAE+C,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE1C,IAAI,EAAE,GAAe;MAAE+C,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE1C,IAAI,EAAE,GAAe;MAAE+C,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE1C,IAAI,EAAE,GAAe;MAAE+C,MAAM,EAAE;IAAY,CAAC,CAAE;IAAA,CACzD;;IAED;IACA,MAAMC,eAAe,GAAGF,aAAa,CAACG,MAAM,CAAC,CAACX,OAAO,EAAEY,OAAO,KAAK;MACjE,OAAOC,IAAI,CAACC,GAAG,CAACF,OAAO,CAACR,CAAC,GAAGA,CAAC,CAAC,GAAGS,IAAI,CAACC,GAAG,CAACd,OAAO,CAACI,CAAC,GAAGA,CAAC,CAAC,GAAGQ,OAAO,GAAGZ,OAAO;IAC9E,CAAC,CAAC;;IAEF;IACA,MAAMe,YAAY,GAAG,GAAG,CAAC,CAAC;IAC1B,MAAMC,MAAM,GAAG,GAAG,CAAC,CAAC;IACpB,MAAMC,SAAS,GAAGhB,CAAC,GAAGe,MAAM;IAC5B,MAAM/D,OAAO,GAAG4D,IAAI,CAACK,GAAG,CAAC,CAAC,EAAEL,IAAI,CAACM,KAAK,CAACF,SAAS,GAAGF,YAAY,CAAC,GAAG,CAAC,CAAC;IACrE,MAAMK,YAAY,GAAKH,SAAS,GAAGF,YAAY,GAAIA,YAAY,GAAI,CAAC,CAAC,CAAC;;IAEtE;IACA,MAAMM,OAAoB,GAAG;MAC3BjD,EAAE,EAAExG,MAAM,CAAC,CAAC;MACZ0J,IAAI,EAAE3G,YAAY;MAAE;MACpB8F,MAAM,EAAEC,eAAe,CAACD,MAAM;MAC9Bc,QAAQ,EAAE9G,gBAAgB;MAC1B+G,UAAU,EAAE3G,kBAAkB,IAAI4G,SAAS;MAC3C9D,QAAQ,EAAE;QACRV,OAAO;QACP2B,IAAI,EAAEiC,IAAI,CAACa,KAAK,CAACN,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;QAAE;QACxCrE,KAAK,EAAE;MACT,CAAC;MACD4E,MAAM,EAAEpH,YAAY,KAAK;IAC3B,CAAC;IAEDa,cAAc,CAACwG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEP,OAAO,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMQ,SAAyB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC;EACrF,MAAM1E,KAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAE7D,IAAI9B,SAAS,EAAE;IACb,oBACEvD,OAAA,CAACC,eAAe;MAAA+J,QAAA,eACdhK,OAAA;QAAKiK,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE,OAAO;UACfC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;QACT,CAAE;QAAAP,QAAA,EAAC;MAEH;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC;EAEtB;EAEA,oBACE3K,OAAA,CAACC,eAAe;IAAA+J,QAAA,gBACdhK,OAAA,CAACI,YAAY;MAAA4J,QAAA,gBACXhK,OAAA,CAACM,WAAW;QAAA0J,QAAA,gBACVhK,OAAA;UAAAgK,QAAA,EAAI;QAAoB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7B3K,OAAA;UAAKiK,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEQ,GAAG,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBACjEhK,OAAA;YACE6K,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEvI,KAAM;YACbwI,QAAQ,EAAGC,CAAC,IAAKxI,QAAQ,CAACwI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC1CI,WAAW,EAAC;UAAsB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,EACDrI,WAAW,IAAIuB,SAAS,iBACvB7D,OAAA;YAAKiK,KAAK,EAAE;cAAEK,QAAQ,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAwB,CAAE;YAAAP,QAAA,GAAC,2BACtD,EAACnG,SAAS,CAACsH,kBAAkB,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd3K,OAAA,CAACQ,aAAa;QAAAwJ,QAAA,gBACZhK,OAAA,CAACU,YAAY;UACX0K,OAAO,EAAEA,CAAA,KAAMpH,YAAY,CAAC8F,IAAI,IAAIf,IAAI,CAACK,GAAG,CAAC,GAAG,EAAEU,IAAI,GAAG,GAAG,CAAC,CAAE;UAC/DjJ,OAAO,EAAC,SAAS;UACjBwK,QAAQ,EAAEtH,SAAS,IAAI,GAAI;UAAAiG,QAAA,EAC5B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACf3K,OAAA,CAACU,YAAY;UACX0K,OAAO,EAAEA,CAAA,KAAMpH,YAAY,CAAC8F,IAAI,IAAIf,IAAI,CAACuC,GAAG,CAAC,CAAC,EAAExB,IAAI,GAAG,GAAG,CAAC,CAAE;UAC7DjJ,OAAO,EAAC,SAAS;UACjBwK,QAAQ,EAAEtH,SAAS,IAAI,CAAE;UAAAiG,QAAA,EAC1B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACf3K,OAAA,CAACU,YAAY;UACX0K,OAAO,EAAEA,CAAA,KAAM;YACb1H,aAAa,CAAC,CAACD,UAAU,CAAC;YAC1BG,aAAa,CAAC,KAAK,CAAC;UACtB,CAAE;UACF/C,OAAO,EAAC,SAAS;UAAAmJ,QAAA,EAEhBvG,UAAU,GAAG,cAAc,GAAG;QAAW;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACf3K,OAAA,CAACU,YAAY;UACX0K,OAAO,EAAEA,CAAA,KAAM;YACbxH,aAAa,CAAC,CAACD,UAAU,CAAC;YAC1BD,aAAa,CAAC,KAAK,CAAC;UACtB,CAAE;UACF7C,OAAO,EAAC,SAAS;UAAAmJ,QAAA,EAEhBrG,UAAU,GAAG,cAAc,GAAG;QAAW;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACf3K,OAAA,CAACU,YAAY;UACX0K,OAAO,EAAEA,CAAA,KAAM9H,cAAc,CAAC,EAAE,CAAE;UAClCzC,OAAO,EAAC,SAAS;UACjBwK,QAAQ,EAAEhI,WAAW,CAACgB,MAAM,KAAK,CAAE;UAAA2F,QAAA,EACpC;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACf3K,OAAA,CAACU,YAAY;UAAC0K,OAAO,EAAE1D,UAAW;UAAC7G,OAAO,EAAC,SAAS;UAAAmJ,QAAA,EACjD/G,SAAS,GAAG,WAAW,GAAG;QAAe;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACf3K,OAAA,CAACU,YAAY;UACX0K,OAAO,EAAE5G,UAAW;UACpB3D,OAAO,EAAC,WAAW;UACnBwK,QAAQ,EAAE,CAAC/I,WAAW,IAAIa,QAAS;UAAA6G,QAAA,EAElC7G,QAAQ,GAAG,gBAAgB,GAAG;QAAW;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEf3K,OAAA,CAACe,aAAa;MAAAiJ,QAAA,gBACZhK,OAAA,CAACiB,SAAS;QAAA+I,QAAA,GACP,CAAC1H,WAAW,iBACXtC,OAAA,CAACiC,YAAY;UAAA+H,QAAA,EAAC;QAEd;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CACf,eAED3K,OAAA,CAACmB,WAAW;UAAA6I,QAAA,gBACVhK,OAAA;YAAAgK,QAAA,EAAI;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB3K,OAAA,CAACqB,QAAQ;YAAA2I,QAAA,gBACPhK,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,MAAO;cAChC2I,OAAO,EAAEA,CAAA,KAAM1I,eAAe,CAAC,MAAM,CAAE;cAAAsH,QAAA,EACxC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3K,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,MAAO;cAChC2I,OAAO,EAAEA,CAAA,KAAM1I,eAAe,CAAC,MAAM,CAAE;cAAAsH,QAAA,EACxC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3K,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,OAAQ;cACjC2I,OAAO,EAAEA,CAAA,KAAM1I,eAAe,CAAC,OAAO,CAAE;cAAAsH,QAAA,EACzC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEd3K,OAAA,CAACmB,WAAW;UAAA6I,QAAA,gBACVhK,OAAA;YAAAgK,QAAA,EAAI;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB3K,OAAA,CAACqB,QAAQ;YAAA2I,QAAA,EACND,SAAS,CAAC3D,GAAG,CAACqD,QAAQ,iBACrBzJ,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEmB,gBAAgB,KAAK8G,QAAS;cACtC2B,OAAO,EAAEA,CAAA,KAAMxI,mBAAmB,CAAC6G,QAAQ,CAAE;cAAAO,QAAA,EAE5CP,QAAQ,KAAK,OAAO,GAAG,IAAI,GAC3BA,QAAQ,KAAK,MAAM,GAAG,MAAM,GAC5BA,QAAQ,KAAK,SAAS,GAAG,GAAG,GAC5BA,QAAQ,KAAK,QAAQ,GAAG,GAAG,GAAG;YAAG,GAP7BA,QAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQH,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEd3K,OAAA,CAACmB,WAAW;UAAA6I,QAAA,gBACVhK,OAAA;YAAAgK,QAAA,EAAI;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjB3K,OAAA,CAACqB,QAAQ;YAAA2I,QAAA,EACN3E,KAAK,CAACe,GAAG,CAACR,IAAI,iBACb5F,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEqB,YAAY,KAAK+C,IAAK;cAC9BwF,OAAO,EAAEA,CAAA,KAAMtI,eAAe,CAAC8C,IAAI,CAAE;cAAAoE,QAAA,EAEpCpE;YAAI,GAJAA,IAAI;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKC,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEd3K,OAAA,CAACmB,WAAW;UAAA6I,QAAA,gBACVhK,OAAA;YAAAgK,QAAA,EAAI;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrB3K,OAAA,CAACqB,QAAQ;YAAA2I,QAAA,gBACPhK,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEuB,kBAAkB,KAAK,OAAQ;cACvCqI,OAAO,EAAEA,CAAA,KAAMpI,qBAAqB,CAACD,kBAAkB,KAAK,OAAO,GAAG,IAAI,GAAG,OAAO,CAAE;cAAAiH,QAAA,EACvF;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3K,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEuB,kBAAkB,KAAK,MAAO;cACtCqI,OAAO,EAAEA,CAAA,KAAMpI,qBAAqB,CAACD,kBAAkB,KAAK,MAAM,GAAG,IAAI,GAAG,MAAM,CAAE;cAAAiH,QAAA,EACrF;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3K,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEuB,kBAAkB,KAAK,SAAU;cACzCqI,OAAO,EAAEA,CAAA,KAAMpI,qBAAqB,CAACD,kBAAkB,KAAK,SAAS,GAAG,IAAI,GAAG,SAAS,CAAE;cAAAiH,QAAA,EAC3F;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3K,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEuB,kBAAkB,KAAK,IAAK;cACpCqI,OAAO,EAAEA,CAAA,KAAMpI,qBAAqB,CAAC,IAAI,CAAE;cAAAgH,QAAA,EAC5C;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEd3K,OAAA,CAACmB,WAAW;UAAA6I,QAAA,gBACVhK,OAAA;YAAAgK,QAAA,EAAI;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB3K,OAAA;YAAKiK,KAAK,EAAE;cAAEK,QAAQ,EAAE,QAAQ;cAAEC,KAAK,EAAE,MAAM;cAAEgB,UAAU,EAAE;YAAM,CAAE;YAAAvB,QAAA,gBACnEhK,OAAA;cAAAgK,QAAA,GAAK,sBAAU,EAAC3G,WAAW,CAACmI,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC5B,MAAM,CAAC,CAACxF,MAAM;YAAA;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChE3K,OAAA;cAAAgK,QAAA,GAAK,uBAAW,EAAC3G,WAAW,CAACmI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5B,MAAM,CAAC,CAACxF,MAAM;YAAA;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChE3K,OAAA;cAAAgK,QAAA,GAAK,sBAAU,EAAC3G,WAAW,CAACgB,MAAM;YAAA;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzC3K,OAAA;cAAAgK,QAAA,GAAK,0BAAc,EAACjB,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE,GAAG/F,WAAW,CAAC+C,GAAG,CAACqF,CAAC,IAAIA,CAAC,CAAC5F,QAAQ,CAACV,OAAO,CAAC,EAAE,CAAC,CAAC;YAAA;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtF3K,OAAA;cAAAgK,QAAA,GAAK,qBAAc,EAAC3G,WAAW,CAACmI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/B,UAAU,KAAK,OAAO,CAAC,CAACrF,MAAM;YAAA;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnF3K,OAAA;cAAAgK,QAAA,GAAK,oBAAU,EAAC3G,WAAW,CAACmI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/B,UAAU,KAAK,MAAM,CAAC,CAACrF,MAAM;YAAA;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9E3K,OAAA;cAAAgK,QAAA,GAAK,qBAAS,EAACjB,IAAI,CAACa,KAAK,CAAC7F,SAAS,GAAG,GAAG,CAAC,EAAC,GAAC;YAAA;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEZ3K,OAAA,CAAC0B,WAAW;QAAAsI,QAAA,EACTvG,UAAU,gBACTzD,OAAA,CAACP,SAAS;UAAC4F,KAAK,EAAEhC,WAAY;UAACd,KAAK,EAAEA;QAAM;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAC7ChH,UAAU,gBACZ3D,OAAA,CAACJ,YAAY;UACXqE,MAAM,EAAEA,MAAO;UACfoB,KAAK,EAAEhC,WAAY;UACnBqI,cAAc,EAAExH,SAAU;UAC1B3B,KAAK,EAAEA;QAAM;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,gBAEF3K,OAAA,CAAC4B,cAAc;UAAAoI,QAAA,gBACbhK,OAAA;YAAKiK,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE,QAAQ;cAAEuB,YAAY,EAAE;YAAO,CAAE;YAAA3B,QAAA,gBAC3GhK,OAAA;cAAIiK,KAAK,EAAE;gBAAE2B,MAAM,EAAE,CAAC;gBAAErB,KAAK,EAAE;cAAU,CAAE;cAAAP,QAAA,GAAC,aAAW,EAACzH,KAAK;YAAA;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnE3K,OAAA;cAAKiK,KAAK,EAAE;gBAAEK,QAAQ,EAAE,QAAQ;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAP,QAAA,GAAC,QAC3C,EAACjB,IAAI,CAACa,KAAK,CAAC7F,SAAS,GAAG,GAAG,CAAC,EAAC,GACrC;YAAA;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3K,OAAA,CAAC8B,KAAK;YACJ+J,OAAO,EAAC,aAAa;YACrB5B,KAAK,EAAE;cAAE6B,SAAS,EAAE,SAAS/H,SAAS,GAAG;cAAEgI,eAAe,EAAE;YAAW,CAAE;YAAA/B,QAAA,GAG1E,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC5D,GAAG,CAAC4F,IAAI,iBACvBhM,OAAA;cAEEiM,EAAE,EAAC,IAAI;cACPC,EAAE,EAAE,EAAE,GAAGF,IAAI,GAAG,EAAG;cACnBG,EAAE,EAAC,KAAK;cACRC,EAAE,EAAE,EAAE,GAAGJ,IAAI,GAAG,EAAG;cACnBK,MAAM,EAAC,MAAM;cACbC,WAAW,EAAC;YAAG,GANVN,IAAI;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV,CACF,CAAC,eAGF3K,OAAA;cAAMmI,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAACgC,QAAQ,EAAC,IAAI;cAACiC,IAAI,EAAC,MAAM;cAAAvC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGvD3K,OAAA;cAAMmI,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAACgC,QAAQ,EAAC,IAAI;cAACiC,IAAI,EAAC,MAAM;cAAAvC,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtD3K,OAAA;cAAMmI,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAACgC,QAAQ,EAAC,IAAI;cAACiC,IAAI,EAAC,MAAM;cAAAvC,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGtD3K,OAAA;cAAMiM,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,KAAK;cAACC,MAAM,EAAC,MAAM;cAACC,WAAW,EAAC;YAAG;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,EAGvE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACvE,GAAG,CAACjB,OAAO,iBACvBnF,OAAA;cAEEiM,EAAE,EAAE,GAAG,GAAI9G,OAAO,GAAG,GAAK;cAC1B+G,EAAE,EAAC,IAAI;cACPC,EAAE,EAAE,GAAG,GAAIhH,OAAO,GAAG,GAAK;cAC1BiH,EAAE,EAAC,KAAK;cACRC,MAAM,EAAC,MAAM;cACbC,WAAW,EAAC;YAAG,GANVnH,OAAO;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOb,CACF,CAAC,EAGD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACvE,GAAG,CAACjB,OAAO,iBACvBnF,OAAA;cAEEmI,CAAC,EAAE,GAAG,GAAI,CAAChD,OAAO,GAAG,CAAC,IAAI,GAAI,GAAG,EAAG;cACpCmD,CAAC,EAAC,IAAI;cACNgC,QAAQ,EAAC,IAAI;cACbiC,IAAI,EAAC,MAAM;cACXC,UAAU,EAAC,QAAQ;cAAAxC,QAAA,EAElB7E;YAAO,GAPHA,OAAO;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQR,CACP,CAAC,eAGF3K,OAAA;cACEmI,CAAC,EAAC,KAAK;cACPG,CAAC,EAAC,IAAI;cACNmE,KAAK,EAAC,KAAK;cACXpC,MAAM,EAAC,KAAK;cACZkC,IAAI,EAAC,aAAa;cAClBtC,KAAK,EAAE;gBAAEyC,MAAM,EAAE;cAAY,CAAE;cAC/BtB,OAAO,EAAEzD;YAAiB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,EAGDtH,WAAW,CAAC+C,GAAG,CAAER,IAAI,IAAK;cACzB,MAAMuC,CAAC,GAAG,GAAG,GAAI,CAACvC,IAAI,CAACC,QAAQ,CAACV,OAAO,GAAG,CAAC,IAAI,GAAI,GAAIS,IAAI,CAACC,QAAQ,CAACiB,IAAI,GAAG,EAAG;cAC/E,MAAM4B,aAAwC,GAAG;gBAC/C,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAChD,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE;cAC9D,CAAC;cACD,MAAMJ,CAAC,GAAGI,aAAa,CAAC,GAAG9C,IAAI,CAAC4D,IAAI,GAAG5D,IAAI,CAAC+C,MAAM,EAAE,CAAC,IAAI,EAAE;cAE3D,oBACE3I,OAAA,CAACL,oBAAoB;gBAEnBiG,IAAI,EAAEA,IAAK;gBACXuC,CAAC,EAAEA,CAAE;gBACLG,CAAC,EAAEA,CAAE;gBACLqE,QAAQ,EAAEA,CAAA,KAAMrJ,cAAc,CAACwG,IAAI,IAAIA,IAAI,CAAC0B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnF,EAAE,KAAKV,IAAI,CAACU,EAAE,CAAC;cAAE,GAJtEV,IAAI,CAACU,EAAE;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKb,CAAC;YAEN,CAAC,CAAC,EAGDtH,WAAW,CAACgB,MAAM,KAAK,CAAC,iBACvBrE,OAAA;cAAMmI,CAAC,EAAC,KAAK;cAACG,CAAC,EAAC,KAAK;cAACgC,QAAQ,EAAC,IAAI;cAACiC,IAAI,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAxC,QAAA,EAAC;YAEpE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MACf;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB,CAAC;AAACtI,EAAA,CA1fWF,WAAuC;EAAA,QAC1B5C,OAAO;AAAA;AAAAqN,IAAA,GADpBzK,WAAuC;AAAA,IAAAhC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAA0K,IAAA;AAAAC,YAAA,CAAA1M,EAAA;AAAA0M,YAAA,CAAAxM,GAAA;AAAAwM,YAAA,CAAAtM,GAAA;AAAAsM,YAAA,CAAApM,GAAA;AAAAoM,YAAA,CAAA/L,GAAA;AAAA+L,YAAA,CAAA7L,GAAA;AAAA6L,YAAA,CAAA3L,GAAA;AAAA2L,YAAA,CAAAzL,GAAA;AAAAyL,YAAA,CAAAvL,GAAA;AAAAuL,YAAA,CAAApL,GAAA;AAAAoL,YAAA,CAAAlL,GAAA;AAAAkL,YAAA,CAAAhL,IAAA;AAAAgL,YAAA,CAAA7K,IAAA;AAAA6K,YAAA,CAAA3K,IAAA;AAAA2K,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}