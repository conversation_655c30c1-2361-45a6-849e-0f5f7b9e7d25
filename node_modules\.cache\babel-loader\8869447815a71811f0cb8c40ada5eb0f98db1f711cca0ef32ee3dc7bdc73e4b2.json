{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { LoginPage } from './pages/Auth/LoginPage';\nimport { RegisterPage } from './pages/Auth/RegisterPage';\nimport { Layout } from './components/Layout/Layout';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  font-size: 1.2rem;\n  color: #666;\n`;\n_c = LoadingContainer;\nconst HomePage = styled.div`\n  text-align: center;\n  padding: 2rem;\n`;\n_c2 = HomePage;\nconst WelcomeTitle = styled.h1`\n  color: #2c3e50;\n  margin-bottom: 1rem;\n`;\n_c3 = WelcomeTitle;\nconst WelcomeText = styled.p`\n  color: #666;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  max-width: 600px;\n  margin: 0 auto;\n`;\n\n// Componente principal da aplicação autenticada\n_c4 = WelcomeText;\nconst AuthenticatedApp = () => {\n  _s();\n  const [currentPage, setCurrentPage] = useState('home');\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return /*#__PURE__*/_jsxDEV(HomePage, {\n          children: [/*#__PURE__*/_jsxDEV(WelcomeTitle, {\n            children: \"Bem-vindo ao Partitura Digital!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(WelcomeText, {\n            children: \"Crie e edite suas partituras musicais de forma intuitiva. Adicione notas, acordes, letras e muito mais. Visualize suas composi\\xE7\\xF5es tanto em nota\\xE7\\xE3o tradicional quanto em cifras.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this);\n      case 'scores':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Lista de Partituras (Em desenvolvimento)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 16\n        }, this);\n      case 'new-score':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Editor de Partituras (Em desenvolvimento)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"P\\xE1gina n\\xE3o encontrada\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    currentPage: currentPage,\n    onNavigate: setCurrentPage,\n    children: renderPage()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n\n// Componente de autenticação\n_s(AuthenticatedApp, \"KLlrbvIFn6o4dTsrFf/Szg7G3bM=\");\n_c5 = AuthenticatedApp;\nconst AuthApp = () => {\n  _s2();\n  const [isLogin, setIsLogin] = useState(true);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(FirebaseTest, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), isLogin ? /*#__PURE__*/_jsxDEV(LoginPage, {\n      onSwitchToRegister: () => setIsLogin(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(RegisterPage, {\n      onSwitchToLogin: () => setIsLogin(true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n\n// Componente principal que decide qual app mostrar\n_s2(AuthApp, \"juHMKC6x2j1wnRvCiB5VrABnZyE=\");\n_c6 = AuthApp;\nconst AppContent = () => {\n  _s3();\n  const {\n    currentUser,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingContainer, {\n      children: \"Carregando...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 12\n    }, this);\n  }\n  return currentUser ? /*#__PURE__*/_jsxDEV(AuthenticatedApp, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 24\n  }, this) : /*#__PURE__*/_jsxDEV(AuthApp, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 47\n  }, this);\n};\n\n// App principal com provider\n_s3(AppContent, \"+loUN5XsQVjYs/gtfuWkb9VBZ7Q=\", false, function () {\n  return [useAuth];\n});\n_c7 = AppContent;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n}\n_c8 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"LoadingContainer\");\n$RefreshReg$(_c2, \"HomePage\");\n$RefreshReg$(_c3, \"WelcomeTitle\");\n$RefreshReg$(_c4, \"WelcomeText\");\n$RefreshReg$(_c5, \"AuthenticatedApp\");\n$RefreshReg$(_c6, \"AuthApp\");\n$RefreshReg$(_c7, \"AppContent\");\n$RefreshReg$(_c8, \"App\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "LoginPage", "RegisterPage", "Layout", "styled", "jsxDEV", "_jsxDEV", "LoadingContainer", "div", "_c", "HomePage", "_c2", "WelcomeTitle", "h1", "_c3", "WelcomeText", "p", "_c4", "AuthenticatedApp", "_s", "currentPage", "setCurrentPage", "renderPage", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onNavigate", "_c5", "AuthApp", "_s2", "is<PERSON>ogin", "setIsLogin", "FirebaseTest", "onSwitchToRegister", "onSwitchToLogin", "_c6", "A<PERSON><PERSON><PERSON>nt", "_s3", "currentUser", "loading", "_c7", "App", "_c8", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { LoginPage } from './pages/Auth/LoginPage';\nimport { RegisterPage } from './pages/Auth/RegisterPage';\nimport { Layout } from './components/Layout/Layout';\nimport styled from 'styled-components';\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  font-size: 1.2rem;\n  color: #666;\n`;\n\nconst HomePage = styled.div`\n  text-align: center;\n  padding: 2rem;\n`;\n\nconst WelcomeTitle = styled.h1`\n  color: #2c3e50;\n  margin-bottom: 1rem;\n`;\n\nconst WelcomeText = styled.p`\n  color: #666;\n  font-size: 1.1rem;\n  line-height: 1.6;\n  max-width: 600px;\n  margin: 0 auto;\n`;\n\n// Componente principal da aplicação autenticada\nconst AuthenticatedApp: React.FC = () => {\n  const [currentPage, setCurrentPage] = useState('home');\n\n  const renderPage = () => {\n    switch (currentPage) {\n      case 'home':\n        return (\n          <HomePage>\n            <WelcomeTitle>Bem-vindo ao Partitura Digital!</WelcomeTitle>\n            <WelcomeText>\n              Crie e edite suas partituras musicais de forma intuitiva.\n              Adicione notas, acordes, letras e muito mais.\n              Visualize suas composições tanto em notação tradicional quanto em cifras.\n            </WelcomeText>\n          </HomePage>\n        );\n      case 'scores':\n        return <div>Lista de Partituras (Em desenvolvimento)</div>;\n      case 'new-score':\n        return <div>Editor de Partituras (Em desenvolvimento)</div>;\n      default:\n        return <div>Página não encontrada</div>;\n    }\n  };\n\n  return (\n    <Layout currentPage={currentPage} onNavigate={setCurrentPage}>\n      {renderPage()}\n    </Layout>\n  );\n};\n\n// Componente de autenticação\nconst AuthApp: React.FC = () => {\n  const [isLogin, setIsLogin] = useState(true);\n\n  return (\n    <div>\n      <FirebaseTest />\n      {isLogin ? (\n        <LoginPage onSwitchToRegister={() => setIsLogin(false)} />\n      ) : (\n        <RegisterPage onSwitchToLogin={() => setIsLogin(true)} />\n      )}\n    </div>\n  );\n};\n\n// Componente principal que decide qual app mostrar\nconst AppContent: React.FC = () => {\n  const { currentUser, loading } = useAuth();\n\n  if (loading) {\n    return <LoadingContainer>Carregando...</LoadingContainer>;\n  }\n\n  return currentUser ? <AuthenticatedApp /> : <AuthApp />;\n};\n\n// App principal com provider\nfunction App() {\n  return (\n    <AuthProvider>\n      <AppContent />\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,MAAM,QAAQ,4BAA4B;AACnD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,gBAAgB,GAAGH,MAAM,CAACI,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,gBAAgB;AAStB,MAAMG,QAAQ,GAAGN,MAAM,CAACI,GAAG;AAC3B;AACA;AACA,CAAC;AAACG,GAAA,GAHID,QAAQ;AAKd,MAAME,YAAY,GAAGR,MAAM,CAACS,EAAE;AAC9B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,YAAY;AAKlB,MAAMG,WAAW,GAAGX,MAAM,CAACY,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GARMF,WAAW;AASjB,MAAMG,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,MAAM,CAAC;EAEtD,MAAMwB,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQF,WAAW;MACjB,KAAK,MAAM;QACT,oBACEd,OAAA,CAACI,QAAQ;UAAAa,QAAA,gBACPjB,OAAA,CAACM,YAAY;YAAAW,QAAA,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eAC5DrB,OAAA,CAACS,WAAW;YAAAQ,QAAA,EAAC;UAIb;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEf,KAAK,QAAQ;QACX,oBAAOrB,OAAA;UAAAiB,QAAA,EAAK;QAAwC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC5D,KAAK,WAAW;QACd,oBAAOrB,OAAA;UAAAiB,QAAA,EAAK;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC7D;QACE,oBAAOrB,OAAA;UAAAiB,QAAA,EAAK;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IAC3C;EACF,CAAC;EAED,oBACErB,OAAA,CAACH,MAAM;IAACiB,WAAW,EAAEA,WAAY;IAACQ,UAAU,EAAEP,cAAe;IAAAE,QAAA,EAC1DD,UAAU,CAAC;EAAC;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEb,CAAC;;AAED;AAAAR,EAAA,CAhCMD,gBAA0B;AAAAW,GAAA,GAA1BX,gBAA0B;AAiChC,MAAMY,OAAiB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC9B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAE5C,oBACEQ,OAAA;IAAAiB,QAAA,gBACEjB,OAAA,CAAC4B,YAAY;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACfK,OAAO,gBACN1B,OAAA,CAACL,SAAS;MAACkC,kBAAkB,EAAEA,CAAA,KAAMF,UAAU,CAAC,KAAK;IAAE;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAE1DrB,OAAA,CAACJ,YAAY;MAACkC,eAAe,EAAEA,CAAA,KAAMH,UAAU,CAAC,IAAI;IAAE;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACzD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAI,GAAA,CAfMD,OAAiB;AAAAO,GAAA,GAAjBP,OAAiB;AAgBvB,MAAMQ,UAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACjC,MAAM;IAAEC,WAAW;IAAEC;EAAQ,CAAC,GAAGzC,OAAO,CAAC,CAAC;EAE1C,IAAIyC,OAAO,EAAE;IACX,oBAAOnC,OAAA,CAACC,gBAAgB;MAAAgB,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAkB,CAAC;EAC3D;EAEA,OAAOa,WAAW,gBAAGlC,OAAA,CAACY,gBAAgB;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,gBAAGrB,OAAA,CAACwB,OAAO;IAAAN,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACzD,CAAC;;AAED;AAAAY,GAAA,CAVMD,UAAoB;EAAA,QACStC,OAAO;AAAA;AAAA0C,GAAA,GADpCJ,UAAoB;AAW1B,SAASK,GAAGA,CAAA,EAAG;EACb,oBACErC,OAAA,CAACP,YAAY;IAAAwB,QAAA,eACXjB,OAAA,CAACgC,UAAU;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB;AAACiB,GAAA,GANQD,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAlC,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAY,GAAA,EAAAQ,GAAA,EAAAK,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAApC,EAAA;AAAAoC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}