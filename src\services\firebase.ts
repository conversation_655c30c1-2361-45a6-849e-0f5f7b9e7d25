import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Configuração do Firebase
// IMPORTANTE: Substitua essas configurações pelas suas próprias do Firebase Console
// const firebaseConfig = {
//   apiKey: "your-api-key-here",
//   authDomain: "your-project-id.firebaseapp.com",
//   projectId: "your-project-id",
//   storageBucket: "your-project-id.appspot.com",
//   messagingSenderId: "your-sender-id",
//   appId: "your-app-id"
// };

const firebaseConfig = {
  apiKey: "AIzaSyBUJQhRgzloPxM9BiWXY_JITyVPmuqplnA",
  authDomain: "partitura-digital.firebaseapp.com",
  projectId: "partitura-digital",
  storageBucket: "partitura-digital.firebasestorage.app",
  messagingSenderId: "235102958549",
  appId: "1:235102958549:web:3e6a5f95d492bc31e9835c",
  measurementId: "G-KQVMBPPNKB"
};

// Inicializar Firebase
const app = initializeApp(firebaseConfig);

// Inicializar serviços
export const auth = getAuth(app);
export const db = getFirestore(app);

export default app;
