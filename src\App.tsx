import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LoginPage } from './pages/Auth/LoginPage';
import { RegisterPage } from './pages/Auth/RegisterPage';
import { Layout } from './components/Layout/Layout';
import { FirebaseTest } from './components/FirebaseTest';
import styled from 'styled-components';

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.2rem;
  color: #666;
`;

const HomePage = styled.div`
  text-align: center;
  padding: 2rem;
`;

const WelcomeTitle = styled.h1`
  color: #2c3e50;
  margin-bottom: 1rem;
`;

const WelcomeText = styled.p`
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
`;

// Componente principal da aplicação autenticada
const AuthenticatedApp: React.FC = () => {
  const [currentPage, setCurrentPage] = useState('home');

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return (
          <HomePage>
            <WelcomeTitle>Bem-vindo ao Partitura Digital!</WelcomeTitle>
            <WelcomeText>
              Crie e edite suas partituras musicais de forma intuitiva.
              Adicione notas, acordes, letras e muito mais.
              Visualize suas composições tanto em notação tradicional quanto em cifras.
            </WelcomeText>
          </HomePage>
        );
      case 'scores':
        return <div>Lista de Partituras (Em desenvolvimento)</div>;
      case 'new-score':
        return <div>Editor de Partituras (Em desenvolvimento)</div>;
      default:
        return <div>Página não encontrada</div>;
    }
  };

  return (
    <Layout currentPage={currentPage} onNavigate={setCurrentPage}>
      {renderPage()}
    </Layout>
  );
};

// Componente de autenticação
const AuthApp: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true);

  return (
    <div>
      <FirebaseTest />
      {isLogin ? (
        <LoginPage onSwitchToRegister={() => setIsLogin(false)} />
      ) : (
        <RegisterPage onSwitchToLogin={() => setIsLogin(true)} />
      )}
    </div>
  );
};

// Componente principal que decide qual app mostrar
const AppContent: React.FC = () => {
  const { currentUser, loading } = useAuth();

  if (loading) {
    return <LoadingContainer>Carregando...</LoadingContainer>;
  }

  return currentUser ? <AuthenticatedApp /> : <AuthApp />;
};

// App principal com provider
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
