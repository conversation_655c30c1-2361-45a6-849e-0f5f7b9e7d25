import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LoginPage } from './pages/Auth/LoginPage';
import { RegisterPage } from './pages/Auth/RegisterPage';
import { Layout } from './components/Layout/Layout';
import { ScoreEditor } from './components/ScoreEditor/ScoreEditor';
import { ScoresList } from './pages/ScoresList/ScoresList';
import { NewScorePage, ScoreConfig } from './pages/NewScore/NewScorePage';
import styled from 'styled-components';

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.2rem;
  color: #666;
`;

const HomePage = styled.div`
  text-align: center;
  padding: 2rem;
`;

const WelcomeTitle = styled.h1`
  color: #2c3e50;
  margin-bottom: 1rem;
`;

const WelcomeText = styled.p`
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto 2rem auto;
`;

const GuestNotice = styled.div`
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 2rem auto;
  max-width: 500px;
  text-align: center;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);

  p {
    margin: 0.5rem 0;
    color: #856404;
  }

  strong {
    color: #533f03;
  }
`;

// Componente principal da aplicação
const MainApp: React.FC = () => {
  const [currentPage, setCurrentPage] = useState('home');
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [editingScoreId, setEditingScoreId] = useState<string | null>(null);
  const [scoreConfig, setScoreConfig] = useState<ScoreConfig | null>(null);
  const { currentUser } = useAuth();

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return (
          <HomePage>
            <WelcomeTitle>🎵 Bem-vindo ao Partitura Digital!</WelcomeTitle>
            <WelcomeText>
              Crie e edite suas partituras musicais de forma intuitiva.
              Adicione notas, acordes, letras e muito mais.
              Visualize suas composições tanto em notação tradicional quanto em cifras.
            </WelcomeText>
            {!currentUser && (
              <GuestNotice>
                <p>💡 <strong>Modo Visitante:</strong> Você pode usar o editor, mas suas partituras não serão salvas.</p>
                <p>Faça login para salvar suas criações!</p>
              </GuestNotice>
            )}
          </HomePage>
        );
      case 'scores':
        return (
          <ScoresList
            onCreateNew={() => {
              setEditingScoreId(null);
              setScoreConfig(null);
              setCurrentPage('new-score-setup');
            }}
            onEditScore={(scoreId) => {
              setEditingScoreId(scoreId);
              setScoreConfig(null);
              setCurrentPage('score-editor');
            }}
          />
        );
      case 'new-score':
        // Redirecionar para setup se não há configuração
        if (!scoreConfig) {
          setCurrentPage('new-score-setup');
          return null;
        }
        return <ScoreEditor scoreId={editingScoreId || undefined} initialConfig={scoreConfig} />;
      case 'new-score-setup':
        return (
          <NewScorePage
            onCreateScore={(config) => {
              setScoreConfig(config);
              setCurrentPage('new-score');
            }}
            onCancel={() => setCurrentPage('home')}
          />
        );
      case 'score-editor':
        return <ScoreEditor scoreId={editingScoreId || undefined} />;
      case 'auth':
        return authMode === 'login' ? (
          <LoginPage onSwitchToRegister={() => setAuthMode('register')} />
        ) : (
          <RegisterPage onSwitchToLogin={() => setAuthMode('login')} />
        );
      default:
        return <div>Página não encontrada</div>;
    }
  };

  const handleNavigate = (page: string) => {
    if (page === 'login') {
      setAuthMode('login');
      setCurrentPage('auth');
    } else if (page === 'register') {
      setAuthMode('register');
      setCurrentPage('auth');
    } else {
      setCurrentPage(page);
    }
  };

  return (
    <Layout
      currentPage={currentPage}
      onNavigate={handleNavigate}
      showAuthInSidebar={!currentUser}
    >
      {renderPage()}
    </Layout>
  );
};

// Componente principal que gerencia o estado de loading
const AppContent: React.FC = () => {
  const { loading } = useAuth();

  if (loading) {
    return <LoadingContainer>Carregando...</LoadingContainer>;
  }

  return <MainApp />;
};

// App principal com provider
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
