# 🎵 Partitura Digital

Um sistema completo e moderno para criação e manipulação de partituras musicais, desenvolvido com React e Firebase.

## ✨ Funcionalidades Principais

- **🎨 Interface Moderna** - Design responsivo com glassmorphism e gradientes
- **🔐 Autenticação Flexível** - Login opcional, modo visitante disponível
- **🎼 Editor de Partituras** - Interface intuitiva para criação musical
- **💾 Salvamento na Nuvem** - Suas partituras ficam seguras no Firebase
- **📱 Responsivo** - Funciona perfeitamente em desktop e mobile
- **🎹 Múltiplos Instrumentos** - Suporte para piano, violão, violino e mais
- **🎵 Notação Tradicional** - Visualização em pauta musical clássica
- **🎸 Sistema de Cifras** - Alternativa de visualização em acordes

## 🚀 Tecnologias Utilizadas

- **React 18** com TypeScript
- **Firebase** (Authentication + Firestore)
- **Styled Components** para estilização
- **React Icons** para ícones
- **UUID** para identificadores únicos

## 📋 Pré-requisitos

- Node.js (versão 16 ou superior)
- npm ou yarn
- Conta no Firebase (opcional para modo visitante)

## ⚙️ Configuração e Instalação

### 1. Clone o repositório
```bash
git clone <url-do-repositorio>
cd partitura_digital
```

### 2. Instale as dependências
```bash
npm install
```

### 3. Configure o Firebase (Opcional)
Para salvar partituras, siga o arquivo `FIREBASE_SETUP.md`

### 4. Execute o projeto
```bash
npm start
```

### 5. Acesse a aplicação
Abra [http://localhost:3000](http://localhost:3000) no navegador

## 🎯 Status do Projeto

### ✅ Funcionalidades Implementadas:

- [x] **Configuração inicial** - React + TypeScript + Firebase
- [x] **Sistema de autenticação** - Login/registro com Firebase Auth
- [x] **Interface moderna** - Design responsivo com gradientes
- [x] **Modo visitante** - Acesso sem login (sem salvamento)
- [x] **Editor de partituras** - Interface básica com ferramentas
- [x] **Gerenciamento de partituras** - Listar, editar, duplicar, excluir
- [x] **Sistema de salvamento** - Integração completa com Firestore
- [x] **Estrutura de dados** - Tipos TypeScript para partituras

### 🚧 Próximas Funcionalidades:

- [ ] **Adição interativa de notas** - Clique para adicionar notas
- [ ] **Sistema de cifras** - Visualização em acordes
- [ ] **Editor de letras** - Sincronização com notas
- [ ] **Reprodução de áudio** - Playback das partituras
- [ ] **Exportação para PDF** - Download das partituras
- [ ] **Templates de instrumentos** - Piano, violão, etc.

## 📁 Estrutura do Projeto

```
src/
├── components/          # Componentes reutilizáveis
│   ├── Layout/         # Layout principal da aplicação
│   └── ScoreEditor/    # Editor de partituras
├── contexts/           # Contextos React (Auth)
├── pages/              # Páginas da aplicação
│   ├── Auth/          # Login e Registro
│   └── ScoresList/    # Lista de partituras
├── services/           # Serviços (Firebase, APIs)
├── types/              # Definições TypeScript
└── utils/              # Utilitários e ícones
```

## 🎼 Como Usar

### Modo Visitante
1. Acesse a aplicação
2. Clique em "Criar Partitura"
3. Use o editor livremente
4. ⚠️ Suas alterações não serão salvas

### Modo Logado
1. Faça login ou crie uma conta
2. Acesse "Minhas Partituras" para ver suas criações
3. Clique em "Criar Partitura" para começar
4. Suas partituras são salvas automaticamente na nuvem

## 🛠️ Desenvolvimento

### Scripts Disponíveis

- `npm start` - Inicia o servidor de desenvolvimento
- `npm build` - Cria build de produção
- `npm test` - Executa os testes
- `npm eject` - Ejeta a configuração (irreversível)

### Contribuindo

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo LICENSE para mais detalhes.

## 🤝 Suporte

Se encontrar problemas ou tiver sugestões:
- Abra uma issue no GitHub
- Consulte o arquivo `FIREBASE_SETUP.md` para problemas de configuração

---

**Desenvolvido com ❤️ para músicos e compositores**
