{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\AdvancedLyricsEditor\\\\AdvancedLyricsEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EditorContainer = styled.div`\n  background: white;\n  border-radius: 16px;\n  padding: 1.5rem;\n  box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n  margin: 1rem 0;\n`;\n_c = EditorContainer;\nconst EditorHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n  border-bottom: 2px solid #e9ecef;\n  padding-bottom: 1rem;\n`;\n_c2 = EditorHeader;\nconst Title = styled.h3`\n  color: #2c3e50;\n  margin: 0;\n  font-size: 1.3rem;\n`;\n_c3 = Title;\nconst ModeToggle = styled.div`\n  display: flex;\n  background: #f8f9fa;\n  border-radius: 25px;\n  padding: 0.25rem;\n`;\n_c4 = ModeToggle;\nconst ModeButton = styled.button`\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 20px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.active ? `\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n  ` : `\n    background: transparent;\n    color: #495057;\n    &:hover {\n      background: #e9ecef;\n    }\n  `}\n`;\n_c5 = ModeButton;\nconst LyricsInput = styled.div`\n  margin-bottom: 1.5rem;\n`;\n_c6 = LyricsInput;\nconst InputLabel = styled.label`\n  display: block;\n  font-weight: 600;\n  color: #495057;\n  margin-bottom: 0.5rem;\n`;\n_c7 = InputLabel;\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 120px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  font-size: 1rem;\n  line-height: 1.5;\n  resize: vertical;\n  transition: border-color 0.3s ease;\n  \n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n  \n  &::placeholder {\n    color: #adb5bd;\n  }\n`;\n_c8 = TextArea;\nconst SyllableInput = styled.input`\n  width: 100%;\n  padding: 0.75rem 1rem;\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n  \n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n`;\n_c9 = SyllableInput;\nconst PositionControls = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n`;\n_c0 = PositionControls;\nconst ControlGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`;\n_c1 = ControlGroup;\nconst NumberInput = styled.input`\n  padding: 0.5rem;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  text-align: center;\n  font-weight: 600;\n  \n  &:focus {\n    outline: none;\n    border-color: #667eea;\n  }\n`;\n_c10 = NumberInput;\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n`;\n_c11 = ActionButtons;\nconst ActionButton = styled.button`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 50px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n    }\n  ` : `\n    background: #f8f9fa;\n    color: #495057;\n    border: 2px solid #e9ecef;\n    &:hover {\n      background: #e9ecef;\n    }\n  `}\n`;\n_c12 = ActionButton;\nconst LyricsList = styled.div`\n  max-height: 300px;\n  overflow-y: auto;\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  padding: 1rem;\n  margin-top: 1rem;\n`;\n_c13 = LyricsList;\nconst LyricItem = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem;\n  border-bottom: 1px solid #f1f3f4;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n_c14 = LyricItem;\nconst LyricText = styled.div`\n  font-weight: 600;\n  color: #2c3e50;\n`;\n_c15 = LyricText;\nconst LyricPosition = styled.div`\n  font-size: 0.9rem;\n  color: #6c757d;\n`;\n_c16 = LyricPosition;\nconst DeleteButton = styled.button`\n  background: #dc3545;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  &:hover {\n    background: #c82333;\n  }\n`;\n_c17 = DeleteButton;\nexport const AdvancedLyricsEditor = ({\n  lyrics,\n  onLyricsAdd,\n  onLyricsRemove,\n  onLyricsClear\n}) => {\n  _s();\n  const [mode, setMode] = useState('bulk');\n  const [bulkText, setBulkText] = useState('');\n  const [currentSyllable, setCurrentSyllable] = useState('');\n  const [currentPosition, setCurrentPosition] = useState({\n    measure: 1,\n    beat: 1,\n    staff: 0\n  });\n  const handleBulkProcess = () => {\n    if (!bulkText.trim()) return;\n    const words = bulkText.trim().split(/\\s+/);\n    let measure = 1;\n    let beat = 1;\n    words.forEach(word => {\n      const syllables = word.split('-');\n      syllables.forEach(syllable => {\n        const lyric = {\n          text: syllable,\n          position: {\n            measure,\n            beat,\n            staff: 0\n          }\n        };\n        onLyricsAdd(lyric);\n\n        // Avançar para próxima posição\n        beat++;\n        if (beat > 4) {\n          beat = 1;\n          measure++;\n        }\n      });\n    });\n    setBulkText('');\n  };\n  const handleIndividualAdd = () => {\n    if (!currentSyllable.trim()) return;\n    const lyric = {\n      text: currentSyllable.trim(),\n      position: {\n        measure: currentPosition.measure,\n        beat: currentPosition.beat,\n        staff: currentPosition.staff\n      }\n    };\n    onLyricsAdd(lyric);\n    setCurrentSyllable('');\n\n    // Auto-avançar posição\n    setCurrentPosition(prev => ({\n      ...prev,\n      beat: prev.beat < 4 ? prev.beat + 1 : 1,\n      measure: prev.beat < 4 ? prev.measure : prev.measure + 1\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(EditorContainer, {\n    children: [/*#__PURE__*/_jsxDEV(EditorHeader, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: \"Editor de Letras\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ModeToggle, {\n        children: [/*#__PURE__*/_jsxDEV(ModeButton, {\n          active: mode === 'bulk',\n          onClick: () => setMode('bulk'),\n          children: \"Texto Completo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ModeButton, {\n          active: mode === 'individual',\n          onClick: () => setMode('individual'),\n          children: \"S\\xEDlaba por S\\xEDlaba\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), mode === 'bulk' ? /*#__PURE__*/_jsxDEV(LyricsInput, {\n      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n        children: \"Cole ou digite a letra completa:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n        value: bulkText,\n        onChange: e => setBulkText(e.target.value),\n        placeholder: \"Digite a letra da m\\xFAsica aqui. Use h\\xEDfens para separar s\\xEDlabas (ex: can-ta-rei) e espa\\xE7os para separar palavras.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ActionButtons, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setBulkText(''),\n          children: \"Limpar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          variant: \"primary\",\n          onClick: handleBulkProcess,\n          children: \"Processar Letra\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(LyricsInput, {\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          children: \"S\\xEDlaba atual:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(SyllableInput, {\n          value: currentSyllable,\n          onChange: e => setCurrentSyllable(e.target.value),\n          placeholder: \"Digite uma s\\xEDlaba...\",\n          onKeyPress: e => e.key === 'Enter' && handleIndividualAdd()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(PositionControls, {\n        children: [/*#__PURE__*/_jsxDEV(ControlGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Compasso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NumberInput, {\n            type: \"number\",\n            min: \"1\",\n            value: currentPosition.measure,\n            onChange: e => setCurrentPosition(prev => ({\n              ...prev,\n              measure: parseInt(e.target.value) || 1\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ControlGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Tempo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NumberInput, {\n            type: \"number\",\n            min: \"1\",\n            max: \"4\",\n            value: currentPosition.beat,\n            onChange: e => setCurrentPosition(prev => ({\n              ...prev,\n              beat: parseInt(e.target.value) || 1\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ControlGroup, {\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Pauta\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NumberInput, {\n            type: \"number\",\n            min: \"0\",\n            value: currentPosition.staff,\n            onChange: e => setCurrentPosition(prev => ({\n              ...prev,\n              staff: parseInt(e.target.value) || 0\n            }))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ActionButtons, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setCurrentSyllable(''),\n          children: \"Limpar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          variant: \"primary\",\n          onClick: handleIndividualAdd,\n          children: \"Adicionar S\\xEDlaba\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), lyrics.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginTop: '1.5rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            margin: 0,\n            color: '#2c3e50'\n          },\n          children: [\"Letras Adicionadas (\", lyrics.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: onLyricsClear,\n          children: \"Limpar Todas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(LyricsList, {\n        children: lyrics.map((lyric, index) => /*#__PURE__*/_jsxDEV(LyricItem, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(LyricText, {\n              children: lyric.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(LyricPosition, {\n              children: [\"Compasso \", lyric.position.measure, \", Tempo \", lyric.position.beat, lyric.position.staff && lyric.position.staff > 0 && `, Pauta ${lyric.position.staff + 1}`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(DeleteButton, {\n            onClick: () => onLyricsRemove(index),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 288,\n    columnNumber: 5\n  }, this);\n};\n_s(AdvancedLyricsEditor, \"n9ucyIT8Zb5MJ+elfuCACr0xf70=\");\n_c18 = AdvancedLyricsEditor;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"EditorContainer\");\n$RefreshReg$(_c2, \"EditorHeader\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"ModeToggle\");\n$RefreshReg$(_c5, \"ModeButton\");\n$RefreshReg$(_c6, \"LyricsInput\");\n$RefreshReg$(_c7, \"InputLabel\");\n$RefreshReg$(_c8, \"TextArea\");\n$RefreshReg$(_c9, \"SyllableInput\");\n$RefreshReg$(_c0, \"PositionControls\");\n$RefreshReg$(_c1, \"ControlGroup\");\n$RefreshReg$(_c10, \"NumberInput\");\n$RefreshReg$(_c11, \"ActionButtons\");\n$RefreshReg$(_c12, \"ActionButton\");\n$RefreshReg$(_c13, \"LyricsList\");\n$RefreshReg$(_c14, \"LyricItem\");\n$RefreshReg$(_c15, \"LyricText\");\n$RefreshReg$(_c16, \"LyricPosition\");\n$RefreshReg$(_c17, \"DeleteButton\");\n$RefreshReg$(_c18, \"AdvancedLyricsEditor\");", "map": {"version": 3, "names": ["React", "useState", "styled", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EditorC<PERSON><PERSON>", "div", "_c", "Editor<PERSON><PERSON>er", "_c2", "Title", "h3", "_c3", "ModeToggle", "_c4", "ModeButton", "button", "props", "active", "_c5", "LyricsInput", "_c6", "InputLabel", "label", "_c7", "TextArea", "textarea", "_c8", "SyllableInput", "input", "_c9", "PositionControls", "_c0", "ControlGroup", "_c1", "NumberInput", "_c10", "ActionButtons", "_c11", "ActionButton", "variant", "_c12", "LyricsList", "_c13", "LyricItem", "_c14", "LyricText", "_c15", "LyricPosition", "_c16", "DeleteButton", "_c17", "AdvancedLyricsEditor", "lyrics", "onLyricsAdd", "onLyricsRemove", "onLyricsClear", "_s", "mode", "setMode", "bulkText", "setBulkText", "currentSyllable", "setCurrentSyllable", "currentPosition", "setCurrentPosition", "measure", "beat", "staff", "handleBulkProcess", "trim", "words", "split", "for<PERSON>ach", "word", "syllables", "syllable", "lyric", "text", "position", "handleIndividualAdd", "prev", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "value", "onChange", "e", "target", "placeholder", "onKeyPress", "key", "type", "min", "parseInt", "max", "length", "style", "display", "justifyContent", "alignItems", "marginTop", "margin", "color", "map", "index", "_c18", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/AdvancedLyricsEditor/AdvancedLyricsEditor.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { Lyrics } from '../../types/music';\n\nconst EditorContainer = styled.div`\n  background: white;\n  border-radius: 16px;\n  padding: 1.5rem;\n  box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n  margin: 1rem 0;\n`;\n\nconst EditorHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n  border-bottom: 2px solid #e9ecef;\n  padding-bottom: 1rem;\n`;\n\nconst Title = styled.h3`\n  color: #2c3e50;\n  margin: 0;\n  font-size: 1.3rem;\n`;\n\nconst ModeToggle = styled.div`\n  display: flex;\n  background: #f8f9fa;\n  border-radius: 25px;\n  padding: 0.25rem;\n`;\n\nconst ModeButton = styled.button<{ active: boolean }>`\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 20px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.active ? `\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n  ` : `\n    background: transparent;\n    color: #495057;\n    &:hover {\n      background: #e9ecef;\n    }\n  `}\n`;\n\nconst LyricsInput = styled.div`\n  margin-bottom: 1.5rem;\n`;\n\nconst InputLabel = styled.label`\n  display: block;\n  font-weight: 600;\n  color: #495057;\n  margin-bottom: 0.5rem;\n`;\n\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 120px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  font-size: 1rem;\n  line-height: 1.5;\n  resize: vertical;\n  transition: border-color 0.3s ease;\n  \n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n  \n  &::placeholder {\n    color: #adb5bd;\n  }\n`;\n\nconst SyllableInput = styled.input`\n  width: 100%;\n  padding: 0.75rem 1rem;\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n  \n  &:focus {\n    outline: none;\n    border-color: #667eea;\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n  }\n`;\n\nconst PositionControls = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n`;\n\nconst ControlGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n`;\n\nconst NumberInput = styled.input`\n  padding: 0.5rem;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  text-align: center;\n  font-weight: 600;\n  \n  &:focus {\n    outline: none;\n    border-color: #667eea;\n  }\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 50px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n    }\n  ` : `\n    background: #f8f9fa;\n    color: #495057;\n    border: 2px solid #e9ecef;\n    &:hover {\n      background: #e9ecef;\n    }\n  `}\n`;\n\nconst LyricsList = styled.div`\n  max-height: 300px;\n  overflow-y: auto;\n  border: 2px solid #e9ecef;\n  border-radius: 12px;\n  padding: 1rem;\n  margin-top: 1rem;\n`;\n\nconst LyricItem = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem;\n  border-bottom: 1px solid #f1f3f4;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n\nconst LyricText = styled.div`\n  font-weight: 600;\n  color: #2c3e50;\n`;\n\nconst LyricPosition = styled.div`\n  font-size: 0.9rem;\n  color: #6c757d;\n`;\n\nconst DeleteButton = styled.button`\n  background: #dc3545;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  &:hover {\n    background: #c82333;\n  }\n`;\n\ninterface AdvancedLyricsEditorProps {\n  lyrics: Lyrics[];\n  onLyricsAdd: (lyric: Lyrics) => void;\n  onLyricsRemove: (index: number) => void;\n  onLyricsClear: () => void;\n}\n\nexport const AdvancedLyricsEditor: React.FC<AdvancedLyricsEditorProps> = ({\n  lyrics,\n  onLyricsAdd,\n  onLyricsRemove,\n  onLyricsClear\n}) => {\n  const [mode, setMode] = useState<'bulk' | 'individual'>('bulk');\n  const [bulkText, setBulkText] = useState('');\n  const [currentSyllable, setCurrentSyllable] = useState('');\n  const [currentPosition, setCurrentPosition] = useState({\n    measure: 1,\n    beat: 1,\n    staff: 0\n  });\n\n  const handleBulkProcess = () => {\n    if (!bulkText.trim()) return;\n\n    const words = bulkText.trim().split(/\\s+/);\n    let measure = 1;\n    let beat = 1;\n\n    words.forEach((word) => {\n      const syllables = word.split('-');\n      \n      syllables.forEach((syllable) => {\n        const lyric: Lyrics = {\n          text: syllable,\n          position: {\n            measure,\n            beat,\n            staff: 0\n          }\n        };\n        \n        onLyricsAdd(lyric);\n        \n        // Avançar para próxima posição\n        beat++;\n        if (beat > 4) {\n          beat = 1;\n          measure++;\n        }\n      });\n    });\n\n    setBulkText('');\n  };\n\n  const handleIndividualAdd = () => {\n    if (!currentSyllable.trim()) return;\n\n    const lyric: Lyrics = {\n      text: currentSyllable.trim(),\n      position: {\n        measure: currentPosition.measure,\n        beat: currentPosition.beat,\n        staff: currentPosition.staff\n      }\n    };\n\n    onLyricsAdd(lyric);\n    setCurrentSyllable('');\n    \n    // Auto-avançar posição\n    setCurrentPosition(prev => ({\n      ...prev,\n      beat: prev.beat < 4 ? prev.beat + 1 : 1,\n      measure: prev.beat < 4 ? prev.measure : prev.measure + 1\n    }));\n  };\n\n  return (\n    <EditorContainer>\n      <EditorHeader>\n        <Title>Editor de Letras</Title>\n        <ModeToggle>\n          <ModeButton \n            active={mode === 'bulk'} \n            onClick={() => setMode('bulk')}\n          >\n            Texto Completo\n          </ModeButton>\n          <ModeButton \n            active={mode === 'individual'} \n            onClick={() => setMode('individual')}\n          >\n            Sílaba por Sílaba\n          </ModeButton>\n        </ModeToggle>\n      </EditorHeader>\n\n      {mode === 'bulk' ? (\n        <LyricsInput>\n          <InputLabel>Cole ou digite a letra completa:</InputLabel>\n          <TextArea\n            value={bulkText}\n            onChange={(e) => setBulkText(e.target.value)}\n            placeholder=\"Digite a letra da música aqui. Use hífens para separar sílabas (ex: can-ta-rei) e espaços para separar palavras.\"\n          />\n          <ActionButtons>\n            <ActionButton onClick={() => setBulkText('')}>\n              Limpar\n            </ActionButton>\n            <ActionButton variant=\"primary\" onClick={handleBulkProcess}>\n              Processar Letra\n            </ActionButton>\n          </ActionButtons>\n        </LyricsInput>\n      ) : (\n        <>\n          <LyricsInput>\n            <InputLabel>Sílaba atual:</InputLabel>\n            <SyllableInput\n              value={currentSyllable}\n              onChange={(e) => setCurrentSyllable(e.target.value)}\n              placeholder=\"Digite uma sílaba...\"\n              onKeyPress={(e) => e.key === 'Enter' && handleIndividualAdd()}\n            />\n          </LyricsInput>\n\n          <PositionControls>\n            <ControlGroup>\n              <InputLabel>Compasso</InputLabel>\n              <NumberInput\n                type=\"number\"\n                min=\"1\"\n                value={currentPosition.measure}\n                onChange={(e) => setCurrentPosition(prev => ({\n                  ...prev,\n                  measure: parseInt(e.target.value) || 1\n                }))}\n              />\n            </ControlGroup>\n            <ControlGroup>\n              <InputLabel>Tempo</InputLabel>\n              <NumberInput\n                type=\"number\"\n                min=\"1\"\n                max=\"4\"\n                value={currentPosition.beat}\n                onChange={(e) => setCurrentPosition(prev => ({\n                  ...prev,\n                  beat: parseInt(e.target.value) || 1\n                }))}\n              />\n            </ControlGroup>\n            <ControlGroup>\n              <InputLabel>Pauta</InputLabel>\n              <NumberInput\n                type=\"number\"\n                min=\"0\"\n                value={currentPosition.staff}\n                onChange={(e) => setCurrentPosition(prev => ({\n                  ...prev,\n                  staff: parseInt(e.target.value) || 0\n                }))}\n              />\n            </ControlGroup>\n          </PositionControls>\n\n          <ActionButtons>\n            <ActionButton onClick={() => setCurrentSyllable('')}>\n              Limpar\n            </ActionButton>\n            <ActionButton variant=\"primary\" onClick={handleIndividualAdd}>\n              Adicionar Sílaba\n            </ActionButton>\n          </ActionButtons>\n        </>\n      )}\n\n      {lyrics.length > 0 && (\n        <>\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '1.5rem' }}>\n            <h4 style={{ margin: 0, color: '#2c3e50' }}>\n              Letras Adicionadas ({lyrics.length})\n            </h4>\n            <ActionButton onClick={onLyricsClear}>\n              Limpar Todas\n            </ActionButton>\n          </div>\n          \n          <LyricsList>\n            {lyrics.map((lyric, index) => (\n              <LyricItem key={index}>\n                <div>\n                  <LyricText>{lyric.text}</LyricText>\n                  <LyricPosition>\n                    Compasso {lyric.position.measure}, Tempo {lyric.position.beat}\n                    {lyric.position.staff && lyric.position.staff > 0 && `, Pauta ${lyric.position.staff + 1}`}\n                  </LyricPosition>\n                </div>\n                <DeleteButton onClick={() => onLyricsRemove(index)}>\n                  ×\n                </DeleteButton>\n              </LyricItem>\n            ))}\n          </LyricsList>\n        </>\n      )}\n    </EditorContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGvC,MAAMC,eAAe,GAAGL,MAAM,CAACM,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,eAAe;AAQrB,MAAMG,YAAY,GAAGR,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,YAAY;AASlB,MAAME,KAAK,GAAGV,MAAM,CAACW,EAAE;AACvB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,KAAK;AAMX,MAAMG,UAAU,GAAGb,MAAM,CAACM,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GALID,UAAU;AAOhB,MAAME,UAAU,GAAGf,MAAM,CAACgB,MAA2B;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG;AAC5B;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACC,GAAA,GAlBIJ,UAAU;AAoBhB,MAAMK,WAAW,GAAGpB,MAAM,CAACM,GAAG;AAC9B;AACA,CAAC;AAACe,GAAA,GAFID,WAAW;AAIjB,MAAME,UAAU,GAAGtB,MAAM,CAACuB,KAAK;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,UAAU;AAOhB,MAAMG,QAAQ,GAAGzB,MAAM,CAAC0B,QAAQ;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GApBIF,QAAQ;AAsBd,MAAMG,aAAa,GAAG5B,MAAM,CAAC6B,KAAK;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAbIF,aAAa;AAenB,MAAMG,gBAAgB,GAAG/B,MAAM,CAACM,GAAG;AACnC;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GALID,gBAAgB;AAOtB,MAAME,YAAY,GAAGjC,MAAM,CAACM,GAAG;AAC/B;AACA;AACA;AACA,CAAC;AAAC4B,GAAA,GAJID,YAAY;AAMlB,MAAME,WAAW,GAAGnC,MAAM,CAAC6B,KAAK;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,IAAA,GAXID,WAAW;AAajB,MAAME,aAAa,GAAGrC,MAAM,CAACM,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGvC,MAAM,CAACgB,MAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACuB,OAAO,KAAK,SAAS,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACC,IAAA,GAvBIF,YAAY;AAyBlB,MAAMG,UAAU,GAAG1C,MAAM,CAACM,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqC,IAAA,GAPID,UAAU;AAShB,MAAME,SAAS,GAAG5C,MAAM,CAACM,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuC,IAAA,GAVID,SAAS;AAYf,MAAME,SAAS,GAAG9C,MAAM,CAACM,GAAG;AAC5B;AACA;AACA,CAAC;AAACyC,IAAA,GAHID,SAAS;AAKf,MAAME,aAAa,GAAGhD,MAAM,CAACM,GAAG;AAChC;AACA;AACA,CAAC;AAAC2C,IAAA,GAHID,aAAa;AAKnB,MAAME,YAAY,GAAGlD,MAAM,CAACgB,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAfID,YAAY;AAwBlB,OAAO,MAAME,oBAAyD,GAAGA,CAAC;EACxEC,MAAM;EACNC,WAAW;EACXC,cAAc;EACdC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG5D,QAAQ,CAAwB,MAAM,CAAC;EAC/D,MAAM,CAAC6D,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+D,eAAe,EAAEC,kBAAkB,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiE,eAAe,EAAEC,kBAAkB,CAAC,GAAGlE,QAAQ,CAAC;IACrDmE,OAAO,EAAE,CAAC;IACVC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACT,QAAQ,CAACU,IAAI,CAAC,CAAC,EAAE;IAEtB,MAAMC,KAAK,GAAGX,QAAQ,CAACU,IAAI,CAAC,CAAC,CAACE,KAAK,CAAC,KAAK,CAAC;IAC1C,IAAIN,OAAO,GAAG,CAAC;IACf,IAAIC,IAAI,GAAG,CAAC;IAEZI,KAAK,CAACE,OAAO,CAAEC,IAAI,IAAK;MACtB,MAAMC,SAAS,GAAGD,IAAI,CAACF,KAAK,CAAC,GAAG,CAAC;MAEjCG,SAAS,CAACF,OAAO,CAAEG,QAAQ,IAAK;QAC9B,MAAMC,KAAa,GAAG;UACpBC,IAAI,EAAEF,QAAQ;UACdG,QAAQ,EAAE;YACRb,OAAO;YACPC,IAAI;YACJC,KAAK,EAAE;UACT;QACF,CAAC;QAEDd,WAAW,CAACuB,KAAK,CAAC;;QAElB;QACAV,IAAI,EAAE;QACN,IAAIA,IAAI,GAAG,CAAC,EAAE;UACZA,IAAI,GAAG,CAAC;UACRD,OAAO,EAAE;QACX;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFL,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,MAAMmB,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAClB,eAAe,CAACQ,IAAI,CAAC,CAAC,EAAE;IAE7B,MAAMO,KAAa,GAAG;MACpBC,IAAI,EAAEhB,eAAe,CAACQ,IAAI,CAAC,CAAC;MAC5BS,QAAQ,EAAE;QACRb,OAAO,EAAEF,eAAe,CAACE,OAAO;QAChCC,IAAI,EAAEH,eAAe,CAACG,IAAI;QAC1BC,KAAK,EAAEJ,eAAe,CAACI;MACzB;IACF,CAAC;IAEDd,WAAW,CAACuB,KAAK,CAAC;IAClBd,kBAAkB,CAAC,EAAE,CAAC;;IAEtB;IACAE,kBAAkB,CAACgB,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACPd,IAAI,EAAEc,IAAI,CAACd,IAAI,GAAG,CAAC,GAAGc,IAAI,CAACd,IAAI,GAAG,CAAC,GAAG,CAAC;MACvCD,OAAO,EAAEe,IAAI,CAACd,IAAI,GAAG,CAAC,GAAGc,IAAI,CAACf,OAAO,GAAGe,IAAI,CAACf,OAAO,GAAG;IACzD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,oBACEhE,OAAA,CAACG,eAAe;IAAA6E,QAAA,gBACdhF,OAAA,CAACM,YAAY;MAAA0E,QAAA,gBACXhF,OAAA,CAACQ,KAAK;QAAAwE,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC/BpF,OAAA,CAACW,UAAU;QAAAqE,QAAA,gBACThF,OAAA,CAACa,UAAU;UACTG,MAAM,EAAEwC,IAAI,KAAK,MAAO;UACxB6B,OAAO,EAAEA,CAAA,KAAM5B,OAAO,CAAC,MAAM,CAAE;UAAAuB,QAAA,EAChC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpF,OAAA,CAACa,UAAU;UACTG,MAAM,EAAEwC,IAAI,KAAK,YAAa;UAC9B6B,OAAO,EAAEA,CAAA,KAAM5B,OAAO,CAAC,YAAY,CAAE;UAAAuB,QAAA,EACtC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEd5B,IAAI,KAAK,MAAM,gBACdxD,OAAA,CAACkB,WAAW;MAAA8D,QAAA,gBACVhF,OAAA,CAACoB,UAAU;QAAA4D,QAAA,EAAC;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACzDpF,OAAA,CAACuB,QAAQ;QACP+D,KAAK,EAAE5B,QAAS;QAChB6B,QAAQ,EAAGC,CAAC,IAAK7B,WAAW,CAAC6B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC7CI,WAAW,EAAC;MAAkH;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/H,CAAC,eACFpF,OAAA,CAACmC,aAAa;QAAA6C,QAAA,gBACZhF,OAAA,CAACqC,YAAY;UAACgD,OAAO,EAAEA,CAAA,KAAM1B,WAAW,CAAC,EAAE,CAAE;UAAAqB,QAAA,EAAC;QAE9C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACfpF,OAAA,CAACqC,YAAY;UAACC,OAAO,EAAC,SAAS;UAAC+C,OAAO,EAAElB,iBAAkB;UAAAa,QAAA,EAAC;QAE5D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,gBAEdpF,OAAA,CAAAE,SAAA;MAAA8E,QAAA,gBACEhF,OAAA,CAACkB,WAAW;QAAA8D,QAAA,gBACVhF,OAAA,CAACoB,UAAU;UAAA4D,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACtCpF,OAAA,CAAC0B,aAAa;UACZ4D,KAAK,EAAE1B,eAAgB;UACvB2B,QAAQ,EAAGC,CAAC,IAAK3B,kBAAkB,CAAC2B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UACpDI,WAAW,EAAC,yBAAsB;UAClCC,UAAU,EAAGH,CAAC,IAAKA,CAAC,CAACI,GAAG,KAAK,OAAO,IAAId,mBAAmB,CAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAEdpF,OAAA,CAAC6B,gBAAgB;QAAAmD,QAAA,gBACfhF,OAAA,CAAC+B,YAAY;UAAAiD,QAAA,gBACXhF,OAAA,CAACoB,UAAU;YAAA4D,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjCpF,OAAA,CAACiC,WAAW;YACV4D,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,GAAG;YACPR,KAAK,EAAExB,eAAe,CAACE,OAAQ;YAC/BuB,QAAQ,EAAGC,CAAC,IAAKzB,kBAAkB,CAACgB,IAAI,KAAK;cAC3C,GAAGA,IAAI;cACPf,OAAO,EAAE+B,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;YACvC,CAAC,CAAC;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACfpF,OAAA,CAAC+B,YAAY;UAAAiD,QAAA,gBACXhF,OAAA,CAACoB,UAAU;YAAA4D,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9BpF,OAAA,CAACiC,WAAW;YACV4D,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,GAAG;YACPE,GAAG,EAAC,GAAG;YACPV,KAAK,EAAExB,eAAe,CAACG,IAAK;YAC5BsB,QAAQ,EAAGC,CAAC,IAAKzB,kBAAkB,CAACgB,IAAI,KAAK;cAC3C,GAAGA,IAAI;cACPd,IAAI,EAAE8B,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;YACpC,CAAC,CAAC;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACfpF,OAAA,CAAC+B,YAAY;UAAAiD,QAAA,gBACXhF,OAAA,CAACoB,UAAU;YAAA4D,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9BpF,OAAA,CAACiC,WAAW;YACV4D,IAAI,EAAC,QAAQ;YACbC,GAAG,EAAC,GAAG;YACPR,KAAK,EAAExB,eAAe,CAACI,KAAM;YAC7BqB,QAAQ,EAAGC,CAAC,IAAKzB,kBAAkB,CAACgB,IAAI,KAAK;cAC3C,GAAGA,IAAI;cACPb,KAAK,EAAE6B,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;YACrC,CAAC,CAAC;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEnBpF,OAAA,CAACmC,aAAa;QAAA6C,QAAA,gBACZhF,OAAA,CAACqC,YAAY;UAACgD,OAAO,EAAEA,CAAA,KAAMxB,kBAAkB,CAAC,EAAE,CAAE;UAAAmB,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACfpF,OAAA,CAACqC,YAAY;UAACC,OAAO,EAAC,SAAS;UAAC+C,OAAO,EAAEP,mBAAoB;UAAAE,QAAA,EAAC;QAE9D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA,eAChB,CACH,EAEAjC,MAAM,CAAC8C,MAAM,GAAG,CAAC,iBAChBjG,OAAA,CAAAE,SAAA;MAAA8E,QAAA,gBACEhF,OAAA;QAAKkG,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAtB,QAAA,gBAC1GhF,OAAA;UAAIkG,KAAK,EAAE;YAAEK,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAxB,QAAA,GAAC,sBACtB,EAAC7B,MAAM,CAAC8C,MAAM,EAAC,GACrC;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpF,OAAA,CAACqC,YAAY;UAACgD,OAAO,EAAE/B,aAAc;UAAA0B,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAENpF,OAAA,CAACwC,UAAU;QAAAwC,QAAA,EACR7B,MAAM,CAACsD,GAAG,CAAC,CAAC9B,KAAK,EAAE+B,KAAK,kBACvB1G,OAAA,CAAC0C,SAAS;UAAAsC,QAAA,gBACRhF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA,CAAC4C,SAAS;cAAAoC,QAAA,EAAEL,KAAK,CAACC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnCpF,OAAA,CAAC8C,aAAa;cAAAkC,QAAA,GAAC,WACJ,EAACL,KAAK,CAACE,QAAQ,CAACb,OAAO,EAAC,UAAQ,EAACW,KAAK,CAACE,QAAQ,CAACZ,IAAI,EAC5DU,KAAK,CAACE,QAAQ,CAACX,KAAK,IAAIS,KAAK,CAACE,QAAQ,CAACX,KAAK,GAAG,CAAC,IAAI,WAAWS,KAAK,CAACE,QAAQ,CAACX,KAAK,GAAG,CAAC,EAAE;YAAA;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACNpF,OAAA,CAACgD,YAAY;YAACqC,OAAO,EAAEA,CAAA,KAAMhC,cAAc,CAACqD,KAAK,CAAE;YAAA1B,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA,GAVDsB,KAAK;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWV,CACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA,eACb,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAEtB,CAAC;AAAC7B,EAAA,CA3MWL,oBAAyD;AAAAyD,IAAA,GAAzDzD,oBAAyD;AAAA,IAAA7C,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA0D,IAAA;AAAAC,YAAA,CAAAvG,EAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAzF,GAAA;AAAAyF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}