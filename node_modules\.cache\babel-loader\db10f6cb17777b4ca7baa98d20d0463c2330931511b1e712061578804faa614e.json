{"ast": null, "code": "import React,{useState}from'react';import styled from'styled-components';import{useAuth}from'../../contexts/AuthContext';import{Icons}from'../../utils/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AuthContainer=styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 2rem;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n    opacity: 0.3;\n  }\n`;const AuthCard=styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-radius: 20px;\n  padding: 3rem;\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);\n  width: 100%;\n  max-width: 450px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  z-index: 1;\n`;const Logo=styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  font-size: 2rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n`;const Title=styled.h2`\n  text-align: center;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n  font-weight: 600;\n`;const Form=styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;const InputGroup=styled.div`\n  position: relative;\n`;const InputIcon=styled.div`\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #6b7280;\n`;const Input=styled.input`\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 3rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n  box-sizing: border-box;\n\n  &:focus {\n    outline: none;\n    border-color: #3b82f6;\n  }\n\n  &::placeholder {\n    color: #9ca3af;\n  }\n`;const PasswordToggle=styled.button`\n  position: absolute;\n  right: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 0.25rem;\n\n  &:hover {\n    color: #374151;\n  }\n`;const SubmitButton=styled.button`\n  width: 100%;\n  padding: 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  margin-top: 1.5rem;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);\n  }\n`;const SwitchAuth=styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  color: #6b7280;\n`;const SwitchLink=styled.button`\n  background: none;\n  border: none;\n  color: #3b82f6;\n  cursor: pointer;\n  font-weight: 600;\n  text-decoration: underline;\n\n  &:hover {\n    color: #2563eb;\n  }\n`;const ErrorMessage=styled.div`\n  background-color: #fee2e2;\n  color: #dc2626;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;export const LoginPage=_ref=>{let{onSwitchToRegister}=_ref;const[email,setEmail]=useState('');const[password,setPassword]=useState('');const[showPassword,setShowPassword]=useState(false);const[loading,setLoading]=useState(false);const[error,setError]=useState('');const{login}=useAuth();const handleSubmit=async e=>{e.preventDefault();if(!email||!password){setError('Por favor, preencha todos os campos');return;}setLoading(true);setError('');try{await login(email,password);}catch(error){console.error('Erro no login:',error);setError('Email ou senha incorretos');}finally{setLoading(false);}};return/*#__PURE__*/_jsx(AuthContainer,{children:/*#__PURE__*/_jsxs(AuthCard,{children:[/*#__PURE__*/_jsxs(Logo,{children:[/*#__PURE__*/_jsx(Icons.Music,{size:32}),\"Partitura Digital\"]}),/*#__PURE__*/_jsx(Title,{children:\"Entrar na sua conta\"}),error&&/*#__PURE__*/_jsx(ErrorMessage,{children:error}),/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(InputIcon,{children:/*#__PURE__*/_jsx(Icons.Mail,{size:20})}),/*#__PURE__*/_jsx(Input,{type:\"email\",placeholder:\"Seu email\",value:email,onChange:e=>setEmail(e.target.value),required:true})]}),/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(InputIcon,{children:/*#__PURE__*/_jsx(Icons.Lock,{size:20})}),/*#__PURE__*/_jsx(Input,{type:showPassword?'text':'password',placeholder:\"Sua senha\",value:password,onChange:e=>setPassword(e.target.value),required:true}),/*#__PURE__*/_jsx(PasswordToggle,{type:\"button\",onClick:()=>setShowPassword(!showPassword),children:showPassword?/*#__PURE__*/_jsx(Icons.EyeOff,{size:20}):/*#__PURE__*/_jsx(Icons.Eye,{size:20})})]}),/*#__PURE__*/_jsx(SubmitButton,{type:\"submit\",disabled:loading,children:loading?'Entrando...':'Entrar'})]}),/*#__PURE__*/_jsxs(SwitchAuth,{children:[\"N\\xE3o tem uma conta?\",' ',/*#__PURE__*/_jsx(SwitchLink,{onClick:onSwitchToRegister,children:\"Criar conta\"})]})]})});};", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "Icons", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "AuthCard", "Logo", "Title", "h2", "Form", "form", "InputGroup", "InputIcon", "Input", "input", "PasswordToggle", "button", "SubmitButton", "SwitchAuth", "SwitchLink", "ErrorMessage", "LoginPage", "_ref", "onSwitchToRegister", "email", "setEmail", "password", "setPassword", "showPassword", "setShowPassword", "loading", "setLoading", "error", "setError", "login", "handleSubmit", "e", "preventDefault", "console", "children", "Music", "size", "onSubmit", "Mail", "type", "placeholder", "value", "onChange", "target", "required", "Lock", "onClick", "Eye<PERSON>ff", "Eye", "disabled"], "sources": ["D:/Dev/partitura_digital/src/pages/Auth/LoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Icons } from '../../utils/icons';\n\nconst AuthContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 2rem;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n    opacity: 0.3;\n  }\n`;\n\nconst AuthCard = styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-radius: 20px;\n  padding: 3rem;\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);\n  width: 100%;\n  max-width: 450px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  z-index: 1;\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  font-size: 2rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n`;\n\nconst Title = styled.h2`\n  text-align: center;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n  font-weight: 600;\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\n\nconst InputGroup = styled.div`\n  position: relative;\n`;\n\nconst InputIcon = styled.div`\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #6b7280;\n`;\n\nconst Input = styled.input`\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 3rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n  box-sizing: border-box;\n\n  &:focus {\n    outline: none;\n    border-color: #3b82f6;\n  }\n\n  &::placeholder {\n    color: #9ca3af;\n  }\n`;\n\nconst PasswordToggle = styled.button`\n  position: absolute;\n  right: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 0.25rem;\n\n  &:hover {\n    color: #374151;\n  }\n`;\n\nconst SubmitButton = styled.button`\n  width: 100%;\n  padding: 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  margin-top: 1.5rem;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);\n  }\n`;\n\nconst SwitchAuth = styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  color: #6b7280;\n`;\n\nconst SwitchLink = styled.button`\n  background: none;\n  border: none;\n  color: #3b82f6;\n  cursor: pointer;\n  font-weight: 600;\n  text-decoration: underline;\n\n  &:hover {\n    color: #2563eb;\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background-color: #fee2e2;\n  color: #dc2626;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;\n\ninterface LoginPageProps {\n  onSwitchToRegister: () => void;\n}\n\nexport const LoginPage: React.FC<LoginPageProps> = ({ onSwitchToRegister }) => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const { login } = useAuth();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!email || !password) {\n      setError('Por favor, preencha todos os campos');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      await login(email, password);\n    } catch (error: any) {\n      console.error('Erro no login:', error);\n      setError('Email ou senha incorretos');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <AuthContainer>\n      <AuthCard>\n        <Logo>\n          <Icons.Music size={32} />\n          Partitura Digital\n        </Logo>\n\n        <Title>Entrar na sua conta</Title>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n\n        <Form onSubmit={handleSubmit}>\n          <InputGroup>\n            <InputIcon>\n              <Icons.Mail size={20} />\n            </InputIcon>\n            <Input\n              type=\"email\"\n              placeholder=\"Seu email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n            />\n          </InputGroup>\n\n          <InputGroup>\n            <InputIcon>\n              <Icons.Lock size={20} />\n            </InputIcon>\n            <Input\n              type={showPassword ? 'text' : 'password'}\n              placeholder=\"Sua senha\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n            />\n            <PasswordToggle\n              type=\"button\"\n              onClick={() => setShowPassword(!showPassword)}\n            >\n              {showPassword ? <Icons.EyeOff size={20} /> : <Icons.Eye size={20} />}\n            </PasswordToggle>\n          </InputGroup>\n\n          <SubmitButton type=\"submit\" disabled={loading}>\n            {loading ? 'Entrando...' : 'Entrar'}\n          </SubmitButton>\n        </Form>\n\n        <SwitchAuth>\n          Não tem uma conta?{' '}\n          <SwitchLink onClick={onSwitchToRegister}>\n            Criar conta\n          </SwitchLink>\n        </SwitchAuth>\n      </AuthCard>\n    </AuthContainer>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,KAAK,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1C,KAAM,CAAAC,aAAa,CAAGP,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGT,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAE,IAAI,CAAGV,MAAM,CAACQ,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAG,KAAK,CAAGX,MAAM,CAACY,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,IAAI,CAAGb,MAAM,CAACc,IAAI;AACxB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGf,MAAM,CAACQ,GAAG;AAC7B;AACA,CAAC,CAED,KAAM,CAAAQ,SAAS,CAAGhB,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAS,KAAK,CAAGjB,MAAM,CAACkB,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGnB,MAAM,CAACoB,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGrB,MAAM,CAACoB,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAE,UAAU,CAAGtB,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAe,UAAU,CAAGvB,MAAM,CAACoB,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,YAAY,CAAGxB,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAMD,MAAO,MAAM,CAAAiB,SAAmC,CAAGC,IAAA,EAA4B,IAA3B,CAAEC,kBAAmB,CAAC,CAAAD,IAAA,CACxE,KAAM,CAACE,KAAK,CAAEC,QAAQ,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC+B,QAAQ,CAAEC,WAAW,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiC,YAAY,CAAEC,eAAe,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACmC,OAAO,CAAEC,UAAU,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACqC,KAAK,CAAEC,QAAQ,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAEtC,KAAM,CAAEuC,KAAM,CAAC,CAAGrC,OAAO,CAAC,CAAC,CAE3B,KAAM,CAAAsC,YAAY,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACjDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,GAAI,CAACb,KAAK,EAAI,CAACE,QAAQ,CAAE,CACvBO,QAAQ,CAAC,qCAAqC,CAAC,CAC/C,OACF,CAEAF,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF,KAAM,CAAAC,KAAK,CAACV,KAAK,CAAEE,QAAQ,CAAC,CAC9B,CAAE,MAAOM,KAAU,CAAE,CACnBM,OAAO,CAACN,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CACtCC,QAAQ,CAAC,2BAA2B,CAAC,CACvC,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACE/B,IAAA,CAACG,aAAa,EAAAoC,QAAA,cACZrC,KAAA,CAACG,QAAQ,EAAAkC,QAAA,eACPrC,KAAA,CAACI,IAAI,EAAAiC,QAAA,eACHvC,IAAA,CAACF,KAAK,CAAC0C,KAAK,EAACC,IAAI,CAAE,EAAG,CAAE,CAAC,oBAE3B,EAAM,CAAC,cAEPzC,IAAA,CAACO,KAAK,EAAAgC,QAAA,CAAC,qBAAmB,CAAO,CAAC,CAEjCP,KAAK,eAAIhC,IAAA,CAACoB,YAAY,EAAAmB,QAAA,CAAEP,KAAK,CAAe,CAAC,cAE9C9B,KAAA,CAACO,IAAI,EAACiC,QAAQ,CAAEP,YAAa,CAAAI,QAAA,eAC3BrC,KAAA,CAACS,UAAU,EAAA4B,QAAA,eACTvC,IAAA,CAACY,SAAS,EAAA2B,QAAA,cACRvC,IAAA,CAACF,KAAK,CAAC6C,IAAI,EAACF,IAAI,CAAE,EAAG,CAAE,CAAC,CACf,CAAC,cACZzC,IAAA,CAACa,KAAK,EACJ+B,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAEtB,KAAM,CACbuB,QAAQ,CAAGX,CAAC,EAAKX,QAAQ,CAACW,CAAC,CAACY,MAAM,CAACF,KAAK,CAAE,CAC1CG,QAAQ,MACT,CAAC,EACQ,CAAC,cAEb/C,KAAA,CAACS,UAAU,EAAA4B,QAAA,eACTvC,IAAA,CAACY,SAAS,EAAA2B,QAAA,cACRvC,IAAA,CAACF,KAAK,CAACoD,IAAI,EAACT,IAAI,CAAE,EAAG,CAAE,CAAC,CACf,CAAC,cACZzC,IAAA,CAACa,KAAK,EACJ+B,IAAI,CAAEhB,YAAY,CAAG,MAAM,CAAG,UAAW,CACzCiB,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAEpB,QAAS,CAChBqB,QAAQ,CAAGX,CAAC,EAAKT,WAAW,CAACS,CAAC,CAACY,MAAM,CAACF,KAAK,CAAE,CAC7CG,QAAQ,MACT,CAAC,cACFjD,IAAA,CAACe,cAAc,EACb6B,IAAI,CAAC,QAAQ,CACbO,OAAO,CAAEA,CAAA,GAAMtB,eAAe,CAAC,CAACD,YAAY,CAAE,CAAAW,QAAA,CAE7CX,YAAY,cAAG5B,IAAA,CAACF,KAAK,CAACsD,MAAM,EAACX,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGzC,IAAA,CAACF,KAAK,CAACuD,GAAG,EAACZ,IAAI,CAAE,EAAG,CAAE,CAAC,CACtD,CAAC,EACP,CAAC,cAEbzC,IAAA,CAACiB,YAAY,EAAC2B,IAAI,CAAC,QAAQ,CAACU,QAAQ,CAAExB,OAAQ,CAAAS,QAAA,CAC3CT,OAAO,CAAG,aAAa,CAAG,QAAQ,CACvB,CAAC,EACX,CAAC,cAEP5B,KAAA,CAACgB,UAAU,EAAAqB,QAAA,EAAC,uBACQ,CAAC,GAAG,cACtBvC,IAAA,CAACmB,UAAU,EAACgC,OAAO,CAAE5B,kBAAmB,CAAAgB,QAAA,CAAC,aAEzC,CAAY,CAAC,EACH,CAAC,EACL,CAAC,CACE,CAAC,CAEpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}