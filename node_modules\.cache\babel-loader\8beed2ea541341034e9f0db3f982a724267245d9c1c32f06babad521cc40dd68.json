{"ast": null, "code": "import validate from './validate.js';\nfunction parse(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n  let v;\n  return Uint8Array.of((v = parseInt(uuid.slice(0, 8), 16)) >>> 24, v >>> 16 & 0xff, v >>> 8 & 0xff, v & 0xff, (v = parseInt(uuid.slice(9, 13), 16)) >>> 8, v & 0xff, (v = parseInt(uuid.slice(14, 18), 16)) >>> 8, v & 0xff, (v = parseInt(uuid.slice(19, 23), 16)) >>> 8, v & 0xff, (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff, v / 0x100000000 & 0xff, v >>> 24 & 0xff, v >>> 16 & 0xff, v >>> 8 & 0xff, v & 0xff);\n}\nexport default parse;", "map": {"version": 3, "names": ["validate", "parse", "uuid", "TypeError", "v", "Uint8Array", "of", "parseInt", "slice"], "sources": ["D:/Dev/partitura_digital/node_modules/uuid/dist/esm-browser/parse.js"], "sourcesContent": ["import validate from './validate.js';\nfunction parse(uuid) {\n    if (!validate(uuid)) {\n        throw TypeError('Invalid UUID');\n    }\n    let v;\n    return Uint8Array.of((v = parseInt(uuid.slice(0, 8), 16)) >>> 24, (v >>> 16) & 0xff, (v >>> 8) & 0xff, v & 0xff, (v = parseInt(uuid.slice(9, 13), 16)) >>> 8, v & 0xff, (v = parseInt(uuid.slice(14, 18), 16)) >>> 8, v & 0xff, (v = parseInt(uuid.slice(19, 23), 16)) >>> 8, v & 0xff, ((v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000) & 0xff, (v / 0x100000000) & 0xff, (v >>> 24) & 0xff, (v >>> 16) & 0xff, (v >>> 8) & 0xff, v & 0xff);\n}\nexport default parse;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,SAASC,KAAKA,CAACC,IAAI,EAAE;EACjB,IAAI,CAACF,QAAQ,CAACE,IAAI,CAAC,EAAE;IACjB,MAAMC,SAAS,CAAC,cAAc,CAAC;EACnC;EACA,IAAIC,CAAC;EACL,OAAOC,UAAU,CAACC,EAAE,CAAC,CAACF,CAAC,GAAGG,QAAQ,CAACL,IAAI,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,EAAGJ,CAAC,KAAK,EAAE,GAAI,IAAI,EAAGA,CAAC,KAAK,CAAC,GAAI,IAAI,EAAEA,CAAC,GAAG,IAAI,EAAE,CAACA,CAAC,GAAGG,QAAQ,CAACL,IAAI,CAACM,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,EAAEJ,CAAC,GAAG,IAAI,EAAE,CAACA,CAAC,GAAGG,QAAQ,CAACL,IAAI,CAACM,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,EAAEJ,CAAC,GAAG,IAAI,EAAE,CAACA,CAAC,GAAGG,QAAQ,CAACL,IAAI,CAACM,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,EAAEJ,CAAC,GAAG,IAAI,EAAG,CAACA,CAAC,GAAGG,QAAQ,CAACL,IAAI,CAACM,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,GAAI,IAAI,EAAGJ,CAAC,GAAG,WAAW,GAAI,IAAI,EAAGA,CAAC,KAAK,EAAE,GAAI,IAAI,EAAGA,CAAC,KAAK,EAAE,GAAI,IAAI,EAAGA,CAAC,KAAK,CAAC,GAAI,IAAI,EAAEA,CAAC,GAAG,IAAI,CAAC;AACxb;AACA,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}