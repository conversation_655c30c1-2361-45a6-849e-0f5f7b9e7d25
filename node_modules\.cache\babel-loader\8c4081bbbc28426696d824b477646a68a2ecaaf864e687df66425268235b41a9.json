{"ast": null, "code": "import React,{useState}from'react';import styled from'styled-components';import{useAuth}from'../../contexts/AuthContext';import{ScoreService}from'../../services/scoreService';import{ChordView}from'../ChordView/ChordView';import{LyricsEditor}from'../LyricsEditor/LyricsEditor';import{InstrumentSelector}from'../InstrumentSelector/InstrumentSelector';import{StaffSystem}from'../StaffSystem/StaffSystem';import{getInstrumentTemplate}from'../../utils/instrumentTemplates';import{v4 as uuidv4}from'uuid';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EditorContainer=styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;const EditorHeader=styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;const EditorTitle=styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;const EditorActions=styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;const ActionButton=styled.button`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props=>props.variant==='primary'?`\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  `:`\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;const EditorContent=styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;const ToolPanel=styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;const ToolSection=styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;const ToolGrid=styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;const ToolButton=styled.button`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props=>props.active?'#667eea':'white'};\n  color: ${props=>props.active?'white':'#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props=>!props.active&&'background: #f8f9fa;'}\n  }\n`;const ScoreCanvas=styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;const StaffContainer=styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;const Staff=styled.svg`\n  width: 100%;\n  height: ${props=>props.staffCount===2?'350px':'200px'};\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;const GuestWarning=styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;export const ScoreEditor=_ref=>{let{scoreId,initialConfig}=_ref;const{currentUser}=useAuth();const[title,setTitle]=useState((initialConfig===null||initialConfig===void 0?void 0:initialConfig.title)||'Nova Partitura');const[selectedTool,setSelectedTool]=useState('note');const[selectedDuration,setSelectedDuration]=useState('quarter');const[selectedNote,setSelectedNote]=useState('C');const[selectedAccidental,setSelectedAccidental]=useState(null);const[isPlaying,setIsPlaying]=useState(false);const[isSaving,setIsSaving]=useState(false);const[placedNotes,setPlacedNotes]=useState([]);const[isLoading,setIsLoading]=useState(false);const[showChords,setShowChords]=useState(false);const[showLyrics,setShowLyrics]=useState(false);const[lastSaved,setLastSaved]=useState(null);const[zoomLevel,setZoomLevel]=useState(1);const[lyrics,setLyrics]=useState([]);const[selectedInstrument,setSelectedInstrument]=useState((initialConfig===null||initialConfig===void 0?void 0:initialConfig.instrument)||'piano');// Carregar partitura existente\nReact.useEffect(()=>{if(scoreId&&currentUser){loadScore();}},[scoreId,currentUser]);// Salvamento automático\nReact.useEffect(()=>{if(!currentUser||!scoreId||placedNotes.length===0)return;const autoSaveTimer=setTimeout(()=>{handleSave(true);// true indica salvamento automático\n},2000);// Salvar após 2 segundos de inatividade\nreturn()=>clearTimeout(autoSaveTimer);},[placedNotes,lyrics,title,currentUser,scoreId]);const loadScore=async()=>{if(!scoreId||!currentUser)return;setIsLoading(true);try{const score=await ScoreService.getScore(scoreId);if(score&&score.userId===currentUser.uid){setTitle(score.title);// Extrair notas de todos os compassos\nconst allNotes=[];score.staffs.forEach(staff=>{staff.measures.forEach(measure=>{allNotes.push(...measure.notes);});});setPlacedNotes(allNotes);setLyrics(score.lyrics||[]);}}catch(error){console.error('Erro ao carregar partitura:',error);}finally{setIsLoading(false);}};const handleSave=async function(){let isAutoSave=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;if(!currentUser){if(!isAutoSave){alert('Você precisa estar logado para salvar partituras!');}return;}setIsSaving(true);try{// Organizar notas por compasso\nconst measureMap=new Map();placedNotes.forEach(note=>{const measure=note.position.measure;if(!measureMap.has(measure)){measureMap.set(measure,[]);}measureMap.get(measure).push(note);});// Criar compassos com as notas\nconst measures=Array.from(measureMap.entries()).map(_ref2=>{let[measureNumber,notes]=_ref2;return{id:uuidv4(),number:measureNumber,timeSignature:{numerator:4,denominator:4},notes:notes.sort((a,b)=>a.position.beat-b.position.beat),chords:[]};});// Criar dados da partitura\nconst scoreData={title,composer:initialConfig===null||initialConfig===void 0?void 0:initialConfig.composer,key:{note:(initialConfig===null||initialConfig===void 0?void 0:initialConfig.key)||'C',mode:(initialConfig===null||initialConfig===void 0?void 0:initialConfig.keyMode)||'major'},timeSignature:{numerator:(initialConfig===null||initialConfig===void 0?void 0:initialConfig.timeSignatureNum)||4,denominator:(initialConfig===null||initialConfig===void 0?void 0:initialConfig.timeSignatureDen)||4},tempo:(initialConfig===null||initialConfig===void 0?void 0:initialConfig.tempo)||120,staffs:[{id:uuidv4(),clef:getInstrumentTemplate(selectedInstrument).clef,instrument:selectedInstrument,measures}],lyrics,userId:currentUser.uid};if(scoreId){// Para update, removemos o userId pois não deve ser alterado\nconst{userId,...updateData}=scoreData;await ScoreService.updateScore(scoreId,updateData);}else{await ScoreService.createScore(scoreData,currentUser.uid);}setLastSaved(new Date());if(!isAutoSave){alert('Partitura salva com sucesso!');}}catch(error){console.error('Erro ao salvar:',error);if(!isAutoSave){alert('Erro ao salvar partitura');}}finally{setIsSaving(false);}};const handlePlay=()=>{setIsPlaying(!isPlaying);// TODO: Implementar reprodução\n};const handleStaffClick=function(event){var _event$currentTarget$;let staffIndex=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;const rect=event.currentTarget.getBoundingClientRect();const svgRect=(_event$currentTarget$=event.currentTarget.closest('svg'))===null||_event$currentTarget$===void 0?void 0:_event$currentTarget$.getBoundingClientRect();if(!svgRect)return;const x=event.clientX-svgRect.left;const y=event.clientY-svgRect.top;// Calcular a linha da pauta baseada na posição Y\nconst staffLines=[50,70,90,110,130];// Posições das 5 linhas da pauta\nconst notePositions=[{y:40,note:'A',octave:5},// Acima da pauta\n{y:50,note:'G',octave:5},// 5ª linha\n{y:60,note:'F',octave:5},// Entre 4ª e 5ª\n{y:70,note:'E',octave:5},// 4ª linha\n{y:80,note:'D',octave:5},// Entre 3ª e 4ª\n{y:90,note:'C',octave:5},// 3ª linha (Dó central)\n{y:100,note:'B',octave:4},// Entre 2ª e 3ª\n{y:110,note:'A',octave:4},// 2ª linha\n{y:120,note:'G',octave:4},// Entre 1ª e 2ª\n{y:130,note:'F',octave:4},// 1ª linha\n{y:140,note:'E',octave:4}// Abaixo da pauta\n];// Encontrar a posição mais próxima\nconst closestPosition=notePositions.reduce((closest,current)=>{return Math.abs(current.y-y)<Math.abs(closest.y-y)?current:closest;});// Calcular a posição horizontal (compasso e tempo)\nconst measureWidth=150;// Largura aproximada de um compasso\nconst startX=120;// Início da área de notas\nconst relativeX=x-startX;const measure=Math.max(1,Math.floor(relativeX/measureWidth)+1);const beatPosition=relativeX%measureWidth/measureWidth*4;// 4 tempos por compasso\n// Criar nova nota\nconst newNote={id:uuidv4(),name:selectedNote,// Usar a nota selecionada no painel\noctave:closestPosition.octave,duration:selectedDuration,accidental:selectedAccidental||undefined,position:{measure,beat:Math.round(beatPosition*4)/4,// Arredondar para 1/4 de tempo\nstaff:staffIndex},isRest:selectedTool==='rest'};setPlacedNotes(prev=>[...prev,newNote]);};const durations=['whole','half','quarter','eighth','sixteenth'];const notes=['C','D','E','F','G','A','B'];if(isLoading){return/*#__PURE__*/_jsx(EditorContainer,{children:/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'center',alignItems:'center',height:'400px',fontSize:'1.2rem',color:'#666'},children:\"\\uD83C\\uDFBC Carregando partitura...\"})});}return/*#__PURE__*/_jsxs(EditorContainer,{children:[/*#__PURE__*/_jsxs(EditorHeader,{children:[/*#__PURE__*/_jsxs(EditorTitle,{children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Editor de Partituras\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'1rem'},children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:title,onChange:e=>setTitle(e.target.value),placeholder:\"Nome da partitura...\"}),currentUser&&lastSaved&&/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.8rem',color:'rgba(255,255,255,0.8)'},children:[\"\\uD83D\\uDCBE Salvo \\xE0s \",lastSaved.toLocaleTimeString()]})]})]}),/*#__PURE__*/_jsxs(EditorActions,{children:[/*#__PURE__*/_jsx(ActionButton,{onClick:()=>setZoomLevel(prev=>Math.max(0.5,prev-0.1)),variant:\"primary\",disabled:zoomLevel<=0.5,children:\"\\uD83D\\uDD0D\\u2796\"}),/*#__PURE__*/_jsx(ActionButton,{onClick:()=>setZoomLevel(prev=>Math.min(2,prev+0.1)),variant:\"primary\",disabled:zoomLevel>=2,children:\"\\uD83D\\uDD0D\\u2795\"}),/*#__PURE__*/_jsx(ActionButton,{onClick:()=>{setShowChords(!showChords);setShowLyrics(false);},variant:\"primary\",children:showChords?'🎼 Partitura':'🎸 Cifras'}),/*#__PURE__*/_jsx(ActionButton,{onClick:()=>{setShowLyrics(!showLyrics);setShowChords(false);},variant:\"primary\",children:showLyrics?'🎼 Partitura':'🎤 Letras'}),/*#__PURE__*/_jsx(ActionButton,{onClick:()=>setPlacedNotes([]),variant:\"primary\",disabled:placedNotes.length===0,children:\"\\uD83D\\uDDD1\\uFE0F Limpar\"}),/*#__PURE__*/_jsx(ActionButton,{onClick:handlePlay,variant:\"primary\",children:isPlaying?'⏸️ Pausar':'▶️ Reproduzir'}),/*#__PURE__*/_jsx(ActionButton,{onClick:()=>handleSave(),variant:\"secondary\",disabled:!currentUser||isSaving,children:isSaving?'💾 Salvando...':'💾 Salvar'})]})]}),/*#__PURE__*/_jsxs(EditorContent,{children:[/*#__PURE__*/_jsxs(ToolPanel,{children:[!currentUser&&/*#__PURE__*/_jsx(GuestWarning,{children:\"\\u26A0\\uFE0F Modo visitante: suas altera\\xE7\\xF5es n\\xE3o ser\\xE3o salvas\"}),/*#__PURE__*/_jsx(InstrumentSelector,{selectedInstrument:selectedInstrument,onInstrumentChange:setSelectedInstrument,compact:true}),/*#__PURE__*/_jsxs(ToolSection,{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83C\\uDFB5 Ferramentas\"}),/*#__PURE__*/_jsxs(ToolGrid,{children:[/*#__PURE__*/_jsx(ToolButton,{active:selectedTool==='note',onClick:()=>setSelectedTool('note'),children:\"\\uD83C\\uDFB5 Nota\"}),/*#__PURE__*/_jsx(ToolButton,{active:selectedTool==='rest',onClick:()=>setSelectedTool('rest'),children:\"\\uD83C\\uDFBC Pausa\"}),/*#__PURE__*/_jsx(ToolButton,{active:selectedTool==='chord',onClick:()=>setSelectedTool('chord'),children:\"\\uD83C\\uDFB9 Acorde\"})]})]}),/*#__PURE__*/_jsxs(ToolSection,{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u23F1\\uFE0F Dura\\xE7\\xE3o\"}),/*#__PURE__*/_jsx(ToolGrid,{children:durations.map(duration=>/*#__PURE__*/_jsx(ToolButton,{active:selectedDuration===duration,onClick:()=>setSelectedDuration(duration),children:duration==='whole'?'𝅝':duration==='half'?'𝅗𝅥':duration==='quarter'?'♩':duration==='eighth'?'♫':'♬'},duration))})]}),/*#__PURE__*/_jsxs(ToolSection,{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83C\\uDFBC Notas\"}),/*#__PURE__*/_jsx(ToolGrid,{children:notes.map(note=>/*#__PURE__*/_jsx(ToolButton,{active:selectedNote===note,onClick:()=>setSelectedNote(note),children:note},note))})]}),/*#__PURE__*/_jsxs(ToolSection,{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u266F\\u266D Acidentes\"}),/*#__PURE__*/_jsxs(ToolGrid,{children:[/*#__PURE__*/_jsx(ToolButton,{active:selectedAccidental==='sharp',onClick:()=>setSelectedAccidental(selectedAccidental==='sharp'?null:'sharp'),children:\"\\u266F\"}),/*#__PURE__*/_jsx(ToolButton,{active:selectedAccidental==='flat',onClick:()=>setSelectedAccidental(selectedAccidental==='flat'?null:'flat'),children:\"\\u266D\"}),/*#__PURE__*/_jsx(ToolButton,{active:selectedAccidental==='natural',onClick:()=>setSelectedAccidental(selectedAccidental==='natural'?null:'natural'),children:\"\\u266E\"}),/*#__PURE__*/_jsx(ToolButton,{active:selectedAccidental===null,onClick:()=>setSelectedAccidental(null),children:\"\\u2014\"})]})]}),/*#__PURE__*/_jsxs(ToolSection,{children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDCCA Estat\\xEDsticas\"}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.9rem',color:'#666',lineHeight:'1.4'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"\\uD83C\\uDFBC Instrumento: \",getInstrumentTemplate(selectedInstrument).name]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\uD83D\\uDCDD Notas: \",placedNotes.filter(n=>!n.isRest).length]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u23F8\\uFE0F Pausas: \",placedNotes.filter(n=>n.isRest).length]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\uD83C\\uDFB5 Total: \",placedNotes.length]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\uD83D\\uDCCF Compassos: \",Math.max(0,...placedNotes.map(n=>n.position.measure),0)]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u266F Sustenidos: \",placedNotes.filter(n=>n.accidental==='sharp').length]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u266D Bem\\xF3is: \",placedNotes.filter(n=>n.accidental==='flat').length]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\uD83C\\uDFA4 Letras: \",lyrics.length]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\uD83D\\uDD0D Zoom: \",Math.round(zoomLevel*100),\"%\"]})]})]})]}),/*#__PURE__*/_jsx(ScoreCanvas,{children:showChords?/*#__PURE__*/_jsx(ChordView,{notes:placedNotes,title:title}):showLyrics?/*#__PURE__*/_jsx(LyricsEditor,{lyrics:lyrics,notes:placedNotes,onLyricsChange:setLyrics,title:title}):/*#__PURE__*/_jsxs(StaffContainer,{children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'1rem'},children:[/*#__PURE__*/_jsxs(\"h3\",{style:{margin:0,color:'#495057'},children:[\"Partitura: \",title]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.9rem',color:'#666'},children:[\"Zoom: \",Math.round(zoomLevel*100),\"%\"]})]}),/*#__PURE__*/_jsx(StaffSystem,{instrument:selectedInstrument,notes:placedNotes,onStaffClick:handleStaffClick,onNoteRemove:noteId=>setPlacedNotes(prev=>prev.filter(n=>n.id!==noteId)),zoomLevel:zoomLevel})]})})]})]});};", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "ScoreService", "ChordView", "LyricsEditor", "InstrumentSelector", "StaffSystem", "getInstrumentTemplate", "v4", "uuidv4", "jsx", "_jsx", "jsxs", "_jsxs", "EditorC<PERSON><PERSON>", "div", "Editor<PERSON><PERSON>er", "EditorT<PERSON>le", "EditorActions", "ActionButton", "button", "props", "variant", "Editor<PERSON><PERSON><PERSON>", "ToolPanel", "ToolSection", "ToolGrid", "<PERSON><PERSON><PERSON><PERSON>on", "active", "ScoreCanvas", "StaffC<PERSON>r", "Staff", "svg", "staffCount", "Guest<PERSON><PERSON>ning", "ScoreEditor", "_ref", "scoreId", "initialConfig", "currentUser", "title", "setTitle", "selectedTool", "setSelectedTool", "selectedDuration", "setSelectedDuration", "selected<PERSON><PERSON>", "setSelectedNote", "selectedAccidental", "setSelectedAccidental", "isPlaying", "setIsPlaying", "isSaving", "setIsSaving", "placedNotes", "setPlacedNotes", "isLoading", "setIsLoading", "showChords", "setShowChords", "showLyrics", "setShowLyrics", "lastSaved", "setLastSaved", "zoomLevel", "setZoomLevel", "lyrics", "setLyrics", "selectedInstrument", "setSelectedInstrument", "instrument", "useEffect", "loadScore", "length", "autoSaveTimer", "setTimeout", "handleSave", "clearTimeout", "score", "getScore", "userId", "uid", "allNotes", "staffs", "for<PERSON>ach", "staff", "measures", "measure", "push", "notes", "error", "console", "isAutoSave", "arguments", "undefined", "alert", "measureMap", "Map", "note", "position", "has", "set", "get", "Array", "from", "entries", "map", "_ref2", "measureNumber", "id", "number", "timeSignature", "numerator", "denominator", "sort", "a", "b", "beat", "chords", "scoreData", "composer", "key", "mode", "keyMode", "timeSignatureNum", "timeSignatureDen", "tempo", "clef", "updateData", "updateScore", "createScore", "Date", "handlePlay", "handleStaffClick", "event", "_event$currentTarget$", "staffIndex", "rect", "currentTarget", "getBoundingClientRect", "svgRect", "closest", "x", "clientX", "left", "y", "clientY", "top", "staffLines", "notePositions", "octave", "closestPosition", "reduce", "current", "Math", "abs", "measureWidth", "startX", "relativeX", "max", "floor", "beatPosition", "newNote", "name", "duration", "accidental", "round", "isRest", "prev", "durations", "children", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "color", "gap", "type", "value", "onChange", "e", "target", "placeholder", "toLocaleTimeString", "onClick", "disabled", "min", "onInstrumentChange", "compact", "lineHeight", "filter", "n", "onLyricsChange", "marginBottom", "margin", "onStaffClick", "onNoteRemove", "noteId"], "sources": ["D:/Dev/partitura_digital/src/components/ScoreEditor/ScoreEditor.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Score, MusicalNote, NoteName, NoteDuration, ClefType, InstrumentType, Octave, Lyrics } from '../../types/music';\nimport { ScoreService } from '../../services/scoreService';\nimport { ChordView } from '../ChordView/ChordView';\nimport { MusicalNote as MusicalNoteComponent } from '../MusicalNote/MusicalNote';\nimport { LyricsEditor } from '../LyricsEditor/LyricsEditor';\nimport { InstrumentSelector } from '../InstrumentSelector/InstrumentSelector';\nimport { StaffSystem } from '../StaffSystem/StaffSystem';\nimport { getInstrumentTemplate } from '../../utils/instrumentTemplates';\nimport { ScoreConfig } from '../../pages/NewScore/NewScorePage';\nimport { v4 as uuidv4 } from 'uuid';\n\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n\nconst ToolButton = styled.button<{ active?: boolean }>`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n\nconst Staff = styled.svg<{ staffCount?: number }>`\n  width: 100%;\n  height: ${props => props.staffCount === 2 ? '350px' : '200px'};\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n\n\n\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n\ninterface ScoreEditorProps {\n  scoreId?: string;\n  initialConfig?: ScoreConfig;\n}\n\nexport const ScoreEditor: React.FC<ScoreEditorProps> = ({ scoreId, initialConfig }) => {\n  const { currentUser } = useAuth();\n  const [title, setTitle] = useState(initialConfig?.title || 'Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState<'note' | 'rest' | 'chord'>('note');\n  const [selectedDuration, setSelectedDuration] = useState<NoteDuration>('quarter');\n  const [selectedNote, setSelectedNote] = useState<NoteName>('C');\n  const [selectedAccidental, setSelectedAccidental] = useState<'sharp' | 'flat' | 'natural' | null>(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [placedNotes, setPlacedNotes] = useState<MusicalNote[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showChords, setShowChords] = useState(false);\n  const [showLyrics, setShowLyrics] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\n  const [zoomLevel, setZoomLevel] = useState(1);\n  const [lyrics, setLyrics] = useState<Lyrics[]>([]);\n  const [selectedInstrument, setSelectedInstrument] = useState<InstrumentType>(initialConfig?.instrument || 'piano');\n\n  // Carregar partitura existente\n  React.useEffect(() => {\n    if (scoreId && currentUser) {\n      loadScore();\n    }\n  }, [scoreId, currentUser]);\n\n  // Salvamento automático\n  React.useEffect(() => {\n    if (!currentUser || !scoreId || placedNotes.length === 0) return;\n\n    const autoSaveTimer = setTimeout(() => {\n      handleSave(true); // true indica salvamento automático\n    }, 2000); // Salvar após 2 segundos de inatividade\n\n    return () => clearTimeout(autoSaveTimer);\n  }, [placedNotes, lyrics, title, currentUser, scoreId]);\n\n  const loadScore = async () => {\n    if (!scoreId || !currentUser) return;\n\n    setIsLoading(true);\n    try {\n      const score = await ScoreService.getScore(scoreId);\n      if (score && score.userId === currentUser.uid) {\n        setTitle(score.title);\n\n        // Extrair notas de todos os compassos\n        const allNotes: MusicalNote[] = [];\n        score.staffs.forEach(staff => {\n          staff.measures.forEach(measure => {\n            allNotes.push(...measure.notes);\n          });\n        });\n\n        setPlacedNotes(allNotes);\n        setLyrics(score.lyrics || []);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar partitura:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSave = async (isAutoSave = false) => {\n    if (!currentUser) {\n      if (!isAutoSave) {\n        alert('Você precisa estar logado para salvar partituras!');\n      }\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      // Organizar notas por compasso\n      const measureMap = new Map<number, MusicalNote[]>();\n      placedNotes.forEach(note => {\n        const measure = note.position.measure;\n        if (!measureMap.has(measure)) {\n          measureMap.set(measure, []);\n        }\n        measureMap.get(measure)!.push(note);\n      });\n\n      // Criar compassos com as notas\n      const measures = Array.from(measureMap.entries()).map(([measureNumber, notes]) => ({\n        id: uuidv4(),\n        number: measureNumber,\n        timeSignature: { numerator: 4, denominator: 4 },\n        notes: notes.sort((a, b) => a.position.beat - b.position.beat),\n        chords: []\n      }));\n\n      // Criar dados da partitura\n      const scoreData = {\n        title,\n        composer: initialConfig?.composer,\n        key: {\n          note: initialConfig?.key || 'C' as NoteName,\n          mode: initialConfig?.keyMode || 'major' as const\n        },\n        timeSignature: {\n          numerator: initialConfig?.timeSignatureNum || 4,\n          denominator: initialConfig?.timeSignatureDen || 4\n        },\n        tempo: initialConfig?.tempo || 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: getInstrumentTemplate(selectedInstrument).clef,\n          instrument: selectedInstrument,\n          measures\n        }],\n        lyrics,\n        userId: currentUser.uid\n      };\n\n      if (scoreId) {\n        // Para update, removemos o userId pois não deve ser alterado\n        const { userId, ...updateData } = scoreData;\n        await ScoreService.updateScore(scoreId, updateData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n\n      setLastSaved(new Date());\n      if (!isAutoSave) {\n        alert('Partitura salva com sucesso!');\n      }\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      if (!isAutoSave) {\n        alert('Erro ao salvar partitura');\n      }\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n\n  const handleStaffClick = (event: React.MouseEvent<SVGRectElement>, staffIndex: number = 0) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const svgRect = event.currentTarget.closest('svg')?.getBoundingClientRect();\n\n    if (!svgRect) return;\n\n    const x = event.clientX - svgRect.left;\n    const y = event.clientY - svgRect.top;\n\n    // Calcular a linha da pauta baseada na posição Y\n    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta\n    const notePositions = [\n      { y: 40, note: 'A' as NoteName, octave: 5 as Octave }, // Acima da pauta\n      { y: 50, note: 'G' as NoteName, octave: 5 as Octave }, // 5ª linha\n      { y: 60, note: 'F' as NoteName, octave: 5 as Octave }, // Entre 4ª e 5ª\n      { y: 70, note: 'E' as NoteName, octave: 5 as Octave }, // 4ª linha\n      { y: 80, note: 'D' as NoteName, octave: 5 as Octave }, // Entre 3ª e 4ª\n      { y: 90, note: 'C' as NoteName, octave: 5 as Octave }, // 3ª linha (Dó central)\n      { y: 100, note: 'B' as NoteName, octave: 4 as Octave }, // Entre 2ª e 3ª\n      { y: 110, note: 'A' as NoteName, octave: 4 as Octave }, // 2ª linha\n      { y: 120, note: 'G' as NoteName, octave: 4 as Octave }, // Entre 1ª e 2ª\n      { y: 130, note: 'F' as NoteName, octave: 4 as Octave }, // 1ª linha\n      { y: 140, note: 'E' as NoteName, octave: 4 as Octave }, // Abaixo da pauta\n    ];\n\n    // Encontrar a posição mais próxima\n    const closestPosition = notePositions.reduce((closest, current) => {\n      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;\n    });\n\n    // Calcular a posição horizontal (compasso e tempo)\n    const measureWidth = 150; // Largura aproximada de um compasso\n    const startX = 120; // Início da área de notas\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = ((relativeX % measureWidth) / measureWidth) * 4; // 4 tempos por compasso\n\n    // Criar nova nota\n    const newNote: MusicalNote = {\n      id: uuidv4(),\n      name: selectedNote, // Usar a nota selecionada no painel\n      octave: closestPosition.octave,\n      duration: selectedDuration,\n      accidental: selectedAccidental || undefined,\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4, // Arredondar para 1/4 de tempo\n        staff: staffIndex\n      },\n      isRest: selectedTool === 'rest'\n    };\n\n    setPlacedNotes(prev => [...prev, newNote]);\n  };\n\n  const durations: NoteDuration[] = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes: NoteName[] = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n\n  if (isLoading) {\n    return (\n      <EditorContainer>\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '400px',\n          fontSize: '1.2rem',\n          color: '#666'\n        }}>\n          🎼 Carregando partitura...\n        </div>\n      </EditorContainer>\n    );\n  }\n\n  return (\n    <EditorContainer>\n      <EditorHeader>\n        <EditorTitle>\n          <h2>Editor de Partituras</h2>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n            <input\n              type=\"text\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              placeholder=\"Nome da partitura...\"\n            />\n            {currentUser && lastSaved && (\n              <div style={{ fontSize: '0.8rem', color: 'rgba(255,255,255,0.8)' }}>\n                💾 Salvo às {lastSaved.toLocaleTimeString()}\n              </div>\n            )}\n          </div>\n        </EditorTitle>\n        <EditorActions>\n          <ActionButton\n            onClick={() => setZoomLevel(prev => Math.max(0.5, prev - 0.1))}\n            variant=\"primary\"\n            disabled={zoomLevel <= 0.5}\n          >\n            🔍➖\n          </ActionButton>\n          <ActionButton\n            onClick={() => setZoomLevel(prev => Math.min(2, prev + 0.1))}\n            variant=\"primary\"\n            disabled={zoomLevel >= 2}\n          >\n            🔍➕\n          </ActionButton>\n          <ActionButton\n            onClick={() => {\n              setShowChords(!showChords);\n              setShowLyrics(false);\n            }}\n            variant=\"primary\"\n          >\n            {showChords ? '🎼 Partitura' : '🎸 Cifras'}\n          </ActionButton>\n          <ActionButton\n            onClick={() => {\n              setShowLyrics(!showLyrics);\n              setShowChords(false);\n            }}\n            variant=\"primary\"\n          >\n            {showLyrics ? '🎼 Partitura' : '🎤 Letras'}\n          </ActionButton>\n          <ActionButton\n            onClick={() => setPlacedNotes([])}\n            variant=\"primary\"\n            disabled={placedNotes.length === 0}\n          >\n            🗑️ Limpar\n          </ActionButton>\n          <ActionButton onClick={handlePlay} variant=\"primary\">\n            {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}\n          </ActionButton>\n          <ActionButton\n            onClick={() => handleSave()}\n            variant=\"secondary\"\n            disabled={!currentUser || isSaving}\n          >\n            {isSaving ? '💾 Salvando...' : '💾 Salvar'}\n          </ActionButton>\n        </EditorActions>\n      </EditorHeader>\n\n      <EditorContent>\n        <ToolPanel>\n          {!currentUser && (\n            <GuestWarning>\n              ⚠️ Modo visitante: suas alterações não serão salvas\n            </GuestWarning>\n          )}\n\n          <InstrumentSelector\n            selectedInstrument={selectedInstrument}\n            onInstrumentChange={setSelectedInstrument}\n            compact={true}\n          />\n          \n          <ToolSection>\n            <h3>🎵 Ferramentas</h3>\n            <ToolGrid>\n              <ToolButton \n                active={selectedTool === 'note'}\n                onClick={() => setSelectedTool('note')}\n              >\n                🎵 Nota\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'rest'}\n                onClick={() => setSelectedTool('rest')}\n              >\n                🎼 Pausa\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'chord'}\n                onClick={() => setSelectedTool('chord')}\n              >\n                🎹 Acorde\n              </ToolButton>\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>⏱️ Duração</h3>\n            <ToolGrid>\n              {durations.map(duration => (\n                <ToolButton\n                  key={duration}\n                  active={selectedDuration === duration}\n                  onClick={() => setSelectedDuration(duration)}\n                >\n                  {duration === 'whole' ? '𝅝' : \n                   duration === 'half' ? '𝅗𝅥' :\n                   duration === 'quarter' ? '♩' :\n                   duration === 'eighth' ? '♫' : '♬'}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>🎼 Notas</h3>\n            <ToolGrid>\n              {notes.map(note => (\n                <ToolButton\n                  key={note}\n                  active={selectedNote === note}\n                  onClick={() => setSelectedNote(note)}\n                >\n                  {note}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>♯♭ Acidentes</h3>\n            <ToolGrid>\n              <ToolButton\n                active={selectedAccidental === 'sharp'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'sharp' ? null : 'sharp')}\n              >\n                ♯\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === 'flat'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'flat' ? null : 'flat')}\n              >\n                ♭\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === 'natural'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'natural' ? null : 'natural')}\n              >\n                ♮\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === null}\n                onClick={() => setSelectedAccidental(null)}\n              >\n                —\n              </ToolButton>\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>📊 Estatísticas</h3>\n            <div style={{ fontSize: '0.9rem', color: '#666', lineHeight: '1.4' }}>\n              <div>🎼 Instrumento: {getInstrumentTemplate(selectedInstrument).name}</div>\n              <div>📝 Notas: {placedNotes.filter(n => !n.isRest).length}</div>\n              <div>⏸️ Pausas: {placedNotes.filter(n => n.isRest).length}</div>\n              <div>🎵 Total: {placedNotes.length}</div>\n              <div>📏 Compassos: {Math.max(0, ...placedNotes.map(n => n.position.measure), 0)}</div>\n              <div>♯ Sustenidos: {placedNotes.filter(n => n.accidental === 'sharp').length}</div>\n              <div>♭ Bemóis: {placedNotes.filter(n => n.accidental === 'flat').length}</div>\n              <div>🎤 Letras: {lyrics.length}</div>\n              <div>🔍 Zoom: {Math.round(zoomLevel * 100)}%</div>\n            </div>\n          </ToolSection>\n        </ToolPanel>\n\n        <ScoreCanvas>\n          {showChords ? (\n            <ChordView notes={placedNotes} title={title} />\n          ) : showLyrics ? (\n            <LyricsEditor\n              lyrics={lyrics}\n              notes={placedNotes}\n              onLyricsChange={setLyrics}\n              title={title}\n            />\n          ) : (\n            <StaffContainer>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>\n                <h3 style={{ margin: 0, color: '#495057' }}>Partitura: {title}</h3>\n                <div style={{ fontSize: '0.9rem', color: '#666' }}>\n                  Zoom: {Math.round(zoomLevel * 100)}%\n                </div>\n              </div>\n              <StaffSystem\n                instrument={selectedInstrument}\n                notes={placedNotes}\n                onStaffClick={handleStaffClick}\n                onNoteRemove={(noteId: string) => setPlacedNotes(prev => prev.filter(n => n.id !== noteId))}\n                zoomLevel={zoomLevel}\n              />\n          </StaffContainer>\n          )}\n        </ScoreCanvas>\n      </EditorContent>\n    </EditorContainer>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAgB,OAAO,CAC/C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,4BAA4B,CAEpD,OAASC,YAAY,KAAQ,6BAA6B,CAC1D,OAASC,SAAS,KAAQ,wBAAwB,CAElD,OAASC,YAAY,KAAQ,8BAA8B,CAC3D,OAASC,kBAAkB,KAAQ,0CAA0C,CAC7E,OAASC,WAAW,KAAQ,4BAA4B,CACxD,OAASC,qBAAqB,KAAQ,iCAAiC,CAEvE,OAASC,EAAE,GAAI,CAAAC,MAAM,KAAQ,MAAM,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpC,KAAM,CAAAC,eAAe,CAAGd,MAAM,CAACe,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGhB,MAAM,CAACe,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAE,WAAW,CAAGjB,MAAM,CAACe,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAG,aAAa,CAAGlB,MAAM,CAACe,GAAG;AAChC;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,YAAY,CAAGnB,MAAM,CAACoB,MAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,EAAIA,KAAK,CAACC,OAAO,GAAK,SAAS,CAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,aAAa,CAAGvB,MAAM,CAACe,GAAG;AAChC;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAS,SAAS,CAAGxB,MAAM,CAACe,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAU,WAAW,CAAGzB,MAAM,CAACe,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAW,QAAQ,CAAG1B,MAAM,CAACe,GAAG;AAC3B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAY,UAAU,CAAG3B,MAAM,CAACoB,MAA4B;AACtD;AACA;AACA;AACA,gBAAgBC,KAAK,EAAIA,KAAK,CAACO,MAAM,CAAG,SAAS,CAAG,OAAO;AAC3D,WAAWP,KAAK,EAAIA,KAAK,CAACO,MAAM,CAAG,OAAO,CAAG,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMP,KAAK,EAAI,CAACA,KAAK,CAACO,MAAM,EAAI,sBAAsB;AACtD;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG7B,MAAM,CAACe,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAe,cAAc,CAAG9B,MAAM,CAACe,GAAG;AACjC;AACA;AACA,CAAC,CAED,KAAM,CAAAgB,KAAK,CAAG/B,MAAM,CAACgC,GAA4B;AACjD;AACA,YAAYX,KAAK,EAAIA,KAAK,CAACY,UAAU,GAAK,CAAC,CAAG,OAAO,CAAG,OAAO;AAC/D;AACA;AACA;AACA;AACA,CAAC,CAID,KAAM,CAAAC,YAAY,CAAGlC,MAAM,CAACe,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAOD,MAAO,MAAM,CAAAoB,WAAuC,CAAGC,IAAA,EAAgC,IAA/B,CAAEC,OAAO,CAAEC,aAAc,CAAC,CAAAF,IAAA,CAChF,KAAM,CAAEG,WAAY,CAAC,CAAGtC,OAAO,CAAC,CAAC,CACjC,KAAM,CAACuC,KAAK,CAAEC,QAAQ,CAAC,CAAG1C,QAAQ,CAAC,CAAAuC,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEE,KAAK,GAAI,gBAAgB,CAAC,CAC5E,KAAM,CAACE,YAAY,CAAEC,eAAe,CAAC,CAAG5C,QAAQ,CAA4B,MAAM,CAAC,CACnF,KAAM,CAAC6C,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG9C,QAAQ,CAAe,SAAS,CAAC,CACjF,KAAM,CAAC+C,YAAY,CAAEC,eAAe,CAAC,CAAGhD,QAAQ,CAAW,GAAG,CAAC,CAC/D,KAAM,CAACiD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlD,QAAQ,CAAsC,IAAI,CAAC,CACvG,KAAM,CAACmD,SAAS,CAAEC,YAAY,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACqD,QAAQ,CAAEC,WAAW,CAAC,CAAGtD,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACuD,WAAW,CAAEC,cAAc,CAAC,CAAGxD,QAAQ,CAAgB,EAAE,CAAC,CACjE,KAAM,CAACyD,SAAS,CAAEC,YAAY,CAAC,CAAG1D,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC2D,UAAU,CAAEC,aAAa,CAAC,CAAG5D,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC6D,UAAU,CAAEC,aAAa,CAAC,CAAG9D,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC+D,SAAS,CAAEC,YAAY,CAAC,CAAGhE,QAAQ,CAAc,IAAI,CAAC,CAC7D,KAAM,CAACiE,SAAS,CAAEC,YAAY,CAAC,CAAGlE,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAACmE,MAAM,CAAEC,SAAS,CAAC,CAAGpE,QAAQ,CAAW,EAAE,CAAC,CAClD,KAAM,CAACqE,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGtE,QAAQ,CAAiB,CAAAuC,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEgC,UAAU,GAAI,OAAO,CAAC,CAElH;AACAxE,KAAK,CAACyE,SAAS,CAAC,IAAM,CACpB,GAAIlC,OAAO,EAAIE,WAAW,CAAE,CAC1BiC,SAAS,CAAC,CAAC,CACb,CACF,CAAC,CAAE,CAACnC,OAAO,CAAEE,WAAW,CAAC,CAAC,CAE1B;AACAzC,KAAK,CAACyE,SAAS,CAAC,IAAM,CACpB,GAAI,CAAChC,WAAW,EAAI,CAACF,OAAO,EAAIiB,WAAW,CAACmB,MAAM,GAAK,CAAC,CAAE,OAE1D,KAAM,CAAAC,aAAa,CAAGC,UAAU,CAAC,IAAM,CACrCC,UAAU,CAAC,IAAI,CAAC,CAAE;AACpB,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMC,YAAY,CAACH,aAAa,CAAC,CAC1C,CAAC,CAAE,CAACpB,WAAW,CAAEY,MAAM,CAAE1B,KAAK,CAAED,WAAW,CAAEF,OAAO,CAAC,CAAC,CAEtD,KAAM,CAAAmC,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CAACnC,OAAO,EAAI,CAACE,WAAW,CAAE,OAE9BkB,YAAY,CAAC,IAAI,CAAC,CAClB,GAAI,CACF,KAAM,CAAAqB,KAAK,CAAG,KAAM,CAAA5E,YAAY,CAAC6E,QAAQ,CAAC1C,OAAO,CAAC,CAClD,GAAIyC,KAAK,EAAIA,KAAK,CAACE,MAAM,GAAKzC,WAAW,CAAC0C,GAAG,CAAE,CAC7CxC,QAAQ,CAACqC,KAAK,CAACtC,KAAK,CAAC,CAErB;AACA,KAAM,CAAA0C,QAAuB,CAAG,EAAE,CAClCJ,KAAK,CAACK,MAAM,CAACC,OAAO,CAACC,KAAK,EAAI,CAC5BA,KAAK,CAACC,QAAQ,CAACF,OAAO,CAACG,OAAO,EAAI,CAChCL,QAAQ,CAACM,IAAI,CAAC,GAAGD,OAAO,CAACE,KAAK,CAAC,CACjC,CAAC,CAAC,CACJ,CAAC,CAAC,CAEFlC,cAAc,CAAC2B,QAAQ,CAAC,CACxBf,SAAS,CAACW,KAAK,CAACZ,MAAM,EAAI,EAAE,CAAC,CAC/B,CACF,CAAE,MAAOwB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CAAC,OAAS,CACRjC,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAmB,UAAU,CAAG,cAAAA,CAAA,CAA8B,IAAvB,CAAAgB,UAAU,CAAAC,SAAA,CAAApB,MAAA,IAAAoB,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,KAAK,CAC1C,GAAI,CAACtD,WAAW,CAAE,CAChB,GAAI,CAACqD,UAAU,CAAE,CACfG,KAAK,CAAC,mDAAmD,CAAC,CAC5D,CACA,OACF,CAEA1C,WAAW,CAAC,IAAI,CAAC,CACjB,GAAI,CACF;AACA,KAAM,CAAA2C,UAAU,CAAG,GAAI,CAAAC,GAAG,CAAwB,CAAC,CACnD3C,WAAW,CAAC8B,OAAO,CAACc,IAAI,EAAI,CAC1B,KAAM,CAAAX,OAAO,CAAGW,IAAI,CAACC,QAAQ,CAACZ,OAAO,CACrC,GAAI,CAACS,UAAU,CAACI,GAAG,CAACb,OAAO,CAAC,CAAE,CAC5BS,UAAU,CAACK,GAAG,CAACd,OAAO,CAAE,EAAE,CAAC,CAC7B,CACAS,UAAU,CAACM,GAAG,CAACf,OAAO,CAAC,CAAEC,IAAI,CAACU,IAAI,CAAC,CACrC,CAAC,CAAC,CAEF;AACA,KAAM,CAAAZ,QAAQ,CAAGiB,KAAK,CAACC,IAAI,CAACR,UAAU,CAACS,OAAO,CAAC,CAAC,CAAC,CAACC,GAAG,CAACC,KAAA,MAAC,CAACC,aAAa,CAAEnB,KAAK,CAAC,CAAAkB,KAAA,OAAM,CACjFE,EAAE,CAAEpG,MAAM,CAAC,CAAC,CACZqG,MAAM,CAAEF,aAAa,CACrBG,aAAa,CAAE,CAAEC,SAAS,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAC/CxB,KAAK,CAAEA,KAAK,CAACyB,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAChB,QAAQ,CAACkB,IAAI,CAAGD,CAAC,CAACjB,QAAQ,CAACkB,IAAI,CAAC,CAC9DC,MAAM,CAAE,EACV,CAAC,EAAC,CAAC,CAEH;AACA,KAAM,CAAAC,SAAS,CAAG,CAChB/E,KAAK,CACLgF,QAAQ,CAAElF,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEkF,QAAQ,CACjCC,GAAG,CAAE,CACHvB,IAAI,CAAE,CAAA5D,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEmF,GAAG,GAAI,GAAe,CAC3CC,IAAI,CAAE,CAAApF,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEqF,OAAO,GAAI,OAClC,CAAC,CACDZ,aAAa,CAAE,CACbC,SAAS,CAAE,CAAA1E,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEsF,gBAAgB,GAAI,CAAC,CAC/CX,WAAW,CAAE,CAAA3E,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEuF,gBAAgB,GAAI,CAClD,CAAC,CACDC,KAAK,CAAE,CAAAxF,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEwF,KAAK,GAAI,GAAG,CAClC3C,MAAM,CAAE,CAAC,CACP0B,EAAE,CAAEpG,MAAM,CAAC,CAAC,CACZsH,IAAI,CAAExH,qBAAqB,CAAC6D,kBAAkB,CAAC,CAAC2D,IAAI,CACpDzD,UAAU,CAAEF,kBAAkB,CAC9BkB,QACF,CAAC,CAAC,CACFpB,MAAM,CACNc,MAAM,CAAEzC,WAAW,CAAC0C,GACtB,CAAC,CAED,GAAI5C,OAAO,CAAE,CACX;AACA,KAAM,CAAE2C,MAAM,CAAE,GAAGgD,UAAW,CAAC,CAAGT,SAAS,CAC3C,KAAM,CAAArH,YAAY,CAAC+H,WAAW,CAAC5F,OAAO,CAAE2F,UAAU,CAAC,CACrD,CAAC,IAAM,CACL,KAAM,CAAA9H,YAAY,CAACgI,WAAW,CAACX,SAAS,CAAEhF,WAAW,CAAC0C,GAAG,CAAC,CAC5D,CAEAlB,YAAY,CAAC,GAAI,CAAAoE,IAAI,CAAC,CAAC,CAAC,CACxB,GAAI,CAACvC,UAAU,CAAE,CACfG,KAAK,CAAC,8BAA8B,CAAC,CACvC,CACF,CAAE,MAAOL,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,GAAI,CAACE,UAAU,CAAE,CACfG,KAAK,CAAC,0BAA0B,CAAC,CACnC,CACF,CAAC,OAAS,CACR1C,WAAW,CAAC,KAAK,CAAC,CACpB,CACF,CAAC,CAED,KAAM,CAAA+E,UAAU,CAAGA,CAAA,GAAM,CACvBjF,YAAY,CAAC,CAACD,SAAS,CAAC,CACxB;AACF,CAAC,CAED,KAAM,CAAAmF,gBAAgB,CAAG,QAAAA,CAACC,KAAuC,CAA6B,KAAAC,qBAAA,IAA3B,CAAAC,UAAkB,CAAA3C,SAAA,CAAApB,MAAA,IAAAoB,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,CAAC,CACvF,KAAM,CAAA4C,IAAI,CAAGH,KAAK,CAACI,aAAa,CAACC,qBAAqB,CAAC,CAAC,CACxD,KAAM,CAAAC,OAAO,EAAAL,qBAAA,CAAGD,KAAK,CAACI,aAAa,CAACG,OAAO,CAAC,KAAK,CAAC,UAAAN,qBAAA,iBAAlCA,qBAAA,CAAoCI,qBAAqB,CAAC,CAAC,CAE3E,GAAI,CAACC,OAAO,CAAE,OAEd,KAAM,CAAAE,CAAC,CAAGR,KAAK,CAACS,OAAO,CAAGH,OAAO,CAACI,IAAI,CACtC,KAAM,CAAAC,CAAC,CAAGX,KAAK,CAACY,OAAO,CAAGN,OAAO,CAACO,GAAG,CAErC;AACA,KAAM,CAAAC,UAAU,CAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAG,CAAE,GAAG,CAAC,CAAE;AAC3C,KAAM,CAAAC,aAAa,CAAG,CACpB,CAAEJ,CAAC,CAAE,EAAE,CAAE/C,IAAI,CAAE,GAAe,CAAEoD,MAAM,CAAE,CAAY,CAAC,CAAE;AACvD,CAAEL,CAAC,CAAE,EAAE,CAAE/C,IAAI,CAAE,GAAe,CAAEoD,MAAM,CAAE,CAAY,CAAC,CAAE;AACvD,CAAEL,CAAC,CAAE,EAAE,CAAE/C,IAAI,CAAE,GAAe,CAAEoD,MAAM,CAAE,CAAY,CAAC,CAAE;AACvD,CAAEL,CAAC,CAAE,EAAE,CAAE/C,IAAI,CAAE,GAAe,CAAEoD,MAAM,CAAE,CAAY,CAAC,CAAE;AACvD,CAAEL,CAAC,CAAE,EAAE,CAAE/C,IAAI,CAAE,GAAe,CAAEoD,MAAM,CAAE,CAAY,CAAC,CAAE;AACvD,CAAEL,CAAC,CAAE,EAAE,CAAE/C,IAAI,CAAE,GAAe,CAAEoD,MAAM,CAAE,CAAY,CAAC,CAAE;AACvD,CAAEL,CAAC,CAAE,GAAG,CAAE/C,IAAI,CAAE,GAAe,CAAEoD,MAAM,CAAE,CAAY,CAAC,CAAE;AACxD,CAAEL,CAAC,CAAE,GAAG,CAAE/C,IAAI,CAAE,GAAe,CAAEoD,MAAM,CAAE,CAAY,CAAC,CAAE;AACxD,CAAEL,CAAC,CAAE,GAAG,CAAE/C,IAAI,CAAE,GAAe,CAAEoD,MAAM,CAAE,CAAY,CAAC,CAAE;AACxD,CAAEL,CAAC,CAAE,GAAG,CAAE/C,IAAI,CAAE,GAAe,CAAEoD,MAAM,CAAE,CAAY,CAAC,CAAE;AACxD,CAAEL,CAAC,CAAE,GAAG,CAAE/C,IAAI,CAAE,GAAe,CAAEoD,MAAM,CAAE,CAAY,CAAG;AAAA,CACzD,CAED;AACA,KAAM,CAAAC,eAAe,CAAGF,aAAa,CAACG,MAAM,CAAC,CAACX,OAAO,CAAEY,OAAO,GAAK,CACjE,MAAO,CAAAC,IAAI,CAACC,GAAG,CAACF,OAAO,CAACR,CAAC,CAAGA,CAAC,CAAC,CAAGS,IAAI,CAACC,GAAG,CAACd,OAAO,CAACI,CAAC,CAAGA,CAAC,CAAC,CAAGQ,OAAO,CAAGZ,OAAO,CAC9E,CAAC,CAAC,CAEF;AACA,KAAM,CAAAe,YAAY,CAAG,GAAG,CAAE;AAC1B,KAAM,CAAAC,MAAM,CAAG,GAAG,CAAE;AACpB,KAAM,CAAAC,SAAS,CAAGhB,CAAC,CAAGe,MAAM,CAC5B,KAAM,CAAAtE,OAAO,CAAGmE,IAAI,CAACK,GAAG,CAAC,CAAC,CAAEL,IAAI,CAACM,KAAK,CAACF,SAAS,CAAGF,YAAY,CAAC,CAAG,CAAC,CAAC,CACrE,KAAM,CAAAK,YAAY,CAAKH,SAAS,CAAGF,YAAY,CAAIA,YAAY,CAAI,CAAC,CAAE;AAEtE;AACA,KAAM,CAAAM,OAAoB,CAAG,CAC3BrD,EAAE,CAAEpG,MAAM,CAAC,CAAC,CACZ0J,IAAI,CAAErH,YAAY,CAAE;AACpBwG,MAAM,CAAEC,eAAe,CAACD,MAAM,CAC9Bc,QAAQ,CAAExH,gBAAgB,CAC1ByH,UAAU,CAAErH,kBAAkB,EAAI8C,SAAS,CAC3CK,QAAQ,CAAE,CACRZ,OAAO,CACP8B,IAAI,CAAEqC,IAAI,CAACY,KAAK,CAACL,YAAY,CAAG,CAAC,CAAC,CAAG,CAAC,CAAE;AACxC5E,KAAK,CAAEmD,UACT,CAAC,CACD+B,MAAM,CAAE7H,YAAY,GAAK,MAC3B,CAAC,CAEDa,cAAc,CAACiH,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEN,OAAO,CAAC,CAAC,CAC5C,CAAC,CAED,KAAM,CAAAO,SAAyB,CAAG,CAAC,OAAO,CAAE,MAAM,CAAE,SAAS,CAAE,QAAQ,CAAE,WAAW,CAAC,CACrF,KAAM,CAAAhF,KAAiB,CAAG,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAE7D,GAAIjC,SAAS,CAAE,CACb,mBACE7C,IAAA,CAACG,eAAe,EAAA4J,QAAA,cACd/J,IAAA,QAAKgK,KAAK,CAAE,CACVC,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,QAAQ,CACpBC,MAAM,CAAE,OAAO,CACfC,QAAQ,CAAE,QAAQ,CAClBC,KAAK,CAAE,MACT,CAAE,CAAAP,QAAA,CAAC,sCAEH,CAAK,CAAC,CACS,CAAC,CAEtB,CAEA,mBACE7J,KAAA,CAACC,eAAe,EAAA4J,QAAA,eACd7J,KAAA,CAACG,YAAY,EAAA0J,QAAA,eACX7J,KAAA,CAACI,WAAW,EAAAyJ,QAAA,eACV/J,IAAA,OAAA+J,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7B7J,KAAA,QAAK8J,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEI,GAAG,CAAE,MAAO,CAAE,CAAAR,QAAA,eACjE/J,IAAA,UACEwK,IAAI,CAAC,MAAM,CACXC,KAAK,CAAE5I,KAAM,CACb6I,QAAQ,CAAGC,CAAC,EAAK7I,QAAQ,CAAC6I,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC1CI,WAAW,CAAC,sBAAsB,CACnC,CAAC,CACDjJ,WAAW,EAAIuB,SAAS,eACvBjD,KAAA,QAAK8J,KAAK,CAAE,CAAEK,QAAQ,CAAE,QAAQ,CAAEC,KAAK,CAAE,uBAAwB,CAAE,CAAAP,QAAA,EAAC,2BACtD,CAAC5G,SAAS,CAAC2H,kBAAkB,CAAC,CAAC,EACxC,CACN,EACE,CAAC,EACK,CAAC,cACd5K,KAAA,CAACK,aAAa,EAAAwJ,QAAA,eACZ/J,IAAA,CAACQ,YAAY,EACXuK,OAAO,CAAEA,CAAA,GAAMzH,YAAY,CAACuG,IAAI,EAAId,IAAI,CAACK,GAAG,CAAC,GAAG,CAAES,IAAI,CAAG,GAAG,CAAC,CAAE,CAC/DlJ,OAAO,CAAC,SAAS,CACjBqK,QAAQ,CAAE3H,SAAS,EAAI,GAAI,CAAA0G,QAAA,CAC5B,oBAED,CAAc,CAAC,cACf/J,IAAA,CAACQ,YAAY,EACXuK,OAAO,CAAEA,CAAA,GAAMzH,YAAY,CAACuG,IAAI,EAAId,IAAI,CAACkC,GAAG,CAAC,CAAC,CAAEpB,IAAI,CAAG,GAAG,CAAC,CAAE,CAC7DlJ,OAAO,CAAC,SAAS,CACjBqK,QAAQ,CAAE3H,SAAS,EAAI,CAAE,CAAA0G,QAAA,CAC1B,oBAED,CAAc,CAAC,cACf/J,IAAA,CAACQ,YAAY,EACXuK,OAAO,CAAEA,CAAA,GAAM,CACb/H,aAAa,CAAC,CAACD,UAAU,CAAC,CAC1BG,aAAa,CAAC,KAAK,CAAC,CACtB,CAAE,CACFvC,OAAO,CAAC,SAAS,CAAAoJ,QAAA,CAEhBhH,UAAU,CAAG,cAAc,CAAG,WAAW,CAC9B,CAAC,cACf/C,IAAA,CAACQ,YAAY,EACXuK,OAAO,CAAEA,CAAA,GAAM,CACb7H,aAAa,CAAC,CAACD,UAAU,CAAC,CAC1BD,aAAa,CAAC,KAAK,CAAC,CACtB,CAAE,CACFrC,OAAO,CAAC,SAAS,CAAAoJ,QAAA,CAEhB9G,UAAU,CAAG,cAAc,CAAG,WAAW,CAC9B,CAAC,cACfjD,IAAA,CAACQ,YAAY,EACXuK,OAAO,CAAEA,CAAA,GAAMnI,cAAc,CAAC,EAAE,CAAE,CAClCjC,OAAO,CAAC,SAAS,CACjBqK,QAAQ,CAAErI,WAAW,CAACmB,MAAM,GAAK,CAAE,CAAAiG,QAAA,CACpC,2BAED,CAAc,CAAC,cACf/J,IAAA,CAACQ,YAAY,EAACuK,OAAO,CAAEtD,UAAW,CAAC9G,OAAO,CAAC,SAAS,CAAAoJ,QAAA,CACjDxH,SAAS,CAAG,WAAW,CAAG,eAAe,CAC9B,CAAC,cACfvC,IAAA,CAACQ,YAAY,EACXuK,OAAO,CAAEA,CAAA,GAAM9G,UAAU,CAAC,CAAE,CAC5BtD,OAAO,CAAC,WAAW,CACnBqK,QAAQ,CAAE,CAACpJ,WAAW,EAAIa,QAAS,CAAAsH,QAAA,CAElCtH,QAAQ,CAAG,gBAAgB,CAAG,WAAW,CAC9B,CAAC,EACF,CAAC,EACJ,CAAC,cAEfvC,KAAA,CAACU,aAAa,EAAAmJ,QAAA,eACZ7J,KAAA,CAACW,SAAS,EAAAkJ,QAAA,EACP,CAACnI,WAAW,eACX5B,IAAA,CAACuB,YAAY,EAAAwI,QAAA,CAAC,2EAEd,CAAc,CACf,cAED/J,IAAA,CAACN,kBAAkB,EACjB+D,kBAAkB,CAAEA,kBAAmB,CACvCyH,kBAAkB,CAAExH,qBAAsB,CAC1CyH,OAAO,CAAE,IAAK,CACf,CAAC,cAEFjL,KAAA,CAACY,WAAW,EAAAiJ,QAAA,eACV/J,IAAA,OAAA+J,QAAA,CAAI,0BAAc,CAAI,CAAC,cACvB7J,KAAA,CAACa,QAAQ,EAAAgJ,QAAA,eACP/J,IAAA,CAACgB,UAAU,EACTC,MAAM,CAAEc,YAAY,GAAK,MAAO,CAChCgJ,OAAO,CAAEA,CAAA,GAAM/I,eAAe,CAAC,MAAM,CAAE,CAAA+H,QAAA,CACxC,mBAED,CAAY,CAAC,cACb/J,IAAA,CAACgB,UAAU,EACTC,MAAM,CAAEc,YAAY,GAAK,MAAO,CAChCgJ,OAAO,CAAEA,CAAA,GAAM/I,eAAe,CAAC,MAAM,CAAE,CAAA+H,QAAA,CACxC,oBAED,CAAY,CAAC,cACb/J,IAAA,CAACgB,UAAU,EACTC,MAAM,CAAEc,YAAY,GAAK,OAAQ,CACjCgJ,OAAO,CAAEA,CAAA,GAAM/I,eAAe,CAAC,OAAO,CAAE,CAAA+H,QAAA,CACzC,qBAED,CAAY,CAAC,EACL,CAAC,EACA,CAAC,cAEd7J,KAAA,CAACY,WAAW,EAAAiJ,QAAA,eACV/J,IAAA,OAAA+J,QAAA,CAAI,4BAAU,CAAI,CAAC,cACnB/J,IAAA,CAACe,QAAQ,EAAAgJ,QAAA,CACND,SAAS,CAAC/D,GAAG,CAAC0D,QAAQ,eACrBzJ,IAAA,CAACgB,UAAU,EAETC,MAAM,CAAEgB,gBAAgB,GAAKwH,QAAS,CACtCsB,OAAO,CAAEA,CAAA,GAAM7I,mBAAmB,CAACuH,QAAQ,CAAE,CAAAM,QAAA,CAE5CN,QAAQ,GAAK,OAAO,CAAG,IAAI,CAC3BA,QAAQ,GAAK,MAAM,CAAG,MAAM,CAC5BA,QAAQ,GAAK,SAAS,CAAG,GAAG,CAC5BA,QAAQ,GAAK,QAAQ,CAAG,GAAG,CAAG,GAAG,EAP7BA,QAQK,CACb,CAAC,CACM,CAAC,EACA,CAAC,cAEdvJ,KAAA,CAACY,WAAW,EAAAiJ,QAAA,eACV/J,IAAA,OAAA+J,QAAA,CAAI,oBAAQ,CAAI,CAAC,cACjB/J,IAAA,CAACe,QAAQ,EAAAgJ,QAAA,CACNjF,KAAK,CAACiB,GAAG,CAACR,IAAI,eACbvF,IAAA,CAACgB,UAAU,EAETC,MAAM,CAAEkB,YAAY,GAAKoD,IAAK,CAC9BwF,OAAO,CAAEA,CAAA,GAAM3I,eAAe,CAACmD,IAAI,CAAE,CAAAwE,QAAA,CAEpCxE,IAAI,EAJAA,IAKK,CACb,CAAC,CACM,CAAC,EACA,CAAC,cAEdrF,KAAA,CAACY,WAAW,EAAAiJ,QAAA,eACV/J,IAAA,OAAA+J,QAAA,CAAI,wBAAY,CAAI,CAAC,cACrB7J,KAAA,CAACa,QAAQ,EAAAgJ,QAAA,eACP/J,IAAA,CAACgB,UAAU,EACTC,MAAM,CAAEoB,kBAAkB,GAAK,OAAQ,CACvC0I,OAAO,CAAEA,CAAA,GAAMzI,qBAAqB,CAACD,kBAAkB,GAAK,OAAO,CAAG,IAAI,CAAG,OAAO,CAAE,CAAA0H,QAAA,CACvF,QAED,CAAY,CAAC,cACb/J,IAAA,CAACgB,UAAU,EACTC,MAAM,CAAEoB,kBAAkB,GAAK,MAAO,CACtC0I,OAAO,CAAEA,CAAA,GAAMzI,qBAAqB,CAACD,kBAAkB,GAAK,MAAM,CAAG,IAAI,CAAG,MAAM,CAAE,CAAA0H,QAAA,CACrF,QAED,CAAY,CAAC,cACb/J,IAAA,CAACgB,UAAU,EACTC,MAAM,CAAEoB,kBAAkB,GAAK,SAAU,CACzC0I,OAAO,CAAEA,CAAA,GAAMzI,qBAAqB,CAACD,kBAAkB,GAAK,SAAS,CAAG,IAAI,CAAG,SAAS,CAAE,CAAA0H,QAAA,CAC3F,QAED,CAAY,CAAC,cACb/J,IAAA,CAACgB,UAAU,EACTC,MAAM,CAAEoB,kBAAkB,GAAK,IAAK,CACpC0I,OAAO,CAAEA,CAAA,GAAMzI,qBAAqB,CAAC,IAAI,CAAE,CAAAyH,QAAA,CAC5C,QAED,CAAY,CAAC,EACL,CAAC,EACA,CAAC,cAEd7J,KAAA,CAACY,WAAW,EAAAiJ,QAAA,eACV/J,IAAA,OAAA+J,QAAA,CAAI,8BAAe,CAAI,CAAC,cACxB7J,KAAA,QAAK8J,KAAK,CAAE,CAAEK,QAAQ,CAAE,QAAQ,CAAEC,KAAK,CAAE,MAAM,CAAEc,UAAU,CAAE,KAAM,CAAE,CAAArB,QAAA,eACnE7J,KAAA,QAAA6J,QAAA,EAAK,4BAAgB,CAACnK,qBAAqB,CAAC6D,kBAAkB,CAAC,CAAC+F,IAAI,EAAM,CAAC,cAC3EtJ,KAAA,QAAA6J,QAAA,EAAK,sBAAU,CAACpH,WAAW,CAAC0I,MAAM,CAACC,CAAC,EAAI,CAACA,CAAC,CAAC1B,MAAM,CAAC,CAAC9F,MAAM,EAAM,CAAC,cAChE5D,KAAA,QAAA6J,QAAA,EAAK,uBAAW,CAACpH,WAAW,CAAC0I,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC1B,MAAM,CAAC,CAAC9F,MAAM,EAAM,CAAC,cAChE5D,KAAA,QAAA6J,QAAA,EAAK,sBAAU,CAACpH,WAAW,CAACmB,MAAM,EAAM,CAAC,cACzC5D,KAAA,QAAA6J,QAAA,EAAK,0BAAc,CAAChB,IAAI,CAACK,GAAG,CAAC,CAAC,CAAE,GAAGzG,WAAW,CAACoD,GAAG,CAACuF,CAAC,EAAIA,CAAC,CAAC9F,QAAQ,CAACZ,OAAO,CAAC,CAAE,CAAC,CAAC,EAAM,CAAC,cACtF1E,KAAA,QAAA6J,QAAA,EAAK,qBAAc,CAACpH,WAAW,CAAC0I,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC5B,UAAU,GAAK,OAAO,CAAC,CAAC5F,MAAM,EAAM,CAAC,cACnF5D,KAAA,QAAA6J,QAAA,EAAK,oBAAU,CAACpH,WAAW,CAAC0I,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC5B,UAAU,GAAK,MAAM,CAAC,CAAC5F,MAAM,EAAM,CAAC,cAC9E5D,KAAA,QAAA6J,QAAA,EAAK,uBAAW,CAACxG,MAAM,CAACO,MAAM,EAAM,CAAC,cACrC5D,KAAA,QAAA6J,QAAA,EAAK,qBAAS,CAAChB,IAAI,CAACY,KAAK,CAACtG,SAAS,CAAG,GAAG,CAAC,CAAC,GAAC,EAAK,CAAC,EAC/C,CAAC,EACK,CAAC,EACL,CAAC,cAEZrD,IAAA,CAACkB,WAAW,EAAA6I,QAAA,CACThH,UAAU,cACT/C,IAAA,CAACR,SAAS,EAACsF,KAAK,CAAEnC,WAAY,CAACd,KAAK,CAAEA,KAAM,CAAE,CAAC,CAC7CoB,UAAU,cACZjD,IAAA,CAACP,YAAY,EACX8D,MAAM,CAAEA,MAAO,CACfuB,KAAK,CAAEnC,WAAY,CACnB4I,cAAc,CAAE/H,SAAU,CAC1B3B,KAAK,CAAEA,KAAM,CACd,CAAC,cAEF3B,KAAA,CAACiB,cAAc,EAAA4I,QAAA,eACb7J,KAAA,QAAK8J,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEqB,YAAY,CAAE,MAAO,CAAE,CAAAzB,QAAA,eAC3G7J,KAAA,OAAI8J,KAAK,CAAE,CAAEyB,MAAM,CAAE,CAAC,CAAEnB,KAAK,CAAE,SAAU,CAAE,CAAAP,QAAA,EAAC,aAAW,CAAClI,KAAK,EAAK,CAAC,cACnE3B,KAAA,QAAK8J,KAAK,CAAE,CAAEK,QAAQ,CAAE,QAAQ,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAP,QAAA,EAAC,QAC3C,CAAChB,IAAI,CAACY,KAAK,CAACtG,SAAS,CAAG,GAAG,CAAC,CAAC,GACrC,EAAK,CAAC,EACH,CAAC,cACNrD,IAAA,CAACL,WAAW,EACVgE,UAAU,CAAEF,kBAAmB,CAC/BqB,KAAK,CAAEnC,WAAY,CACnB+I,YAAY,CAAEhE,gBAAiB,CAC/BiE,YAAY,CAAGC,MAAc,EAAKhJ,cAAc,CAACiH,IAAI,EAAIA,IAAI,CAACwB,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACpF,EAAE,GAAK0F,MAAM,CAAC,CAAE,CAC5FvI,SAAS,CAAEA,SAAU,CACtB,CAAC,EACU,CACf,CACU,CAAC,EACD,CAAC,EACD,CAAC,CAEtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}