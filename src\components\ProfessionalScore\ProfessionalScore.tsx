import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
// VexFlow será implementado em versão futura
import * as Tone from 'tone';
import { MusicalNote, Lyrics, InstrumentType } from '../../types/music';
import { getOrchestralInstrument } from '../../utils/orchestralInstruments';

const ScoreContainer = styled.div`
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  margin: 1rem 0;
  overflow-x: auto;
`;

const ScoreHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 1rem;
`;

const ScoreTitle = styled.h1`
  font-size: 2rem;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  font-family: 'Times New Roman', serif;
`;

const ScoreSubtitle = styled.div`
  font-size: 1.1rem;
  color: #666;
  font-style: italic;
`;

const ScoreCanvas = styled.div`
  min-height: 400px;
  width: 100%;
  
  svg {
    width: 100%;
    height: auto;
  }
`;

const PlaybackControls = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
`;

const PlayButton = styled.button<{ isPlaying?: boolean }>`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ${props => props.isPlaying ? `
    background: #e74c3c;
    color: white;
    &:hover { background: #c0392b; }
  ` : `
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    &:hover { 
      background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
    }
  `}
`;

const TempoControl = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  label {
    font-weight: 600;
    color: #495057;
  }
  
  input {
    width: 80px;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    text-align: center;
  }
`;

const ProgressBar = styled.div`
  flex: 1;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
`;

const ProgressFill = styled.div<{ progress: number }>`
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  width: ${props => props.progress}%;
  transition: width 0.1s ease;
`;

// PlaybackLine será implementado em versão futura

interface ProfessionalScoreProps {
  title: string;
  composer?: string;
  notes: MusicalNote[];
  lyrics: Lyrics[];
  instruments: InstrumentType[];
  tempo: number;
  onTempoChange: (tempo: number) => void;
  onNoteClick?: (x: number, y: number, staffIndex: number) => void;
}

export const ProfessionalScore: React.FC<ProfessionalScoreProps> = ({
  title,
  composer,
  notes,
  lyrics,
  instruments,
  tempo,
  onTempoChange,
  onNoteClick
}) => {
  const scoreRef = useRef<HTMLDivElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackProgress, setPlaybackProgress] = useState(0);
  const [synth, setSynth] = useState<Tone.PolySynth | null>(null);

  // Inicializar Tone.js
  useEffect(() => {
    const synthInstance = new Tone.PolySynth(Tone.Synth).toDestination();
    setSynth(synthInstance);
    
    return () => {
      synthInstance.dispose();
    };
  }, []);

  // Renderizar partitura (versão simplificada)
  useEffect(() => {
    if (!scoreRef.current) return;

    // Limpar canvas anterior
    scoreRef.current.innerHTML = '';

    // Criar SVG simples para demonstração
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '800');
    svg.setAttribute('height', '600');
    svg.style.background = 'white';
    svg.style.border = '1px solid #e9ecef';
    svg.style.borderRadius = '8px';

    // Adicionar título
    const titleText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    titleText.setAttribute('x', '400');
    titleText.setAttribute('y', '30');
    titleText.setAttribute('text-anchor', 'middle');
    titleText.setAttribute('font-size', '18');
    titleText.setAttribute('font-weight', 'bold');
    titleText.textContent = title || 'Partitura';
    svg.appendChild(titleText);

    // Criar pautas para cada instrumento
    let yPosition = 60;
    const systemHeight = 120;

    instruments.forEach((instrumentId, instrumentIndex) => {
      const template = getOrchestralInstrument(instrumentId);

      // Desenhar 5 linhas da pauta
      for (let i = 0; i < 5; i++) {
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', '50');
        line.setAttribute('y1', (yPosition + i * 10).toString());
        line.setAttribute('x2', '750');
        line.setAttribute('y2', (yPosition + i * 10).toString());
        line.setAttribute('stroke', '#000');
        line.setAttribute('stroke-width', '1');
        svg.appendChild(line);
      }

      // Adicionar nome do instrumento
      const instrumentText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      instrumentText.setAttribute('x', '10');
      instrumentText.setAttribute('y', (yPosition + 20).toString());
      instrumentText.setAttribute('font-size', '12');
      instrumentText.setAttribute('font-weight', 'bold');
      instrumentText.textContent = template.name;
      svg.appendChild(instrumentText);

      // Adicionar clave
      const clefText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      clefText.setAttribute('x', '60');
      clefText.setAttribute('y', (yPosition + 25).toString());
      clefText.setAttribute('font-size', '24');
      clefText.textContent = template.clef === 'treble' ? '𝄞' : template.clef === 'bass' ? '𝄢' : '𝄡';
      svg.appendChild(clefText);

      // Renderizar notas deste instrumento
      const instrumentNotes = notes.filter(note =>
        note.position.staff === instrumentIndex
      );

      instrumentNotes.forEach((note, noteIndex) => {
        const x = 100 + (note.position.measure - 1) * 150 + (note.position.beat - 1) * 30;
        const y = yPosition + 20; // Posição simplificada

        // Desenhar nota
        const noteCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        noteCircle.setAttribute('cx', x.toString());
        noteCircle.setAttribute('cy', y.toString());
        noteCircle.setAttribute('r', '4');
        noteCircle.setAttribute('fill', note.isRest ? 'none' : '#000');
        noteCircle.setAttribute('stroke', '#000');
        svg.appendChild(noteCircle);

        // Adicionar nome da nota
        const noteText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        noteText.setAttribute('x', x.toString());
        noteText.setAttribute('y', (y + 20).toString());
        noteText.setAttribute('font-size', '10');
        noteText.setAttribute('text-anchor', 'middle');
        noteText.textContent = note.isRest ? '𝄽' : note.name;
        svg.appendChild(noteText);
      });

      // Adicionar letras
      const instrumentLyrics = lyrics.filter(lyric =>
        !lyric.position.staff || lyric.position.staff === instrumentIndex
      );

      instrumentLyrics.forEach((lyric) => {
        const x = 100 + (lyric.position.measure - 1) * 150 + (lyric.position.beat - 1) * 30;
        const y = yPosition + 60;

        const lyricText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        lyricText.setAttribute('x', x.toString());
        lyricText.setAttribute('y', y.toString());
        lyricText.setAttribute('font-size', '12');
        lyricText.setAttribute('text-anchor', 'middle');
        lyricText.textContent = lyric.text;
        svg.appendChild(lyricText);
      });

      yPosition += systemHeight;
    });

    scoreRef.current.appendChild(svg);

  }, [notes, lyrics, instruments, title]);

  // Função para converter duração (será usada em versão futura)
  // const convertDurationToVex = (duration: string): string => {
  //   const durationMap: { [key: string]: string } = {
  //     'whole': 'w',
  //     'half': 'h',
  //     'quarter': 'q',
  //     'eighth': '8',
  //     'sixteenth': '16'
  //   };
  //   return durationMap[duration] || 'q';
  // };

  // Função para reproduzir partitura
  const handlePlay = async () => {
    if (!synth) return;

    if (isPlaying) {
      // Parar reprodução
      Tone.Transport.stop();
      Tone.Transport.cancel();
      setIsPlaying(false);
      setPlaybackProgress(0);
      return;
    }

    // Iniciar Tone.js se necessário
    if (Tone.context.state !== 'running') {
      await Tone.start();
    }

    // Configurar tempo
    Tone.Transport.bpm.value = tempo;

    // Agendar notas para reprodução
    const sortedNotes = [...notes]
      .filter(note => !note.isRest)
      .sort((a, b) => {
        if (a.position.measure !== b.position.measure) {
          return a.position.measure - b.position.measure;
        }
        return a.position.beat - b.position.beat;
      });

    sortedNotes.forEach(note => {
      const time = ((note.position.measure - 1) * 4 + (note.position.beat - 1)) * (60 / tempo);
      const noteName = `${note.name}${note.octave}`;
      const duration = getDurationInSeconds(note.duration, tempo);

      Tone.Transport.schedule((time) => {
        synth.triggerAttackRelease(noteName, duration);
      }, time);
    });

    // Calcular duração total
    const totalDuration = sortedNotes.length > 0 
      ? ((sortedNotes[sortedNotes.length - 1].position.measure - 1) * 4 + 
         sortedNotes[sortedNotes.length - 1].position.beat) * (60 / tempo)
      : 4 * (60 / tempo);

    // Atualizar progresso
    const updateProgress = () => {
      if (Tone.Transport.state === 'started') {
        const currentTime = Tone.Transport.seconds;
        const progress = (currentTime / totalDuration) * 100;
        setPlaybackProgress(Math.min(progress, 100));
        
        if (progress < 100) {
          requestAnimationFrame(updateProgress);
        } else {
          setIsPlaying(false);
          setPlaybackProgress(0);
        }
      }
    };

    // Iniciar reprodução
    Tone.Transport.start();
    setIsPlaying(true);
    updateProgress();
  };

  // Função para calcular duração em segundos
  const getDurationInSeconds = (duration: string, bpm: number): number => {
    const beatDuration = 60 / bpm;
    const durationMap: { [key: string]: number } = {
      'whole': beatDuration * 4,
      'half': beatDuration * 2,
      'quarter': beatDuration,
      'eighth': beatDuration / 2,
      'sixteenth': beatDuration / 4
    };
    return durationMap[duration] || beatDuration;
  };

  // Função para lidar com cliques na partitura
  const handleScoreClick = (event: React.MouseEvent) => {
    if (!onNoteClick) return;

    const rect = scoreRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    // Determinar qual pauta foi clicada
    const staffIndex = Math.floor((y - 40) / 120);
    
    if (staffIndex >= 0 && staffIndex < instruments.length) {
      onNoteClick(x, y, staffIndex);
    }
  };

  return (
    <ScoreContainer>
      <ScoreHeader>
        <ScoreTitle>{title}</ScoreTitle>
        {composer && <ScoreSubtitle>por {composer}</ScoreSubtitle>}
      </ScoreHeader>

      <PlaybackControls>
        <PlayButton isPlaying={isPlaying} onClick={handlePlay}>
          {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}
        </PlayButton>
        
        <TempoControl>
          <label>Tempo:</label>
          <input
            type="number"
            min="60"
            max="200"
            value={tempo}
            onChange={(e) => onTempoChange(parseInt(e.target.value) || 120)}
          />
          <span>BPM</span>
        </TempoControl>

        <ProgressBar>
          <ProgressFill progress={playbackProgress} />
        </ProgressBar>
      </PlaybackControls>

      <ScoreCanvas 
        ref={scoreRef} 
        onClick={handleScoreClick}
        style={{ cursor: onNoteClick ? 'crosshair' : 'default' }}
      />
    </ScoreContainer>
  );
};
