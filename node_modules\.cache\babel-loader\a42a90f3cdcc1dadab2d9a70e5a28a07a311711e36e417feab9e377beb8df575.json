{"ast": null, "code": "import md5 from './md5.js';\nimport v35, { DNS, URL } from './v35.js';\nexport { DNS, URL } from './v35.js';\nfunction v3(value, namespace, buf, offset) {\n  return v35(0x30, md5, value, namespace, buf, offset);\n}\nv3.DNS = DNS;\nv3.URL = URL;\nexport default v3;", "map": {"version": 3, "names": ["md5", "v35", "DNS", "URL", "v3", "value", "namespace", "buf", "offset"], "sources": ["D:/Dev/partitura_digital/node_modules/uuid/dist/esm-browser/v3.js"], "sourcesContent": ["import md5 from './md5.js';\nimport v35, { DNS, URL } from './v35.js';\nexport { DNS, URL } from './v35.js';\nfunction v3(value, namespace, buf, offset) {\n    return v35(0x30, md5, value, namespace, buf, offset);\n}\nv3.DNS = DNS;\nv3.URL = URL;\nexport default v3;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,OAAOC,GAAG,IAAIC,GAAG,EAAEC,GAAG,QAAQ,UAAU;AACxC,SAASD,GAAG,EAAEC,GAAG,QAAQ,UAAU;AACnC,SAASC,EAAEA,CAACC,KAAK,EAAEC,SAAS,EAAEC,GAAG,EAAEC,MAAM,EAAE;EACvC,OAAOP,GAAG,CAAC,IAAI,EAAED,GAAG,EAAEK,KAAK,EAAEC,SAAS,EAAEC,GAAG,EAAEC,MAAM,CAAC;AACxD;AACAJ,EAAE,CAACF,GAAG,GAAGA,GAAG;AACZE,EAAE,CAACD,GAAG,GAAGA,GAAG;AACZ,eAAeC,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}