{"ast": null, "code": "import{collection,doc,addDoc,updateDoc,deleteDoc,getDocs,getDoc,query,where,orderBy,Timestamp}from'firebase/firestore';import{db}from'./firebase';const SCORES_COLLECTION='scores';// Converter Score para formato do Firestore\nconst scoreToFirestore=score=>({...score,createdAt:Timestamp.fromDate(score.createdAt),updatedAt:Timestamp.fromDate(score.updatedAt)});// Converter documento do Firestore para Score\nconst firestoreToScore=doc=>({id:doc.id,...doc.data(),createdAt:doc.data().createdAt.toDate(),updatedAt:doc.data().updatedAt.toDate()});export class ScoreService{// Criar nova partitura\nstatic async createScore(scoreData,userId){const now=new Date();const score={...scoreData,userId,createdAt:now,updatedAt:now};const docRef=await addDoc(collection(db,SCORES_COLLECTION),scoreToFirestore(score));return docRef.id;}// Atualizar partitura existente\nstatic async updateScore(scoreId,updates){const scoreRef=doc(db,SCORES_COLLECTION,scoreId);const updateData={...updates,updatedAt:Timestamp.fromDate(new Date())};await updateDoc(scoreRef,updateData);}// Deletar partitura\nstatic async deleteScore(scoreId){const scoreRef=doc(db,SCORES_COLLECTION,scoreId);await deleteDoc(scoreRef);}// Buscar partitura por ID\nstatic async getScore(scoreId){const scoreRef=doc(db,SCORES_COLLECTION,scoreId);const scoreSnap=await getDoc(scoreRef);if(scoreSnap.exists()){return firestoreToScore(scoreSnap);}return null;}// Buscar todas as partituras de um usuário\nstatic async getUserScores(userId){const q=query(collection(db,SCORES_COLLECTION),where('userId','==',userId),orderBy('updatedAt','desc'));const querySnapshot=await getDocs(q);return querySnapshot.docs.map(doc=>firestoreToScore(doc));}// Buscar partituras por título (busca parcial)\nstatic async searchScoresByTitle(userId,searchTerm){const q=query(collection(db,SCORES_COLLECTION),where('userId','==',userId),orderBy('title'));const querySnapshot=await getDocs(q);const scores=querySnapshot.docs.map(doc=>firestoreToScore(doc));// Filtrar localmente por título (Firestore não suporta busca parcial nativa)\nreturn scores.filter(score=>score.title.toLowerCase().includes(searchTerm.toLowerCase()));}// Duplicar partitura\nstatic async duplicateScore(scoreId,newTitle){const originalScore=await this.getScore(scoreId);if(!originalScore){throw new Error('Partitura não encontrada');}const duplicatedScore={...originalScore,title:newTitle};return await this.createScore(duplicatedScore,originalScore.userId);}}", "map": {"version": 3, "names": ["collection", "doc", "addDoc", "updateDoc", "deleteDoc", "getDocs", "getDoc", "query", "where", "orderBy", "Timestamp", "db", "SCORES_COLLECTION", "scoreToFirestore", "score", "createdAt", "fromDate", "updatedAt", "firestoreToScore", "id", "data", "toDate", "ScoreService", "createScore", "scoreData", "userId", "now", "Date", "doc<PERSON>ef", "updateScore", "scoreId", "updates", "scoreRef", "updateData", "deleteScore", "getScore", "scoreSnap", "exists", "getUserScores", "q", "querySnapshot", "docs", "map", "searchScoresByTitle", "searchTerm", "scores", "filter", "title", "toLowerCase", "includes", "duplicateScore", "newTitle", "originalScore", "Error", "duplicatedScore"], "sources": ["D:/Dev/partitura_digital/src/services/scoreService.ts"], "sourcesContent": ["import { \n  collection, \n  doc, \n  addDoc, \n  updateDoc, \n  deleteDoc, \n  getDocs, \n  getDoc, \n  query, \n  where, \n  orderBy, \n  Timestamp \n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { Score } from '../types/music';\n\nconst SCORES_COLLECTION = 'scores';\n\n// Converter Score para formato do Firestore\nconst scoreToFirestore = (score: Omit<Score, 'id'>) => ({\n  ...score,\n  createdAt: Timestamp.fromDate(score.createdAt),\n  updatedAt: Timestamp.fromDate(score.updatedAt)\n});\n\n// Converter documento do Firestore para Score\nconst firestoreToScore = (doc: any): Score => ({\n  id: doc.id,\n  ...doc.data(),\n  createdAt: doc.data().createdAt.toDate(),\n  updatedAt: doc.data().updatedAt.toDate()\n});\n\nexport class ScoreService {\n  // Criar nova partitura\n  static async createScore(scoreData: Omit<Score, 'id' | 'createdAt' | 'updatedAt'>, userId: string): Promise<string> {\n    const now = new Date();\n    const score: Omit<Score, 'id'> = {\n      ...scoreData,\n      userId,\n      createdAt: now,\n      updatedAt: now\n    };\n\n    const docRef = await addDoc(collection(db, SCORES_COLLECTION), scoreToFirestore(score));\n    return docRef.id;\n  }\n\n  // Atualizar partitura existente\n  static async updateScore(scoreId: string, updates: Partial<Omit<Score, 'id' | 'createdAt' | 'userId'>>): Promise<void> {\n    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);\n    const updateData = {\n      ...updates,\n      updatedAt: Timestamp.fromDate(new Date())\n    };\n    \n    await updateDoc(scoreRef, updateData);\n  }\n\n  // Deletar partitura\n  static async deleteScore(scoreId: string): Promise<void> {\n    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);\n    await deleteDoc(scoreRef);\n  }\n\n  // Buscar partitura por ID\n  static async getScore(scoreId: string): Promise<Score | null> {\n    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);\n    const scoreSnap = await getDoc(scoreRef);\n    \n    if (scoreSnap.exists()) {\n      return firestoreToScore(scoreSnap);\n    }\n    \n    return null;\n  }\n\n  // Buscar todas as partituras de um usuário\n  static async getUserScores(userId: string): Promise<Score[]> {\n    const q = query(\n      collection(db, SCORES_COLLECTION),\n      where('userId', '==', userId),\n      orderBy('updatedAt', 'desc')\n    );\n    \n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => firestoreToScore(doc));\n  }\n\n  // Buscar partituras por título (busca parcial)\n  static async searchScoresByTitle(userId: string, searchTerm: string): Promise<Score[]> {\n    const q = query(\n      collection(db, SCORES_COLLECTION),\n      where('userId', '==', userId),\n      orderBy('title')\n    );\n    \n    const querySnapshot = await getDocs(q);\n    const scores = querySnapshot.docs.map(doc => firestoreToScore(doc));\n    \n    // Filtrar localmente por título (Firestore não suporta busca parcial nativa)\n    return scores.filter(score => \n      score.title.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n  }\n\n  // Duplicar partitura\n  static async duplicateScore(scoreId: string, newTitle: string): Promise<string> {\n    const originalScore = await this.getScore(scoreId);\n    if (!originalScore) {\n      throw new Error('Partitura não encontrada');\n    }\n\n    const duplicatedScore: Omit<Score, 'id' | 'createdAt' | 'updatedAt'> = {\n      ...originalScore,\n      title: newTitle\n    };\n\n    return await this.createScore(duplicatedScore, originalScore.userId);\n  }\n}\n"], "mappings": "AAAA,OACEA,UAAU,CACVC,GAAG,CACHC,MAAM,CACNC,SAAS,CACTC,SAAS,CACTC,OAAO,CACPC,MAAM,CACNC,KAAK,CACLC,KAAK,CACLC,OAAO,CACPC,SAAS,KACJ,oBAAoB,CAC3B,OAASC,EAAE,KAAQ,YAAY,CAG/B,KAAM,CAAAC,iBAAiB,CAAG,QAAQ,CAElC;AACA,KAAM,CAAAC,gBAAgB,CAAIC,KAAwB,GAAM,CACtD,GAAGA,KAAK,CACRC,SAAS,CAAEL,SAAS,CAACM,QAAQ,CAACF,KAAK,CAACC,SAAS,CAAC,CAC9CE,SAAS,CAAEP,SAAS,CAACM,QAAQ,CAACF,KAAK,CAACG,SAAS,CAC/C,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,gBAAgB,CAAIjB,GAAQ,GAAa,CAC7CkB,EAAE,CAAElB,GAAG,CAACkB,EAAE,CACV,GAAGlB,GAAG,CAACmB,IAAI,CAAC,CAAC,CACbL,SAAS,CAAEd,GAAG,CAACmB,IAAI,CAAC,CAAC,CAACL,SAAS,CAACM,MAAM,CAAC,CAAC,CACxCJ,SAAS,CAAEhB,GAAG,CAACmB,IAAI,CAAC,CAAC,CAACH,SAAS,CAACI,MAAM,CAAC,CACzC,CAAC,CAAC,CAEF,MAAO,MAAM,CAAAC,YAAa,CACxB;AACA,YAAa,CAAAC,WAAWA,CAACC,SAAwD,CAAEC,MAAc,CAAmB,CAClH,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAb,KAAwB,CAAG,CAC/B,GAAGU,SAAS,CACZC,MAAM,CACNV,SAAS,CAAEW,GAAG,CACdT,SAAS,CAAES,GACb,CAAC,CAED,KAAM,CAAAE,MAAM,CAAG,KAAM,CAAA1B,MAAM,CAACF,UAAU,CAACW,EAAE,CAAEC,iBAAiB,CAAC,CAAEC,gBAAgB,CAACC,KAAK,CAAC,CAAC,CACvF,MAAO,CAAAc,MAAM,CAACT,EAAE,CAClB,CAEA;AACA,YAAa,CAAAU,WAAWA,CAACC,OAAe,CAAEC,OAA4D,CAAiB,CACrH,KAAM,CAAAC,QAAQ,CAAG/B,GAAG,CAACU,EAAE,CAAEC,iBAAiB,CAAEkB,OAAO,CAAC,CACpD,KAAM,CAAAG,UAAU,CAAG,CACjB,GAAGF,OAAO,CACVd,SAAS,CAAEP,SAAS,CAACM,QAAQ,CAAC,GAAI,CAAAW,IAAI,CAAC,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAxB,SAAS,CAAC6B,QAAQ,CAAEC,UAAU,CAAC,CACvC,CAEA;AACA,YAAa,CAAAC,WAAWA,CAACJ,OAAe,CAAiB,CACvD,KAAM,CAAAE,QAAQ,CAAG/B,GAAG,CAACU,EAAE,CAAEC,iBAAiB,CAAEkB,OAAO,CAAC,CACpD,KAAM,CAAA1B,SAAS,CAAC4B,QAAQ,CAAC,CAC3B,CAEA;AACA,YAAa,CAAAG,QAAQA,CAACL,OAAe,CAAyB,CAC5D,KAAM,CAAAE,QAAQ,CAAG/B,GAAG,CAACU,EAAE,CAAEC,iBAAiB,CAAEkB,OAAO,CAAC,CACpD,KAAM,CAAAM,SAAS,CAAG,KAAM,CAAA9B,MAAM,CAAC0B,QAAQ,CAAC,CAExC,GAAII,SAAS,CAACC,MAAM,CAAC,CAAC,CAAE,CACtB,MAAO,CAAAnB,gBAAgB,CAACkB,SAAS,CAAC,CACpC,CAEA,MAAO,KAAI,CACb,CAEA;AACA,YAAa,CAAAE,aAAaA,CAACb,MAAc,CAAoB,CAC3D,KAAM,CAAAc,CAAC,CAAGhC,KAAK,CACbP,UAAU,CAACW,EAAE,CAAEC,iBAAiB,CAAC,CACjCJ,KAAK,CAAC,QAAQ,CAAE,IAAI,CAAEiB,MAAM,CAAC,CAC7BhB,OAAO,CAAC,WAAW,CAAE,MAAM,CAC7B,CAAC,CAED,KAAM,CAAA+B,aAAa,CAAG,KAAM,CAAAnC,OAAO,CAACkC,CAAC,CAAC,CACtC,MAAO,CAAAC,aAAa,CAACC,IAAI,CAACC,GAAG,CAACzC,GAAG,EAAIiB,gBAAgB,CAACjB,GAAG,CAAC,CAAC,CAC7D,CAEA;AACA,YAAa,CAAA0C,mBAAmBA,CAAClB,MAAc,CAAEmB,UAAkB,CAAoB,CACrF,KAAM,CAAAL,CAAC,CAAGhC,KAAK,CACbP,UAAU,CAACW,EAAE,CAAEC,iBAAiB,CAAC,CACjCJ,KAAK,CAAC,QAAQ,CAAE,IAAI,CAAEiB,MAAM,CAAC,CAC7BhB,OAAO,CAAC,OAAO,CACjB,CAAC,CAED,KAAM,CAAA+B,aAAa,CAAG,KAAM,CAAAnC,OAAO,CAACkC,CAAC,CAAC,CACtC,KAAM,CAAAM,MAAM,CAAGL,aAAa,CAACC,IAAI,CAACC,GAAG,CAACzC,GAAG,EAAIiB,gBAAgB,CAACjB,GAAG,CAAC,CAAC,CAEnE;AACA,MAAO,CAAA4C,MAAM,CAACC,MAAM,CAAChC,KAAK,EACxBA,KAAK,CAACiC,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,UAAU,CAACI,WAAW,CAAC,CAAC,CAC7D,CAAC,CACH,CAEA;AACA,YAAa,CAAAE,cAAcA,CAACpB,OAAe,CAAEqB,QAAgB,CAAmB,CAC9E,KAAM,CAAAC,aAAa,CAAG,KAAM,KAAI,CAACjB,QAAQ,CAACL,OAAO,CAAC,CAClD,GAAI,CAACsB,aAAa,CAAE,CAClB,KAAM,IAAI,CAAAC,KAAK,CAAC,0BAA0B,CAAC,CAC7C,CAEA,KAAM,CAAAC,eAA8D,CAAG,CACrE,GAAGF,aAAa,CAChBL,KAAK,CAAEI,QACT,CAAC,CAED,MAAO,MAAM,KAAI,CAAC5B,WAAW,CAAC+B,eAAe,CAAEF,aAAa,CAAC3B,MAAM,CAAC,CACtE,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}