{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\StaffSystem\\\\StaffSystem.tsx\";\nimport React from 'react';\nimport { MusicalNote as MusicalNoteComponent } from '../MusicalNote/MusicalNote';\nimport { getInstrumentTemplate } from '../../utils/instrumentTemplates';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport const StaffSystem = ({\n  instrument,\n  notes,\n  onStaffClick,\n  onNoteRemove,\n  zoomLevel\n}) => {\n  const template = getInstrumentTemplate(instrument);\n  const staffCount = template.staffCount;\n\n  // Configurações para diferentes tipos de pauta\n  const getStaffConfig = staffIndex => {\n    if (instrument === 'piano' && staffCount === 2) {\n      return {\n        clef: staffIndex === 0 ? 'treble' : 'bass',\n        yOffset: staffIndex * 175,\n        notePositions: staffIndex === 0 ? {\n          // Clave de Sol (pauta superior)\n          'A5': 40,\n          'G5': 50,\n          'F5': 60,\n          'E5': 70,\n          'D5': 80,\n          'C5': 90,\n          'B4': 100,\n          'A4': 110,\n          'G4': 120,\n          'F4': 130,\n          'E4': 140\n        } : {\n          // Clave de Fá (pauta inferior)\n          'C4': 215,\n          'B3': 225,\n          'A3': 235,\n          'G3': 245,\n          'F3': 255,\n          'E3': 265,\n          'D3': 275,\n          'C3': 285,\n          'B2': 295,\n          'A2': 305,\n          'G2': 315\n        }\n      };\n    }\n\n    // Configuração padrão para instrumentos de pauta única\n    return {\n      clef: template.clef,\n      yOffset: 0,\n      notePositions: {\n        'A5': 40,\n        'G5': 50,\n        'F5': 60,\n        'E5': 70,\n        'D5': 80,\n        'C5': 90,\n        'B4': 100,\n        'A4': 110,\n        'G4': 120,\n        'F4': 130,\n        'E4': 140\n      }\n    };\n  };\n  const renderStaff = staffIndex => {\n    const config = getStaffConfig(staffIndex);\n    const staffY = config.yOffset;\n    return /*#__PURE__*/_jsxDEV(\"g\", {\n      children: [[0, 1, 2, 3, 4].map(line => /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"50\",\n        y1: 50 + staffY + line * 20,\n        x2: \"750\",\n        y2: 50 + staffY + line * 20,\n        stroke: \"#333\",\n        strokeWidth: \"1\"\n      }, line, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"text\", {\n        x: \"20\",\n        y: 90 + staffY,\n        fontSize: \"40\",\n        fill: \"#333\",\n        children: config.clef === 'treble' ? '𝄞' : config.clef === 'bass' ? '𝄢' : config.clef === 'alto' ? '𝄡' : '𝄞'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), staffIndex === 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"text\", {\n          x: \"80\",\n          y: 75 + staffY,\n          fontSize: \"16\",\n          fill: \"#333\",\n          children: \"4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n          x: \"80\",\n          y: 95 + staffY,\n          fontSize: \"16\",\n          fill: \"#333\",\n          children: \"4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: \"110\",\n        y1: 50 + staffY,\n        x2: \"110\",\n        y2: 130 + staffY,\n        stroke: \"#333\",\n        strokeWidth: \"2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"line\", {\n        x1: 120 + measure * 150,\n        y1: 50 + staffY,\n        x2: 120 + measure * 150,\n        y2: 130 + staffY,\n        stroke: \"#999\",\n        strokeWidth: \"1\"\n      }, measure, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this)), staffIndex === 0 && [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"text\", {\n        x: 120 + (measure - 1) * 150 + 75,\n        y: \"35\",\n        fontSize: \"12\",\n        fill: \"#666\",\n        textAnchor: \"middle\",\n        children: measure\n      }, measure, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"rect\", {\n        x: \"120\",\n        y: 40 + staffY,\n        width: \"600\",\n        height: \"100\",\n        fill: \"transparent\",\n        style: {\n          cursor: 'crosshair'\n        },\n        onClick: e => onStaffClick(e, staffIndex)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), notes.filter(note => note.position.staff === staffIndex).map(note => {\n        const x = 120 + (note.position.measure - 1) * 150 + note.position.beat * 30;\n        const y = config.notePositions[`${note.name}${note.octave}`] || 90 + staffY;\n        return /*#__PURE__*/_jsxDEV(MusicalNoteComponent, {\n          note: note,\n          x: x,\n          y: y,\n          onRemove: () => onNoteRemove(note.id)\n        }, note.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 15\n        }, this);\n      }), notes.filter(n => n.position.staff === staffIndex).length === 0 && /*#__PURE__*/_jsxDEV(\"text\", {\n        x: \"400\",\n        y: 100 + staffY,\n        fontSize: \"14\",\n        fill: \"#999\",\n        textAnchor: \"middle\",\n        children: staffIndex === 0 ? 'Clique na pauta para adicionar notas' : staffCount === 2 ? 'Pauta inferior (mão esquerda)' : ''\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this), instrument === 'piano' && staffCount === 2 && staffIndex === 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"line\", {\n          x1: \"50\",\n          y1: \"50\",\n          x2: \"50\",\n          y2: \"305\",\n          stroke: \"#333\",\n          strokeWidth: \"3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n          x1: \"750\",\n          y1: \"50\",\n          x2: \"750\",\n          y2: \"305\",\n          stroke: \"#333\",\n          strokeWidth: \"1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, staffIndex, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  };\n  const svgHeight = staffCount === 2 ? 350 : 200;\n  return /*#__PURE__*/_jsxDEV(\"svg\", {\n    viewBox: `0 0 800 ${svgHeight}`,\n    style: {\n      width: '100%',\n      height: `${svgHeight}px`,\n      border: '1px solid #e9ecef',\n      borderRadius: '8px',\n      background: 'white',\n      marginBottom: '2rem',\n      transform: `scale(${zoomLevel})`,\n      transformOrigin: 'top left'\n    },\n    children: [Array.from({\n      length: staffCount\n    }, (_, index) => renderStaff(index)), notes.length === 0 && /*#__PURE__*/_jsxDEV(\"text\", {\n      x: \"400\",\n      y: svgHeight - 20,\n      fontSize: \"12\",\n      fill: \"#999\",\n      textAnchor: \"middle\",\n      children: \"Clique em uma nota para remov\\xEA-la \\u2022 Use o painel lateral para selecionar ferramentas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 197,\n    columnNumber: 5\n  }, this);\n};\n_c = StaffSystem;\nvar _c;\n$RefreshReg$(_c, \"StaffSystem\");", "map": {"version": 3, "names": ["React", "MusicalNote", "MusicalNoteComponent", "getInstrumentTemplate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StaffSystem", "instrument", "notes", "onStaffClick", "onNoteRemove", "zoomLevel", "template", "staffCount", "getStaffConfig", "staffIndex", "clef", "yOffset", "notePositions", "renderStaff", "config", "staffY", "children", "map", "line", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x", "y", "fontSize", "fill", "measure", "textAnchor", "width", "height", "style", "cursor", "onClick", "e", "filter", "note", "position", "staff", "beat", "name", "octave", "onRemove", "id", "n", "length", "svgHeight", "viewBox", "border", "borderRadius", "background", "marginBottom", "transform", "transform<PERSON><PERSON>in", "Array", "from", "_", "index", "_c", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/StaffSystem/StaffSystem.tsx"], "sourcesContent": ["import React from 'react';\nimport { MusicalNote, InstrumentType, ClefType } from '../../types/music';\nimport { MusicalNote as MusicalNoteComponent } from '../MusicalNote/MusicalNote';\nimport { getInstrumentTemplate } from '../../utils/instrumentTemplates';\n\ninterface StaffSystemProps {\n  instrument: InstrumentType;\n  notes: MusicalNote[];\n  onStaffClick: (event: React.MouseEvent<SVGRectElement>, staffIndex: number) => void;\n  onNoteRemove: (noteId: string) => void;\n  zoomLevel: number;\n}\n\nexport const StaffSystem: React.FC<StaffSystemProps> = ({\n  instrument,\n  notes,\n  onStaffClick,\n  onNoteRemove,\n  zoomLevel\n}) => {\n  const template = getInstrumentTemplate(instrument);\n  const staffCount = template.staffCount;\n  \n  // Configurações para diferentes tipos de pauta\n  const getStaffConfig = (staffIndex: number) => {\n    if (instrument === 'piano' && staffCount === 2) {\n      return {\n        clef: staffIndex === 0 ? 'treble' : 'bass',\n        yOffset: staffIndex * 175,\n        notePositions: staffIndex === 0 ? {\n          // Clave de Sol (pauta superior)\n          'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,\n          'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140\n        } : {\n          // Clave de Fá (pauta inferior)\n          'C4': 215, 'B3': 225, 'A3': 235, 'G3': 245, 'F3': 255,\n          'E3': 265, 'D3': 275, 'C3': 285, 'B2': 295, 'A2': 305, 'G2': 315\n        }\n      };\n    }\n    \n    // Configuração padrão para instrumentos de pauta única\n    return {\n      clef: template.clef,\n      yOffset: 0,\n      notePositions: {\n        'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,\n        'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140\n      }\n    };\n  };\n\n  const renderStaff = (staffIndex: number) => {\n    const config = getStaffConfig(staffIndex);\n    const staffY = config.yOffset;\n    \n    return (\n      <g key={staffIndex}>\n        {/* Linhas da pauta */}\n        {[0, 1, 2, 3, 4].map(line => (\n          <line\n            key={line}\n            x1=\"50\"\n            y1={50 + staffY + line * 20}\n            x2=\"750\"\n            y2={50 + staffY + line * 20}\n            stroke=\"#333\"\n            strokeWidth=\"1\"\n          />\n        ))}\n        \n        {/* Clave */}\n        <text x=\"20\" y={90 + staffY} fontSize=\"40\" fill=\"#333\">\n          {config.clef === 'treble' ? '𝄞' : \n           config.clef === 'bass' ? '𝄢' : \n           config.clef === 'alto' ? '𝄡' : '𝄞'}\n        </text>\n        \n        {/* Compasso (apenas na primeira pauta) */}\n        {staffIndex === 0 && (\n          <>\n            <text x=\"80\" y={75 + staffY} fontSize=\"16\" fill=\"#333\">4</text>\n            <text x=\"80\" y={95 + staffY} fontSize=\"16\" fill=\"#333\">4</text>\n          </>\n        )}\n        \n        {/* Linha divisória inicial */}\n        <line \n          x1=\"110\" \n          y1={50 + staffY} \n          x2=\"110\" \n          y2={130 + staffY} \n          stroke=\"#333\" \n          strokeWidth=\"2\"\n        />\n        \n        {/* Divisões de compasso */}\n        {[1, 2, 3, 4].map(measure => (\n          <line\n            key={measure}\n            x1={120 + (measure * 150)}\n            y1={50 + staffY}\n            x2={120 + (measure * 150)}\n            y2={130 + staffY}\n            stroke=\"#999\"\n            strokeWidth=\"1\"\n          />\n        ))}\n        \n        {/* Números dos compassos (apenas na primeira pauta) */}\n        {staffIndex === 0 && [1, 2, 3, 4].map(measure => (\n          <text\n            key={measure}\n            x={120 + ((measure - 1) * 150) + 75}\n            y=\"35\"\n            fontSize=\"12\"\n            fill=\"#666\"\n            textAnchor=\"middle\"\n          >\n            {measure}\n          </text>\n        ))}\n        \n        {/* Área clicável para adicionar notas */}\n        <rect \n          x=\"120\" \n          y={40 + staffY} \n          width=\"600\" \n          height=\"100\" \n          fill=\"transparent\" \n          style={{ cursor: 'crosshair' }}\n          onClick={(e) => onStaffClick(e, staffIndex)}\n        />\n        \n        {/* Renderizar notas desta pauta */}\n        {notes\n          .filter(note => note.position.staff === staffIndex)\n          .map((note) => {\n            const x = 120 + ((note.position.measure - 1) * 150) + (note.position.beat * 30);\n            const y = config.notePositions[`${note.name}${note.octave}`] || (90 + staffY);\n            \n            return (\n              <MusicalNoteComponent\n                key={note.id}\n                note={note}\n                x={x}\n                y={y}\n                onRemove={() => onNoteRemove(note.id)}\n              />\n            );\n          })}\n        \n        {/* Instruções (apenas se não há notas) */}\n        {notes.filter(n => n.position.staff === staffIndex).length === 0 && (\n          <text \n            x=\"400\" \n            y={100 + staffY} \n            fontSize=\"14\" \n            fill=\"#999\" \n            textAnchor=\"middle\"\n          >\n            {staffIndex === 0 ? 'Clique na pauta para adicionar notas' : \n             staffCount === 2 ? 'Pauta inferior (mão esquerda)' : ''}\n          </text>\n        )}\n        \n        {/* Conectores entre pautas (para piano) */}\n        {instrument === 'piano' && staffCount === 2 && staffIndex === 0 && (\n          <>\n            {/* Linha conectora esquerda */}\n            <line\n              x1=\"50\"\n              y1=\"50\"\n              x2=\"50\"\n              y2=\"305\"\n              stroke=\"#333\"\n              strokeWidth=\"3\"\n            />\n            {/* Linha conectora direita */}\n            <line\n              x1=\"750\"\n              y1=\"50\"\n              x2=\"750\"\n              y2=\"305\"\n              stroke=\"#333\"\n              strokeWidth=\"1\"\n            />\n          </>\n        )}\n      </g>\n    );\n  };\n\n  const svgHeight = staffCount === 2 ? 350 : 200;\n\n  return (\n    <svg \n      viewBox={`0 0 800 ${svgHeight}`}\n      style={{ \n        width: '100%',\n        height: `${svgHeight}px`,\n        border: '1px solid #e9ecef',\n        borderRadius: '8px',\n        background: 'white',\n        marginBottom: '2rem',\n        transform: `scale(${zoomLevel})`,\n        transformOrigin: 'top left'\n      }}\n    >\n      {Array.from({ length: staffCount }, (_, index) => renderStaff(index))}\n      \n      {/* Instruções gerais */}\n      {notes.length === 0 && (\n        <text x=\"400\" y={svgHeight - 20} fontSize=\"12\" fill=\"#999\" textAnchor=\"middle\">\n          Clique em uma nota para removê-la • Use o painel lateral para selecionar ferramentas\n        </text>\n      )}\n    </svg>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,WAAW,IAAIC,oBAAoB,QAAQ,4BAA4B;AAChF,SAASC,qBAAqB,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAUxE,OAAO,MAAMC,WAAuC,GAAGA,CAAC;EACtDC,UAAU;EACVC,KAAK;EACLC,YAAY;EACZC,YAAY;EACZC;AACF,CAAC,KAAK;EACJ,MAAMC,QAAQ,GAAGX,qBAAqB,CAACM,UAAU,CAAC;EAClD,MAAMM,UAAU,GAAGD,QAAQ,CAACC,UAAU;;EAEtC;EACA,MAAMC,cAAc,GAAIC,UAAkB,IAAK;IAC7C,IAAIR,UAAU,KAAK,OAAO,IAAIM,UAAU,KAAK,CAAC,EAAE;MAC9C,OAAO;QACLG,IAAI,EAAED,UAAU,KAAK,CAAC,GAAG,QAAQ,GAAG,MAAM;QAC1CE,OAAO,EAAEF,UAAU,GAAG,GAAG;QACzBG,aAAa,EAAEH,UAAU,KAAK,CAAC,GAAG;UAChC;UACA,IAAI,EAAE,EAAE;UAAE,IAAI,EAAE,EAAE;UAAE,IAAI,EAAE,EAAE;UAAE,IAAI,EAAE,EAAE;UAAE,IAAI,EAAE,EAAE;UAChD,IAAI,EAAE,EAAE;UAAE,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE;QAC9D,CAAC,GAAG;UACF;UACA,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE,GAAG;UACrD,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE,GAAG;UAAE,IAAI,EAAE;QAC/D;MACF,CAAC;IACH;;IAEA;IACA,OAAO;MACLC,IAAI,EAAEJ,QAAQ,CAACI,IAAI;MACnBC,OAAO,EAAE,CAAC;MACVC,aAAa,EAAE;QACb,IAAI,EAAE,EAAE;QAAE,IAAI,EAAE,EAAE;QAAE,IAAI,EAAE,EAAE;QAAE,IAAI,EAAE,EAAE;QAAE,IAAI,EAAE,EAAE;QAChD,IAAI,EAAE,EAAE;QAAE,IAAI,EAAE,GAAG;QAAE,IAAI,EAAE,GAAG;QAAE,IAAI,EAAE,GAAG;QAAE,IAAI,EAAE,GAAG;QAAE,IAAI,EAAE;MAC9D;IACF,CAAC;EACH,CAAC;EAED,MAAMC,WAAW,GAAIJ,UAAkB,IAAK;IAC1C,MAAMK,MAAM,GAAGN,cAAc,CAACC,UAAU,CAAC;IACzC,MAAMM,MAAM,GAAGD,MAAM,CAACH,OAAO;IAE7B,oBACEd,OAAA;MAAAmB,QAAA,GAEG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAACC,IAAI,iBACvBrB,OAAA;QAEEsB,EAAE,EAAC,IAAI;QACPC,EAAE,EAAE,EAAE,GAAGL,MAAM,GAAGG,IAAI,GAAG,EAAG;QAC5BG,EAAE,EAAC,KAAK;QACRC,EAAE,EAAE,EAAE,GAAGP,MAAM,GAAGG,IAAI,GAAG,EAAG;QAC5BK,MAAM,EAAC,MAAM;QACbC,WAAW,EAAC;MAAG,GANVN,IAAI;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOV,CACF,CAAC,eAGF/B,OAAA;QAAMgC,CAAC,EAAC,IAAI;QAACC,CAAC,EAAE,EAAE,GAAGf,MAAO;QAACgB,QAAQ,EAAC,IAAI;QAACC,IAAI,EAAC,MAAM;QAAAhB,QAAA,EACnDF,MAAM,CAACJ,IAAI,KAAK,QAAQ,GAAG,IAAI,GAC/BI,MAAM,CAACJ,IAAI,KAAK,MAAM,GAAG,IAAI,GAC7BI,MAAM,CAACJ,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG;MAAI;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,EAGNnB,UAAU,KAAK,CAAC,iBACfZ,OAAA,CAAAE,SAAA;QAAAiB,QAAA,gBACEnB,OAAA;UAAMgC,CAAC,EAAC,IAAI;UAACC,CAAC,EAAE,EAAE,GAAGf,MAAO;UAACgB,QAAQ,EAAC,IAAI;UAACC,IAAI,EAAC,MAAM;UAAAhB,QAAA,EAAC;QAAC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/D/B,OAAA;UAAMgC,CAAC,EAAC,IAAI;UAACC,CAAC,EAAE,EAAE,GAAGf,MAAO;UAACgB,QAAQ,EAAC,IAAI;UAACC,IAAI,EAAC,MAAM;UAAAhB,QAAA,EAAC;QAAC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,eAC/D,CACH,eAGD/B,OAAA;QACEsB,EAAE,EAAC,KAAK;QACRC,EAAE,EAAE,EAAE,GAAGL,MAAO;QAChBM,EAAE,EAAC,KAAK;QACRC,EAAE,EAAE,GAAG,GAAGP,MAAO;QACjBQ,MAAM,EAAC,MAAM;QACbC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,EAGD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACX,GAAG,CAACgB,OAAO,iBACvBpC,OAAA;QAEEsB,EAAE,EAAE,GAAG,GAAIc,OAAO,GAAG,GAAK;QAC1Bb,EAAE,EAAE,EAAE,GAAGL,MAAO;QAChBM,EAAE,EAAE,GAAG,GAAIY,OAAO,GAAG,GAAK;QAC1BX,EAAE,EAAE,GAAG,GAAGP,MAAO;QACjBQ,MAAM,EAAC,MAAM;QACbC,WAAW,EAAC;MAAG,GANVS,OAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOb,CACF,CAAC,EAGDnB,UAAU,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACQ,GAAG,CAACgB,OAAO,iBAC3CpC,OAAA;QAEEgC,CAAC,EAAE,GAAG,GAAI,CAACI,OAAO,GAAG,CAAC,IAAI,GAAI,GAAG,EAAG;QACpCH,CAAC,EAAC,IAAI;QACNC,QAAQ,EAAC,IAAI;QACbC,IAAI,EAAC,MAAM;QACXE,UAAU,EAAC,QAAQ;QAAAlB,QAAA,EAElBiB;MAAO,GAPHA,OAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQR,CACP,CAAC,eAGF/B,OAAA;QACEgC,CAAC,EAAC,KAAK;QACPC,CAAC,EAAE,EAAE,GAAGf,MAAO;QACfoB,KAAK,EAAC,KAAK;QACXC,MAAM,EAAC,KAAK;QACZJ,IAAI,EAAC,aAAa;QAClBK,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAY,CAAE;QAC/BC,OAAO,EAAGC,CAAC,IAAKrC,YAAY,CAACqC,CAAC,EAAE/B,UAAU;MAAE;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,EAGD1B,KAAK,CACHuC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAACC,KAAK,KAAKnC,UAAU,CAAC,CAClDQ,GAAG,CAAEyB,IAAI,IAAK;QACb,MAAMb,CAAC,GAAG,GAAG,GAAI,CAACa,IAAI,CAACC,QAAQ,CAACV,OAAO,GAAG,CAAC,IAAI,GAAI,GAAIS,IAAI,CAACC,QAAQ,CAACE,IAAI,GAAG,EAAG;QAC/E,MAAMf,CAAC,GAAGhB,MAAM,CAACF,aAAa,CAAC,GAAG8B,IAAI,CAACI,IAAI,GAAGJ,IAAI,CAACK,MAAM,EAAE,CAAC,IAAK,EAAE,GAAGhC,MAAO;QAE7E,oBACElB,OAAA,CAACH,oBAAoB;UAEnBgD,IAAI,EAAEA,IAAK;UACXb,CAAC,EAAEA,CAAE;UACLC,CAAC,EAAEA,CAAE;UACLkB,QAAQ,EAAEA,CAAA,KAAM5C,YAAY,CAACsC,IAAI,CAACO,EAAE;QAAE,GAJjCP,IAAI,CAACO,EAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKb,CAAC;MAEN,CAAC,CAAC,EAGH1B,KAAK,CAACuC,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACP,QAAQ,CAACC,KAAK,KAAKnC,UAAU,CAAC,CAAC0C,MAAM,KAAK,CAAC,iBAC9DtD,OAAA;QACEgC,CAAC,EAAC,KAAK;QACPC,CAAC,EAAE,GAAG,GAAGf,MAAO;QAChBgB,QAAQ,EAAC,IAAI;QACbC,IAAI,EAAC,MAAM;QACXE,UAAU,EAAC,QAAQ;QAAAlB,QAAA,EAElBP,UAAU,KAAK,CAAC,GAAG,sCAAsC,GACzDF,UAAU,KAAK,CAAC,GAAG,+BAA+B,GAAG;MAAE;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACP,EAGA3B,UAAU,KAAK,OAAO,IAAIM,UAAU,KAAK,CAAC,IAAIE,UAAU,KAAK,CAAC,iBAC7DZ,OAAA,CAAAE,SAAA;QAAAiB,QAAA,gBAEEnB,OAAA;UACEsB,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,KAAK;UACRC,MAAM,EAAC,MAAM;UACbC,WAAW,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAEF/B,OAAA;UACEsB,EAAE,EAAC,KAAK;UACRC,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,KAAK;UACRC,EAAE,EAAC,KAAK;UACRC,MAAM,EAAC,MAAM;UACbC,WAAW,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA,eACF,CACH;IAAA,GAnIKnB,UAAU;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAoIf,CAAC;EAER,CAAC;EAED,MAAMwB,SAAS,GAAG7C,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG;EAE9C,oBACEV,OAAA;IACEwD,OAAO,EAAE,WAAWD,SAAS,EAAG;IAChCf,KAAK,EAAE;MACLF,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,GAAGgB,SAAS,IAAI;MACxBE,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,MAAM;MACpBC,SAAS,EAAE,SAASrD,SAAS,GAAG;MAChCsD,eAAe,EAAE;IACnB,CAAE;IAAA3C,QAAA,GAED4C,KAAK,CAACC,IAAI,CAAC;MAAEV,MAAM,EAAE5C;IAAW,CAAC,EAAE,CAACuD,CAAC,EAAEC,KAAK,KAAKlD,WAAW,CAACkD,KAAK,CAAC,CAAC,EAGpE7D,KAAK,CAACiD,MAAM,KAAK,CAAC,iBACjBtD,OAAA;MAAMgC,CAAC,EAAC,KAAK;MAACC,CAAC,EAAEsB,SAAS,GAAG,EAAG;MAACrB,QAAQ,EAAC,IAAI;MAACC,IAAI,EAAC,MAAM;MAACE,UAAU,EAAC,QAAQ;MAAAlB,QAAA,EAAC;IAE/E;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACoC,EAAA,GA9MWhE,WAAuC;AAAA,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}