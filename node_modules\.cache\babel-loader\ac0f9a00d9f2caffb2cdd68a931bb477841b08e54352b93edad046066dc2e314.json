{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\InstrumentSelector\\\\InstrumentSelector.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { INSTRUMENT_TEMPLATES, getInstrumentTemplate } from '../../utils/instrumentTemplates';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SelectorContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 1.5rem;\n  margin-bottom: 1rem;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n`;\n_c = SelectorContainer;\nconst SelectorHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n`;\n_c2 = SelectorHeader;\nconst SelectorTitle = styled.h4`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.1rem;\n`;\n_c3 = SelectorTitle;\nconst CurrentInstrument = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1rem;\n  color: #495057;\n  font-weight: 600;\n`;\n_c4 = CurrentInstrument;\nconst InstrumentGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1rem;\n`;\n_c5 = InstrumentGrid;\nconst InstrumentCard = styled.button`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 1rem;\n  border: 2px solid ${props => props.active ? '#667eea' : '#e9ecef'};\n  border-radius: 12px;\n  background: ${props => props.active ? 'rgba(102, 126, 234, 0.1)' : 'white'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    border-color: #667eea;\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\n  }\n`;\n_c6 = InstrumentCard;\nconst InstrumentEmoji = styled.div`\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n`;\n_c7 = InstrumentEmoji;\nconst InstrumentName = styled.div`\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 0.25rem;\n`;\n_c8 = InstrumentName;\nconst InstrumentDescription = styled.div`\n  font-size: 0.8rem;\n  color: #666;\n  text-align: center;\n  line-height: 1.3;\n`;\n_c9 = InstrumentDescription;\nconst InstrumentDetails = styled.div`\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-top: 1rem;\n  border: 1px solid #e9ecef;\n`;\n_c0 = InstrumentDetails;\nconst DetailRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n_c1 = DetailRow;\nconst DetailLabel = styled.span`\n  font-weight: 600;\n  color: #495057;\n`;\n_c10 = DetailLabel;\nconst DetailValue = styled.span`\n  color: #666;\n`;\n_c11 = DetailValue;\nconst TuningInfo = styled.div`\n  background: #e3f2fd;\n  border-radius: 6px;\n  padding: 0.5rem;\n  margin-top: 0.5rem;\n  font-size: 0.8rem;\n  color: #1565c0;\n`;\n_c12 = TuningInfo;\nconst KeySuggestions = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n  margin-top: 0.5rem;\n`;\n_c13 = KeySuggestions;\nconst KeyChip = styled.span`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0.2rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: 600;\n`;\n_c14 = KeyChip;\nexport const InstrumentSelector = ({\n  selectedInstrument,\n  onInstrumentChange,\n  compact = false\n}) => {\n  _s();\n  const [showDetails, setShowDetails] = useState(!compact);\n  const currentTemplate = getInstrumentTemplate(selectedInstrument);\n  const formatRange = template => {\n    return `${template.range.lowest.note}${template.range.lowest.octave} - ${template.range.highest.note}${template.range.highest.octave}`;\n  };\n  const getClefName = clef => {\n    const clefNames = {\n      treble: 'Sol',\n      bass: 'Fá',\n      alto: 'Dó (3ª linha)',\n      tenor: 'Dó (4ª linha)'\n    };\n    return clefNames[clef] || clef;\n  };\n  if (compact) {\n    return /*#__PURE__*/_jsxDEV(SelectorContainer, {\n      children: [/*#__PURE__*/_jsxDEV(SelectorHeader, {\n        children: [/*#__PURE__*/_jsxDEV(SelectorTitle, {\n          children: \"\\uD83C\\uDFBC Instrumento\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CurrentInstrument, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: currentTemplate.emoji\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: currentTemplate.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: selectedInstrument,\n        onChange: e => onInstrumentChange(e.target.value),\n        style: {\n          width: '100%',\n          padding: '0.5rem',\n          border: '1px solid #dee2e6',\n          borderRadius: '6px',\n          fontSize: '0.9rem'\n        },\n        children: INSTRUMENT_TEMPLATES.map(template => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: template.id,\n          children: [template.emoji, \" \", template.name]\n        }, template.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(SelectorContainer, {\n    children: [/*#__PURE__*/_jsxDEV(SelectorHeader, {\n      children: [/*#__PURE__*/_jsxDEV(SelectorTitle, {\n        children: \"\\uD83C\\uDFBC Selecionar Instrumento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CurrentInstrument, {\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: currentTemplate.emoji\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: currentTemplate.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(InstrumentGrid, {\n      children: INSTRUMENT_TEMPLATES.map(template => /*#__PURE__*/_jsxDEV(InstrumentCard, {\n        active: selectedInstrument === template.id,\n        onClick: () => onInstrumentChange(template.id),\n        children: [/*#__PURE__*/_jsxDEV(InstrumentEmoji, {\n          children: template.emoji\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(InstrumentName, {\n          children: template.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(InstrumentDescription, {\n          children: template.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)]\n      }, template.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), showDetails && /*#__PURE__*/_jsxDEV(InstrumentDetails, {\n      children: [/*#__PURE__*/_jsxDEV(DetailRow, {\n        children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n          children: \"Clave:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n          children: getClefName(currentTemplate.clef)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n        children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n          children: \"Extens\\xE3o:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n          children: formatRange(currentTemplate)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DetailRow, {\n        children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n          children: \"Pautas:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n          children: currentTemplate.staffCount === 1 ? 'Simples' : 'Dupla (Sol + Fá)'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this), currentTemplate.transposition && /*#__PURE__*/_jsxDEV(DetailRow, {\n        children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n          children: \"Transposi\\xE7\\xE3o:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n          children: [currentTemplate.transposition > 0 ? '+' : '', currentTemplate.transposition, \" semitons\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 13\n      }, this), currentTemplate.tuning && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(DetailRow, {\n          children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n            children: \"Afina\\xE7\\xE3o:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n            children: \"Cordas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(TuningInfo, {\n          children: [\"\\uD83C\\uDFB8 \", currentTemplate.tuning.join(' - ')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(DetailRow, {\n        children: [/*#__PURE__*/_jsxDEV(DetailLabel, {\n          children: \"Tonalidades comuns:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DetailValue, {\n          children: /*#__PURE__*/_jsxDEV(KeySuggestions, {\n            children: currentTemplate.commonKeys.slice(0, 6).map(key => /*#__PURE__*/_jsxDEV(KeyChip, {\n              children: key\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n};\n_s(InstrumentSelector, \"hJTCwqobdOjiC4UcX081kI7JwMM=\");\n_c15 = InstrumentSelector;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15;\n$RefreshReg$(_c, \"SelectorContainer\");\n$RefreshReg$(_c2, \"SelectorHeader\");\n$RefreshReg$(_c3, \"SelectorTitle\");\n$RefreshReg$(_c4, \"CurrentInstrument\");\n$RefreshReg$(_c5, \"InstrumentGrid\");\n$RefreshReg$(_c6, \"InstrumentCard\");\n$RefreshReg$(_c7, \"InstrumentEmoji\");\n$RefreshReg$(_c8, \"InstrumentName\");\n$RefreshReg$(_c9, \"InstrumentDescription\");\n$RefreshReg$(_c0, \"InstrumentDetails\");\n$RefreshReg$(_c1, \"DetailRow\");\n$RefreshReg$(_c10, \"DetailLabel\");\n$RefreshReg$(_c11, \"DetailValue\");\n$RefreshReg$(_c12, \"TuningInfo\");\n$RefreshReg$(_c13, \"KeySuggestions\");\n$RefreshReg$(_c14, \"KeyChip\");\n$RefreshReg$(_c15, \"InstrumentSelector\");", "map": {"version": 3, "names": ["React", "useState", "styled", "INSTRUMENT_TEMPLATES", "getInstrumentTemplate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SelectorContainer", "div", "_c", "SelectorHeader", "_c2", "SelectorTitle", "h4", "_c3", "CurrentInstrument", "_c4", "InstrumentGrid", "_c5", "InstrumentCard", "button", "props", "active", "_c6", "InstrumentEmoji", "_c7", "InstrumentName", "_c8", "InstrumentDescription", "_c9", "InstrumentDetails", "_c0", "DetailRow", "_c1", "DetailLabel", "span", "_c10", "DetailValue", "_c11", "TuningInfo", "_c12", "KeySuggestions", "_c13", "KeyChip", "_c14", "InstrumentSelector", "selectedInstrument", "onInstrumentChange", "compact", "_s", "showDetails", "setShowDetails", "currentTemplate", "formatRange", "template", "range", "lowest", "note", "octave", "highest", "getClefName", "clef", "clefNames", "treble", "bass", "alto", "tenor", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "emoji", "name", "value", "onChange", "e", "target", "style", "width", "padding", "border", "borderRadius", "fontSize", "map", "id", "onClick", "description", "staffCount", "transposition", "tuning", "join", "commonKeys", "slice", "key", "_c15", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/InstrumentSelector/InstrumentSelector.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { InstrumentType } from '../../types/music';\nimport { INSTRUMENT_TEMPLATES, getInstrumentTemplate, getInstrumentCategories } from '../../utils/instrumentTemplates';\n\nconst SelectorContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 1.5rem;\n  margin-bottom: 1rem;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n`;\n\nconst SelectorHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n`;\n\nconst SelectorTitle = styled.h4`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.1rem;\n`;\n\nconst CurrentInstrument = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1rem;\n  color: #495057;\n  font-weight: 600;\n`;\n\nconst InstrumentGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1rem;\n`;\n\nconst InstrumentCard = styled.button<{ active?: boolean }>`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 1rem;\n  border: 2px solid ${props => props.active ? '#667eea' : '#e9ecef'};\n  border-radius: 12px;\n  background: ${props => props.active ? 'rgba(102, 126, 234, 0.1)' : 'white'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    border-color: #667eea;\n    transform: translateY(-2px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);\n  }\n`;\n\nconst InstrumentEmoji = styled.div`\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n`;\n\nconst InstrumentName = styled.div`\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 0.25rem;\n`;\n\nconst InstrumentDescription = styled.div`\n  font-size: 0.8rem;\n  color: #666;\n  text-align: center;\n  line-height: 1.3;\n`;\n\nconst InstrumentDetails = styled.div`\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-top: 1rem;\n  border: 1px solid #e9ecef;\n`;\n\nconst DetailRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\nconst DetailLabel = styled.span`\n  font-weight: 600;\n  color: #495057;\n`;\n\nconst DetailValue = styled.span`\n  color: #666;\n`;\n\nconst TuningInfo = styled.div`\n  background: #e3f2fd;\n  border-radius: 6px;\n  padding: 0.5rem;\n  margin-top: 0.5rem;\n  font-size: 0.8rem;\n  color: #1565c0;\n`;\n\nconst KeySuggestions = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n  margin-top: 0.5rem;\n`;\n\nconst KeyChip = styled.span`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 0.2rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: 600;\n`;\n\ninterface InstrumentSelectorProps {\n  selectedInstrument: InstrumentType;\n  onInstrumentChange: (instrument: InstrumentType) => void;\n  compact?: boolean;\n}\n\nexport const InstrumentSelector: React.FC<InstrumentSelectorProps> = ({\n  selectedInstrument,\n  onInstrumentChange,\n  compact = false\n}) => {\n  const [showDetails, setShowDetails] = useState(!compact);\n  const currentTemplate = getInstrumentTemplate(selectedInstrument);\n\n  const formatRange = (template: typeof currentTemplate) => {\n    return `${template.range.lowest.note}${template.range.lowest.octave} - ${template.range.highest.note}${template.range.highest.octave}`;\n  };\n\n  const getClefName = (clef: string) => {\n    const clefNames = {\n      treble: 'Sol',\n      bass: 'Fá',\n      alto: 'Dó (3ª linha)',\n      tenor: 'Dó (4ª linha)'\n    };\n    return clefNames[clef as keyof typeof clefNames] || clef;\n  };\n\n  if (compact) {\n    return (\n      <SelectorContainer>\n        <SelectorHeader>\n          <SelectorTitle>🎼 Instrumento</SelectorTitle>\n          <CurrentInstrument>\n            <span>{currentTemplate.emoji}</span>\n            <span>{currentTemplate.name}</span>\n          </CurrentInstrument>\n        </SelectorHeader>\n        \n        <select\n          value={selectedInstrument}\n          onChange={(e) => onInstrumentChange(e.target.value as InstrumentType)}\n          style={{\n            width: '100%',\n            padding: '0.5rem',\n            border: '1px solid #dee2e6',\n            borderRadius: '6px',\n            fontSize: '0.9rem'\n          }}\n        >\n          {INSTRUMENT_TEMPLATES.map(template => (\n            <option key={template.id} value={template.id}>\n              {template.emoji} {template.name}\n            </option>\n          ))}\n        </select>\n      </SelectorContainer>\n    );\n  }\n\n  return (\n    <SelectorContainer>\n      <SelectorHeader>\n        <SelectorTitle>🎼 Selecionar Instrumento</SelectorTitle>\n        <CurrentInstrument>\n          <span>{currentTemplate.emoji}</span>\n          <span>{currentTemplate.name}</span>\n        </CurrentInstrument>\n      </SelectorHeader>\n\n      <InstrumentGrid>\n        {INSTRUMENT_TEMPLATES.map(template => (\n          <InstrumentCard\n            key={template.id}\n            active={selectedInstrument === template.id}\n            onClick={() => onInstrumentChange(template.id)}\n          >\n            <InstrumentEmoji>{template.emoji}</InstrumentEmoji>\n            <InstrumentName>{template.name}</InstrumentName>\n            <InstrumentDescription>{template.description}</InstrumentDescription>\n          </InstrumentCard>\n        ))}\n      </InstrumentGrid>\n\n      {showDetails && (\n        <InstrumentDetails>\n          <DetailRow>\n            <DetailLabel>Clave:</DetailLabel>\n            <DetailValue>{getClefName(currentTemplate.clef)}</DetailValue>\n          </DetailRow>\n          \n          <DetailRow>\n            <DetailLabel>Extensão:</DetailLabel>\n            <DetailValue>{formatRange(currentTemplate)}</DetailValue>\n          </DetailRow>\n          \n          <DetailRow>\n            <DetailLabel>Pautas:</DetailLabel>\n            <DetailValue>{currentTemplate.staffCount === 1 ? 'Simples' : 'Dupla (Sol + Fá)'}</DetailValue>\n          </DetailRow>\n          \n          {currentTemplate.transposition && (\n            <DetailRow>\n              <DetailLabel>Transposição:</DetailLabel>\n              <DetailValue>\n                {currentTemplate.transposition > 0 ? '+' : ''}{currentTemplate.transposition} semitons\n              </DetailValue>\n            </DetailRow>\n          )}\n          \n          {currentTemplate.tuning && (\n            <>\n              <DetailRow>\n                <DetailLabel>Afinação:</DetailLabel>\n                <DetailValue>Cordas</DetailValue>\n              </DetailRow>\n              <TuningInfo>\n                🎸 {currentTemplate.tuning.join(' - ')}\n              </TuningInfo>\n            </>\n          )}\n          \n          <DetailRow>\n            <DetailLabel>Tonalidades comuns:</DetailLabel>\n            <DetailValue>\n              <KeySuggestions>\n                {currentTemplate.commonKeys.slice(0, 6).map(key => (\n                  <KeyChip key={key}>{key}</KeyChip>\n                ))}\n              </KeySuggestions>\n            </DetailValue>\n          </DetailRow>\n        </InstrumentDetails>\n      )}\n    </SelectorContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AAEtC,SAASC,oBAAoB,EAAEC,qBAAqB,QAAiC,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvH,MAAMC,iBAAiB,GAAGP,MAAM,CAACQ,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,iBAAiB;AAQvB,MAAMG,cAAc,GAAGV,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GALID,cAAc;AAOpB,MAAME,aAAa,GAAGZ,MAAM,CAACa,EAAE;AAC/B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,aAAa;AAMnB,MAAMG,iBAAiB,GAAGf,MAAM,CAACQ,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAPID,iBAAiB;AASvB,MAAME,cAAc,GAAGjB,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GALID,cAAc;AAOpB,MAAME,cAAc,GAAGnB,MAAM,CAACoB,MAA4B;AAC1D;AACA;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,SAAS;AACnE;AACA,gBAAgBD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,0BAA0B,GAAG,OAAO;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhBIJ,cAAc;AAkBpB,MAAMK,eAAe,GAAGxB,MAAM,CAACQ,GAAG;AAClC;AACA;AACA,CAAC;AAACiB,GAAA,GAHID,eAAe;AAKrB,MAAME,cAAc,GAAG1B,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GAJID,cAAc;AAMpB,MAAME,qBAAqB,GAAG5B,MAAM,CAACQ,GAAG;AACxC;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GALID,qBAAqB;AAO3B,MAAME,iBAAiB,GAAG9B,MAAM,CAACQ,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GANID,iBAAiB;AAQvB,MAAME,SAAS,GAAGhC,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GATID,SAAS;AAWf,MAAME,WAAW,GAAGlC,MAAM,CAACmC,IAAI;AAC/B;AACA;AACA,CAAC;AAACC,IAAA,GAHIF,WAAW;AAKjB,MAAMG,WAAW,GAAGrC,MAAM,CAACmC,IAAI;AAC/B;AACA,CAAC;AAACG,IAAA,GAFID,WAAW;AAIjB,MAAME,UAAU,GAAGvC,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GAPID,UAAU;AAShB,MAAME,cAAc,GAAGzC,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GALID,cAAc;AAOpB,MAAME,OAAO,GAAG3C,MAAM,CAACmC,IAAI;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACS,IAAA,GAPID,OAAO;AAeb,OAAO,MAAME,kBAAqD,GAAGA,CAAC;EACpEC,kBAAkB;EAClBC,kBAAkB;EAClBC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,CAACiD,OAAO,CAAC;EACxD,MAAMI,eAAe,GAAGlD,qBAAqB,CAAC4C,kBAAkB,CAAC;EAEjE,MAAMO,WAAW,GAAIC,QAAgC,IAAK;IACxD,OAAO,GAAGA,QAAQ,CAACC,KAAK,CAACC,MAAM,CAACC,IAAI,GAAGH,QAAQ,CAACC,KAAK,CAACC,MAAM,CAACE,MAAM,MAAMJ,QAAQ,CAACC,KAAK,CAACI,OAAO,CAACF,IAAI,GAAGH,QAAQ,CAACC,KAAK,CAACI,OAAO,CAACD,MAAM,EAAE;EACxI,CAAC;EAED,MAAME,WAAW,GAAIC,IAAY,IAAK;IACpC,MAAMC,SAAS,GAAG;MAChBC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE;IACT,CAAC;IACD,OAAOJ,SAAS,CAACD,IAAI,CAA2B,IAAIA,IAAI;EAC1D,CAAC;EAED,IAAIb,OAAO,EAAE;IACX,oBACE5C,OAAA,CAACG,iBAAiB;MAAA4D,QAAA,gBAChB/D,OAAA,CAACM,cAAc;QAAAyD,QAAA,gBACb/D,OAAA,CAACQ,aAAa;UAAAuD,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAC7CnE,OAAA,CAACW,iBAAiB;UAAAoD,QAAA,gBAChB/D,OAAA;YAAA+D,QAAA,EAAOf,eAAe,CAACoB;UAAK;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpCnE,OAAA;YAAA+D,QAAA,EAAOf,eAAe,CAACqB;UAAI;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEjBnE,OAAA;QACEsE,KAAK,EAAE5B,kBAAmB;QAC1B6B,QAAQ,EAAGC,CAAC,IAAK7B,kBAAkB,CAAC6B,CAAC,CAACC,MAAM,CAACH,KAAuB,CAAE;QACtEI,KAAK,EAAE;UACLC,KAAK,EAAE,MAAM;UACbC,OAAO,EAAE,QAAQ;UACjBC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE;QACZ,CAAE;QAAAhB,QAAA,EAEDlE,oBAAoB,CAACmF,GAAG,CAAC9B,QAAQ,iBAChClD,OAAA;UAA0BsE,KAAK,EAAEpB,QAAQ,CAAC+B,EAAG;UAAAlB,QAAA,GAC1Cb,QAAQ,CAACkB,KAAK,EAAC,GAAC,EAAClB,QAAQ,CAACmB,IAAI;QAAA,GADpBnB,QAAQ,CAAC+B,EAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhB,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAExB;EAEA,oBACEnE,OAAA,CAACG,iBAAiB;IAAA4D,QAAA,gBAChB/D,OAAA,CAACM,cAAc;MAAAyD,QAAA,gBACb/D,OAAA,CAACQ,aAAa;QAAAuD,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eACxDnE,OAAA,CAACW,iBAAiB;QAAAoD,QAAA,gBAChB/D,OAAA;UAAA+D,QAAA,EAAOf,eAAe,CAACoB;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpCnE,OAAA;UAAA+D,QAAA,EAAOf,eAAe,CAACqB;QAAI;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEjBnE,OAAA,CAACa,cAAc;MAAAkD,QAAA,EACZlE,oBAAoB,CAACmF,GAAG,CAAC9B,QAAQ,iBAChClD,OAAA,CAACe,cAAc;QAEbG,MAAM,EAAEwB,kBAAkB,KAAKQ,QAAQ,CAAC+B,EAAG;QAC3CC,OAAO,EAAEA,CAAA,KAAMvC,kBAAkB,CAACO,QAAQ,CAAC+B,EAAE,CAAE;QAAAlB,QAAA,gBAE/C/D,OAAA,CAACoB,eAAe;UAAA2C,QAAA,EAAEb,QAAQ,CAACkB;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC,eACnDnE,OAAA,CAACsB,cAAc;UAAAyC,QAAA,EAAEb,QAAQ,CAACmB;QAAI;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAiB,CAAC,eAChDnE,OAAA,CAACwB,qBAAqB;UAAAuC,QAAA,EAAEb,QAAQ,CAACiC;QAAW;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAwB,CAAC;MAAA,GANhEjB,QAAQ,CAAC+B,EAAE;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOF,CACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,EAEhBrB,WAAW,iBACV9C,OAAA,CAAC0B,iBAAiB;MAAAqC,QAAA,gBAChB/D,OAAA,CAAC4B,SAAS;QAAAmC,QAAA,gBACR/D,OAAA,CAAC8B,WAAW;UAAAiC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjCnE,OAAA,CAACiC,WAAW;UAAA8B,QAAA,EAAEP,WAAW,CAACR,eAAe,CAACS,IAAI;QAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eAEZnE,OAAA,CAAC4B,SAAS;QAAAmC,QAAA,gBACR/D,OAAA,CAAC8B,WAAW;UAAAiC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACpCnE,OAAA,CAACiC,WAAW;UAAA8B,QAAA,EAAEd,WAAW,CAACD,eAAe;QAAC;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eAEZnE,OAAA,CAAC4B,SAAS;QAAAmC,QAAA,gBACR/D,OAAA,CAAC8B,WAAW;UAAAiC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClCnE,OAAA,CAACiC,WAAW;UAAA8B,QAAA,EAAEf,eAAe,CAACoC,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG;QAAkB;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CAAC,EAEXnB,eAAe,CAACqC,aAAa,iBAC5BrF,OAAA,CAAC4B,SAAS;QAAAmC,QAAA,gBACR/D,OAAA,CAAC8B,WAAW;UAAAiC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxCnE,OAAA,CAACiC,WAAW;UAAA8B,QAAA,GACTf,eAAe,CAACqC,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAErC,eAAe,CAACqC,aAAa,EAAC,WAC/E;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACZ,EAEAnB,eAAe,CAACsC,MAAM,iBACrBtF,OAAA,CAAAE,SAAA;QAAA6D,QAAA,gBACE/D,OAAA,CAAC4B,SAAS;UAAAmC,QAAA,gBACR/D,OAAA,CAAC8B,WAAW;YAAAiC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACpCnE,OAAA,CAACiC,WAAW;YAAA8B,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACZnE,OAAA,CAACmC,UAAU;UAAA4B,QAAA,GAAC,eACP,EAACf,eAAe,CAACsC,MAAM,CAACC,IAAI,CAAC,KAAK,CAAC;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA,eACb,CACH,eAEDnE,OAAA,CAAC4B,SAAS;QAAAmC,QAAA,gBACR/D,OAAA,CAAC8B,WAAW;UAAAiC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC9CnE,OAAA,CAACiC,WAAW;UAAA8B,QAAA,eACV/D,OAAA,CAACqC,cAAc;YAAA0B,QAAA,EACZf,eAAe,CAACwC,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACT,GAAG,CAACU,GAAG,iBAC7C1F,OAAA,CAACuC,OAAO;cAAAwB,QAAA,EAAY2B;YAAG,GAATA,GAAG;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CACpB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAExB,CAAC;AAACtB,EAAA,CAlIWJ,kBAAqD;AAAAkD,IAAA,GAArDlD,kBAAqD;AAAA,IAAApC,EAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAmD,IAAA;AAAAC,YAAA,CAAAvF,EAAA;AAAAuF,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}