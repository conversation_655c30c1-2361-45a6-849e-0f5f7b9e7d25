{"ast": null, "code": "import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\n\n// Configuração do Firebase\n// IMPORTANTE: Substitua essas configurações pelas suas próprias do Firebase Console\nconst firebaseConfig = {\n  apiKey: \"your-api-key-here\",\n  authDomain: \"your-project-id.firebaseapp.com\",\n  projectId: \"your-project-id\",\n  storageBucket: \"your-project-id.appspot.com\",\n  messagingSenderId: \"your-sender-id\",\n  appId: \"your-app-id\"\n};\n\n// Inicializar Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Inicializar serviços\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\nexport default app;", "map": {"version": 3, "names": ["initializeApp", "getAuth", "getFirestore", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "app", "auth", "db"], "sources": ["D:/Dev/partitura_digital/src/services/firebase.ts"], "sourcesContent": ["import { initializeApp } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\n\n// Configuração do Firebase\n// IMPORTANTE: Substitua essas configurações pelas suas próprias do Firebase Console\nconst firebaseConfig = {\n  apiKey: \"your-api-key-here\",\n  authDomain: \"your-project-id.firebaseapp.com\",\n  projectId: \"your-project-id\",\n  storageBucket: \"your-project-id.appspot.com\",\n  messagingSenderId: \"your-sender-id\",\n  appId: \"your-app-id\"\n};\n\n// Inicializar Firebase\nconst app = initializeApp(firebaseConfig);\n\n// Inicializar serviços\nexport const auth = getAuth(app);\nexport const db = getFirestore(app);\n\nexport default app;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,cAAc;AAC5C,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,YAAY,QAAQ,oBAAoB;;AAEjD;AACA;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,mBAAmB;EAC3BC,UAAU,EAAE,iCAAiC;EAC7CC,SAAS,EAAE,iBAAiB;EAC5BC,aAAa,EAAE,6BAA6B;EAC5CC,iBAAiB,EAAE,gBAAgB;EACnCC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,GAAG,GAAGV,aAAa,CAACG,cAAc,CAAC;;AAEzC;AACA,OAAO,MAAMQ,IAAI,GAAGV,OAAO,CAACS,GAAG,CAAC;AAChC,OAAO,MAAME,EAAE,GAAGV,YAAY,CAACQ,GAAG,CAAC;AAEnC,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}