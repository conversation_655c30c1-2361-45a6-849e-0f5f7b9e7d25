{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ScoreEditor\\\\ScoreEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { ScoreService } from '../../services/scoreService';\nimport { ChordView } from '../ChordView/ChordView';\nimport { MusicalNote as MusicalNoteComponent } from '../MusicalNote/MusicalNote';\nimport { v4 as uuidv4 } from 'uuid';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n_c = EditorContainer;\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c2 = EditorHeader;\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n_c3 = EditorTitle;\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n_c4 = EditorActions;\nconst ActionButton = styled.button`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c5 = ActionButton;\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n_c6 = EditorContent;\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n_c7 = ToolPanel;\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n_c8 = ToolSection;\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n_c9 = ToolGrid;\nconst ToolButton = styled.button`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n_c0 = ToolButton;\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n_c1 = ScoreCanvas;\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n_c10 = StaffContainer;\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n_c11 = Staff;\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n_c12 = GuestWarning;\nexport const ScoreEditor = ({\n  scoreId\n}) => {\n  _s();\n  const {\n    currentUser\n  } = useAuth();\n  const [title, setTitle] = useState('Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState('note');\n  const [selectedDuration, setSelectedDuration] = useState('quarter');\n  const [selectedNote, setSelectedNote] = useState('C');\n  const [selectedAccidental, setSelectedAccidental] = useState(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [placedNotes, setPlacedNotes] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showChords, setShowChords] = useState(false);\n  const [lastSaved, setLastSaved] = useState(null);\n  const [zoomLevel, setZoomLevel] = useState(1);\n\n  // Carregar partitura existente\n  React.useEffect(() => {\n    if (scoreId && currentUser) {\n      loadScore();\n    }\n  }, [scoreId, currentUser]);\n\n  // Salvamento automático\n  React.useEffect(() => {\n    if (!currentUser || !scoreId || placedNotes.length === 0) return;\n    const autoSaveTimer = setTimeout(() => {\n      handleSave(true); // true indica salvamento automático\n    }, 2000); // Salvar após 2 segundos de inatividade\n\n    return () => clearTimeout(autoSaveTimer);\n  }, [placedNotes, title, currentUser, scoreId]);\n  const loadScore = async () => {\n    if (!scoreId || !currentUser) return;\n    setIsLoading(true);\n    try {\n      const score = await ScoreService.getScore(scoreId);\n      if (score && score.userId === currentUser.uid) {\n        setTitle(score.title);\n\n        // Extrair notas de todos os compassos\n        const allNotes = [];\n        score.staffs.forEach(staff => {\n          staff.measures.forEach(measure => {\n            allNotes.push(...measure.notes);\n          });\n        });\n        setPlacedNotes(allNotes);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar partitura:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleSave = async (isAutoSave = false) => {\n    if (!currentUser) {\n      if (!isAutoSave) {\n        alert('Você precisa estar logado para salvar partituras!');\n      }\n      return;\n    }\n    setIsSaving(true);\n    try {\n      // Organizar notas por compasso\n      const measureMap = new Map();\n      placedNotes.forEach(note => {\n        const measure = note.position.measure;\n        if (!measureMap.has(measure)) {\n          measureMap.set(measure, []);\n        }\n        measureMap.get(measure).push(note);\n      });\n\n      // Criar compassos com as notas\n      const measures = Array.from(measureMap.entries()).map(([measureNumber, notes]) => ({\n        id: uuidv4(),\n        number: measureNumber,\n        timeSignature: {\n          numerator: 4,\n          denominator: 4\n        },\n        notes: notes.sort((a, b) => a.position.beat - b.position.beat),\n        chords: []\n      }));\n\n      // Criar dados da partitura\n      const scoreData = {\n        title,\n        key: {\n          note: 'C',\n          mode: 'major'\n        },\n        timeSignature: {\n          numerator: 4,\n          denominator: 4\n        },\n        tempo: 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: 'treble',\n          instrument: 'piano',\n          measures\n        }],\n        lyrics: [],\n        userId: currentUser.uid\n      };\n      if (scoreId) {\n        // Para update, removemos o userId pois não deve ser alterado\n        const {\n          userId,\n          ...updateData\n        } = scoreData;\n        await ScoreService.updateScore(scoreId, updateData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n      setLastSaved(new Date());\n      if (!isAutoSave) {\n        alert('Partitura salva com sucesso!');\n      }\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      if (!isAutoSave) {\n        alert('Erro ao salvar partitura');\n      }\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n  const handleStaffClick = event => {\n    var _event$currentTarget$;\n    const rect = event.currentTarget.getBoundingClientRect();\n    const svgRect = (_event$currentTarget$ = event.currentTarget.closest('svg')) === null || _event$currentTarget$ === void 0 ? void 0 : _event$currentTarget$.getBoundingClientRect();\n    if (!svgRect) return;\n    const x = event.clientX - svgRect.left;\n    const y = event.clientY - svgRect.top;\n\n    // Calcular a linha da pauta baseada na posição Y\n    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta\n    const notePositions = [{\n      y: 40,\n      note: 'A',\n      octave: 5\n    },\n    // Acima da pauta\n    {\n      y: 50,\n      note: 'G',\n      octave: 5\n    },\n    // 5ª linha\n    {\n      y: 60,\n      note: 'F',\n      octave: 5\n    },\n    // Entre 4ª e 5ª\n    {\n      y: 70,\n      note: 'E',\n      octave: 5\n    },\n    // 4ª linha\n    {\n      y: 80,\n      note: 'D',\n      octave: 5\n    },\n    // Entre 3ª e 4ª\n    {\n      y: 90,\n      note: 'C',\n      octave: 5\n    },\n    // 3ª linha (Dó central)\n    {\n      y: 100,\n      note: 'B',\n      octave: 4\n    },\n    // Entre 2ª e 3ª\n    {\n      y: 110,\n      note: 'A',\n      octave: 4\n    },\n    // 2ª linha\n    {\n      y: 120,\n      note: 'G',\n      octave: 4\n    },\n    // Entre 1ª e 2ª\n    {\n      y: 130,\n      note: 'F',\n      octave: 4\n    },\n    // 1ª linha\n    {\n      y: 140,\n      note: 'E',\n      octave: 4\n    } // Abaixo da pauta\n    ];\n\n    // Encontrar a posição mais próxima\n    const closestPosition = notePositions.reduce((closest, current) => {\n      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;\n    });\n\n    // Calcular a posição horizontal (compasso e tempo)\n    const measureWidth = 150; // Largura aproximada de um compasso\n    const startX = 120; // Início da área de notas\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = relativeX % measureWidth / measureWidth * 4; // 4 tempos por compasso\n\n    // Criar nova nota\n    const newNote = {\n      id: uuidv4(),\n      name: selectedNote,\n      // Usar a nota selecionada no painel\n      octave: closestPosition.octave,\n      duration: selectedDuration,\n      accidental: selectedAccidental || undefined,\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4,\n        // Arredondar para 1/4 de tempo\n        staff: 0\n      },\n      isRest: selectedTool === 'rest'\n    };\n    setPlacedNotes(prev => [...prev, newNote]);\n  };\n  const durations = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(EditorContainer, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '400px',\n          fontSize: '1.2rem',\n          color: '#666'\n        },\n        children: \"\\uD83C\\uDFBC Carregando partitura...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(EditorContainer, {\n    children: [/*#__PURE__*/_jsxDEV(EditorHeader, {\n      children: [/*#__PURE__*/_jsxDEV(EditorTitle, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Editor de Partituras\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: title,\n            onChange: e => setTitle(e.target.value),\n            placeholder: \"Nome da partitura...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this), currentUser && lastSaved && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.8rem',\n              color: 'rgba(255,255,255,0.8)'\n            },\n            children: [\"\\uD83D\\uDCBE Salvo \\xE0s \", lastSaved.toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorActions, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setShowChords(!showChords),\n          variant: \"primary\",\n          children: showChords ? '🎼 Partitura' : '🎸 Cifras'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setPlacedNotes([]),\n          variant: \"primary\",\n          disabled: placedNotes.length === 0,\n          children: \"\\uD83D\\uDDD1\\uFE0F Limpar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handlePlay,\n          variant: \"primary\",\n          children: isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handleSave,\n          variant: \"secondary\",\n          disabled: !currentUser || isSaving,\n          children: isSaving ? '💾 Salvando...' : '💾 Salvar'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditorContent, {\n      children: [/*#__PURE__*/_jsxDEV(ToolPanel, {\n        children: [!currentUser && /*#__PURE__*/_jsxDEV(GuestWarning, {\n          children: \"\\u26A0\\uFE0F Modo visitante: suas altera\\xE7\\xF5es n\\xE3o ser\\xE3o salvas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFB5 Ferramentas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: [/*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'note',\n              onClick: () => setSelectedTool('note'),\n              children: \"\\uD83C\\uDFB5 Nota\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'rest',\n              onClick: () => setSelectedTool('rest'),\n              children: \"\\uD83C\\uDFBC Pausa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'chord',\n              onClick: () => setSelectedTool('chord'),\n              children: \"\\uD83C\\uDFB9 Acorde\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u23F1\\uFE0F Dura\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: durations.map(duration => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedDuration === duration,\n              onClick: () => setSelectedDuration(duration),\n              children: duration === 'whole' ? '𝅝' : duration === 'half' ? '𝅗𝅥' : duration === 'quarter' ? '♩' : duration === 'eighth' ? '♫' : '♬'\n            }, duration, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFBC Notas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: notes.map(note => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedNote === note,\n              onClick: () => setSelectedNote(note),\n              children: note\n            }, note, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u266F\\u266D Acidentes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: [/*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === 'sharp',\n              onClick: () => setSelectedAccidental(selectedAccidental === 'sharp' ? null : 'sharp'),\n              children: \"\\u266F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === 'flat',\n              onClick: () => setSelectedAccidental(selectedAccidental === 'flat' ? null : 'flat'),\n              children: \"\\u266D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === 'natural',\n              onClick: () => setSelectedAccidental(selectedAccidental === 'natural' ? null : 'natural'),\n              children: \"\\u266E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedAccidental === null,\n              onClick: () => setSelectedAccidental(null),\n              children: \"\\u2014\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCCA Estat\\xEDsticas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.9rem',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83D\\uDCDD Notas: \", placedNotes.filter(n => !n.isRest).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u23F8\\uFE0F Pausas: \", placedNotes.filter(n => n.isRest).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83C\\uDFB5 Total: \", placedNotes.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScoreCanvas, {\n        children: showChords ? /*#__PURE__*/_jsxDEV(ChordView, {\n          notes: placedNotes,\n          title: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(StaffContainer, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 1rem 0',\n              color: '#495057'\n            },\n            children: [\"Partitura: \", title]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Staff, {\n            viewBox: \"0 0 800 200\",\n            children: [[0, 1, 2, 3, 4].map(line => /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"50\",\n              y1: 50 + line * 20,\n              x2: \"750\",\n              y2: 50 + line * 20,\n              stroke: \"#333\",\n              strokeWidth: \"1\"\n            }, line, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"20\",\n              y: \"90\",\n              fontSize: \"40\",\n              fill: \"#333\",\n              children: \"\\uD834\\uDD1E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"75\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"95\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"110\",\n              y1: \"50\",\n              x2: \"110\",\n              y2: \"130\",\n              stroke: \"#333\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this), [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: 120 + measure * 150,\n              y1: \"50\",\n              x2: 120 + measure * 150,\n              y2: \"130\",\n              stroke: \"#999\",\n              strokeWidth: \"1\"\n            }, measure, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 17\n            }, this)), [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"text\", {\n              x: 120 + (measure - 1) * 150 + 75,\n              y: \"35\",\n              fontSize: \"12\",\n              fill: \"#666\",\n              textAnchor: \"middle\",\n              children: measure\n            }, measure, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"120\",\n              y: \"40\",\n              width: \"600\",\n              height: \"100\",\n              fill: \"transparent\",\n              style: {\n                cursor: 'crosshair'\n              },\n              onClick: handleStaffClick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 15\n            }, this), placedNotes.map(note => {\n              const x = 120 + (note.position.measure - 1) * 150 + note.position.beat * 30;\n              const notePositions = {\n                'A5': 40,\n                'G5': 50,\n                'F5': 60,\n                'E5': 70,\n                'D5': 80,\n                'C5': 90,\n                'B4': 100,\n                'A4': 110,\n                'G4': 120,\n                'F4': 130,\n                'E4': 140\n              };\n              const y = notePositions[`${note.name}${note.octave}`] || 90;\n              return /*#__PURE__*/_jsxDEV(MusicalNoteComponent, {\n                note: note,\n                x: x,\n                y: y,\n                onRemove: () => setPlacedNotes(prev => prev.filter(n => n.id !== note.id))\n              }, note.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 19\n              }, this);\n            }), placedNotes.length === 0 && /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"100\",\n              fontSize: \"14\",\n              fill: \"#999\",\n              textAnchor: \"middle\",\n              children: \"Clique na pauta para adicionar notas \\u2022 Clique em uma nota para remov\\xEA-la\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 390,\n    columnNumber: 5\n  }, this);\n};\n_s(ScoreEditor, \"qi1S0HmlzbmN3vo7dGPwIRfn3Ik=\", false, function () {\n  return [useAuth];\n});\n_c13 = ScoreEditor;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"EditorContainer\");\n$RefreshReg$(_c2, \"EditorHeader\");\n$RefreshReg$(_c3, \"EditorTitle\");\n$RefreshReg$(_c4, \"EditorActions\");\n$RefreshReg$(_c5, \"ActionButton\");\n$RefreshReg$(_c6, \"EditorContent\");\n$RefreshReg$(_c7, \"ToolPanel\");\n$RefreshReg$(_c8, \"ToolSection\");\n$RefreshReg$(_c9, \"ToolGrid\");\n$RefreshReg$(_c0, \"ToolButton\");\n$RefreshReg$(_c1, \"ScoreCanvas\");\n$RefreshReg$(_c10, \"StaffContainer\");\n$RefreshReg$(_c11, \"Staff\");\n$RefreshReg$(_c12, \"GuestWarning\");\n$RefreshReg$(_c13, \"ScoreEditor\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "ScoreService", "ChordView", "MusicalNote", "MusicalNoteComponent", "v4", "uuidv4", "jsxDEV", "_jsxDEV", "EditorC<PERSON><PERSON>", "div", "_c", "Editor<PERSON><PERSON>er", "_c2", "EditorT<PERSON>le", "_c3", "EditorActions", "_c4", "ActionButton", "button", "props", "variant", "_c5", "Editor<PERSON><PERSON><PERSON>", "_c6", "ToolPanel", "_c7", "ToolSection", "_c8", "ToolGrid", "_c9", "<PERSON><PERSON><PERSON><PERSON>on", "active", "_c0", "ScoreCanvas", "_c1", "StaffC<PERSON>r", "_c10", "Staff", "svg", "_c11", "Guest<PERSON><PERSON>ning", "_c12", "ScoreEditor", "scoreId", "_s", "currentUser", "title", "setTitle", "selectedTool", "setSelectedTool", "selectedDuration", "setSelectedDuration", "selected<PERSON><PERSON>", "setSelectedNote", "selectedAccidental", "setSelectedAccidental", "isPlaying", "setIsPlaying", "isSaving", "setIsSaving", "placedNotes", "setPlacedNotes", "isLoading", "setIsLoading", "showChords", "setShowChords", "lastSaved", "setLastSaved", "zoomLevel", "setZoomLevel", "useEffect", "loadScore", "length", "autoSaveTimer", "setTimeout", "handleSave", "clearTimeout", "score", "getScore", "userId", "uid", "allNotes", "staffs", "for<PERSON>ach", "staff", "measures", "measure", "push", "notes", "error", "console", "isAutoSave", "alert", "measureMap", "Map", "note", "position", "has", "set", "get", "Array", "from", "entries", "map", "measureNumber", "id", "number", "timeSignature", "numerator", "denominator", "sort", "a", "b", "beat", "chords", "scoreData", "key", "mode", "tempo", "clef", "instrument", "lyrics", "updateData", "updateScore", "createScore", "Date", "handlePlay", "handleStaffClick", "event", "_event$currentTarget$", "rect", "currentTarget", "getBoundingClientRect", "svgRect", "closest", "x", "clientX", "left", "y", "clientY", "top", "staffLines", "notePositions", "octave", "closestPosition", "reduce", "current", "Math", "abs", "measureWidth", "startX", "relativeX", "max", "floor", "beatPosition", "newNote", "name", "duration", "accidental", "undefined", "round", "isRest", "prev", "durations", "children", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gap", "type", "value", "onChange", "e", "target", "placeholder", "toLocaleTimeString", "onClick", "disabled", "filter", "n", "margin", "viewBox", "line", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "fill", "textAnchor", "width", "cursor", "onRemove", "_c13", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ScoreEditor/ScoreEditor.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Score, MusicalNote, NoteName, NoteDuration, ClefType, InstrumentType, Octave } from '../../types/music';\nimport { ScoreService } from '../../services/scoreService';\nimport { ChordView } from '../ChordView/ChordView';\nimport { MusicalNote as MusicalNoteComponent } from '../MusicalNote/MusicalNote';\nimport { v4 as uuidv4 } from 'uuid';\n\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n\nconst ToolButton = styled.button<{ active?: boolean }>`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n\ninterface ScoreEditorProps {\n  scoreId?: string;\n}\n\nexport const ScoreEditor: React.FC<ScoreEditorProps> = ({ scoreId }) => {\n  const { currentUser } = useAuth();\n  const [title, setTitle] = useState('Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState<'note' | 'rest' | 'chord'>('note');\n  const [selectedDuration, setSelectedDuration] = useState<NoteDuration>('quarter');\n  const [selectedNote, setSelectedNote] = useState<NoteName>('C');\n  const [selectedAccidental, setSelectedAccidental] = useState<'sharp' | 'flat' | 'natural' | null>(null);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [placedNotes, setPlacedNotes] = useState<MusicalNote[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showChords, setShowChords] = useState(false);\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\n  const [zoomLevel, setZoomLevel] = useState(1);\n\n  // Carregar partitura existente\n  React.useEffect(() => {\n    if (scoreId && currentUser) {\n      loadScore();\n    }\n  }, [scoreId, currentUser]);\n\n  // Salvamento automático\n  React.useEffect(() => {\n    if (!currentUser || !scoreId || placedNotes.length === 0) return;\n\n    const autoSaveTimer = setTimeout(() => {\n      handleSave(true); // true indica salvamento automático\n    }, 2000); // Salvar após 2 segundos de inatividade\n\n    return () => clearTimeout(autoSaveTimer);\n  }, [placedNotes, title, currentUser, scoreId]);\n\n  const loadScore = async () => {\n    if (!scoreId || !currentUser) return;\n\n    setIsLoading(true);\n    try {\n      const score = await ScoreService.getScore(scoreId);\n      if (score && score.userId === currentUser.uid) {\n        setTitle(score.title);\n\n        // Extrair notas de todos os compassos\n        const allNotes: MusicalNote[] = [];\n        score.staffs.forEach(staff => {\n          staff.measures.forEach(measure => {\n            allNotes.push(...measure.notes);\n          });\n        });\n\n        setPlacedNotes(allNotes);\n      }\n    } catch (error) {\n      console.error('Erro ao carregar partitura:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSave = async (isAutoSave = false) => {\n    if (!currentUser) {\n      if (!isAutoSave) {\n        alert('Você precisa estar logado para salvar partituras!');\n      }\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      // Organizar notas por compasso\n      const measureMap = new Map<number, MusicalNote[]>();\n      placedNotes.forEach(note => {\n        const measure = note.position.measure;\n        if (!measureMap.has(measure)) {\n          measureMap.set(measure, []);\n        }\n        measureMap.get(measure)!.push(note);\n      });\n\n      // Criar compassos com as notas\n      const measures = Array.from(measureMap.entries()).map(([measureNumber, notes]) => ({\n        id: uuidv4(),\n        number: measureNumber,\n        timeSignature: { numerator: 4, denominator: 4 },\n        notes: notes.sort((a, b) => a.position.beat - b.position.beat),\n        chords: []\n      }));\n\n      // Criar dados da partitura\n      const scoreData = {\n        title,\n        key: { note: 'C' as NoteName, mode: 'major' as const },\n        timeSignature: { numerator: 4, denominator: 4 },\n        tempo: 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: 'treble' as ClefType,\n          instrument: 'piano' as InstrumentType,\n          measures\n        }],\n        lyrics: [],\n        userId: currentUser.uid\n      };\n\n      if (scoreId) {\n        // Para update, removemos o userId pois não deve ser alterado\n        const { userId, ...updateData } = scoreData;\n        await ScoreService.updateScore(scoreId, updateData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n\n      setLastSaved(new Date());\n      if (!isAutoSave) {\n        alert('Partitura salva com sucesso!');\n      }\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      if (!isAutoSave) {\n        alert('Erro ao salvar partitura');\n      }\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n\n  const handleStaffClick = (event: React.MouseEvent<SVGRectElement>) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const svgRect = event.currentTarget.closest('svg')?.getBoundingClientRect();\n\n    if (!svgRect) return;\n\n    const x = event.clientX - svgRect.left;\n    const y = event.clientY - svgRect.top;\n\n    // Calcular a linha da pauta baseada na posição Y\n    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta\n    const notePositions = [\n      { y: 40, note: 'A' as NoteName, octave: 5 as Octave }, // Acima da pauta\n      { y: 50, note: 'G' as NoteName, octave: 5 as Octave }, // 5ª linha\n      { y: 60, note: 'F' as NoteName, octave: 5 as Octave }, // Entre 4ª e 5ª\n      { y: 70, note: 'E' as NoteName, octave: 5 as Octave }, // 4ª linha\n      { y: 80, note: 'D' as NoteName, octave: 5 as Octave }, // Entre 3ª e 4ª\n      { y: 90, note: 'C' as NoteName, octave: 5 as Octave }, // 3ª linha (Dó central)\n      { y: 100, note: 'B' as NoteName, octave: 4 as Octave }, // Entre 2ª e 3ª\n      { y: 110, note: 'A' as NoteName, octave: 4 as Octave }, // 2ª linha\n      { y: 120, note: 'G' as NoteName, octave: 4 as Octave }, // Entre 1ª e 2ª\n      { y: 130, note: 'F' as NoteName, octave: 4 as Octave }, // 1ª linha\n      { y: 140, note: 'E' as NoteName, octave: 4 as Octave }, // Abaixo da pauta\n    ];\n\n    // Encontrar a posição mais próxima\n    const closestPosition = notePositions.reduce((closest, current) => {\n      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;\n    });\n\n    // Calcular a posição horizontal (compasso e tempo)\n    const measureWidth = 150; // Largura aproximada de um compasso\n    const startX = 120; // Início da área de notas\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = ((relativeX % measureWidth) / measureWidth) * 4; // 4 tempos por compasso\n\n    // Criar nova nota\n    const newNote: MusicalNote = {\n      id: uuidv4(),\n      name: selectedNote, // Usar a nota selecionada no painel\n      octave: closestPosition.octave,\n      duration: selectedDuration,\n      accidental: selectedAccidental || undefined,\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4, // Arredondar para 1/4 de tempo\n        staff: 0\n      },\n      isRest: selectedTool === 'rest'\n    };\n\n    setPlacedNotes(prev => [...prev, newNote]);\n  };\n\n  const durations: NoteDuration[] = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes: NoteName[] = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n\n  if (isLoading) {\n    return (\n      <EditorContainer>\n        <div style={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '400px',\n          fontSize: '1.2rem',\n          color: '#666'\n        }}>\n          🎼 Carregando partitura...\n        </div>\n      </EditorContainer>\n    );\n  }\n\n  return (\n    <EditorContainer>\n      <EditorHeader>\n        <EditorTitle>\n          <h2>Editor de Partituras</h2>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n            <input\n              type=\"text\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              placeholder=\"Nome da partitura...\"\n            />\n            {currentUser && lastSaved && (\n              <div style={{ fontSize: '0.8rem', color: 'rgba(255,255,255,0.8)' }}>\n                💾 Salvo às {lastSaved.toLocaleTimeString()}\n              </div>\n            )}\n          </div>\n        </EditorTitle>\n        <EditorActions>\n          <ActionButton\n            onClick={() => setShowChords(!showChords)}\n            variant=\"primary\"\n          >\n            {showChords ? '🎼 Partitura' : '🎸 Cifras'}\n          </ActionButton>\n          <ActionButton\n            onClick={() => setPlacedNotes([])}\n            variant=\"primary\"\n            disabled={placedNotes.length === 0}\n          >\n            🗑️ Limpar\n          </ActionButton>\n          <ActionButton onClick={handlePlay} variant=\"primary\">\n            {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}\n          </ActionButton>\n          <ActionButton\n            onClick={handleSave}\n            variant=\"secondary\"\n            disabled={!currentUser || isSaving}\n          >\n            {isSaving ? '💾 Salvando...' : '💾 Salvar'}\n          </ActionButton>\n        </EditorActions>\n      </EditorHeader>\n\n      <EditorContent>\n        <ToolPanel>\n          {!currentUser && (\n            <GuestWarning>\n              ⚠️ Modo visitante: suas alterações não serão salvas\n            </GuestWarning>\n          )}\n          \n          <ToolSection>\n            <h3>🎵 Ferramentas</h3>\n            <ToolGrid>\n              <ToolButton \n                active={selectedTool === 'note'}\n                onClick={() => setSelectedTool('note')}\n              >\n                🎵 Nota\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'rest'}\n                onClick={() => setSelectedTool('rest')}\n              >\n                🎼 Pausa\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'chord'}\n                onClick={() => setSelectedTool('chord')}\n              >\n                🎹 Acorde\n              </ToolButton>\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>⏱️ Duração</h3>\n            <ToolGrid>\n              {durations.map(duration => (\n                <ToolButton\n                  key={duration}\n                  active={selectedDuration === duration}\n                  onClick={() => setSelectedDuration(duration)}\n                >\n                  {duration === 'whole' ? '𝅝' : \n                   duration === 'half' ? '𝅗𝅥' :\n                   duration === 'quarter' ? '♩' :\n                   duration === 'eighth' ? '♫' : '♬'}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>🎼 Notas</h3>\n            <ToolGrid>\n              {notes.map(note => (\n                <ToolButton\n                  key={note}\n                  active={selectedNote === note}\n                  onClick={() => setSelectedNote(note)}\n                >\n                  {note}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>♯♭ Acidentes</h3>\n            <ToolGrid>\n              <ToolButton\n                active={selectedAccidental === 'sharp'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'sharp' ? null : 'sharp')}\n              >\n                ♯\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === 'flat'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'flat' ? null : 'flat')}\n              >\n                ♭\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === 'natural'}\n                onClick={() => setSelectedAccidental(selectedAccidental === 'natural' ? null : 'natural')}\n              >\n                ♮\n              </ToolButton>\n              <ToolButton\n                active={selectedAccidental === null}\n                onClick={() => setSelectedAccidental(null)}\n              >\n                —\n              </ToolButton>\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>📊 Estatísticas</h3>\n            <div style={{ fontSize: '0.9rem', color: '#666' }}>\n              <div>📝 Notas: {placedNotes.filter(n => !n.isRest).length}</div>\n              <div>⏸️ Pausas: {placedNotes.filter(n => n.isRest).length}</div>\n              <div>🎵 Total: {placedNotes.length}</div>\n            </div>\n          </ToolSection>\n        </ToolPanel>\n\n        <ScoreCanvas>\n          {showChords ? (\n            <ChordView notes={placedNotes} title={title} />\n          ) : (\n            <StaffContainer>\n              <h3 style={{ margin: '0 0 1rem 0', color: '#495057' }}>Partitura: {title}</h3>\n              <Staff viewBox=\"0 0 800 200\">\n              {/* Linhas da pauta */}\n              {[0, 1, 2, 3, 4].map(line => (\n                <line\n                  key={line}\n                  x1=\"50\"\n                  y1={50 + line * 20}\n                  x2=\"750\"\n                  y2={50 + line * 20}\n                  stroke=\"#333\"\n                  strokeWidth=\"1\"\n                />\n              ))}\n              \n              {/* Clave de Sol */}\n              <text x=\"20\" y=\"90\" fontSize=\"40\" fill=\"#333\">𝄞</text>\n              \n              {/* Compasso 4/4 */}\n              <text x=\"80\" y=\"75\" fontSize=\"16\" fill=\"#333\">4</text>\n              <text x=\"80\" y=\"95\" fontSize=\"16\" fill=\"#333\">4</text>\n              \n              {/* Linha divisória inicial */}\n              <line x1=\"110\" y1=\"50\" x2=\"110\" y2=\"130\" stroke=\"#333\" strokeWidth=\"2\"/>\n\n              {/* Divisões de compasso */}\n              {[1, 2, 3, 4].map(measure => (\n                <line\n                  key={measure}\n                  x1={120 + (measure * 150)}\n                  y1=\"50\"\n                  x2={120 + (measure * 150)}\n                  y2=\"130\"\n                  stroke=\"#999\"\n                  strokeWidth=\"1\"\n                />\n              ))}\n\n              {/* Números dos compassos */}\n              {[1, 2, 3, 4].map(measure => (\n                <text\n                  key={measure}\n                  x={120 + ((measure - 1) * 150) + 75}\n                  y=\"35\"\n                  fontSize=\"12\"\n                  fill=\"#666\"\n                  textAnchor=\"middle\"\n                >\n                  {measure}\n                </text>\n              ))}\n              \n              {/* Área clicável para adicionar notas */}\n              <rect\n                x=\"120\"\n                y=\"40\"\n                width=\"600\"\n                height=\"100\"\n                fill=\"transparent\"\n                style={{ cursor: 'crosshair' }}\n                onClick={handleStaffClick}\n              />\n\n              {/* Renderizar notas colocadas */}\n              {placedNotes.map((note) => {\n                const x = 120 + ((note.position.measure - 1) * 150) + (note.position.beat * 30);\n                const notePositions: { [key: string]: number } = {\n                  'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,\n                  'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140\n                };\n                const y = notePositions[`${note.name}${note.octave}`] || 90;\n\n                return (\n                  <MusicalNoteComponent\n                    key={note.id}\n                    note={note}\n                    x={x}\n                    y={y}\n                    onRemove={() => setPlacedNotes(prev => prev.filter(n => n.id !== note.id))}\n                  />\n                );\n              })}\n\n              {/* Instruções */}\n              {placedNotes.length === 0 && (\n                <text x=\"400\" y=\"100\" fontSize=\"14\" fill=\"#999\" textAnchor=\"middle\">\n                  Clique na pauta para adicionar notas • Clique em uma nota para removê-la\n                </text>\n              )}\n            </Staff>\n          </StaffContainer>\n          )}\n        </ScoreCanvas>\n      </EditorContent>\n    </EditorContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAgB,OAAO;AAC/C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AAEpD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,WAAW,IAAIC,oBAAoB,QAAQ,4BAA4B;AAChF,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGV,MAAM,CAACW,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,eAAe;AAUrB,MAAMG,YAAY,GAAGb,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,YAAY;AASlB,MAAME,WAAW,GAAGf,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GA1BID,WAAW;AA4BjB,MAAME,aAAa,GAAGjB,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGnB,MAAM,CAACoB,MAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhCIJ,YAAY;AAkClB,MAAMK,aAAa,GAAGxB,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,aAAa;AAMnB,MAAME,SAAS,GAAG1B,MAAM,CAACW,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GANID,SAAS;AAQf,MAAME,WAAW,GAAG5B,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GATID,WAAW;AAWjB,MAAME,QAAQ,GAAG9B,MAAM,CAACW,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,QAAQ;AAMd,MAAME,UAAU,GAAGhC,MAAM,CAACoB,MAA4B;AACtD;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D,WAAWZ,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,OAAO,GAAG,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMZ,KAAK,IAAI,CAACA,KAAK,CAACY,MAAM,IAAI,sBAAsB;AACtD;AACA,CAAC;AAACC,GAAA,GAdIF,UAAU;AAgBhB,MAAMG,WAAW,GAAGnC,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GARID,WAAW;AAUjB,MAAME,cAAc,GAAGrC,MAAM,CAACW,GAAG;AACjC;AACA;AACA,CAAC;AAAC2B,IAAA,GAHID,cAAc;AAKpB,MAAME,KAAK,GAAGvC,MAAM,CAACwC,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAPIF,KAAK;AASX,MAAMG,YAAY,GAAG1C,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GATID,YAAY;AAelB,OAAO,MAAME,WAAuC,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM;IAAEC;EAAY,CAAC,GAAG9C,OAAO,CAAC,CAAC;EACjC,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,gBAAgB,CAAC;EACpD,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAA4B,MAAM,CAAC;EACnF,MAAM,CAACqD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtD,QAAQ,CAAe,SAAS,CAAC;EACjF,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAW,GAAG,CAAC;EAC/D,MAAM,CAACyD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1D,QAAQ,CAAsC,IAAI,CAAC;EACvG,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6D,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC+D,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAAgB,EAAE,CAAC;EACjE,MAAM,CAACiE,SAAS,EAAEC,YAAY,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmE,UAAU,EAAEC,aAAa,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqE,SAAS,EAAEC,YAAY,CAAC,GAAGtE,QAAQ,CAAc,IAAI,CAAC;EAC7D,MAAM,CAACuE,SAAS,EAAEC,YAAY,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC;;EAE7C;EACAD,KAAK,CAAC0E,SAAS,CAAC,MAAM;IACpB,IAAI3B,OAAO,IAAIE,WAAW,EAAE;MAC1B0B,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAAC5B,OAAO,EAAEE,WAAW,CAAC,CAAC;;EAE1B;EACAjD,KAAK,CAAC0E,SAAS,CAAC,MAAM;IACpB,IAAI,CAACzB,WAAW,IAAI,CAACF,OAAO,IAAIiB,WAAW,CAACY,MAAM,KAAK,CAAC,EAAE;IAE1D,MAAMC,aAAa,GAAGC,UAAU,CAAC,MAAM;MACrCC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IACpB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMC,YAAY,CAACH,aAAa,CAAC;EAC1C,CAAC,EAAE,CAACb,WAAW,EAAEd,KAAK,EAAED,WAAW,EAAEF,OAAO,CAAC,CAAC;EAE9C,MAAM4B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAAC5B,OAAO,IAAI,CAACE,WAAW,EAAE;IAE9BkB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMc,KAAK,GAAG,MAAM7E,YAAY,CAAC8E,QAAQ,CAACnC,OAAO,CAAC;MAClD,IAAIkC,KAAK,IAAIA,KAAK,CAACE,MAAM,KAAKlC,WAAW,CAACmC,GAAG,EAAE;QAC7CjC,QAAQ,CAAC8B,KAAK,CAAC/B,KAAK,CAAC;;QAErB;QACA,MAAMmC,QAAuB,GAAG,EAAE;QAClCJ,KAAK,CAACK,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;UAC5BA,KAAK,CAACC,QAAQ,CAACF,OAAO,CAACG,OAAO,IAAI;YAChCL,QAAQ,CAACM,IAAI,CAAC,GAAGD,OAAO,CAACE,KAAK,CAAC;UACjC,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF3B,cAAc,CAACoB,QAAQ,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACR1B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMY,UAAU,GAAG,MAAAA,CAAOgB,UAAU,GAAG,KAAK,KAAK;IAC/C,IAAI,CAAC9C,WAAW,EAAE;MAChB,IAAI,CAAC8C,UAAU,EAAE;QACfC,KAAK,CAAC,mDAAmD,CAAC;MAC5D;MACA;IACF;IAEAjC,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF;MACA,MAAMkC,UAAU,GAAG,IAAIC,GAAG,CAAwB,CAAC;MACnDlC,WAAW,CAACuB,OAAO,CAACY,IAAI,IAAI;QAC1B,MAAMT,OAAO,GAAGS,IAAI,CAACC,QAAQ,CAACV,OAAO;QACrC,IAAI,CAACO,UAAU,CAACI,GAAG,CAACX,OAAO,CAAC,EAAE;UAC5BO,UAAU,CAACK,GAAG,CAACZ,OAAO,EAAE,EAAE,CAAC;QAC7B;QACAO,UAAU,CAACM,GAAG,CAACb,OAAO,CAAC,CAAEC,IAAI,CAACQ,IAAI,CAAC;MACrC,CAAC,CAAC;;MAEF;MACA,MAAMV,QAAQ,GAAGe,KAAK,CAACC,IAAI,CAACR,UAAU,CAACS,OAAO,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,aAAa,EAAEhB,KAAK,CAAC,MAAM;QACjFiB,EAAE,EAAEpG,MAAM,CAAC,CAAC;QACZqG,MAAM,EAAEF,aAAa;QACrBG,aAAa,EAAE;UAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;QAC/CrB,KAAK,EAAEA,KAAK,CAACsB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACf,QAAQ,CAACiB,IAAI,GAAGD,CAAC,CAAChB,QAAQ,CAACiB,IAAI,CAAC;QAC9DC,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;;MAEH;MACA,MAAMC,SAAS,GAAG;QAChBrE,KAAK;QACLsE,GAAG,EAAE;UAAErB,IAAI,EAAE,GAAe;UAAEsB,IAAI,EAAE;QAAiB,CAAC;QACtDV,aAAa,EAAE;UAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;QAC/CS,KAAK,EAAE,GAAG;QACVpC,MAAM,EAAE,CAAC;UACPuB,EAAE,EAAEpG,MAAM,CAAC,CAAC;UACZkH,IAAI,EAAE,QAAoB;UAC1BC,UAAU,EAAE,OAAyB;UACrCnC;QACF,CAAC,CAAC;QACFoC,MAAM,EAAE,EAAE;QACV1C,MAAM,EAAElC,WAAW,CAACmC;MACtB,CAAC;MAED,IAAIrC,OAAO,EAAE;QACX;QACA,MAAM;UAAEoC,MAAM;UAAE,GAAG2C;QAAW,CAAC,GAAGP,SAAS;QAC3C,MAAMnH,YAAY,CAAC2H,WAAW,CAAChF,OAAO,EAAE+E,UAAU,CAAC;MACrD,CAAC,MAAM;QACL,MAAM1H,YAAY,CAAC4H,WAAW,CAACT,SAAS,EAAEtE,WAAW,CAACmC,GAAG,CAAC;MAC5D;MAEAb,YAAY,CAAC,IAAI0D,IAAI,CAAC,CAAC,CAAC;MACxB,IAAI,CAAClC,UAAU,EAAE;QACfC,KAAK,CAAC,8BAA8B,CAAC;MACvC;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,IAAI,CAACE,UAAU,EAAE;QACfC,KAAK,CAAC,0BAA0B,CAAC;MACnC;IACF,CAAC,SAAS;MACRjC,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMmE,UAAU,GAAGA,CAAA,KAAM;IACvBrE,YAAY,CAAC,CAACD,SAAS,CAAC;IACxB;EACF,CAAC;EAED,MAAMuE,gBAAgB,GAAIC,KAAuC,IAAK;IAAA,IAAAC,qBAAA;IACpE,MAAMC,IAAI,GAAGF,KAAK,CAACG,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,OAAO,IAAAJ,qBAAA,GAAGD,KAAK,CAACG,aAAa,CAACG,OAAO,CAAC,KAAK,CAAC,cAAAL,qBAAA,uBAAlCA,qBAAA,CAAoCG,qBAAqB,CAAC,CAAC;IAE3E,IAAI,CAACC,OAAO,EAAE;IAEd,MAAME,CAAC,GAAGP,KAAK,CAACQ,OAAO,GAAGH,OAAO,CAACI,IAAI;IACtC,MAAMC,CAAC,GAAGV,KAAK,CAACW,OAAO,GAAGN,OAAO,CAACO,GAAG;;IAErC;IACA,MAAMC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3C,MAAMC,aAAa,GAAG,CACpB;MAAEJ,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,EAAE;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACvD;MAAEL,CAAC,EAAE,GAAG;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC;IAAE;IACxD;MAAEL,CAAC,EAAE,GAAG;MAAE3C,IAAI,EAAE,GAAe;MAAEgD,MAAM,EAAE;IAAY,CAAC,CAAE;IAAA,CACzD;;IAED;IACA,MAAMC,eAAe,GAAGF,aAAa,CAACG,MAAM,CAAC,CAACX,OAAO,EAAEY,OAAO,KAAK;MACjE,OAAOC,IAAI,CAACC,GAAG,CAACF,OAAO,CAACR,CAAC,GAAGA,CAAC,CAAC,GAAGS,IAAI,CAACC,GAAG,CAACd,OAAO,CAACI,CAAC,GAAGA,CAAC,CAAC,GAAGQ,OAAO,GAAGZ,OAAO;IAC9E,CAAC,CAAC;;IAEF;IACA,MAAMe,YAAY,GAAG,GAAG,CAAC,CAAC;IAC1B,MAAMC,MAAM,GAAG,GAAG,CAAC,CAAC;IACpB,MAAMC,SAAS,GAAGhB,CAAC,GAAGe,MAAM;IAC5B,MAAMhE,OAAO,GAAG6D,IAAI,CAACK,GAAG,CAAC,CAAC,EAAEL,IAAI,CAACM,KAAK,CAACF,SAAS,GAAGF,YAAY,CAAC,GAAG,CAAC,CAAC;IACrE,MAAMK,YAAY,GAAKH,SAAS,GAAGF,YAAY,GAAIA,YAAY,GAAI,CAAC,CAAC,CAAC;;IAEtE;IACA,MAAMM,OAAoB,GAAG;MAC3BlD,EAAE,EAAEpG,MAAM,CAAC,CAAC;MACZuJ,IAAI,EAAExG,YAAY;MAAE;MACpB2F,MAAM,EAAEC,eAAe,CAACD,MAAM;MAC9Bc,QAAQ,EAAE3G,gBAAgB;MAC1B4G,UAAU,EAAExG,kBAAkB,IAAIyG,SAAS;MAC3C/D,QAAQ,EAAE;QACRV,OAAO;QACP2B,IAAI,EAAEkC,IAAI,CAACa,KAAK,CAACN,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;QAAE;QACxCtE,KAAK,EAAE;MACT,CAAC;MACD6E,MAAM,EAAEjH,YAAY,KAAK;IAC3B,CAAC;IAEDa,cAAc,CAACqG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEP,OAAO,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMQ,SAAyB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC;EACrF,MAAM3E,KAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAE7D,IAAI1B,SAAS,EAAE;IACb,oBACEvD,OAAA,CAACC,eAAe;MAAA4J,QAAA,eACd7J,OAAA;QAAK8J,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE,OAAO;UACfC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;QACT,CAAE;QAAAP,QAAA,EAAC;MAEH;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC;EAEtB;EAEA,oBACExK,OAAA,CAACC,eAAe;IAAA4J,QAAA,gBACd7J,OAAA,CAACI,YAAY;MAAAyJ,QAAA,gBACX7J,OAAA,CAACM,WAAW;QAAAuJ,QAAA,gBACV7J,OAAA;UAAA6J,QAAA,EAAI;QAAoB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BxK,OAAA;UAAK8J,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEQ,GAAG,EAAE;UAAO,CAAE;UAAAZ,QAAA,gBACjE7J,OAAA;YACE0K,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEpI,KAAM;YACbqI,QAAQ,EAAGC,CAAC,IAAKrI,QAAQ,CAACqI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC1CI,WAAW,EAAC;UAAsB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,EACDlI,WAAW,IAAIqB,SAAS,iBACvB3D,OAAA;YAAK8J,KAAK,EAAE;cAAEK,QAAQ,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAwB,CAAE;YAAAP,QAAA,GAAC,2BACtD,EAAClG,SAAS,CAACqH,kBAAkB,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdxK,OAAA,CAACQ,aAAa;QAAAqJ,QAAA,gBACZ7J,OAAA,CAACU,YAAY;UACXuK,OAAO,EAAEA,CAAA,KAAMvH,aAAa,CAAC,CAACD,UAAU,CAAE;UAC1C5C,OAAO,EAAC,SAAS;UAAAgJ,QAAA,EAEhBpG,UAAU,GAAG,cAAc,GAAG;QAAW;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACfxK,OAAA,CAACU,YAAY;UACXuK,OAAO,EAAEA,CAAA,KAAM3H,cAAc,CAAC,EAAE,CAAE;UAClCzC,OAAO,EAAC,SAAS;UACjBqK,QAAQ,EAAE7H,WAAW,CAACY,MAAM,KAAK,CAAE;UAAA4F,QAAA,EACpC;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACfxK,OAAA,CAACU,YAAY;UAACuK,OAAO,EAAE1D,UAAW;UAAC1G,OAAO,EAAC,SAAS;UAAAgJ,QAAA,EACjD5G,SAAS,GAAG,WAAW,GAAG;QAAe;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACfxK,OAAA,CAACU,YAAY;UACXuK,OAAO,EAAE7G,UAAW;UACpBvD,OAAO,EAAC,WAAW;UACnBqK,QAAQ,EAAE,CAAC5I,WAAW,IAAIa,QAAS;UAAA0G,QAAA,EAElC1G,QAAQ,GAAG,gBAAgB,GAAG;QAAW;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEfxK,OAAA,CAACe,aAAa;MAAA8I,QAAA,gBACZ7J,OAAA,CAACiB,SAAS;QAAA4I,QAAA,GACP,CAACvH,WAAW,iBACXtC,OAAA,CAACiC,YAAY;UAAA4H,QAAA,EAAC;QAEd;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CACf,eAEDxK,OAAA,CAACmB,WAAW;UAAA0I,QAAA,gBACV7J,OAAA;YAAA6J,QAAA,EAAI;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBxK,OAAA,CAACqB,QAAQ;YAAAwI,QAAA,gBACP7J,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,MAAO;cAChCwI,OAAO,EAAEA,CAAA,KAAMvI,eAAe,CAAC,MAAM,CAAE;cAAAmH,QAAA,EACxC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxK,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,MAAO;cAChCwI,OAAO,EAAEA,CAAA,KAAMvI,eAAe,CAAC,MAAM,CAAE;cAAAmH,QAAA,EACxC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxK,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,OAAQ;cACjCwI,OAAO,EAAEA,CAAA,KAAMvI,eAAe,CAAC,OAAO,CAAE;cAAAmH,QAAA,EACzC;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdxK,OAAA,CAACmB,WAAW;UAAA0I,QAAA,gBACV7J,OAAA;YAAA6J,QAAA,EAAI;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBxK,OAAA,CAACqB,QAAQ;YAAAwI,QAAA,EACND,SAAS,CAAC5D,GAAG,CAACsD,QAAQ,iBACrBtJ,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEmB,gBAAgB,KAAK2G,QAAS;cACtC2B,OAAO,EAAEA,CAAA,KAAMrI,mBAAmB,CAAC0G,QAAQ,CAAE;cAAAO,QAAA,EAE5CP,QAAQ,KAAK,OAAO,GAAG,IAAI,GAC3BA,QAAQ,KAAK,MAAM,GAAG,MAAM,GAC5BA,QAAQ,KAAK,SAAS,GAAG,GAAG,GAC5BA,QAAQ,KAAK,QAAQ,GAAG,GAAG,GAAG;YAAG,GAP7BA,QAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQH,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdxK,OAAA,CAACmB,WAAW;UAAA0I,QAAA,gBACV7J,OAAA;YAAA6J,QAAA,EAAI;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBxK,OAAA,CAACqB,QAAQ;YAAAwI,QAAA,EACN5E,KAAK,CAACe,GAAG,CAACR,IAAI,iBACbxF,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEqB,YAAY,KAAK2C,IAAK;cAC9ByF,OAAO,EAAEA,CAAA,KAAMnI,eAAe,CAAC0C,IAAI,CAAE;cAAAqE,QAAA,EAEpCrE;YAAI,GAJAA,IAAI;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKC,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdxK,OAAA,CAACmB,WAAW;UAAA0I,QAAA,gBACV7J,OAAA;YAAA6J,QAAA,EAAI;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBxK,OAAA,CAACqB,QAAQ;YAAAwI,QAAA,gBACP7J,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEuB,kBAAkB,KAAK,OAAQ;cACvCkI,OAAO,EAAEA,CAAA,KAAMjI,qBAAqB,CAACD,kBAAkB,KAAK,OAAO,GAAG,IAAI,GAAG,OAAO,CAAE;cAAA8G,QAAA,EACvF;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxK,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEuB,kBAAkB,KAAK,MAAO;cACtCkI,OAAO,EAAEA,CAAA,KAAMjI,qBAAqB,CAACD,kBAAkB,KAAK,MAAM,GAAG,IAAI,GAAG,MAAM,CAAE;cAAA8G,QAAA,EACrF;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxK,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEuB,kBAAkB,KAAK,SAAU;cACzCkI,OAAO,EAAEA,CAAA,KAAMjI,qBAAqB,CAACD,kBAAkB,KAAK,SAAS,GAAG,IAAI,GAAG,SAAS,CAAE;cAAA8G,QAAA,EAC3F;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxK,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEuB,kBAAkB,KAAK,IAAK;cACpCkI,OAAO,EAAEA,CAAA,KAAMjI,qBAAqB,CAAC,IAAI,CAAE;cAAA6G,QAAA,EAC5C;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdxK,OAAA,CAACmB,WAAW;UAAA0I,QAAA,gBACV7J,OAAA;YAAA6J,QAAA,EAAI;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBxK,OAAA;YAAK8J,KAAK,EAAE;cAAEK,QAAQ,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAP,QAAA,gBAChD7J,OAAA;cAAA6J,QAAA,GAAK,sBAAU,EAACxG,WAAW,CAAC8H,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC1B,MAAM,CAAC,CAACzF,MAAM;YAAA;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChExK,OAAA;cAAA6J,QAAA,GAAK,uBAAW,EAACxG,WAAW,CAAC8H,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,MAAM,CAAC,CAACzF,MAAM;YAAA;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChExK,OAAA;cAAA6J,QAAA,GAAK,sBAAU,EAACxG,WAAW,CAACY,MAAM;YAAA;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEZxK,OAAA,CAAC0B,WAAW;QAAAmI,QAAA,EACTpG,UAAU,gBACTzD,OAAA,CAACN,SAAS;UAACuF,KAAK,EAAE5B,WAAY;UAACd,KAAK,EAAEA;QAAM;UAAA8H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE/CxK,OAAA,CAAC4B,cAAc;UAAAiI,QAAA,gBACb7J,OAAA;YAAI8J,KAAK,EAAE;cAAEuB,MAAM,EAAE,YAAY;cAAEjB,KAAK,EAAE;YAAU,CAAE;YAAAP,QAAA,GAAC,aAAW,EAACtH,KAAK;UAAA;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9ExK,OAAA,CAAC8B,KAAK;YAACwJ,OAAO,EAAC,aAAa;YAAAzB,QAAA,GAE3B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC7D,GAAG,CAACuF,IAAI,iBACvBvL,OAAA;cAEEwL,EAAE,EAAC,IAAI;cACPC,EAAE,EAAE,EAAE,GAAGF,IAAI,GAAG,EAAG;cACnBG,EAAE,EAAC,KAAK;cACRC,EAAE,EAAE,EAAE,GAAGJ,IAAI,GAAG,EAAG;cACnBK,MAAM,EAAC,MAAM;cACbC,WAAW,EAAC;YAAG,GANVN,IAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV,CACF,CAAC,eAGFxK,OAAA;cAAMgI,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAACgC,QAAQ,EAAC,IAAI;cAAC2B,IAAI,EAAC,MAAM;cAAAjC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGvDxK,OAAA;cAAMgI,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAACgC,QAAQ,EAAC,IAAI;cAAC2B,IAAI,EAAC,MAAM;cAAAjC,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDxK,OAAA;cAAMgI,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAACgC,QAAQ,EAAC,IAAI;cAAC2B,IAAI,EAAC,MAAM;cAAAjC,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGtDxK,OAAA;cAAMwL,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,KAAK;cAACC,MAAM,EAAC,MAAM;cAACC,WAAW,EAAC;YAAG;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,EAGvE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACxE,GAAG,CAACjB,OAAO,iBACvB/E,OAAA;cAEEwL,EAAE,EAAE,GAAG,GAAIzG,OAAO,GAAG,GAAK;cAC1B0G,EAAE,EAAC,IAAI;cACPC,EAAE,EAAE,GAAG,GAAI3G,OAAO,GAAG,GAAK;cAC1B4G,EAAE,EAAC,KAAK;cACRC,MAAM,EAAC,MAAM;cACbC,WAAW,EAAC;YAAG,GANV9G,OAAO;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOb,CACF,CAAC,EAGD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACxE,GAAG,CAACjB,OAAO,iBACvB/E,OAAA;cAEEgI,CAAC,EAAE,GAAG,GAAI,CAACjD,OAAO,GAAG,CAAC,IAAI,GAAI,GAAG,EAAG;cACpCoD,CAAC,EAAC,IAAI;cACNgC,QAAQ,EAAC,IAAI;cACb2B,IAAI,EAAC,MAAM;cACXC,UAAU,EAAC,QAAQ;cAAAlC,QAAA,EAElB9E;YAAO,GAPHA,OAAO;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQR,CACP,CAAC,eAGFxK,OAAA;cACEgI,CAAC,EAAC,KAAK;cACPG,CAAC,EAAC,IAAI;cACN6D,KAAK,EAAC,KAAK;cACX9B,MAAM,EAAC,KAAK;cACZ4B,IAAI,EAAC,aAAa;cAClBhC,KAAK,EAAE;gBAAEmC,MAAM,EAAE;cAAY,CAAE;cAC/BhB,OAAO,EAAEzD;YAAiB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,EAGDnH,WAAW,CAAC2C,GAAG,CAAER,IAAI,IAAK;cACzB,MAAMwC,CAAC,GAAG,GAAG,GAAI,CAACxC,IAAI,CAACC,QAAQ,CAACV,OAAO,GAAG,CAAC,IAAI,GAAI,GAAIS,IAAI,CAACC,QAAQ,CAACiB,IAAI,GAAG,EAAG;cAC/E,MAAM6B,aAAwC,GAAG;gBAC/C,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAChD,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE;cAC9D,CAAC;cACD,MAAMJ,CAAC,GAAGI,aAAa,CAAC,GAAG/C,IAAI,CAAC6D,IAAI,GAAG7D,IAAI,CAACgD,MAAM,EAAE,CAAC,IAAI,EAAE;cAE3D,oBACExI,OAAA,CAACJ,oBAAoB;gBAEnB4F,IAAI,EAAEA,IAAK;gBACXwC,CAAC,EAAEA,CAAE;gBACLG,CAAC,EAAEA,CAAE;gBACL+D,QAAQ,EAAEA,CAAA,KAAM5I,cAAc,CAACqG,IAAI,IAAIA,IAAI,CAACwB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClF,EAAE,KAAKV,IAAI,CAACU,EAAE,CAAC;cAAE,GAJtEV,IAAI,CAACU,EAAE;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKb,CAAC;YAEN,CAAC,CAAC,EAGDnH,WAAW,CAACY,MAAM,KAAK,CAAC,iBACvBjE,OAAA;cAAMgI,CAAC,EAAC,KAAK;cAACG,CAAC,EAAC,KAAK;cAACgC,QAAQ,EAAC,IAAI;cAAC2B,IAAI,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAAlC,QAAA,EAAC;YAEpE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MACf;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB,CAAC;AAACnI,EAAA,CA1cWF,WAAuC;EAAA,QAC1B3C,OAAO;AAAA;AAAA2M,IAAA,GADpBhK,WAAuC;AAAA,IAAAhC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAiK,IAAA;AAAAC,YAAA,CAAAjM,EAAA;AAAAiM,YAAA,CAAA/L,GAAA;AAAA+L,YAAA,CAAA7L,GAAA;AAAA6L,YAAA,CAAA3L,GAAA;AAAA2L,YAAA,CAAAtL,GAAA;AAAAsL,YAAA,CAAApL,GAAA;AAAAoL,YAAA,CAAAlL,GAAA;AAAAkL,YAAA,CAAAhL,GAAA;AAAAgL,YAAA,CAAA9K,GAAA;AAAA8K,YAAA,CAAA3K,GAAA;AAAA2K,YAAA,CAAAzK,GAAA;AAAAyK,YAAA,CAAAvK,IAAA;AAAAuK,YAAA,CAAApK,IAAA;AAAAoK,YAAA,CAAAlK,IAAA;AAAAkK,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}