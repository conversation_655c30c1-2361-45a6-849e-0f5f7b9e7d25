{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ProfessionalScoreEditor\\\\ProfessionalScoreEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport styled from 'styled-components';\nimport { ProfessionalScore } from '../ProfessionalScore/ProfessionalScore';\nimport { OrchestralSelector } from '../OrchestralSelector/OrchestralSelector';\nimport { AdvancedLyricsEditor } from '../AdvancedLyricsEditor/AdvancedLyricsEditor';\nimport { v4 as uuidv4 } from 'uuid';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditorContainer = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 2rem;\n`;\n_c = EditorContainer;\nconst EditorWrapper = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  background: rgba(255,255,255,0.95);\n  border-radius: 24px;\n  overflow: hidden;\n  box-shadow: 0 20px 60px rgba(0,0,0,0.2);\n`;\n_c2 = EditorWrapper;\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\n  color: white;\n  padding: 2rem;\n  text-align: center;\n`;\n_c3 = EditorHeader;\nconst EditorTitle = styled.h1`\n  margin: 0 0 0.5rem 0;\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-shadow: 0 2px 4px rgba(0,0,0,0.3);\n`;\n_c4 = EditorTitle;\nconst EditorSubtitle = styled.p`\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n`;\n_c5 = EditorSubtitle;\nconst EditorContent = styled.div`\n  padding: 2rem;\n`;\n_c6 = EditorContent;\nconst ControlsSection = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 2rem;\n  margin-bottom: 2rem;\n`;\n_c7 = ControlsSection;\nconst ScoreSection = styled.div`\n  margin-bottom: 2rem;\n`;\n_c8 = ScoreSection;\nconst PlaybackControls = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 2rem;\n  margin: 2rem 0;\n  padding: 1.5rem;\n  background: #f8f9fa;\n  border-radius: 16px;\n`;\n_c9 = PlaybackControls;\nconst PlayButton = styled.button`\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 50px;\n  font-size: 1.2rem;\n  font-weight: 700;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.isPlaying ? `\n    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\n    color: white;\n    &:hover { \n      background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);\n      transform: translateY(-3px);\n      box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);\n    }\n  ` : `\n    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\n    color: white;\n    &:hover { \n      background: linear-gradient(135deg, #229954 0%, #27ae60 100%);\n      transform: translateY(-3px);\n      box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);\n    }\n  `}\n`;\n_c0 = PlayButton;\nconst TempoControl = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  \n  label {\n    font-weight: 700;\n    color: #2c3e50;\n    font-size: 1.1rem;\n  }\n  \n  input {\n    width: 100px;\n    padding: 0.75rem;\n    border: 2px solid #e9ecef;\n    border-radius: 12px;\n    text-align: center;\n    font-size: 1.1rem;\n    font-weight: 600;\n    \n    &:focus {\n      outline: none;\n      border-color: #667eea;\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n    }\n  }\n`;\n_c1 = TempoControl;\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-top: 2rem;\n`;\n_c10 = ActionButtons;\nconst ActionButton = styled.button`\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 50px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => {\n  switch (props.variant) {\n    case 'primary':\n      return `\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          &:hover {\n            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n            transform: translateY(-2px);\n            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n          }\n        `;\n    case 'danger':\n      return `\n          background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\n          color: white;\n          &:hover {\n            background: linear-gradient(135deg, #d62c1a 0%, #a93226 100%);\n            transform: translateY(-2px);\n            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);\n          }\n        `;\n    default:\n      return `\n          background: #f8f9fa;\n          color: #495057;\n          border: 2px solid #e9ecef;\n          &:hover {\n            background: #e9ecef;\n            transform: translateY(-2px);\n          }\n        `;\n  }\n}}\n`;\n_c11 = ActionButton;\nexport const ProfessionalScoreEditor = ({\n  initialTitle = \"Nova Partitura\",\n  initialComposer = \"\"\n}) => {\n  _s();\n  const [title, setTitle] = useState(initialTitle);\n  const [composer, setComposer] = useState(initialComposer);\n  const [selectedInstruments, setSelectedInstruments] = useState(['piano']);\n  const [notes, setNotes] = useState([]);\n  const [lyrics, setLyrics] = useState([]);\n  const [tempo, setTempo] = useState(120);\n  const [isPlaying, setIsPlaying] = useState(false);\n  const handleInstrumentAdd = useCallback(instrumentId => {\n    setSelectedInstruments(prev => [...prev, instrumentId]);\n  }, []);\n  const handleInstrumentRemove = useCallback(instrumentId => {\n    setSelectedInstruments(prev => prev.filter(id => id !== instrumentId));\n  }, []);\n  const handleLyricsAdd = useCallback(lyric => {\n    setLyrics(prev => [...prev, lyric]);\n  }, []);\n  const handleLyricsRemove = useCallback(index => {\n    setLyrics(prev => prev.filter((_, i) => i !== index));\n  }, []);\n  const handleLyricsClear = useCallback(() => {\n    setLyrics([]);\n  }, []);\n  const handleNoteClick = useCallback((x, y, staffIndex) => {\n    // Calcular posição da nota baseada no clique\n    const measureWidth = 200;\n    const startX = 50;\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = relativeX % measureWidth / measureWidth * 4;\n\n    // Calcular altura da nota (simplificado)\n    const staffHeight = 120;\n    const lineSpacing = 10;\n    const staffTop = 40 + staffIndex * 140;\n    const relativeY = y - staffTop;\n    const lineIndex = Math.round(relativeY / lineSpacing);\n\n    // Mapear para notas (clave de sol)\n    const noteMap = ['F', 'E', 'D', 'C', 'B', 'A', 'G', 'F', 'E'];\n    const octaveMap = [5, 5, 5, 5, 4, 4, 4, 4, 4];\n    const noteIndex = Math.max(0, Math.min(noteMap.length - 1, lineIndex + 4));\n    const newNote = {\n      id: uuidv4(),\n      name: noteMap[noteIndex],\n      octave: octaveMap[noteIndex],\n      duration: 'quarter',\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4,\n        staff: staffIndex\n      },\n      isRest: false\n    };\n    setNotes(prev => [...prev, newNote]);\n  }, []);\n  const handlePlay = useCallback(() => {\n    setIsPlaying(!isPlaying);\n  }, [isPlaying]);\n  const handleClearAll = useCallback(() => {\n    if (window.confirm('Tem certeza que deseja limpar toda a partitura?')) {\n      setNotes([]);\n      setLyrics([]);\n    }\n  }, []);\n  const handleExport = useCallback(() => {\n    // TODO: Implementar exportação\n    alert('Funcionalidade de exportação em desenvolvimento!');\n  }, []);\n  return /*#__PURE__*/_jsxDEV(EditorContainer, {\n    children: /*#__PURE__*/_jsxDEV(EditorWrapper, {\n      children: [/*#__PURE__*/_jsxDEV(EditorHeader, {\n        children: [/*#__PURE__*/_jsxDEV(EditorTitle, {\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(EditorSubtitle, {\n          children: [composer && `por ${composer}`, selectedInstruments.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\" \\u2022 \", selectedInstruments.length, \" instrumento\", selectedInstruments.length > 1 ? 's' : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorContent, {\n        children: [/*#__PURE__*/_jsxDEV(ControlsSection, {\n          children: [/*#__PURE__*/_jsxDEV(OrchestralSelector, {\n            selectedInstruments: selectedInstruments,\n            onInstrumentAdd: handleInstrumentAdd,\n            onInstrumentRemove: handleInstrumentRemove\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AdvancedLyricsEditor, {\n            lyrics: lyrics,\n            onLyricsAdd: handleLyricsAdd,\n            onLyricsRemove: handleLyricsRemove,\n            onLyricsClear: handleLyricsClear\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ScoreSection, {\n          children: /*#__PURE__*/_jsxDEV(ProfessionalScore, {\n            title: title,\n            composer: composer,\n            notes: notes,\n            lyrics: lyrics,\n            instruments: selectedInstruments,\n            tempo: tempo,\n            onTempoChange: setTempo,\n            onNoteClick: handleNoteClick\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PlaybackControls, {\n          children: [/*#__PURE__*/_jsxDEV(PlayButton, {\n            isPlaying: isPlaying,\n            onClick: handlePlay,\n            children: isPlaying ? '⏸️ Pausar Reprodução' : '▶️ Reproduzir Partitura'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TempoControl, {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Andamento:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              min: \"60\",\n              max: \"200\",\n              value: tempo,\n              onChange: e => setTempo(parseInt(e.target.value) || 120)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"BPM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButtons, {\n          children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n            onClick: handleExport,\n            variant: \"primary\",\n            children: \"\\uD83D\\uDCC4 Exportar PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n            onClick: () => alert('Salvar em desenvolvimento!'),\n            children: \"\\uD83D\\uDCBE Salvar Projeto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n            onClick: handleClearAll,\n            variant: \"danger\",\n            children: \"\\uD83D\\uDDD1\\uFE0F Limpar Tudo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 269,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfessionalScoreEditor, \"iSOJj7KEVjQ++J5xis0lGcPPZZg=\");\n_c12 = ProfessionalScoreEditor;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"EditorContainer\");\n$RefreshReg$(_c2, \"EditorWrapper\");\n$RefreshReg$(_c3, \"EditorHeader\");\n$RefreshReg$(_c4, \"EditorTitle\");\n$RefreshReg$(_c5, \"EditorSubtitle\");\n$RefreshReg$(_c6, \"EditorContent\");\n$RefreshReg$(_c7, \"ControlsSection\");\n$RefreshReg$(_c8, \"ScoreSection\");\n$RefreshReg$(_c9, \"PlaybackControls\");\n$RefreshReg$(_c0, \"PlayButton\");\n$RefreshReg$(_c1, \"TempoControl\");\n$RefreshReg$(_c10, \"ActionButtons\");\n$RefreshReg$(_c11, \"ActionButton\");\n$RefreshReg$(_c12, \"ProfessionalScoreEditor\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "styled", "ProfessionalScore", "OrchestralSelector", "AdvancedLyricsEditor", "v4", "uuidv4", "jsxDEV", "_jsxDEV", "EditorC<PERSON><PERSON>", "div", "_c", "EditorWrapper", "_c2", "Editor<PERSON><PERSON>er", "_c3", "EditorT<PERSON>le", "h1", "_c4", "EditorSubtitle", "p", "_c5", "Editor<PERSON><PERSON><PERSON>", "_c6", "ControlsSection", "_c7", "ScoreSection", "_c8", "PlaybackControls", "_c9", "PlayButton", "button", "props", "isPlaying", "_c0", "TempoControl", "_c1", "ActionButtons", "_c10", "ActionButton", "variant", "_c11", "ProfessionalScoreEditor", "initialTitle", "initialComposer", "_s", "title", "setTitle", "composer", "setComposer", "selectedInstruments", "setSelectedInstruments", "notes", "setNotes", "lyrics", "setLyrics", "tempo", "set<PERSON><PERSON><PERSON>", "setIsPlaying", "handleInstrumentAdd", "instrumentId", "prev", "handleInstrumentRemove", "filter", "id", "handleLyricsAdd", "lyric", "handleLyricsRemove", "index", "_", "i", "handleLyricsClear", "handleNoteClick", "x", "y", "staffIndex", "measureWidth", "startX", "relativeX", "measure", "Math", "max", "floor", "beatPosition", "staffHeight", "lineSpacing", "staffTop", "relativeY", "lineIndex", "round", "noteMap", "octaveMap", "noteIndex", "min", "length", "newNote", "name", "octave", "duration", "position", "beat", "staff", "isRest", "handlePlay", "handleClearAll", "window", "confirm", "handleExport", "alert", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onInstrumentAdd", "onInstrumentRemove", "onLyricsAdd", "onLyricsRemove", "onLyricsClear", "instruments", "onTempoChange", "onNoteClick", "onClick", "type", "value", "onChange", "e", "parseInt", "target", "_c12", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ProfessionalScoreEditor/ProfessionalScoreEditor.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport styled from 'styled-components';\nimport { MusicalNote, Lyrics, InstrumentType } from '../../types/music';\nimport { ProfessionalScore } from '../ProfessionalScore/ProfessionalScore';\nimport { OrchestralSelector } from '../OrchestralSelector/OrchestralSelector';\nimport { AdvancedLyricsEditor } from '../AdvancedLyricsEditor/AdvancedLyricsEditor';\nimport { getOrchestralInstrument } from '../../utils/orchestralInstruments';\nimport { v4 as uuidv4 } from 'uuid';\n\nconst EditorContainer = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 2rem;\n`;\n\nconst EditorWrapper = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  background: rgba(255,255,255,0.95);\n  border-radius: 24px;\n  overflow: hidden;\n  box-shadow: 0 20px 60px rgba(0,0,0,0.2);\n`;\n\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\n  color: white;\n  padding: 2rem;\n  text-align: center;\n`;\n\nconst EditorTitle = styled.h1`\n  margin: 0 0 0.5rem 0;\n  font-size: 2.5rem;\n  font-weight: 700;\n  text-shadow: 0 2px 4px rgba(0,0,0,0.3);\n`;\n\nconst EditorSubtitle = styled.p`\n  margin: 0;\n  font-size: 1.2rem;\n  opacity: 0.9;\n`;\n\nconst EditorContent = styled.div`\n  padding: 2rem;\n`;\n\nconst ControlsSection = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 2rem;\n  margin-bottom: 2rem;\n`;\n\nconst ScoreSection = styled.div`\n  margin-bottom: 2rem;\n`;\n\nconst PlaybackControls = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 2rem;\n  margin: 2rem 0;\n  padding: 1.5rem;\n  background: #f8f9fa;\n  border-radius: 16px;\n`;\n\nconst PlayButton = styled.button<{ isPlaying?: boolean }>`\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 50px;\n  font-size: 1.2rem;\n  font-weight: 700;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => props.isPlaying ? `\n    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\n    color: white;\n    &:hover { \n      background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);\n      transform: translateY(-3px);\n      box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);\n    }\n  ` : `\n    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);\n    color: white;\n    &:hover { \n      background: linear-gradient(135deg, #229954 0%, #27ae60 100%);\n      transform: translateY(-3px);\n      box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);\n    }\n  `}\n`;\n\nconst TempoControl = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  \n  label {\n    font-weight: 700;\n    color: #2c3e50;\n    font-size: 1.1rem;\n  }\n  \n  input {\n    width: 100px;\n    padding: 0.75rem;\n    border: 2px solid #e9ecef;\n    border-radius: 12px;\n    text-align: center;\n    font-size: 1.1rem;\n    font-weight: 600;\n    \n    &:focus {\n      outline: none;\n      border-color: #667eea;\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n    }\n  }\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-top: 2rem;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' | 'danger' }>`\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 50px;\n  font-size: 1.1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  \n  ${props => {\n    switch(props.variant) {\n      case 'primary':\n        return `\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          color: white;\n          &:hover {\n            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);\n            transform: translateY(-2px);\n            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n          }\n        `;\n      case 'danger':\n        return `\n          background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\n          color: white;\n          &:hover {\n            background: linear-gradient(135deg, #d62c1a 0%, #a93226 100%);\n            transform: translateY(-2px);\n            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);\n          }\n        `;\n      default:\n        return `\n          background: #f8f9fa;\n          color: #495057;\n          border: 2px solid #e9ecef;\n          &:hover {\n            background: #e9ecef;\n            transform: translateY(-2px);\n          }\n        `;\n    }\n  }}\n`;\n\ninterface ProfessionalScoreEditorProps {\n  initialTitle?: string;\n  initialComposer?: string;\n}\n\nexport const ProfessionalScoreEditor: React.FC<ProfessionalScoreEditorProps> = ({\n  initialTitle = \"Nova Partitura\",\n  initialComposer = \"\"\n}) => {\n  const [title, setTitle] = useState(initialTitle);\n  const [composer, setComposer] = useState(initialComposer);\n  const [selectedInstruments, setSelectedInstruments] = useState<string[]>(['piano']);\n  const [notes, setNotes] = useState<MusicalNote[]>([]);\n  const [lyrics, setLyrics] = useState<Lyrics[]>([]);\n  const [tempo, setTempo] = useState(120);\n  const [isPlaying, setIsPlaying] = useState(false);\n\n  const handleInstrumentAdd = useCallback((instrumentId: string) => {\n    setSelectedInstruments(prev => [...prev, instrumentId]);\n  }, []);\n\n  const handleInstrumentRemove = useCallback((instrumentId: string) => {\n    setSelectedInstruments(prev => prev.filter(id => id !== instrumentId));\n  }, []);\n\n  const handleLyricsAdd = useCallback((lyric: Lyrics) => {\n    setLyrics(prev => [...prev, lyric]);\n  }, []);\n\n  const handleLyricsRemove = useCallback((index: number) => {\n    setLyrics(prev => prev.filter((_, i) => i !== index));\n  }, []);\n\n  const handleLyricsClear = useCallback(() => {\n    setLyrics([]);\n  }, []);\n\n  const handleNoteClick = useCallback((x: number, y: number, staffIndex: number) => {\n    // Calcular posição da nota baseada no clique\n    const measureWidth = 200;\n    const startX = 50;\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = ((relativeX % measureWidth) / measureWidth) * 4;\n\n    // Calcular altura da nota (simplificado)\n    const staffHeight = 120;\n    const lineSpacing = 10;\n    const staffTop = 40 + (staffIndex * 140);\n    const relativeY = y - staffTop;\n    const lineIndex = Math.round(relativeY / lineSpacing);\n    \n    // Mapear para notas (clave de sol)\n    const noteMap = ['F', 'E', 'D', 'C', 'B', 'A', 'G', 'F', 'E'];\n    const octaveMap = [5, 5, 5, 5, 4, 4, 4, 4, 4];\n    const noteIndex = Math.max(0, Math.min(noteMap.length - 1, lineIndex + 4));\n\n    const newNote: MusicalNote = {\n      id: uuidv4(),\n      name: noteMap[noteIndex] as any,\n      octave: octaveMap[noteIndex] as any,\n      duration: 'quarter',\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4,\n        staff: staffIndex\n      },\n      isRest: false\n    };\n\n    setNotes(prev => [...prev, newNote]);\n  }, []);\n\n  const handlePlay = useCallback(() => {\n    setIsPlaying(!isPlaying);\n  }, [isPlaying]);\n\n  const handleClearAll = useCallback(() => {\n    if (window.confirm('Tem certeza que deseja limpar toda a partitura?')) {\n      setNotes([]);\n      setLyrics([]);\n    }\n  }, []);\n\n  const handleExport = useCallback(() => {\n    // TODO: Implementar exportação\n    alert('Funcionalidade de exportação em desenvolvimento!');\n  }, []);\n\n  return (\n    <EditorContainer>\n      <EditorWrapper>\n        <EditorHeader>\n          <EditorTitle>{title}</EditorTitle>\n          <EditorSubtitle>\n            {composer && `por ${composer}`}\n            {selectedInstruments.length > 0 && (\n              <span> • {selectedInstruments.length} instrumento{selectedInstruments.length > 1 ? 's' : ''}</span>\n            )}\n          </EditorSubtitle>\n        </EditorHeader>\n\n        <EditorContent>\n          <ControlsSection>\n            <OrchestralSelector\n              selectedInstruments={selectedInstruments}\n              onInstrumentAdd={handleInstrumentAdd}\n              onInstrumentRemove={handleInstrumentRemove}\n            />\n            \n            <AdvancedLyricsEditor\n              lyrics={lyrics}\n              onLyricsAdd={handleLyricsAdd}\n              onLyricsRemove={handleLyricsRemove}\n              onLyricsClear={handleLyricsClear}\n            />\n          </ControlsSection>\n\n          <ScoreSection>\n            <ProfessionalScore\n              title={title}\n              composer={composer}\n              notes={notes}\n              lyrics={lyrics}\n              instruments={selectedInstruments as any[]}\n              tempo={tempo}\n              onTempoChange={setTempo}\n              onNoteClick={handleNoteClick}\n            />\n          </ScoreSection>\n\n          <PlaybackControls>\n            <PlayButton isPlaying={isPlaying} onClick={handlePlay}>\n              {isPlaying ? '⏸️ Pausar Reprodução' : '▶️ Reproduzir Partitura'}\n            </PlayButton>\n            \n            <TempoControl>\n              <label>Andamento:</label>\n              <input\n                type=\"number\"\n                min=\"60\"\n                max=\"200\"\n                value={tempo}\n                onChange={(e) => setTempo(parseInt(e.target.value) || 120)}\n              />\n              <span>BPM</span>\n            </TempoControl>\n          </PlaybackControls>\n\n          <ActionButtons>\n            <ActionButton onClick={handleExport} variant=\"primary\">\n              📄 Exportar PDF\n            </ActionButton>\n            <ActionButton onClick={() => alert('Salvar em desenvolvimento!')}>\n              💾 Salvar Projeto\n            </ActionButton>\n            <ActionButton onClick={handleClearAll} variant=\"danger\">\n              🗑️ Limpar Tudo\n            </ActionButton>\n          </ActionButtons>\n        </EditorContent>\n      </EditorWrapper>\n    </EditorContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,OAAOC,MAAM,MAAM,mBAAmB;AAEtC,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,oBAAoB,QAAQ,8CAA8C;AAEnF,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGR,MAAM,CAACS,GAAG;AAClC;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAJIF,eAAe;AAMrB,MAAMG,aAAa,GAAGX,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,aAAa;AASnB,MAAME,YAAY,GAAGb,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GALID,YAAY;AAOlB,MAAME,WAAW,GAAGf,MAAM,CAACgB,EAAE;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,WAAW;AAOjB,MAAMG,cAAc,GAAGlB,MAAM,CAACmB,CAAC;AAC/B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,cAAc;AAMpB,MAAMG,aAAa,GAAGrB,MAAM,CAACS,GAAG;AAChC;AACA,CAAC;AAACa,GAAA,GAFID,aAAa;AAInB,MAAME,eAAe,GAAGvB,MAAM,CAACS,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GALID,eAAe;AAOrB,MAAME,YAAY,GAAGzB,MAAM,CAACS,GAAG;AAC/B;AACA,CAAC;AAACiB,GAAA,GAFID,YAAY;AAIlB,MAAME,gBAAgB,GAAG3B,MAAM,CAACS,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GATID,gBAAgB;AAWtB,MAAME,UAAU,GAAG7B,MAAM,CAAC8B,MAA+B;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,CAAC;AAACC,GAAA,GA1BIJ,UAAU;AA4BhB,MAAMK,YAAY,GAAGlC,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,GAAA,GA1BID,YAAY;AA4BlB,MAAME,aAAa,GAAGpC,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAAC4B,IAAA,GALID,aAAa;AAOnB,MAAME,YAAY,GAAGtC,MAAM,CAAC8B,MAAwD;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAI;EACT,QAAOA,KAAK,CAACQ,OAAO;IAClB,KAAK,SAAS;MACZ,OAAO;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;IACH,KAAK,QAAQ;MACX,OAAO;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;IACH;MACE,OAAO;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;EACL;AACF,CAAC;AACH,CAAC;AAACC,IAAA,GA3CIF,YAAY;AAkDlB,OAAO,MAAMG,uBAA+D,GAAGA,CAAC;EAC9EC,YAAY,GAAG,gBAAgB;EAC/BC,eAAe,GAAG;AACpB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC4C,YAAY,CAAC;EAChD,MAAM,CAACK,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC6C,eAAe,CAAC;EACzD,MAAM,CAACM,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpD,QAAQ,CAAW,CAAC,OAAO,CAAC,CAAC;EACnF,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAgB,EAAE,CAAC;EACrD,MAAM,CAACuD,MAAM,EAAEC,SAAS,CAAC,GAAGxD,QAAQ,CAAW,EAAE,CAAC;EAClD,MAAM,CAACyD,KAAK,EAAEC,QAAQ,CAAC,GAAG1D,QAAQ,CAAC,GAAG,CAAC;EACvC,MAAM,CAACkC,SAAS,EAAEyB,YAAY,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM4D,mBAAmB,GAAG3D,WAAW,CAAE4D,YAAoB,IAAK;IAChET,sBAAsB,CAACU,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,YAAY,CAAC,CAAC;EACzD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,sBAAsB,GAAG9D,WAAW,CAAE4D,YAAoB,IAAK;IACnET,sBAAsB,CAACU,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKJ,YAAY,CAAC,CAAC;EACxE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAGjE,WAAW,CAAEkE,KAAa,IAAK;IACrDX,SAAS,CAACM,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEK,KAAK,CAAC,CAAC;EACrC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,kBAAkB,GAAGnE,WAAW,CAAEoE,KAAa,IAAK;IACxDb,SAAS,CAACM,IAAI,IAAIA,IAAI,CAACE,MAAM,CAAC,CAACM,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKF,KAAK,CAAC,CAAC;EACvD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,iBAAiB,GAAGvE,WAAW,CAAC,MAAM;IAC1CuD,SAAS,CAAC,EAAE,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiB,eAAe,GAAGxE,WAAW,CAAC,CAACyE,CAAS,EAAEC,CAAS,EAAEC,UAAkB,KAAK;IAChF;IACA,MAAMC,YAAY,GAAG,GAAG;IACxB,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAMC,SAAS,GAAGL,CAAC,GAAGI,MAAM;IAC5B,MAAME,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,KAAK,CAACJ,SAAS,GAAGF,YAAY,CAAC,GAAG,CAAC,CAAC;IACrE,MAAMO,YAAY,GAAKL,SAAS,GAAGF,YAAY,GAAIA,YAAY,GAAI,CAAC;;IAEpE;IACA,MAAMQ,WAAW,GAAG,GAAG;IACvB,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMC,QAAQ,GAAG,EAAE,GAAIX,UAAU,GAAG,GAAI;IACxC,MAAMY,SAAS,GAAGb,CAAC,GAAGY,QAAQ;IAC9B,MAAME,SAAS,GAAGR,IAAI,CAACS,KAAK,CAACF,SAAS,GAAGF,WAAW,CAAC;;IAErD;IACA,MAAMK,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC7D,MAAMC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC7C,MAAMC,SAAS,GAAGZ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACa,GAAG,CAACH,OAAO,CAACI,MAAM,GAAG,CAAC,EAAEN,SAAS,GAAG,CAAC,CAAC,CAAC;IAE1E,MAAMO,OAAoB,GAAG;MAC3B/B,EAAE,EAAE1D,MAAM,CAAC,CAAC;MACZ0F,IAAI,EAAEN,OAAO,CAACE,SAAS,CAAQ;MAC/BK,MAAM,EAAEN,SAAS,CAACC,SAAS,CAAQ;MACnCM,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE;QACRpB,OAAO;QACPqB,IAAI,EAAEpB,IAAI,CAACS,KAAK,CAACN,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;QACtCkB,KAAK,EAAE1B;MACT,CAAC;MACD2B,MAAM,EAAE;IACV,CAAC;IAEDjD,QAAQ,CAACQ,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEkC,OAAO,CAAC,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,UAAU,GAAGvG,WAAW,CAAC,MAAM;IACnC0D,YAAY,CAAC,CAACzB,SAAS,CAAC;EAC1B,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEf,MAAMuE,cAAc,GAAGxG,WAAW,CAAC,MAAM;IACvC,IAAIyG,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACrErD,QAAQ,CAAC,EAAE,CAAC;MACZE,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoD,YAAY,GAAG3G,WAAW,CAAC,MAAM;IACrC;IACA4G,KAAK,CAAC,kDAAkD,CAAC;EAC3D,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEpG,OAAA,CAACC,eAAe;IAAAoG,QAAA,eACdrG,OAAA,CAACI,aAAa;MAAAiG,QAAA,gBACZrG,OAAA,CAACM,YAAY;QAAA+F,QAAA,gBACXrG,OAAA,CAACQ,WAAW;UAAA6F,QAAA,EAAE/D;QAAK;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAClCzG,OAAA,CAACW,cAAc;UAAA0F,QAAA,GACZ7D,QAAQ,IAAI,OAAOA,QAAQ,EAAE,EAC7BE,mBAAmB,CAAC4C,MAAM,GAAG,CAAC,iBAC7BtF,OAAA;YAAAqG,QAAA,GAAM,UAAG,EAAC3D,mBAAmB,CAAC4C,MAAM,EAAC,cAAY,EAAC5C,mBAAmB,CAAC4C,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;UAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACnG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEfzG,OAAA,CAACc,aAAa;QAAAuF,QAAA,gBACZrG,OAAA,CAACgB,eAAe;UAAAqF,QAAA,gBACdrG,OAAA,CAACL,kBAAkB;YACjB+C,mBAAmB,EAAEA,mBAAoB;YACzCgE,eAAe,EAAEvD,mBAAoB;YACrCwD,kBAAkB,EAAErD;UAAuB;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAEFzG,OAAA,CAACJ,oBAAoB;YACnBkD,MAAM,EAAEA,MAAO;YACf8D,WAAW,EAAEnD,eAAgB;YAC7BoD,cAAc,EAAElD,kBAAmB;YACnCmD,aAAa,EAAE/C;UAAkB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa,CAAC,eAElBzG,OAAA,CAACkB,YAAY;UAAAmF,QAAA,eACXrG,OAAA,CAACN,iBAAiB;YAChB4C,KAAK,EAAEA,KAAM;YACbE,QAAQ,EAAEA,QAAS;YACnBI,KAAK,EAAEA,KAAM;YACbE,MAAM,EAAEA,MAAO;YACfiE,WAAW,EAAErE,mBAA6B;YAC1CM,KAAK,EAAEA,KAAM;YACbgE,aAAa,EAAE/D,QAAS;YACxBgE,WAAW,EAAEjD;UAAgB;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eAEfzG,OAAA,CAACoB,gBAAgB;UAAAiF,QAAA,gBACfrG,OAAA,CAACsB,UAAU;YAACG,SAAS,EAAEA,SAAU;YAACyF,OAAO,EAAEnB,UAAW;YAAAM,QAAA,EACnD5E,SAAS,GAAG,sBAAsB,GAAG;UAAyB;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eAEbzG,OAAA,CAAC2B,YAAY;YAAA0E,QAAA,gBACXrG,OAAA;cAAAqG,QAAA,EAAO;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzBzG,OAAA;cACEmH,IAAI,EAAC,QAAQ;cACb9B,GAAG,EAAC,IAAI;cACRZ,GAAG,EAAC,KAAK;cACT2C,KAAK,EAAEpE,KAAM;cACbqE,QAAQ,EAAGC,CAAC,IAAKrE,QAAQ,CAACsE,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,IAAI,GAAG;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACFzG,OAAA;cAAAqG,QAAA,EAAM;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEnBzG,OAAA,CAAC6B,aAAa;UAAAwE,QAAA,gBACZrG,OAAA,CAAC+B,YAAY;YAACmF,OAAO,EAAEf,YAAa;YAACnE,OAAO,EAAC,SAAS;YAAAqE,QAAA,EAAC;UAEvD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfzG,OAAA,CAAC+B,YAAY;YAACmF,OAAO,EAAEA,CAAA,KAAMd,KAAK,CAAC,4BAA4B,CAAE;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACfzG,OAAA,CAAC+B,YAAY;YAACmF,OAAO,EAAElB,cAAe;YAAChE,OAAO,EAAC,QAAQ;YAAAqE,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB,CAAC;AAACpE,EAAA,CA/JWH,uBAA+D;AAAAuF,IAAA,GAA/DvF,uBAA+D;AAAA,IAAA/B,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAwF,IAAA;AAAAC,YAAA,CAAAvH,EAAA;AAAAuH,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAAnH,GAAA;AAAAmH,YAAA,CAAAhH,GAAA;AAAAgH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,IAAA;AAAA4F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}