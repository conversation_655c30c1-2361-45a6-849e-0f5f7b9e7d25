{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ScoreEditor\\\\ScoreEditor.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { ScoreService } from '../../services/scoreService';\nimport { v4 as uuidv4 } from 'uuid';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n_c = EditorContainer;\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c2 = EditorHeader;\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n_c3 = EditorTitle;\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n_c4 = EditorActions;\nconst ActionButton = styled.button`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n_c5 = ActionButton;\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n_c6 = EditorContent;\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n_c7 = ToolPanel;\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n_c8 = ToolSection;\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n_c9 = ToolGrid;\nconst ToolButton = styled.button`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n_c0 = ToolButton;\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n_c1 = ScoreCanvas;\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n_c10 = StaffContainer;\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n_c11 = Staff;\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n_c12 = GuestWarning;\nexport const ScoreEditor = ({\n  scoreId\n}) => {\n  _s();\n  const {\n    currentUser\n  } = useAuth();\n  const [title, setTitle] = useState('Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState('note');\n  const [selectedDuration, setSelectedDuration] = useState('quarter');\n  const [selectedNote, setSelectedNote] = useState('C');\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [placedNotes, setPlacedNotes] = useState([]);\n  const handleSave = async () => {\n    if (!currentUser) {\n      alert('Você precisa estar logado para salvar partituras!');\n      return;\n    }\n    setIsSaving(true);\n    try {\n      // Criar dados básicos da partitura\n      const scoreData = {\n        title,\n        key: {\n          note: 'C',\n          mode: 'major'\n        },\n        timeSignature: {\n          numerator: 4,\n          denominator: 4\n        },\n        tempo: 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: 'treble',\n          instrument: 'piano',\n          measures: []\n        }],\n        lyrics: [],\n        userId: currentUser.uid\n      };\n      if (scoreId) {\n        // Para update, removemos o userId pois não deve ser alterado\n        const {\n          userId,\n          ...updateData\n        } = scoreData;\n        await ScoreService.updateScore(scoreId, updateData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n      alert('Partitura salva com sucesso!');\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      alert('Erro ao salvar partitura');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n  const handleStaffClick = event => {\n    var _event$currentTarget$;\n    const rect = event.currentTarget.getBoundingClientRect();\n    const svgRect = (_event$currentTarget$ = event.currentTarget.closest('svg')) === null || _event$currentTarget$ === void 0 ? void 0 : _event$currentTarget$.getBoundingClientRect();\n    if (!svgRect) return;\n    const x = event.clientX - svgRect.left;\n    const y = event.clientY - svgRect.top;\n\n    // Calcular a linha da pauta baseada na posição Y\n    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta\n    const notePositions = [{\n      y: 40,\n      note: 'A',\n      octave: 5\n    },\n    // Acima da pauta\n    {\n      y: 50,\n      note: 'G',\n      octave: 5\n    },\n    // 5ª linha\n    {\n      y: 60,\n      note: 'F',\n      octave: 5\n    },\n    // Entre 4ª e 5ª\n    {\n      y: 70,\n      note: 'E',\n      octave: 5\n    },\n    // 4ª linha\n    {\n      y: 80,\n      note: 'D',\n      octave: 5\n    },\n    // Entre 3ª e 4ª\n    {\n      y: 90,\n      note: 'C',\n      octave: 5\n    },\n    // 3ª linha (Dó central)\n    {\n      y: 100,\n      note: 'B',\n      octave: 4\n    },\n    // Entre 2ª e 3ª\n    {\n      y: 110,\n      note: 'A',\n      octave: 4\n    },\n    // 2ª linha\n    {\n      y: 120,\n      note: 'G',\n      octave: 4\n    },\n    // Entre 1ª e 2ª\n    {\n      y: 130,\n      note: 'F',\n      octave: 4\n    },\n    // 1ª linha\n    {\n      y: 140,\n      note: 'E',\n      octave: 4\n    } // Abaixo da pauta\n    ];\n\n    // Encontrar a posição mais próxima\n    const closestPosition = notePositions.reduce((closest, current) => {\n      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;\n    });\n\n    // Calcular a posição horizontal (compasso e tempo)\n    const measureWidth = 150; // Largura aproximada de um compasso\n    const startX = 120; // Início da área de notas\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = relativeX % measureWidth / measureWidth * 4; // 4 tempos por compasso\n\n    // Criar nova nota\n    const newNote = {\n      id: uuidv4(),\n      name: selectedNote,\n      // Usar a nota selecionada no painel\n      octave: closestPosition.octave,\n      duration: selectedDuration,\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4,\n        // Arredondar para 1/4 de tempo\n        staff: 0\n      },\n      isRest: selectedTool === 'rest'\n    };\n    setPlacedNotes(prev => [...prev, newNote]);\n  };\n  const durations = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n  return /*#__PURE__*/_jsxDEV(EditorContainer, {\n    children: [/*#__PURE__*/_jsxDEV(EditorHeader, {\n      children: [/*#__PURE__*/_jsxDEV(EditorTitle, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Editor de Partituras\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: title,\n          onChange: e => setTitle(e.target.value),\n          placeholder: \"Nome da partitura...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditorActions, {\n        children: [/*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: () => setPlacedNotes([]),\n          variant: \"primary\",\n          disabled: placedNotes.length === 0,\n          children: \"\\uD83D\\uDDD1\\uFE0F Limpar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handlePlay,\n          variant: \"primary\",\n          children: isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionButton, {\n          onClick: handleSave,\n          variant: \"secondary\",\n          disabled: !currentUser || isSaving,\n          children: isSaving ? '💾 Salvando...' : '💾 Salvar'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditorContent, {\n      children: [/*#__PURE__*/_jsxDEV(ToolPanel, {\n        children: [!currentUser && /*#__PURE__*/_jsxDEV(GuestWarning, {\n          children: \"\\u26A0\\uFE0F Modo visitante: suas altera\\xE7\\xF5es n\\xE3o ser\\xE3o salvas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFB5 Ferramentas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: [/*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'note',\n              onClick: () => setSelectedTool('note'),\n              children: \"\\uD83C\\uDFB5 Nota\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'rest',\n              onClick: () => setSelectedTool('rest'),\n              children: \"\\uD83C\\uDFBC Pausa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedTool === 'chord',\n              onClick: () => setSelectedTool('chord'),\n              children: \"\\uD83C\\uDFB9 Acorde\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\u23F1\\uFE0F Dura\\xE7\\xE3o\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: durations.map(duration => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedDuration === duration,\n              onClick: () => setSelectedDuration(duration),\n              children: duration === 'whole' ? '𝅝' : duration === 'half' ? '𝅗𝅥' : duration === 'quarter' ? '♩' : duration === 'eighth' ? '♫' : '♬'\n            }, duration, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83C\\uDFBC Notas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ToolGrid, {\n            children: notes.map(note => /*#__PURE__*/_jsxDEV(ToolButton, {\n              active: selectedNote === note,\n              onClick: () => setSelectedNote(note),\n              children: note\n            }, note, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToolSection, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCCA Estat\\xEDsticas\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.9rem',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83D\\uDCDD Notas: \", placedNotes.filter(n => !n.isRest).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u23F8\\uFE0F Pausas: \", placedNotes.filter(n => n.isRest).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\uD83C\\uDFB5 Total: \", placedNotes.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ScoreCanvas, {\n        children: /*#__PURE__*/_jsxDEV(StaffContainer, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 1rem 0',\n              color: '#495057'\n            },\n            children: [\"Partitura: \", title]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Staff, {\n            viewBox: \"0 0 800 200\",\n            children: [[0, 1, 2, 3, 4].map(line => /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"50\",\n              y1: 50 + line * 20,\n              x2: \"750\",\n              y2: 50 + line * 20,\n              stroke: \"#333\",\n              strokeWidth: \"1\"\n            }, line, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"20\",\n              y: \"90\",\n              fontSize: \"40\",\n              fill: \"#333\",\n              children: \"\\uD834\\uDD1E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"75\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"80\",\n              y: \"95\",\n              fontSize: \"16\",\n              fill: \"#333\",\n              children: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: \"110\",\n              y1: \"50\",\n              x2: \"110\",\n              y2: \"130\",\n              stroke: \"#333\",\n              strokeWidth: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"line\", {\n              x1: 120 + measure * 150,\n              y1: \"50\",\n              x2: 120 + measure * 150,\n              y2: \"130\",\n              stroke: \"#999\",\n              strokeWidth: \"1\"\n            }, measure, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this)), [1, 2, 3, 4].map(measure => /*#__PURE__*/_jsxDEV(\"text\", {\n              x: 120 + (measure - 1) * 150 + 75,\n              y: \"35\",\n              fontSize: \"12\",\n              fill: \"#666\",\n              textAnchor: \"middle\",\n              children: measure\n            }, measure, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"rect\", {\n              x: \"120\",\n              y: \"40\",\n              width: \"600\",\n              height: \"100\",\n              fill: \"transparent\",\n              style: {\n                cursor: 'crosshair'\n              },\n              onClick: handleStaffClick\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 15\n            }, this), placedNotes.map((note, index) => {\n              const x = 120 + (note.position.measure - 1) * 150 + note.position.beat * 30;\n              const notePositions = {\n                'A5': 40,\n                'G5': 50,\n                'F5': 60,\n                'E5': 70,\n                'D5': 80,\n                'C5': 90,\n                'B4': 100,\n                'A4': 110,\n                'G4': 120,\n                'F4': 130,\n                'E4': 140\n              };\n              const y = notePositions[`${note.name}${note.octave}`] || 90;\n              return /*#__PURE__*/_jsxDEV(\"g\", {\n                children: [(y < 50 || y > 130) && /*#__PURE__*/_jsxDEV(\"line\", {\n                  x1: x - 10,\n                  y1: y,\n                  x2: x + 10,\n                  y2: y,\n                  stroke: \"#666\",\n                  strokeWidth: \"1\",\n                  strokeDasharray: \"2,2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 23\n                }, this), note.isRest ?\n                /*#__PURE__*/\n                // Símbolo de pausa\n                _jsxDEV(\"g\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"rect\", {\n                    x: x - 4,\n                    y: y - 8,\n                    width: \"8\",\n                    height: \"4\",\n                    fill: \"#333\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"rect\", {\n                    x: x - 4,\n                    y: y + 4,\n                    width: \"8\",\n                    height: \"4\",\n                    fill: \"#333\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 23\n                }, this) :\n                /*#__PURE__*/\n                // Nota musical\n                _jsxDEV(\"ellipse\", {\n                  cx: x,\n                  cy: y,\n                  rx: \"7\",\n                  ry: \"5\",\n                  fill: note.duration === 'whole' || note.duration === 'half' ? 'white' : '#333',\n                  stroke: \"#333\",\n                  strokeWidth: \"2\",\n                  transform: `rotate(-20 ${x} ${y})`,\n                  style: {\n                    cursor: 'pointer'\n                  },\n                  onClick: e => {\n                    e.stopPropagation();\n                    setPlacedNotes(prev => prev.filter(n => n.id !== note.id));\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 23\n                }, this), !note.isRest && note.duration !== 'whole' && note.duration !== 'half' && /*#__PURE__*/_jsxDEV(\"line\", {\n                  x1: x + (y > 90 ? -7 : 7),\n                  y1: y,\n                  x2: x + (y > 90 ? -7 : 7),\n                  y2: y + (y > 90 ? 25 : -25),\n                  stroke: \"#333\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 23\n                }, this), !note.isRest && (note.duration === 'eighth' || note.duration === 'sixteenth') && /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: y > 90 ? `M ${x - 7} ${y + 25} Q ${x - 15} ${y + 20} ${x - 12} ${y + 15}` : `M ${x + 7} ${y - 25} Q ${x + 15} ${y - 20} ${x + 12} ${y - 15}`,\n                  fill: \"#333\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"text\", {\n                  x: x,\n                  y: y - 15,\n                  fontSize: \"10\",\n                  fill: \"#666\",\n                  textAnchor: \"middle\",\n                  style: {\n                    pointerEvents: 'none'\n                  },\n                  children: [note.name, note.octave]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this)]\n              }, note.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this);\n            }), placedNotes.length === 0 && /*#__PURE__*/_jsxDEV(\"text\", {\n              x: \"400\",\n              y: \"100\",\n              fontSize: \"14\",\n              fill: \"#999\",\n              textAnchor: \"middle\",\n              children: \"Clique na pauta para adicionar notas \\u2022 Clique em uma nota para remov\\xEA-la\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 295,\n    columnNumber: 5\n  }, this);\n};\n_s(ScoreEditor, \"RvrVYwdUhNGkCQ7jbUczc2Hd/ms=\", false, function () {\n  return [useAuth];\n});\n_c13 = ScoreEditor;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13;\n$RefreshReg$(_c, \"EditorContainer\");\n$RefreshReg$(_c2, \"EditorHeader\");\n$RefreshReg$(_c3, \"EditorTitle\");\n$RefreshReg$(_c4, \"EditorActions\");\n$RefreshReg$(_c5, \"ActionButton\");\n$RefreshReg$(_c6, \"EditorContent\");\n$RefreshReg$(_c7, \"ToolPanel\");\n$RefreshReg$(_c8, \"ToolSection\");\n$RefreshReg$(_c9, \"ToolGrid\");\n$RefreshReg$(_c0, \"ToolButton\");\n$RefreshReg$(_c1, \"ScoreCanvas\");\n$RefreshReg$(_c10, \"StaffContainer\");\n$RefreshReg$(_c11, \"Staff\");\n$RefreshReg$(_c12, \"GuestWarning\");\n$RefreshReg$(_c13, \"ScoreEditor\");", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "ScoreService", "v4", "uuidv4", "jsxDEV", "_jsxDEV", "EditorC<PERSON><PERSON>", "div", "_c", "Editor<PERSON><PERSON>er", "_c2", "EditorT<PERSON>le", "_c3", "EditorActions", "_c4", "ActionButton", "button", "props", "variant", "_c5", "Editor<PERSON><PERSON><PERSON>", "_c6", "ToolPanel", "_c7", "ToolSection", "_c8", "ToolGrid", "_c9", "<PERSON><PERSON><PERSON><PERSON>on", "active", "_c0", "ScoreCanvas", "_c1", "StaffC<PERSON>r", "_c10", "Staff", "svg", "_c11", "Guest<PERSON><PERSON>ning", "_c12", "ScoreEditor", "scoreId", "_s", "currentUser", "title", "setTitle", "selectedTool", "setSelectedTool", "selectedDuration", "setSelectedDuration", "selected<PERSON><PERSON>", "setSelectedNote", "isPlaying", "setIsPlaying", "isSaving", "setIsSaving", "placedNotes", "setPlacedNotes", "handleSave", "alert", "scoreData", "key", "note", "mode", "timeSignature", "numerator", "denominator", "tempo", "staffs", "id", "clef", "instrument", "measures", "lyrics", "userId", "uid", "updateData", "updateScore", "createScore", "error", "console", "handlePlay", "handleStaffClick", "event", "_event$currentTarget$", "rect", "currentTarget", "getBoundingClientRect", "svgRect", "closest", "x", "clientX", "left", "y", "clientY", "top", "staffLines", "notePositions", "octave", "closestPosition", "reduce", "current", "Math", "abs", "measureWidth", "startX", "relativeX", "measure", "max", "floor", "beatPosition", "newNote", "name", "duration", "position", "beat", "round", "staff", "isRest", "prev", "durations", "notes", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "value", "onChange", "e", "target", "placeholder", "onClick", "disabled", "length", "map", "style", "fontSize", "color", "filter", "n", "margin", "viewBox", "line", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "fill", "textAnchor", "width", "height", "cursor", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cx", "cy", "rx", "ry", "transform", "stopPropagation", "d", "pointerEvents", "_c13", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ScoreEditor/ScoreEditor.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Score, MusicalNote, NoteName, NoteDuration, ClefType, InstrumentType } from '../../types/music';\nimport { ScoreService } from '../../services/scoreService';\nimport { v4 as uuidv4 } from 'uuid';\n\nconst EditorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst EditorHeader = styled.div`\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst EditorTitle = styled.div`\n  h2 {\n    margin: 0 0 0.5rem 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n  }\n  \n  input {\n    background: rgba(255,255,255,0.2);\n    border: 1px solid rgba(255,255,255,0.3);\n    border-radius: 8px;\n    padding: 0.5rem;\n    color: white;\n    font-size: 1.1rem;\n    width: 300px;\n    \n    &::placeholder {\n      color: rgba(255,255,255,0.7);\n    }\n    \n    &:focus {\n      outline: none;\n      border-color: rgba(255,255,255,0.6);\n      background: rgba(255,255,255,0.3);\n    }\n  }\n`;\n\nconst EditorActions = styled.div`\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: rgba(255,255,255,0.2);\n    color: white;\n    border: 1px solid rgba(255,255,255,0.3);\n    \n    &:hover {\n      background: rgba(255,255,255,0.3);\n      transform: translateY(-1px);\n    }\n  ` : `\n    background: rgba(255,255,255,0.9);\n    color: #667eea;\n    \n    &:hover {\n      background: white;\n      transform: translateY(-1px);\n    }\n  `}\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst EditorContent = styled.div`\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n`;\n\nconst ToolPanel = styled.div`\n  width: 250px;\n  background: #f8f9fa;\n  border-right: 1px solid #e9ecef;\n  padding: 1.5rem;\n  overflow-y: auto;\n`;\n\nconst ToolSection = styled.div`\n  margin-bottom: 2rem;\n  \n  h3 {\n    margin: 0 0 1rem 0;\n    color: #495057;\n    font-size: 1rem;\n    font-weight: 600;\n  }\n`;\n\nconst ToolGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 0.5rem;\n`;\n\nconst ToolButton = styled.button<{ active?: boolean }>`\n  padding: 0.75rem;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  background: ${props => props.active ? '#667eea' : 'white'};\n  color: ${props => props.active ? 'white' : '#495057'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 0.9rem;\n  \n  &:hover {\n    border-color: #667eea;\n    ${props => !props.active && 'background: #f8f9fa;'}\n  }\n`;\n\nconst ScoreCanvas = styled.div`\n  flex: 1;\n  background: white;\n  margin: 1rem;\n  border-radius: 12px;\n  box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n  position: relative;\n  overflow: auto;\n`;\n\nconst StaffContainer = styled.div`\n  padding: 2rem;\n  min-height: 400px;\n`;\n\nconst Staff = styled.svg`\n  width: 100%;\n  height: 200px;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: white;\n  margin-bottom: 2rem;\n`;\n\nconst GuestWarning = styled.div`\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\n  border: 1px solid #ffeaa7;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  color: #856404;\n  text-align: center;\n  font-size: 0.9rem;\n`;\n\ninterface ScoreEditorProps {\n  scoreId?: string;\n}\n\nexport const ScoreEditor: React.FC<ScoreEditorProps> = ({ scoreId }) => {\n  const { currentUser } = useAuth();\n  const [title, setTitle] = useState('Nova Partitura');\n  const [selectedTool, setSelectedTool] = useState<'note' | 'rest' | 'chord'>('note');\n  const [selectedDuration, setSelectedDuration] = useState<NoteDuration>('quarter');\n  const [selectedNote, setSelectedNote] = useState<NoteName>('C');\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [placedNotes, setPlacedNotes] = useState<MusicalNote[]>([]);\n\n  const handleSave = async () => {\n    if (!currentUser) {\n      alert('Você precisa estar logado para salvar partituras!');\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      // Criar dados básicos da partitura\n      const scoreData = {\n        title,\n        key: { note: 'C' as NoteName, mode: 'major' as const },\n        timeSignature: { numerator: 4, denominator: 4 },\n        tempo: 120,\n        staffs: [{\n          id: uuidv4(),\n          clef: 'treble' as ClefType,\n          instrument: 'piano' as InstrumentType,\n          measures: []\n        }],\n        lyrics: [],\n        userId: currentUser.uid\n      };\n\n      if (scoreId) {\n        // Para update, removemos o userId pois não deve ser alterado\n        const { userId, ...updateData } = scoreData;\n        await ScoreService.updateScore(scoreId, updateData);\n      } else {\n        await ScoreService.createScore(scoreData, currentUser.uid);\n      }\n      \n      alert('Partitura salva com sucesso!');\n    } catch (error) {\n      console.error('Erro ao salvar:', error);\n      alert('Erro ao salvar partitura');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handlePlay = () => {\n    setIsPlaying(!isPlaying);\n    // TODO: Implementar reprodução\n  };\n\n  const handleStaffClick = (event: React.MouseEvent<SVGRectElement>) => {\n    const rect = event.currentTarget.getBoundingClientRect();\n    const svgRect = event.currentTarget.closest('svg')?.getBoundingClientRect();\n\n    if (!svgRect) return;\n\n    const x = event.clientX - svgRect.left;\n    const y = event.clientY - svgRect.top;\n\n    // Calcular a linha da pauta baseada na posição Y\n    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta\n    const notePositions = [\n      { y: 40, note: 'A' as NoteName, octave: 5 }, // Acima da pauta\n      { y: 50, note: 'G' as NoteName, octave: 5 }, // 5ª linha\n      { y: 60, note: 'F' as NoteName, octave: 5 }, // Entre 4ª e 5ª\n      { y: 70, note: 'E' as NoteName, octave: 5 }, // 4ª linha\n      { y: 80, note: 'D' as NoteName, octave: 5 }, // Entre 3ª e 4ª\n      { y: 90, note: 'C' as NoteName, octave: 5 }, // 3ª linha (Dó central)\n      { y: 100, note: 'B' as NoteName, octave: 4 }, // Entre 2ª e 3ª\n      { y: 110, note: 'A' as NoteName, octave: 4 }, // 2ª linha\n      { y: 120, note: 'G' as NoteName, octave: 4 }, // Entre 1ª e 2ª\n      { y: 130, note: 'F' as NoteName, octave: 4 }, // 1ª linha\n      { y: 140, note: 'E' as NoteName, octave: 4 }, // Abaixo da pauta\n    ];\n\n    // Encontrar a posição mais próxima\n    const closestPosition = notePositions.reduce((closest, current) => {\n      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;\n    });\n\n    // Calcular a posição horizontal (compasso e tempo)\n    const measureWidth = 150; // Largura aproximada de um compasso\n    const startX = 120; // Início da área de notas\n    const relativeX = x - startX;\n    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);\n    const beatPosition = ((relativeX % measureWidth) / measureWidth) * 4; // 4 tempos por compasso\n\n    // Criar nova nota\n    const newNote: MusicalNote = {\n      id: uuidv4(),\n      name: selectedNote, // Usar a nota selecionada no painel\n      octave: closestPosition.octave,\n      duration: selectedDuration,\n      position: {\n        measure,\n        beat: Math.round(beatPosition * 4) / 4, // Arredondar para 1/4 de tempo\n        staff: 0\n      },\n      isRest: selectedTool === 'rest'\n    };\n\n    setPlacedNotes(prev => [...prev, newNote]);\n  };\n\n  const durations: NoteDuration[] = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];\n  const notes: NoteName[] = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];\n\n  return (\n    <EditorContainer>\n      <EditorHeader>\n        <EditorTitle>\n          <h2>Editor de Partituras</h2>\n          <input\n            type=\"text\"\n            value={title}\n            onChange={(e) => setTitle(e.target.value)}\n            placeholder=\"Nome da partitura...\"\n          />\n        </EditorTitle>\n        <EditorActions>\n          <ActionButton\n            onClick={() => setPlacedNotes([])}\n            variant=\"primary\"\n            disabled={placedNotes.length === 0}\n          >\n            🗑️ Limpar\n          </ActionButton>\n          <ActionButton onClick={handlePlay} variant=\"primary\">\n            {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}\n          </ActionButton>\n          <ActionButton\n            onClick={handleSave}\n            variant=\"secondary\"\n            disabled={!currentUser || isSaving}\n          >\n            {isSaving ? '💾 Salvando...' : '💾 Salvar'}\n          </ActionButton>\n        </EditorActions>\n      </EditorHeader>\n\n      <EditorContent>\n        <ToolPanel>\n          {!currentUser && (\n            <GuestWarning>\n              ⚠️ Modo visitante: suas alterações não serão salvas\n            </GuestWarning>\n          )}\n          \n          <ToolSection>\n            <h3>🎵 Ferramentas</h3>\n            <ToolGrid>\n              <ToolButton \n                active={selectedTool === 'note'}\n                onClick={() => setSelectedTool('note')}\n              >\n                🎵 Nota\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'rest'}\n                onClick={() => setSelectedTool('rest')}\n              >\n                🎼 Pausa\n              </ToolButton>\n              <ToolButton \n                active={selectedTool === 'chord'}\n                onClick={() => setSelectedTool('chord')}\n              >\n                🎹 Acorde\n              </ToolButton>\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>⏱️ Duração</h3>\n            <ToolGrid>\n              {durations.map(duration => (\n                <ToolButton\n                  key={duration}\n                  active={selectedDuration === duration}\n                  onClick={() => setSelectedDuration(duration)}\n                >\n                  {duration === 'whole' ? '𝅝' : \n                   duration === 'half' ? '𝅗𝅥' :\n                   duration === 'quarter' ? '♩' :\n                   duration === 'eighth' ? '♫' : '♬'}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>🎼 Notas</h3>\n            <ToolGrid>\n              {notes.map(note => (\n                <ToolButton\n                  key={note}\n                  active={selectedNote === note}\n                  onClick={() => setSelectedNote(note)}\n                >\n                  {note}\n                </ToolButton>\n              ))}\n            </ToolGrid>\n          </ToolSection>\n\n          <ToolSection>\n            <h3>📊 Estatísticas</h3>\n            <div style={{ fontSize: '0.9rem', color: '#666' }}>\n              <div>📝 Notas: {placedNotes.filter(n => !n.isRest).length}</div>\n              <div>⏸️ Pausas: {placedNotes.filter(n => n.isRest).length}</div>\n              <div>🎵 Total: {placedNotes.length}</div>\n            </div>\n          </ToolSection>\n        </ToolPanel>\n\n        <ScoreCanvas>\n          <StaffContainer>\n            <h3 style={{ margin: '0 0 1rem 0', color: '#495057' }}>Partitura: {title}</h3>\n            <Staff viewBox=\"0 0 800 200\">\n              {/* Linhas da pauta */}\n              {[0, 1, 2, 3, 4].map(line => (\n                <line\n                  key={line}\n                  x1=\"50\"\n                  y1={50 + line * 20}\n                  x2=\"750\"\n                  y2={50 + line * 20}\n                  stroke=\"#333\"\n                  strokeWidth=\"1\"\n                />\n              ))}\n              \n              {/* Clave de Sol */}\n              <text x=\"20\" y=\"90\" fontSize=\"40\" fill=\"#333\">𝄞</text>\n              \n              {/* Compasso 4/4 */}\n              <text x=\"80\" y=\"75\" fontSize=\"16\" fill=\"#333\">4</text>\n              <text x=\"80\" y=\"95\" fontSize=\"16\" fill=\"#333\">4</text>\n              \n              {/* Linha divisória inicial */}\n              <line x1=\"110\" y1=\"50\" x2=\"110\" y2=\"130\" stroke=\"#333\" strokeWidth=\"2\"/>\n\n              {/* Divisões de compasso */}\n              {[1, 2, 3, 4].map(measure => (\n                <line\n                  key={measure}\n                  x1={120 + (measure * 150)}\n                  y1=\"50\"\n                  x2={120 + (measure * 150)}\n                  y2=\"130\"\n                  stroke=\"#999\"\n                  strokeWidth=\"1\"\n                />\n              ))}\n\n              {/* Números dos compassos */}\n              {[1, 2, 3, 4].map(measure => (\n                <text\n                  key={measure}\n                  x={120 + ((measure - 1) * 150) + 75}\n                  y=\"35\"\n                  fontSize=\"12\"\n                  fill=\"#666\"\n                  textAnchor=\"middle\"\n                >\n                  {measure}\n                </text>\n              ))}\n              \n              {/* Área clicável para adicionar notas */}\n              <rect\n                x=\"120\"\n                y=\"40\"\n                width=\"600\"\n                height=\"100\"\n                fill=\"transparent\"\n                style={{ cursor: 'crosshair' }}\n                onClick={handleStaffClick}\n              />\n\n              {/* Renderizar notas colocadas */}\n              {placedNotes.map((note, index) => {\n                const x = 120 + ((note.position.measure - 1) * 150) + (note.position.beat * 30);\n                const notePositions: { [key: string]: number } = {\n                  'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,\n                  'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140\n                };\n                const y = notePositions[`${note.name}${note.octave}`] || 90;\n\n                return (\n                  <g key={note.id}>\n                    {/* Linha auxiliar para notas fora da pauta */}\n                    {(y < 50 || y > 130) && (\n                      <line\n                        x1={x - 10}\n                        y1={y}\n                        x2={x + 10}\n                        y2={y}\n                        stroke=\"#666\"\n                        strokeWidth=\"1\"\n                        strokeDasharray=\"2,2\"\n                      />\n                    )}\n\n                    {/* Nota */}\n                    {note.isRest ? (\n                      // Símbolo de pausa\n                      <g>\n                        <rect\n                          x={x - 4}\n                          y={y - 8}\n                          width=\"8\"\n                          height=\"4\"\n                          fill=\"#333\"\n                        />\n                        <rect\n                          x={x - 4}\n                          y={y + 4}\n                          width=\"8\"\n                          height=\"4\"\n                          fill=\"#333\"\n                        />\n                      </g>\n                    ) : (\n                      // Nota musical\n                      <ellipse\n                        cx={x}\n                        cy={y}\n                        rx=\"7\"\n                        ry=\"5\"\n                        fill={note.duration === 'whole' || note.duration === 'half' ? 'white' : '#333'}\n                        stroke=\"#333\"\n                        strokeWidth=\"2\"\n                        transform={`rotate(-20 ${x} ${y})`}\n                        style={{ cursor: 'pointer' }}\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          setPlacedNotes(prev => prev.filter(n => n.id !== note.id));\n                        }}\n                      />\n                    )}\n\n                    {/* Haste da nota */}\n                    {!note.isRest && note.duration !== 'whole' && note.duration !== 'half' && (\n                      <line\n                        x1={x + (y > 90 ? -7 : 7)}\n                        y1={y}\n                        x2={x + (y > 90 ? -7 : 7)}\n                        y2={y + (y > 90 ? 25 : -25)}\n                        stroke=\"#333\"\n                        strokeWidth=\"2\"\n                      />\n                    )}\n\n                    {/* Bandeirola para colcheias e semicolcheias */}\n                    {!note.isRest && (note.duration === 'eighth' || note.duration === 'sixteenth') && (\n                      <path\n                        d={y > 90 ?\n                          `M ${x - 7} ${y + 25} Q ${x - 15} ${y + 20} ${x - 12} ${y + 15}` :\n                          `M ${x + 7} ${y - 25} Q ${x + 15} ${y - 20} ${x + 12} ${y - 15}`\n                        }\n                        fill=\"#333\"\n                      />\n                    )}\n\n                    {/* Texto da nota (para debug) */}\n                    <text\n                      x={x}\n                      y={y - 15}\n                      fontSize=\"10\"\n                      fill=\"#666\"\n                      textAnchor=\"middle\"\n                      style={{ pointerEvents: 'none' }}\n                    >\n                      {note.name}{note.octave}\n                    </text>\n                  </g>\n                );\n              })}\n\n              {/* Instruções */}\n              {placedNotes.length === 0 && (\n                <text x=\"400\" y=\"100\" fontSize=\"14\" fill=\"#999\" textAnchor=\"middle\">\n                  Clique na pauta para adicionar notas • Clique em uma nota para removê-la\n                </text>\n              )}\n            </Staff>\n          </StaffContainer>\n        </ScoreCanvas>\n      </EditorContent>\n    </EditorContainer>\n  );\n};\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAgB,OAAO;AAC/C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AAEpD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,eAAe,GAAGP,MAAM,CAACQ,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,eAAe;AAUrB,MAAMG,YAAY,GAAGV,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAPID,YAAY;AASlB,MAAME,WAAW,GAAGZ,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GA1BID,WAAW;AA4BjB,MAAME,aAAa,GAAGd,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,aAAa;AAMnB,MAAME,YAAY,GAAGhB,MAAM,CAACiB,MAA6C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,KAAK,IAAIA,KAAK,CAACC,OAAO,KAAK,SAAS,GAAG;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhCIJ,YAAY;AAkClB,MAAMK,aAAa,GAAGrB,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,aAAa;AAMnB,MAAME,SAAS,GAAGvB,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GANID,SAAS;AAQf,MAAME,WAAW,GAAGzB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GATID,WAAW;AAWjB,MAAME,QAAQ,GAAG3B,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GAJID,QAAQ;AAMd,MAAME,UAAU,GAAG7B,MAAM,CAACiB,MAA4B;AACtD;AACA;AACA;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D,WAAWZ,KAAK,IAAIA,KAAK,CAACY,MAAM,GAAG,OAAO,GAAG,SAAS;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMZ,KAAK,IAAI,CAACA,KAAK,CAACY,MAAM,IAAI,sBAAsB;AACtD;AACA,CAAC;AAACC,GAAA,GAdIF,UAAU;AAgBhB,MAAMG,WAAW,GAAGhC,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GARID,WAAW;AAUjB,MAAME,cAAc,GAAGlC,MAAM,CAACQ,GAAG;AACjC;AACA;AACA,CAAC;AAAC2B,IAAA,GAHID,cAAc;AAKpB,MAAME,KAAK,GAAGpC,MAAM,CAACqC,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAPIF,KAAK;AASX,MAAMG,YAAY,GAAGvC,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GATID,YAAY;AAelB,OAAO,MAAME,WAAuC,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM;IAAEC;EAAY,CAAC,GAAG3C,OAAO,CAAC,CAAC;EACjC,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,gBAAgB,CAAC;EACpD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAA4B,MAAM,CAAC;EACnF,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnD,QAAQ,CAAe,SAAS,CAAC;EACjF,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAW,GAAG,CAAC;EAC/D,MAAM,CAACsD,SAAS,EAAEC,YAAY,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwD,QAAQ,EAAEC,WAAW,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC0D,WAAW,EAAEC,cAAc,CAAC,GAAG3D,QAAQ,CAAgB,EAAE,CAAC;EAEjE,MAAM4D,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACf,WAAW,EAAE;MAChBgB,KAAK,CAAC,mDAAmD,CAAC;MAC1D;IACF;IAEAJ,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACF;MACA,MAAMK,SAAS,GAAG;QAChBhB,KAAK;QACLiB,GAAG,EAAE;UAAEC,IAAI,EAAE,GAAe;UAAEC,IAAI,EAAE;QAAiB,CAAC;QACtDC,aAAa,EAAE;UAAEC,SAAS,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAE,CAAC;QAC/CC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,CAAC;UACPC,EAAE,EAAElE,MAAM,CAAC,CAAC;UACZmE,IAAI,EAAE,QAAoB;UAC1BC,UAAU,EAAE,OAAyB;UACrCC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFC,MAAM,EAAE,EAAE;QACVC,MAAM,EAAE/B,WAAW,CAACgC;MACtB,CAAC;MAED,IAAIlC,OAAO,EAAE;QACX;QACA,MAAM;UAAEiC,MAAM;UAAE,GAAGE;QAAW,CAAC,GAAGhB,SAAS;QAC3C,MAAM3D,YAAY,CAAC4E,WAAW,CAACpC,OAAO,EAAEmC,UAAU,CAAC;MACrD,CAAC,MAAM;QACL,MAAM3E,YAAY,CAAC6E,WAAW,CAAClB,SAAS,EAAEjB,WAAW,CAACgC,GAAG,CAAC;MAC5D;MAEAhB,KAAK,CAAC,8BAA8B,CAAC;IACvC,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCpB,KAAK,CAAC,0BAA0B,CAAC;IACnC,CAAC,SAAS;MACRJ,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAM0B,UAAU,GAAGA,CAAA,KAAM;IACvB5B,YAAY,CAAC,CAACD,SAAS,CAAC;IACxB;EACF,CAAC;EAED,MAAM8B,gBAAgB,GAAIC,KAAuC,IAAK;IAAA,IAAAC,qBAAA;IACpE,MAAMC,IAAI,GAAGF,KAAK,CAACG,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,OAAO,IAAAJ,qBAAA,GAAGD,KAAK,CAACG,aAAa,CAACG,OAAO,CAAC,KAAK,CAAC,cAAAL,qBAAA,uBAAlCA,qBAAA,CAAoCG,qBAAqB,CAAC,CAAC;IAE3E,IAAI,CAACC,OAAO,EAAE;IAEd,MAAME,CAAC,GAAGP,KAAK,CAACQ,OAAO,GAAGH,OAAO,CAACI,IAAI;IACtC,MAAMC,CAAC,GAAGV,KAAK,CAACW,OAAO,GAAGN,OAAO,CAACO,GAAG;;IAErC;IACA,MAAMC,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3C,MAAMC,aAAa,GAAG,CACpB;MAAEJ,CAAC,EAAE,EAAE;MAAE/B,IAAI,EAAE,GAAe;MAAEoC,MAAM,EAAE;IAAE,CAAC;IAAE;IAC7C;MAAEL,CAAC,EAAE,EAAE;MAAE/B,IAAI,EAAE,GAAe;MAAEoC,MAAM,EAAE;IAAE,CAAC;IAAE;IAC7C;MAAEL,CAAC,EAAE,EAAE;MAAE/B,IAAI,EAAE,GAAe;MAAEoC,MAAM,EAAE;IAAE,CAAC;IAAE;IAC7C;MAAEL,CAAC,EAAE,EAAE;MAAE/B,IAAI,EAAE,GAAe;MAAEoC,MAAM,EAAE;IAAE,CAAC;IAAE;IAC7C;MAAEL,CAAC,EAAE,EAAE;MAAE/B,IAAI,EAAE,GAAe;MAAEoC,MAAM,EAAE;IAAE,CAAC;IAAE;IAC7C;MAAEL,CAAC,EAAE,EAAE;MAAE/B,IAAI,EAAE,GAAe;MAAEoC,MAAM,EAAE;IAAE,CAAC;IAAE;IAC7C;MAAEL,CAAC,EAAE,GAAG;MAAE/B,IAAI,EAAE,GAAe;MAAEoC,MAAM,EAAE;IAAE,CAAC;IAAE;IAC9C;MAAEL,CAAC,EAAE,GAAG;MAAE/B,IAAI,EAAE,GAAe;MAAEoC,MAAM,EAAE;IAAE,CAAC;IAAE;IAC9C;MAAEL,CAAC,EAAE,GAAG;MAAE/B,IAAI,EAAE,GAAe;MAAEoC,MAAM,EAAE;IAAE,CAAC;IAAE;IAC9C;MAAEL,CAAC,EAAE,GAAG;MAAE/B,IAAI,EAAE,GAAe;MAAEoC,MAAM,EAAE;IAAE,CAAC;IAAE;IAC9C;MAAEL,CAAC,EAAE,GAAG;MAAE/B,IAAI,EAAE,GAAe;MAAEoC,MAAM,EAAE;IAAE,CAAC,CAAE;IAAA,CAC/C;;IAED;IACA,MAAMC,eAAe,GAAGF,aAAa,CAACG,MAAM,CAAC,CAACX,OAAO,EAAEY,OAAO,KAAK;MACjE,OAAOC,IAAI,CAACC,GAAG,CAACF,OAAO,CAACR,CAAC,GAAGA,CAAC,CAAC,GAAGS,IAAI,CAACC,GAAG,CAACd,OAAO,CAACI,CAAC,GAAGA,CAAC,CAAC,GAAGQ,OAAO,GAAGZ,OAAO;IAC9E,CAAC,CAAC;;IAEF;IACA,MAAMe,YAAY,GAAG,GAAG,CAAC,CAAC;IAC1B,MAAMC,MAAM,GAAG,GAAG,CAAC,CAAC;IACpB,MAAMC,SAAS,GAAGhB,CAAC,GAAGe,MAAM;IAC5B,MAAME,OAAO,GAAGL,IAAI,CAACM,GAAG,CAAC,CAAC,EAAEN,IAAI,CAACO,KAAK,CAACH,SAAS,GAAGF,YAAY,CAAC,GAAG,CAAC,CAAC;IACrE,MAAMM,YAAY,GAAKJ,SAAS,GAAGF,YAAY,GAAIA,YAAY,GAAI,CAAC,CAAC,CAAC;;IAEtE;IACA,MAAMO,OAAoB,GAAG;MAC3B1C,EAAE,EAAElE,MAAM,CAAC,CAAC;MACZ6G,IAAI,EAAE9D,YAAY;MAAE;MACpBgD,MAAM,EAAEC,eAAe,CAACD,MAAM;MAC9Be,QAAQ,EAAEjE,gBAAgB;MAC1BkE,QAAQ,EAAE;QACRP,OAAO;QACPQ,IAAI,EAAEb,IAAI,CAACc,KAAK,CAACN,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;QAAE;QACxCO,KAAK,EAAE;MACT,CAAC;MACDC,MAAM,EAAExE,YAAY,KAAK;IAC3B,CAAC;IAEDW,cAAc,CAAC8D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAER,OAAO,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMS,SAAyB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC;EACrF,MAAMC,KAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAE7D,oBACEpH,OAAA,CAACC,eAAe;IAAAoH,QAAA,gBACdrH,OAAA,CAACI,YAAY;MAAAiH,QAAA,gBACXrH,OAAA,CAACM,WAAW;QAAA+G,QAAA,gBACVrH,OAAA;UAAAqH,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BzH,OAAA;UACE0H,IAAI,EAAC,MAAM;UACXC,KAAK,EAAEpF,KAAM;UACbqF,QAAQ,EAAGC,CAAC,IAAKrF,QAAQ,CAACqF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC1CI,WAAW,EAAC;QAAsB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACdzH,OAAA,CAACQ,aAAa;QAAA6G,QAAA,gBACZrH,OAAA,CAACU,YAAY;UACXsH,OAAO,EAAEA,CAAA,KAAM5E,cAAc,CAAC,EAAE,CAAE;UAClCvC,OAAO,EAAC,SAAS;UACjBoH,QAAQ,EAAE9E,WAAW,CAAC+E,MAAM,KAAK,CAAE;UAAAb,QAAA,EACpC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eACfzH,OAAA,CAACU,YAAY;UAACsH,OAAO,EAAEpD,UAAW;UAAC/D,OAAO,EAAC,SAAS;UAAAwG,QAAA,EACjDtE,SAAS,GAAG,WAAW,GAAG;QAAe;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACfzH,OAAA,CAACU,YAAY;UACXsH,OAAO,EAAE3E,UAAW;UACpBxC,OAAO,EAAC,WAAW;UACnBoH,QAAQ,EAAE,CAAC3F,WAAW,IAAIW,QAAS;UAAAoE,QAAA,EAElCpE,QAAQ,GAAG,gBAAgB,GAAG;QAAW;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEfzH,OAAA,CAACe,aAAa;MAAAsG,QAAA,gBACZrH,OAAA,CAACiB,SAAS;QAAAoG,QAAA,GACP,CAAC/E,WAAW,iBACXtC,OAAA,CAACiC,YAAY;UAAAoF,QAAA,EAAC;QAEd;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CACf,eAEDzH,OAAA,CAACmB,WAAW;UAAAkG,QAAA,gBACVrH,OAAA;YAAAqH,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBzH,OAAA,CAACqB,QAAQ;YAAAgG,QAAA,gBACPrH,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,MAAO;cAChCuF,OAAO,EAAEA,CAAA,KAAMtF,eAAe,CAAC,MAAM,CAAE;cAAA2E,QAAA,EACxC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzH,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,MAAO;cAChCuF,OAAO,EAAEA,CAAA,KAAMtF,eAAe,CAAC,MAAM,CAAE;cAAA2E,QAAA,EACxC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzH,OAAA,CAACuB,UAAU;cACTC,MAAM,EAAEiB,YAAY,KAAK,OAAQ;cACjCuF,OAAO,EAAEA,CAAA,KAAMtF,eAAe,CAAC,OAAO,CAAE;cAAA2E,QAAA,EACzC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdzH,OAAA,CAACmB,WAAW;UAAAkG,QAAA,gBACVrH,OAAA;YAAAqH,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnBzH,OAAA,CAACqB,QAAQ;YAAAgG,QAAA,EACNF,SAAS,CAACgB,GAAG,CAACvB,QAAQ,iBACrB5G,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEmB,gBAAgB,KAAKiE,QAAS;cACtCoB,OAAO,EAAEA,CAAA,KAAMpF,mBAAmB,CAACgE,QAAQ,CAAE;cAAAS,QAAA,EAE5CT,QAAQ,KAAK,OAAO,GAAG,IAAI,GAC3BA,QAAQ,KAAK,MAAM,GAAG,MAAM,GAC5BA,QAAQ,KAAK,SAAS,GAAG,GAAG,GAC5BA,QAAQ,KAAK,QAAQ,GAAG,GAAG,GAAG;YAAG,GAP7BA,QAAQ;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQH,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdzH,OAAA,CAACmB,WAAW;UAAAkG,QAAA,gBACVrH,OAAA;YAAAqH,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBzH,OAAA,CAACqB,QAAQ;YAAAgG,QAAA,EACND,KAAK,CAACe,GAAG,CAAC1E,IAAI,iBACbzD,OAAA,CAACuB,UAAU;cAETC,MAAM,EAAEqB,YAAY,KAAKY,IAAK;cAC9BuE,OAAO,EAAEA,CAAA,KAAMlF,eAAe,CAACW,IAAI,CAAE;cAAA4D,QAAA,EAEpC5D;YAAI,GAJAA,IAAI;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKC,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEdzH,OAAA,CAACmB,WAAW;UAAAkG,QAAA,gBACVrH,OAAA;YAAAqH,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBzH,OAAA;YAAKoI,KAAK,EAAE;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAjB,QAAA,gBAChDrH,OAAA;cAAAqH,QAAA,GAAK,sBAAU,EAAClE,WAAW,CAACoF,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACvB,MAAM,CAAC,CAACiB,MAAM;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEzH,OAAA;cAAAqH,QAAA,GAAK,uBAAW,EAAClE,WAAW,CAACoF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACvB,MAAM,CAAC,CAACiB,MAAM;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEzH,OAAA;cAAAqH,QAAA,GAAK,sBAAU,EAAClE,WAAW,CAAC+E,MAAM;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEZzH,OAAA,CAAC0B,WAAW;QAAA2F,QAAA,eACVrH,OAAA,CAAC4B,cAAc;UAAAyF,QAAA,gBACbrH,OAAA;YAAIoI,KAAK,EAAE;cAAEK,MAAM,EAAE,YAAY;cAAEH,KAAK,EAAE;YAAU,CAAE;YAAAjB,QAAA,GAAC,aAAW,EAAC9E,KAAK;UAAA;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9EzH,OAAA,CAAC8B,KAAK;YAAC4G,OAAO,EAAC,aAAa;YAAArB,QAAA,GAEzB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACc,GAAG,CAACQ,IAAI,iBACvB3I,OAAA;cAEE4I,EAAE,EAAC,IAAI;cACPC,EAAE,EAAE,EAAE,GAAGF,IAAI,GAAG,EAAG;cACnBG,EAAE,EAAC,KAAK;cACRC,EAAE,EAAE,EAAE,GAAGJ,IAAI,GAAG,EAAG;cACnBK,MAAM,EAAC,MAAM;cACbC,WAAW,EAAC;YAAG,GANVN,IAAI;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV,CACF,CAAC,eAGFzH,OAAA;cAAMqF,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAAC6C,QAAQ,EAAC,IAAI;cAACa,IAAI,EAAC,MAAM;cAAA7B,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGvDzH,OAAA;cAAMqF,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAAC6C,QAAQ,EAAC,IAAI;cAACa,IAAI,EAAC,MAAM;cAAA7B,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtDzH,OAAA;cAAMqF,CAAC,EAAC,IAAI;cAACG,CAAC,EAAC,IAAI;cAAC6C,QAAQ,EAAC,IAAI;cAACa,IAAI,EAAC,MAAM;cAAA7B,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGtDzH,OAAA;cAAM4I,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,KAAK;cAACC,EAAE,EAAC,KAAK;cAACC,MAAM,EAAC,MAAM;cAACC,WAAW,EAAC;YAAG;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC,CAAC,EAGvE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACU,GAAG,CAAC7B,OAAO,iBACvBtG,OAAA;cAEE4I,EAAE,EAAE,GAAG,GAAItC,OAAO,GAAG,GAAK;cAC1BuC,EAAE,EAAC,IAAI;cACPC,EAAE,EAAE,GAAG,GAAIxC,OAAO,GAAG,GAAK;cAC1ByC,EAAE,EAAC,KAAK;cACRC,MAAM,EAAC,MAAM;cACbC,WAAW,EAAC;YAAG,GANV3C,OAAO;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOb,CACF,CAAC,EAGD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACU,GAAG,CAAC7B,OAAO,iBACvBtG,OAAA;cAEEqF,CAAC,EAAE,GAAG,GAAI,CAACiB,OAAO,GAAG,CAAC,IAAI,GAAI,GAAG,EAAG;cACpCd,CAAC,EAAC,IAAI;cACN6C,QAAQ,EAAC,IAAI;cACba,IAAI,EAAC,MAAM;cACXC,UAAU,EAAC,QAAQ;cAAA9B,QAAA,EAElBf;YAAO,GAPHA,OAAO;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQR,CACP,CAAC,eAGFzH,OAAA;cACEqF,CAAC,EAAC,KAAK;cACPG,CAAC,EAAC,IAAI;cACN4D,KAAK,EAAC,KAAK;cACXC,MAAM,EAAC,KAAK;cACZH,IAAI,EAAC,aAAa;cAClBd,KAAK,EAAE;gBAAEkB,MAAM,EAAE;cAAY,CAAE;cAC/BtB,OAAO,EAAEnD;YAAiB;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,EAGDtE,WAAW,CAACgF,GAAG,CAAC,CAAC1E,IAAI,EAAE8F,KAAK,KAAK;cAChC,MAAMlE,CAAC,GAAG,GAAG,GAAI,CAAC5B,IAAI,CAACoD,QAAQ,CAACP,OAAO,GAAG,CAAC,IAAI,GAAI,GAAI7C,IAAI,CAACoD,QAAQ,CAACC,IAAI,GAAG,EAAG;cAC/E,MAAMlB,aAAwC,GAAG;gBAC/C,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,EAAE;gBAChD,IAAI,EAAE,EAAE;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE,GAAG;gBAAE,IAAI,EAAE;cAC9D,CAAC;cACD,MAAMJ,CAAC,GAAGI,aAAa,CAAC,GAAGnC,IAAI,CAACkD,IAAI,GAAGlD,IAAI,CAACoC,MAAM,EAAE,CAAC,IAAI,EAAE;cAE3D,oBACE7F,OAAA;gBAAAqH,QAAA,GAEG,CAAC7B,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,kBACjBxF,OAAA;kBACE4I,EAAE,EAAEvD,CAAC,GAAG,EAAG;kBACXwD,EAAE,EAAErD,CAAE;kBACNsD,EAAE,EAAEzD,CAAC,GAAG,EAAG;kBACX0D,EAAE,EAAEvD,CAAE;kBACNwD,MAAM,EAAC,MAAM;kBACbC,WAAW,EAAC,GAAG;kBACfO,eAAe,EAAC;gBAAK;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CACF,EAGAhE,IAAI,CAACwD,MAAM;gBAAA;gBACV;gBACAjH,OAAA;kBAAAqH,QAAA,gBACErH,OAAA;oBACEqF,CAAC,EAAEA,CAAC,GAAG,CAAE;oBACTG,CAAC,EAAEA,CAAC,GAAG,CAAE;oBACT4D,KAAK,EAAC,GAAG;oBACTC,MAAM,EAAC,GAAG;oBACVH,IAAI,EAAC;kBAAM;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,eACFzH,OAAA;oBACEqF,CAAC,EAAEA,CAAC,GAAG,CAAE;oBACTG,CAAC,EAAEA,CAAC,GAAG,CAAE;oBACT4D,KAAK,EAAC,GAAG;oBACTC,MAAM,EAAC,GAAG;oBACVH,IAAI,EAAC;kBAAM;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;gBAAA;gBAEJ;gBACAzH,OAAA;kBACEyJ,EAAE,EAAEpE,CAAE;kBACNqE,EAAE,EAAElE,CAAE;kBACNmE,EAAE,EAAC,GAAG;kBACNC,EAAE,EAAC,GAAG;kBACNV,IAAI,EAAEzF,IAAI,CAACmD,QAAQ,KAAK,OAAO,IAAInD,IAAI,CAACmD,QAAQ,KAAK,MAAM,GAAG,OAAO,GAAG,MAAO;kBAC/EoC,MAAM,EAAC,MAAM;kBACbC,WAAW,EAAC,GAAG;kBACfY,SAAS,EAAE,cAAcxE,CAAC,IAAIG,CAAC,GAAI;kBACnC4C,KAAK,EAAE;oBAAEkB,MAAM,EAAE;kBAAU,CAAE;kBAC7BtB,OAAO,EAAGH,CAAC,IAAK;oBACdA,CAAC,CAACiC,eAAe,CAAC,CAAC;oBACnB1G,cAAc,CAAC8D,IAAI,IAAIA,IAAI,CAACqB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxE,EAAE,KAAKP,IAAI,CAACO,EAAE,CAAC,CAAC;kBAC5D;gBAAE;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF,EAGA,CAAChE,IAAI,CAACwD,MAAM,IAAIxD,IAAI,CAACmD,QAAQ,KAAK,OAAO,IAAInD,IAAI,CAACmD,QAAQ,KAAK,MAAM,iBACpE5G,OAAA;kBACE4I,EAAE,EAAEvD,CAAC,IAAIG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAE;kBAC1BqD,EAAE,EAAErD,CAAE;kBACNsD,EAAE,EAAEzD,CAAC,IAAIG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAE;kBAC1BuD,EAAE,EAAEvD,CAAC,IAAIA,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAE;kBAC5BwD,MAAM,EAAC,MAAM;kBACbC,WAAW,EAAC;gBAAG;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CACF,EAGA,CAAChE,IAAI,CAACwD,MAAM,KAAKxD,IAAI,CAACmD,QAAQ,KAAK,QAAQ,IAAInD,IAAI,CAACmD,QAAQ,KAAK,WAAW,CAAC,iBAC5E5G,OAAA;kBACE+J,CAAC,EAAEvE,CAAC,GAAG,EAAE,GACP,KAAKH,CAAC,GAAG,CAAC,IAAIG,CAAC,GAAG,EAAE,MAAMH,CAAC,GAAG,EAAE,IAAIG,CAAC,GAAG,EAAE,IAAIH,CAAC,GAAG,EAAE,IAAIG,CAAC,GAAG,EAAE,EAAE,GAChE,KAAKH,CAAC,GAAG,CAAC,IAAIG,CAAC,GAAG,EAAE,MAAMH,CAAC,GAAG,EAAE,IAAIG,CAAC,GAAG,EAAE,IAAIH,CAAC,GAAG,EAAE,IAAIG,CAAC,GAAG,EAAE,EAC/D;kBACD0D,IAAI,EAAC;gBAAM;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CACF,eAGDzH,OAAA;kBACEqF,CAAC,EAAEA,CAAE;kBACLG,CAAC,EAAEA,CAAC,GAAG,EAAG;kBACV6C,QAAQ,EAAC,IAAI;kBACba,IAAI,EAAC,MAAM;kBACXC,UAAU,EAAC,QAAQ;kBACnBf,KAAK,EAAE;oBAAE4B,aAAa,EAAE;kBAAO,CAAE;kBAAA3C,QAAA,GAEhC5D,IAAI,CAACkD,IAAI,EAAElD,IAAI,CAACoC,MAAM;gBAAA;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA,GArFDhE,IAAI,CAACO,EAAE;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsFZ,CAAC;YAER,CAAC,CAAC,EAGDtE,WAAW,CAAC+E,MAAM,KAAK,CAAC,iBACvBlI,OAAA;cAAMqF,CAAC,EAAC,KAAK;cAACG,CAAC,EAAC,KAAK;cAAC6C,QAAQ,EAAC,IAAI;cAACa,IAAI,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAAA9B,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB,CAAC;AAACpF,EAAA,CA9YWF,WAAuC;EAAA,QAC1BxC,OAAO;AAAA;AAAAsK,IAAA,GADpB9H,WAAuC;AAAA,IAAAhC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAA+H,IAAA;AAAAC,YAAA,CAAA/J,EAAA;AAAA+J,YAAA,CAAA7J,GAAA;AAAA6J,YAAA,CAAA3J,GAAA;AAAA2J,YAAA,CAAAzJ,GAAA;AAAAyJ,YAAA,CAAApJ,GAAA;AAAAoJ,YAAA,CAAAlJ,GAAA;AAAAkJ,YAAA,CAAAhJ,GAAA;AAAAgJ,YAAA,CAAA9I,GAAA;AAAA8I,YAAA,CAAA5I,GAAA;AAAA4I,YAAA,CAAAzI,GAAA;AAAAyI,YAAA,CAAAvI,GAAA;AAAAuI,YAAA,CAAArI,IAAA;AAAAqI,YAAA,CAAAlI,IAAA;AAAAkI,YAAA,CAAAhI,IAAA;AAAAgI,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}