import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { Score, MusicalNote, NoteName, NoteDuration, ClefType, InstrumentType } from '../../types/music';
import { ScoreService } from '../../services/scoreService';
import { ChordView } from '../ChordView/ChordView';
import { v4 as uuidv4 } from 'uuid';

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(255,255,255,0.95);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
`;

const EditorHeader = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const EditorTitle = styled.div`
  h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
  }
  
  input {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 8px;
    padding: 0.5rem;
    color: white;
    font-size: 1.1rem;
    width: 300px;
    
    &::placeholder {
      color: rgba(255,255,255,0.7);
    }
    
    &:focus {
      outline: none;
      border-color: rgba(255,255,255,0.6);
      background: rgba(255,255,255,0.3);
    }
  }
`;

const EditorActions = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.variant === 'primary' ? `
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    
    &:hover {
      background: rgba(255,255,255,0.3);
      transform: translateY(-1px);
    }
  ` : `
    background: rgba(255,255,255,0.9);
    color: #667eea;
    
    &:hover {
      background: white;
      transform: translateY(-1px);
    }
  `}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const EditorContent = styled.div`
  flex: 1;
  display: flex;
  overflow: hidden;
`;

const ToolPanel = styled.div`
  width: 250px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  padding: 1.5rem;
  overflow-y: auto;
`;

const ToolSection = styled.div`
  margin-bottom: 2rem;
  
  h3 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
  }
`;

const ToolGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
`;

const ToolButton = styled.button<{ active?: boolean }>`
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: ${props => props.active ? '#667eea' : 'white'};
  color: ${props => props.active ? 'white' : '#495057'};
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  
  &:hover {
    border-color: #667eea;
    ${props => !props.active && 'background: #f8f9fa;'}
  }
`;

const ScoreCanvas = styled.div`
  flex: 1;
  background: white;
  margin: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  position: relative;
  overflow: auto;
`;

const StaffContainer = styled.div`
  padding: 2rem;
  min-height: 400px;
`;

const Staff = styled.svg`
  width: 100%;
  height: 200px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  margin-bottom: 2rem;
`;

const GuestWarning = styled.div`
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  color: #856404;
  text-align: center;
  font-size: 0.9rem;
`;

interface ScoreEditorProps {
  scoreId?: string;
}

export const ScoreEditor: React.FC<ScoreEditorProps> = ({ scoreId }) => {
  const { currentUser } = useAuth();
  const [title, setTitle] = useState('Nova Partitura');
  const [selectedTool, setSelectedTool] = useState<'note' | 'rest' | 'chord'>('note');
  const [selectedDuration, setSelectedDuration] = useState<NoteDuration>('quarter');
  const [selectedNote, setSelectedNote] = useState<NoteName>('C');
  const [isPlaying, setIsPlaying] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [placedNotes, setPlacedNotes] = useState<MusicalNote[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showChords, setShowChords] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Carregar partitura existente
  React.useEffect(() => {
    if (scoreId && currentUser) {
      loadScore();
    }
  }, [scoreId, currentUser]);

  // Salvamento automático
  React.useEffect(() => {
    if (!currentUser || !scoreId || placedNotes.length === 0) return;

    const autoSaveTimer = setTimeout(() => {
      handleSave(true); // true indica salvamento automático
    }, 2000); // Salvar após 2 segundos de inatividade

    return () => clearTimeout(autoSaveTimer);
  }, [placedNotes, title, currentUser, scoreId]);

  const loadScore = async () => {
    if (!scoreId || !currentUser) return;

    setIsLoading(true);
    try {
      const score = await ScoreService.getScore(scoreId);
      if (score && score.userId === currentUser.uid) {
        setTitle(score.title);

        // Extrair notas de todos os compassos
        const allNotes: MusicalNote[] = [];
        score.staffs.forEach(staff => {
          staff.measures.forEach(measure => {
            allNotes.push(...measure.notes);
          });
        });

        setPlacedNotes(allNotes);
      }
    } catch (error) {
      console.error('Erro ao carregar partitura:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async (isAutoSave = false) => {
    if (!currentUser) {
      if (!isAutoSave) {
        alert('Você precisa estar logado para salvar partituras!');
      }
      return;
    }

    setIsSaving(true);
    try {
      // Organizar notas por compasso
      const measureMap = new Map<number, MusicalNote[]>();
      placedNotes.forEach(note => {
        const measure = note.position.measure;
        if (!measureMap.has(measure)) {
          measureMap.set(measure, []);
        }
        measureMap.get(measure)!.push(note);
      });

      // Criar compassos com as notas
      const measures = Array.from(measureMap.entries()).map(([measureNumber, notes]) => ({
        id: uuidv4(),
        number: measureNumber,
        timeSignature: { numerator: 4, denominator: 4 },
        notes: notes.sort((a, b) => a.position.beat - b.position.beat),
        chords: []
      }));

      // Criar dados da partitura
      const scoreData = {
        title,
        key: { note: 'C' as NoteName, mode: 'major' as const },
        timeSignature: { numerator: 4, denominator: 4 },
        tempo: 120,
        staffs: [{
          id: uuidv4(),
          clef: 'treble' as ClefType,
          instrument: 'piano' as InstrumentType,
          measures
        }],
        lyrics: [],
        userId: currentUser.uid
      };

      if (scoreId) {
        // Para update, removemos o userId pois não deve ser alterado
        const { userId, ...updateData } = scoreData;
        await ScoreService.updateScore(scoreId, updateData);
      } else {
        await ScoreService.createScore(scoreData, currentUser.uid);
      }

      setLastSaved(new Date());
      if (!isAutoSave) {
        alert('Partitura salva com sucesso!');
      }
    } catch (error) {
      console.error('Erro ao salvar:', error);
      if (!isAutoSave) {
        alert('Erro ao salvar partitura');
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handlePlay = () => {
    setIsPlaying(!isPlaying);
    // TODO: Implementar reprodução
  };

  const handleStaffClick = (event: React.MouseEvent<SVGRectElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const svgRect = event.currentTarget.closest('svg')?.getBoundingClientRect();

    if (!svgRect) return;

    const x = event.clientX - svgRect.left;
    const y = event.clientY - svgRect.top;

    // Calcular a linha da pauta baseada na posição Y
    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta
    const notePositions = [
      { y: 40, note: 'A' as NoteName, octave: 5 }, // Acima da pauta
      { y: 50, note: 'G' as NoteName, octave: 5 }, // 5ª linha
      { y: 60, note: 'F' as NoteName, octave: 5 }, // Entre 4ª e 5ª
      { y: 70, note: 'E' as NoteName, octave: 5 }, // 4ª linha
      { y: 80, note: 'D' as NoteName, octave: 5 }, // Entre 3ª e 4ª
      { y: 90, note: 'C' as NoteName, octave: 5 }, // 3ª linha (Dó central)
      { y: 100, note: 'B' as NoteName, octave: 4 }, // Entre 2ª e 3ª
      { y: 110, note: 'A' as NoteName, octave: 4 }, // 2ª linha
      { y: 120, note: 'G' as NoteName, octave: 4 }, // Entre 1ª e 2ª
      { y: 130, note: 'F' as NoteName, octave: 4 }, // 1ª linha
      { y: 140, note: 'E' as NoteName, octave: 4 }, // Abaixo da pauta
    ];

    // Encontrar a posição mais próxima
    const closestPosition = notePositions.reduce((closest, current) => {
      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;
    });

    // Calcular a posição horizontal (compasso e tempo)
    const measureWidth = 150; // Largura aproximada de um compasso
    const startX = 120; // Início da área de notas
    const relativeX = x - startX;
    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);
    const beatPosition = ((relativeX % measureWidth) / measureWidth) * 4; // 4 tempos por compasso

    // Criar nova nota
    const newNote: MusicalNote = {
      id: uuidv4(),
      name: selectedNote, // Usar a nota selecionada no painel
      octave: closestPosition.octave,
      duration: selectedDuration,
      position: {
        measure,
        beat: Math.round(beatPosition * 4) / 4, // Arredondar para 1/4 de tempo
        staff: 0
      },
      isRest: selectedTool === 'rest'
    };

    setPlacedNotes(prev => [...prev, newNote]);
  };

  const durations: NoteDuration[] = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];
  const notes: NoteName[] = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];

  if (isLoading) {
    return (
      <EditorContainer>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '400px',
          fontSize: '1.2rem',
          color: '#666'
        }}>
          🎼 Carregando partitura...
        </div>
      </EditorContainer>
    );
  }

  return (
    <EditorContainer>
      <EditorHeader>
        <EditorTitle>
          <h2>Editor de Partituras</h2>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Nome da partitura..."
            />
            {currentUser && lastSaved && (
              <div style={{ fontSize: '0.8rem', color: 'rgba(255,255,255,0.8)' }}>
                💾 Salvo às {lastSaved.toLocaleTimeString()}
              </div>
            )}
          </div>
        </EditorTitle>
        <EditorActions>
          <ActionButton
            onClick={() => setShowChords(!showChords)}
            variant="primary"
          >
            {showChords ? '🎼 Partitura' : '🎸 Cifras'}
          </ActionButton>
          <ActionButton
            onClick={() => setPlacedNotes([])}
            variant="primary"
            disabled={placedNotes.length === 0}
          >
            🗑️ Limpar
          </ActionButton>
          <ActionButton onClick={handlePlay} variant="primary">
            {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}
          </ActionButton>
          <ActionButton
            onClick={handleSave}
            variant="secondary"
            disabled={!currentUser || isSaving}
          >
            {isSaving ? '💾 Salvando...' : '💾 Salvar'}
          </ActionButton>
        </EditorActions>
      </EditorHeader>

      <EditorContent>
        <ToolPanel>
          {!currentUser && (
            <GuestWarning>
              ⚠️ Modo visitante: suas alterações não serão salvas
            </GuestWarning>
          )}
          
          <ToolSection>
            <h3>🎵 Ferramentas</h3>
            <ToolGrid>
              <ToolButton 
                active={selectedTool === 'note'}
                onClick={() => setSelectedTool('note')}
              >
                🎵 Nota
              </ToolButton>
              <ToolButton 
                active={selectedTool === 'rest'}
                onClick={() => setSelectedTool('rest')}
              >
                🎼 Pausa
              </ToolButton>
              <ToolButton 
                active={selectedTool === 'chord'}
                onClick={() => setSelectedTool('chord')}
              >
                🎹 Acorde
              </ToolButton>
            </ToolGrid>
          </ToolSection>

          <ToolSection>
            <h3>⏱️ Duração</h3>
            <ToolGrid>
              {durations.map(duration => (
                <ToolButton
                  key={duration}
                  active={selectedDuration === duration}
                  onClick={() => setSelectedDuration(duration)}
                >
                  {duration === 'whole' ? '𝅝' : 
                   duration === 'half' ? '𝅗𝅥' :
                   duration === 'quarter' ? '♩' :
                   duration === 'eighth' ? '♫' : '♬'}
                </ToolButton>
              ))}
            </ToolGrid>
          </ToolSection>

          <ToolSection>
            <h3>🎼 Notas</h3>
            <ToolGrid>
              {notes.map(note => (
                <ToolButton
                  key={note}
                  active={selectedNote === note}
                  onClick={() => setSelectedNote(note)}
                >
                  {note}
                </ToolButton>
              ))}
            </ToolGrid>
          </ToolSection>

          <ToolSection>
            <h3>📊 Estatísticas</h3>
            <div style={{ fontSize: '0.9rem', color: '#666' }}>
              <div>📝 Notas: {placedNotes.filter(n => !n.isRest).length}</div>
              <div>⏸️ Pausas: {placedNotes.filter(n => n.isRest).length}</div>
              <div>🎵 Total: {placedNotes.length}</div>
            </div>
          </ToolSection>
        </ToolPanel>

        <ScoreCanvas>
          {showChords ? (
            <ChordView notes={placedNotes} title={title} />
          ) : (
            <StaffContainer>
              <h3 style={{ margin: '0 0 1rem 0', color: '#495057' }}>Partitura: {title}</h3>
              <Staff viewBox="0 0 800 200">
              {/* Linhas da pauta */}
              {[0, 1, 2, 3, 4].map(line => (
                <line
                  key={line}
                  x1="50"
                  y1={50 + line * 20}
                  x2="750"
                  y2={50 + line * 20}
                  stroke="#333"
                  strokeWidth="1"
                />
              ))}
              
              {/* Clave de Sol */}
              <text x="20" y="90" fontSize="40" fill="#333">𝄞</text>
              
              {/* Compasso 4/4 */}
              <text x="80" y="75" fontSize="16" fill="#333">4</text>
              <text x="80" y="95" fontSize="16" fill="#333">4</text>
              
              {/* Linha divisória inicial */}
              <line x1="110" y1="50" x2="110" y2="130" stroke="#333" strokeWidth="2"/>

              {/* Divisões de compasso */}
              {[1, 2, 3, 4].map(measure => (
                <line
                  key={measure}
                  x1={120 + (measure * 150)}
                  y1="50"
                  x2={120 + (measure * 150)}
                  y2="130"
                  stroke="#999"
                  strokeWidth="1"
                />
              ))}

              {/* Números dos compassos */}
              {[1, 2, 3, 4].map(measure => (
                <text
                  key={measure}
                  x={120 + ((measure - 1) * 150) + 75}
                  y="35"
                  fontSize="12"
                  fill="#666"
                  textAnchor="middle"
                >
                  {measure}
                </text>
              ))}
              
              {/* Área clicável para adicionar notas */}
              <rect
                x="120"
                y="40"
                width="600"
                height="100"
                fill="transparent"
                style={{ cursor: 'crosshair' }}
                onClick={handleStaffClick}
              />

              {/* Renderizar notas colocadas */}
              {placedNotes.map((note, index) => {
                const x = 120 + ((note.position.measure - 1) * 150) + (note.position.beat * 30);
                const notePositions: { [key: string]: number } = {
                  'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,
                  'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140
                };
                const y = notePositions[`${note.name}${note.octave}`] || 90;

                return (
                  <g key={note.id}>
                    {/* Linha auxiliar para notas fora da pauta */}
                    {(y < 50 || y > 130) && (
                      <line
                        x1={x - 10}
                        y1={y}
                        x2={x + 10}
                        y2={y}
                        stroke="#666"
                        strokeWidth="1"
                        strokeDasharray="2,2"
                      />
                    )}

                    {/* Nota */}
                    {note.isRest ? (
                      // Símbolo de pausa
                      <g>
                        <rect
                          x={x - 4}
                          y={y - 8}
                          width="8"
                          height="4"
                          fill="#333"
                        />
                        <rect
                          x={x - 4}
                          y={y + 4}
                          width="8"
                          height="4"
                          fill="#333"
                        />
                      </g>
                    ) : (
                      // Nota musical
                      <ellipse
                        cx={x}
                        cy={y}
                        rx="7"
                        ry="5"
                        fill={note.duration === 'whole' || note.duration === 'half' ? 'white' : '#333'}
                        stroke="#333"
                        strokeWidth="2"
                        transform={`rotate(-20 ${x} ${y})`}
                        style={{ cursor: 'pointer' }}
                        onClick={(e) => {
                          e.stopPropagation();
                          setPlacedNotes(prev => prev.filter(n => n.id !== note.id));
                        }}
                      />
                    )}

                    {/* Haste da nota */}
                    {!note.isRest && note.duration !== 'whole' && note.duration !== 'half' && (
                      <line
                        x1={x + (y > 90 ? -7 : 7)}
                        y1={y}
                        x2={x + (y > 90 ? -7 : 7)}
                        y2={y + (y > 90 ? 25 : -25)}
                        stroke="#333"
                        strokeWidth="2"
                      />
                    )}

                    {/* Bandeirola para colcheias e semicolcheias */}
                    {!note.isRest && (note.duration === 'eighth' || note.duration === 'sixteenth') && (
                      <path
                        d={y > 90 ?
                          `M ${x - 7} ${y + 25} Q ${x - 15} ${y + 20} ${x - 12} ${y + 15}` :
                          `M ${x + 7} ${y - 25} Q ${x + 15} ${y - 20} ${x + 12} ${y - 15}`
                        }
                        fill="#333"
                      />
                    )}

                    {/* Texto da nota (para debug) */}
                    <text
                      x={x}
                      y={y - 15}
                      fontSize="10"
                      fill="#666"
                      textAnchor="middle"
                      style={{ pointerEvents: 'none' }}
                    >
                      {note.name}{note.octave}
                    </text>
                  </g>
                );
              })}

              {/* Instruções */}
              {placedNotes.length === 0 && (
                <text x="400" y="100" fontSize="14" fill="#999" textAnchor="middle">
                  Clique na pauta para adicionar notas • Clique em uma nota para removê-la
                </text>
              )}
            </Staff>
          </StaffContainer>
          )}
        </ScoreCanvas>
      </EditorContent>
    </EditorContainer>
  );
};
