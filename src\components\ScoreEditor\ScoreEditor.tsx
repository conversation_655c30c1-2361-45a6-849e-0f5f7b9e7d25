import React, { useState } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { MusicalNote, NoteName, NoteDuration, InstrumentType, Octave, Lyrics } from '../../types/music';
import { ScoreService } from '../../services/scoreService';
import { ChordView } from '../ChordView/ChordView';

import { LyricsEditor } from '../LyricsEditor/LyricsEditor';
import { InstrumentSelector } from '../InstrumentSelector/InstrumentSelector';
import { StaffSystem } from '../StaffSystem/StaffSystem';
import { getInstrumentTemplate } from '../../utils/instrumentTemplates';
import { ScoreConfig } from '../../pages/NewScore/NewScorePage';
import { v4 as uuidv4 } from 'uuid';

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(255,255,255,0.95);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
`;

const EditorHeader = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const EditorTitle = styled.div`
  h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
  }
  
  input {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 8px;
    padding: 0.5rem;
    color: white;
    font-size: 1.1rem;
    width: 300px;
    
    &::placeholder {
      color: rgba(255,255,255,0.7);
    }
    
    &:focus {
      outline: none;
      border-color: rgba(255,255,255,0.6);
      background: rgba(255,255,255,0.3);
    }
  }
`;

const EditorActions = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.variant === 'primary' ? `
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    
    &:hover {
      background: rgba(255,255,255,0.3);
      transform: translateY(-1px);
    }
  ` : `
    background: rgba(255,255,255,0.9);
    color: #667eea;
    
    &:hover {
      background: white;
      transform: translateY(-1px);
    }
  `}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const EditorContent = styled.div`
  flex: 1;
  display: flex;
  overflow: hidden;
`;

const ToolPanel = styled.div`
  width: 250px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  padding: 1.5rem;
  overflow-y: auto;
`;

const ToolSection = styled.div`
  margin-bottom: 2rem;
  
  h3 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
  }
`;

const ToolGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
`;

const ToolButton = styled.button<{ active?: boolean }>`
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: ${props => props.active ? '#667eea' : 'white'};
  color: ${props => props.active ? 'white' : '#495057'};
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  
  &:hover {
    border-color: #667eea;
    ${props => !props.active && 'background: #f8f9fa;'}
  }
`;

const ScoreCanvas = styled.div`
  flex: 1;
  background: white;
  margin: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  position: relative;
  overflow: auto;
`;

const StaffContainer = styled.div`
  padding: 2rem;
  min-height: 400px;
`;

const Staff = styled.svg<{ staffCount?: number }>`
  width: 100%;
  height: ${props => props.staffCount === 2 ? '350px' : '200px'};
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  margin-bottom: 2rem;
`;



const GuestWarning = styled.div`
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  color: #856404;
  text-align: center;
  font-size: 0.9rem;
`;

interface ScoreEditorProps {
  scoreId?: string;
  initialConfig?: ScoreConfig;
}

export const ScoreEditor: React.FC<ScoreEditorProps> = ({ scoreId, initialConfig }) => {
  const { currentUser } = useAuth();
  const [title, setTitle] = useState(initialConfig?.title || 'Nova Partitura');
  const [selectedTool, setSelectedTool] = useState<'note' | 'rest' | 'chord'>('note');
  const [selectedDuration, setSelectedDuration] = useState<NoteDuration>('quarter');
  const [selectedNote, setSelectedNote] = useState<NoteName>('C');
  const [selectedAccidental, setSelectedAccidental] = useState<'sharp' | 'flat' | 'natural' | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [placedNotes, setPlacedNotes] = useState<MusicalNote[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showChords, setShowChords] = useState(false);
  const [showLyrics, setShowLyrics] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [lyrics, setLyrics] = useState<Lyrics[]>([]);
  const [selectedInstrument, setSelectedInstrument] = useState<InstrumentType>(initialConfig?.instrument || 'piano');

  // Carregar partitura existente
  React.useEffect(() => {
    if (scoreId && currentUser) {
      loadScore();
    }
  }, [scoreId, currentUser]);

  // Salvamento automático
  React.useEffect(() => {
    if (!currentUser || !scoreId || placedNotes.length === 0) return;

    const autoSaveTimer = setTimeout(() => {
      handleSave(true); // true indica salvamento automático
    }, 2000); // Salvar após 2 segundos de inatividade

    return () => clearTimeout(autoSaveTimer);
  }, [placedNotes, lyrics, title, currentUser, scoreId]);

  const loadScore = async () => {
    if (!scoreId || !currentUser) return;

    setIsLoading(true);
    try {
      const score = await ScoreService.getScore(scoreId);
      if (score && score.userId === currentUser.uid) {
        setTitle(score.title);

        // Extrair notas de todos os compassos
        const allNotes: MusicalNote[] = [];
        score.staffs.forEach(staff => {
          staff.measures.forEach(measure => {
            allNotes.push(...measure.notes);
          });
        });

        setPlacedNotes(allNotes);
        setLyrics(score.lyrics || []);
      }
    } catch (error) {
      console.error('Erro ao carregar partitura:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async (isAutoSave = false) => {
    if (!currentUser) {
      if (!isAutoSave) {
        alert('Você precisa estar logado para salvar partituras!');
      }
      return;
    }

    setIsSaving(true);
    try {
      // Organizar notas por compasso
      const measureMap = new Map<number, MusicalNote[]>();
      placedNotes.forEach(note => {
        const measure = note.position.measure;
        if (!measureMap.has(measure)) {
          measureMap.set(measure, []);
        }
        measureMap.get(measure)!.push(note);
      });

      // Criar compassos com as notas
      const measures = Array.from(measureMap.entries()).map(([measureNumber, notes]) => ({
        id: uuidv4(),
        number: measureNumber,
        timeSignature: { numerator: 4, denominator: 4 },
        notes: notes.sort((a, b) => a.position.beat - b.position.beat),
        chords: []
      }));

      // Criar dados da partitura
      const scoreData = {
        title,
        composer: initialConfig?.composer,
        key: {
          note: initialConfig?.key || 'C' as NoteName,
          mode: initialConfig?.keyMode || 'major' as const
        },
        timeSignature: {
          numerator: initialConfig?.timeSignatureNum || 4,
          denominator: initialConfig?.timeSignatureDen || 4
        },
        tempo: initialConfig?.tempo || 120,
        staffs: [{
          id: uuidv4(),
          clef: getInstrumentTemplate(selectedInstrument).clef,
          instrument: selectedInstrument,
          measures
        }],
        lyrics,
        userId: currentUser.uid
      };

      if (scoreId) {
        // Para update, removemos o userId pois não deve ser alterado
        const { userId, ...updateData } = scoreData;
        await ScoreService.updateScore(scoreId, updateData);
      } else {
        await ScoreService.createScore(scoreData, currentUser.uid);
      }

      setLastSaved(new Date());
      if (!isAutoSave) {
        alert('Partitura salva com sucesso!');
      }
    } catch (error) {
      console.error('Erro ao salvar:', error);
      if (!isAutoSave) {
        alert('Erro ao salvar partitura');
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handlePlay = () => {
    setIsPlaying(!isPlaying);
    // TODO: Implementar reprodução
  };

  const handleStaffClick = (event: React.MouseEvent<SVGRectElement>, staffIndex: number = 0) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const svgRect = event.currentTarget.closest('svg')?.getBoundingClientRect();

    if (!svgRect) return;

    const x = event.clientX - svgRect.left;
    const y = event.clientY - svgRect.top;

    // Calcular a linha da pauta baseada na posição Y
    const staffLines = [50, 70, 90, 110, 130]; // Posições das 5 linhas da pauta
    const notePositions = [
      { y: 40, note: 'A' as NoteName, octave: 5 as Octave }, // Acima da pauta
      { y: 50, note: 'G' as NoteName, octave: 5 as Octave }, // 5ª linha
      { y: 60, note: 'F' as NoteName, octave: 5 as Octave }, // Entre 4ª e 5ª
      { y: 70, note: 'E' as NoteName, octave: 5 as Octave }, // 4ª linha
      { y: 80, note: 'D' as NoteName, octave: 5 as Octave }, // Entre 3ª e 4ª
      { y: 90, note: 'C' as NoteName, octave: 5 as Octave }, // 3ª linha (Dó central)
      { y: 100, note: 'B' as NoteName, octave: 4 as Octave }, // Entre 2ª e 3ª
      { y: 110, note: 'A' as NoteName, octave: 4 as Octave }, // 2ª linha
      { y: 120, note: 'G' as NoteName, octave: 4 as Octave }, // Entre 1ª e 2ª
      { y: 130, note: 'F' as NoteName, octave: 4 as Octave }, // 1ª linha
      { y: 140, note: 'E' as NoteName, octave: 4 as Octave }, // Abaixo da pauta
    ];

    // Encontrar a posição mais próxima
    const closestPosition = notePositions.reduce((closest, current) => {
      return Math.abs(current.y - y) < Math.abs(closest.y - y) ? current : closest;
    });

    // Calcular a posição horizontal (compasso e tempo)
    const measureWidth = 150; // Largura aproximada de um compasso
    const startX = 120; // Início da área de notas
    const relativeX = x - startX;
    const measure = Math.max(1, Math.floor(relativeX / measureWidth) + 1);
    const beatPosition = ((relativeX % measureWidth) / measureWidth) * 4; // 4 tempos por compasso

    // Criar nova nota
    const newNote: MusicalNote = {
      id: uuidv4(),
      name: selectedNote, // Usar a nota selecionada no painel
      octave: closestPosition.octave,
      duration: selectedDuration,
      accidental: selectedAccidental || undefined,
      position: {
        measure,
        beat: Math.round(beatPosition * 4) / 4, // Arredondar para 1/4 de tempo
        staff: staffIndex
      },
      isRest: selectedTool === 'rest'
    };

    setPlacedNotes(prev => [...prev, newNote]);
  };

  const durations: NoteDuration[] = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];
  const notes: NoteName[] = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];

  if (isLoading) {
    return (
      <EditorContainer>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '400px',
          fontSize: '1.2rem',
          color: '#666'
        }}>
          🎼 Carregando partitura...
        </div>
      </EditorContainer>
    );
  }

  return (
    <EditorContainer>
      <EditorHeader>
        <EditorTitle>
          <h2>Editor de Partituras</h2>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Nome da partitura..."
            />
            {currentUser && lastSaved && (
              <div style={{ fontSize: '0.8rem', color: 'rgba(255,255,255,0.8)' }}>
                💾 Salvo às {lastSaved.toLocaleTimeString()}
              </div>
            )}
          </div>
        </EditorTitle>
        <EditorActions>
          <ActionButton
            onClick={() => setZoomLevel(prev => Math.max(0.5, prev - 0.1))}
            variant="primary"
            disabled={zoomLevel <= 0.5}
          >
            🔍➖
          </ActionButton>
          <ActionButton
            onClick={() => setZoomLevel(prev => Math.min(2, prev + 0.1))}
            variant="primary"
            disabled={zoomLevel >= 2}
          >
            🔍➕
          </ActionButton>
          <ActionButton
            onClick={() => {
              setShowChords(!showChords);
              setShowLyrics(false);
            }}
            variant="primary"
          >
            {showChords ? '🎼 Partitura' : '🎸 Cifras'}
          </ActionButton>
          <ActionButton
            onClick={() => {
              setShowLyrics(!showLyrics);
              setShowChords(false);
            }}
            variant="primary"
          >
            {showLyrics ? '🎼 Partitura' : '🎤 Letras'}
          </ActionButton>
          <ActionButton
            onClick={() => setPlacedNotes([])}
            variant="primary"
            disabled={placedNotes.length === 0}
          >
            🗑️ Limpar
          </ActionButton>
          <ActionButton onClick={handlePlay} variant="primary">
            {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}
          </ActionButton>
          <ActionButton
            onClick={() => handleSave()}
            variant="secondary"
            disabled={!currentUser || isSaving}
          >
            {isSaving ? '💾 Salvando...' : '💾 Salvar'}
          </ActionButton>
        </EditorActions>
      </EditorHeader>

      <EditorContent>
        <ToolPanel>
          {!currentUser && (
            <GuestWarning>
              ⚠️ Modo visitante: suas alterações não serão salvas
            </GuestWarning>
          )}

          <InstrumentSelector
            selectedInstrument={selectedInstrument}
            onInstrumentChange={setSelectedInstrument}
            compact={true}
          />
          
          <ToolSection>
            <h3>🎵 Ferramentas</h3>
            <ToolGrid>
              <ToolButton 
                active={selectedTool === 'note'}
                onClick={() => setSelectedTool('note')}
              >
                🎵 Nota
              </ToolButton>
              <ToolButton 
                active={selectedTool === 'rest'}
                onClick={() => setSelectedTool('rest')}
              >
                🎼 Pausa
              </ToolButton>
              <ToolButton 
                active={selectedTool === 'chord'}
                onClick={() => setSelectedTool('chord')}
              >
                🎹 Acorde
              </ToolButton>
            </ToolGrid>
          </ToolSection>

          <ToolSection>
            <h3>⏱️ Duração</h3>
            <ToolGrid>
              {durations.map(duration => (
                <ToolButton
                  key={duration}
                  active={selectedDuration === duration}
                  onClick={() => setSelectedDuration(duration)}
                >
                  {duration === 'whole' ? '𝅝' : 
                   duration === 'half' ? '𝅗𝅥' :
                   duration === 'quarter' ? '♩' :
                   duration === 'eighth' ? '♫' : '♬'}
                </ToolButton>
              ))}
            </ToolGrid>
          </ToolSection>

          <ToolSection>
            <h3>🎼 Notas</h3>
            <ToolGrid>
              {notes.map(note => (
                <ToolButton
                  key={note}
                  active={selectedNote === note}
                  onClick={() => setSelectedNote(note)}
                >
                  {note}
                </ToolButton>
              ))}
            </ToolGrid>
          </ToolSection>

          <ToolSection>
            <h3>♯♭ Acidentes</h3>
            <ToolGrid>
              <ToolButton
                active={selectedAccidental === 'sharp'}
                onClick={() => setSelectedAccidental(selectedAccidental === 'sharp' ? null : 'sharp')}
              >
                ♯
              </ToolButton>
              <ToolButton
                active={selectedAccidental === 'flat'}
                onClick={() => setSelectedAccidental(selectedAccidental === 'flat' ? null : 'flat')}
              >
                ♭
              </ToolButton>
              <ToolButton
                active={selectedAccidental === 'natural'}
                onClick={() => setSelectedAccidental(selectedAccidental === 'natural' ? null : 'natural')}
              >
                ♮
              </ToolButton>
              <ToolButton
                active={selectedAccidental === null}
                onClick={() => setSelectedAccidental(null)}
              >
                —
              </ToolButton>
            </ToolGrid>
          </ToolSection>

          <ToolSection>
            <h3>📊 Estatísticas</h3>
            <div style={{ fontSize: '0.9rem', color: '#666', lineHeight: '1.4' }}>
              <div>🎼 Instrumento: {getInstrumentTemplate(selectedInstrument).name}</div>
              <div>📝 Notas: {placedNotes.filter(n => !n.isRest).length}</div>
              <div>⏸️ Pausas: {placedNotes.filter(n => n.isRest).length}</div>
              <div>🎵 Total: {placedNotes.length}</div>
              <div>📏 Compassos: {Math.max(0, ...placedNotes.map(n => n.position.measure), 0)}</div>
              <div>♯ Sustenidos: {placedNotes.filter(n => n.accidental === 'sharp').length}</div>
              <div>♭ Bemóis: {placedNotes.filter(n => n.accidental === 'flat').length}</div>
              <div>🎤 Letras: {lyrics.length}</div>
              <div>🔍 Zoom: {Math.round(zoomLevel * 100)}%</div>
            </div>
          </ToolSection>
        </ToolPanel>

        <ScoreCanvas>
          {showChords ? (
            <ChordView notes={placedNotes} title={title} />
          ) : showLyrics ? (
            <LyricsEditor
              lyrics={lyrics}
              notes={placedNotes}
              onLyricsChange={setLyrics}
              title={title}
            />
          ) : (
            <StaffContainer>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                <h3 style={{ margin: 0, color: '#495057' }}>Partitura: {title}</h3>
                <div style={{ fontSize: '0.9rem', color: '#666' }}>
                  Zoom: {Math.round(zoomLevel * 100)}%
                </div>
              </div>
              <StaffSystem
                instrument={selectedInstrument}
                notes={placedNotes}
                onStaffClick={handleStaffClick}
                onNoteRemove={(noteId: string) => setPlacedNotes(prev => prev.filter(n => n.id !== noteId))}
                zoomLevel={zoomLevel}
              />
          </StaffContainer>
          )}
        </ScoreCanvas>
      </EditorContent>
    </EditorContainer>
  );
};
