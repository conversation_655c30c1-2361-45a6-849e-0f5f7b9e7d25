import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { Score, MusicalNote, NoteName, NoteDuration, ClefType, InstrumentType } from '../../types/music';
import { ScoreService } from '../../services/scoreService';
import { v4 as uuidv4 } from 'uuid';

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(255,255,255,0.95);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
`;

const EditorHeader = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const EditorTitle = styled.div`
  h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
  }
  
  input {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    border-radius: 8px;
    padding: 0.5rem;
    color: white;
    font-size: 1.1rem;
    width: 300px;
    
    &::placeholder {
      color: rgba(255,255,255,0.7);
    }
    
    &:focus {
      outline: none;
      border-color: rgba(255,255,255,0.6);
      background: rgba(255,255,255,0.3);
    }
  }
`;

const EditorActions = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;

const ActionButton = styled.button<{ variant?: 'primary' | 'secondary' }>`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.variant === 'primary' ? `
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    
    &:hover {
      background: rgba(255,255,255,0.3);
      transform: translateY(-1px);
    }
  ` : `
    background: rgba(255,255,255,0.9);
    color: #667eea;
    
    &:hover {
      background: white;
      transform: translateY(-1px);
    }
  `}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const EditorContent = styled.div`
  flex: 1;
  display: flex;
  overflow: hidden;
`;

const ToolPanel = styled.div`
  width: 250px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  padding: 1.5rem;
  overflow-y: auto;
`;

const ToolSection = styled.div`
  margin-bottom: 2rem;
  
  h3 {
    margin: 0 0 1rem 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
  }
`;

const ToolGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
`;

const ToolButton = styled.button<{ active?: boolean }>`
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: ${props => props.active ? '#667eea' : 'white'};
  color: ${props => props.active ? 'white' : '#495057'};
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  
  &:hover {
    border-color: #667eea;
    ${props => !props.active && 'background: #f8f9fa;'}
  }
`;

const ScoreCanvas = styled.div`
  flex: 1;
  background: white;
  margin: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  position: relative;
  overflow: auto;
`;

const StaffContainer = styled.div`
  padding: 2rem;
  min-height: 400px;
`;

const Staff = styled.svg`
  width: 100%;
  height: 200px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
  margin-bottom: 2rem;
`;

const GuestWarning = styled.div`
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  color: #856404;
  text-align: center;
  font-size: 0.9rem;
`;

interface ScoreEditorProps {
  scoreId?: string;
}

export const ScoreEditor: React.FC<ScoreEditorProps> = ({ scoreId }) => {
  const { currentUser } = useAuth();
  const [title, setTitle] = useState('Nova Partitura');
  const [selectedTool, setSelectedTool] = useState<'note' | 'rest' | 'chord'>('note');
  const [selectedDuration, setSelectedDuration] = useState<NoteDuration>('quarter');
  const [selectedNote, setSelectedNote] = useState<NoteName>('C');
  const [isPlaying, setIsPlaying] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    if (!currentUser) {
      alert('Você precisa estar logado para salvar partituras!');
      return;
    }

    setIsSaving(true);
    try {
      // Criar dados básicos da partitura
      const scoreData = {
        title,
        key: { note: 'C' as NoteName, mode: 'major' as const },
        timeSignature: { numerator: 4, denominator: 4 },
        tempo: 120,
        staffs: [{
          id: uuidv4(),
          clef: 'treble' as ClefType,
          instrument: 'piano' as InstrumentType,
          measures: []
        }],
        lyrics: []
      };

      if (scoreId) {
        await ScoreService.updateScore(scoreId, scoreData);
      } else {
        await ScoreService.createScore(scoreData, currentUser.uid);
      }
      
      alert('Partitura salva com sucesso!');
    } catch (error) {
      console.error('Erro ao salvar:', error);
      alert('Erro ao salvar partitura');
    } finally {
      setIsSaving(false);
    }
  };

  const handlePlay = () => {
    setIsPlaying(!isPlaying);
    // TODO: Implementar reprodução
  };

  const durations: NoteDuration[] = ['whole', 'half', 'quarter', 'eighth', 'sixteenth'];
  const notes: NoteName[] = ['C', 'D', 'E', 'F', 'G', 'A', 'B'];

  return (
    <EditorContainer>
      <EditorHeader>
        <EditorTitle>
          <h2>Editor de Partituras</h2>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Nome da partitura..."
          />
        </EditorTitle>
        <EditorActions>
          <ActionButton onClick={handlePlay} variant="primary">
            {isPlaying ? '⏸️ Pausar' : '▶️ Reproduzir'}
          </ActionButton>
          <ActionButton 
            onClick={handleSave} 
            variant="secondary"
            disabled={!currentUser || isSaving}
          >
            {isSaving ? '💾 Salvando...' : '💾 Salvar'}
          </ActionButton>
        </EditorActions>
      </EditorHeader>

      <EditorContent>
        <ToolPanel>
          {!currentUser && (
            <GuestWarning>
              ⚠️ Modo visitante: suas alterações não serão salvas
            </GuestWarning>
          )}
          
          <ToolSection>
            <h3>🎵 Ferramentas</h3>
            <ToolGrid>
              <ToolButton 
                active={selectedTool === 'note'}
                onClick={() => setSelectedTool('note')}
              >
                🎵 Nota
              </ToolButton>
              <ToolButton 
                active={selectedTool === 'rest'}
                onClick={() => setSelectedTool('rest')}
              >
                🎼 Pausa
              </ToolButton>
              <ToolButton 
                active={selectedTool === 'chord'}
                onClick={() => setSelectedTool('chord')}
              >
                🎹 Acorde
              </ToolButton>
            </ToolGrid>
          </ToolSection>

          <ToolSection>
            <h3>⏱️ Duração</h3>
            <ToolGrid>
              {durations.map(duration => (
                <ToolButton
                  key={duration}
                  active={selectedDuration === duration}
                  onClick={() => setSelectedDuration(duration)}
                >
                  {duration === 'whole' ? '𝅝' : 
                   duration === 'half' ? '𝅗𝅥' :
                   duration === 'quarter' ? '♩' :
                   duration === 'eighth' ? '♫' : '♬'}
                </ToolButton>
              ))}
            </ToolGrid>
          </ToolSection>

          <ToolSection>
            <h3>🎼 Notas</h3>
            <ToolGrid>
              {notes.map(note => (
                <ToolButton
                  key={note}
                  active={selectedNote === note}
                  onClick={() => setSelectedNote(note)}
                >
                  {note}
                </ToolButton>
              ))}
            </ToolGrid>
          </ToolSection>
        </ToolPanel>

        <ScoreCanvas>
          <StaffContainer>
            <h3 style={{ margin: '0 0 1rem 0', color: '#495057' }}>Partitura: {title}</h3>
            <Staff viewBox="0 0 800 200">
              {/* Linhas da pauta */}
              {[0, 1, 2, 3, 4].map(line => (
                <line
                  key={line}
                  x1="50"
                  y1={50 + line * 20}
                  x2="750"
                  y2={50 + line * 20}
                  stroke="#333"
                  strokeWidth="1"
                />
              ))}
              
              {/* Clave de Sol */}
              <text x="20" y="90" fontSize="40" fill="#333">𝄞</text>
              
              {/* Compasso 4/4 */}
              <text x="80" y="75" fontSize="16" fill="#333">4</text>
              <text x="80" y="95" fontSize="16" fill="#333">4</text>
              
              {/* Linha divisória */}
              <line x1="110" y1="50" x2="110" y2="130" stroke="#333" strokeWidth="2"/>
              
              {/* Área clicável para adicionar notas */}
              <rect 
                x="120" 
                y="40" 
                width="600" 
                height="100" 
                fill="transparent" 
                style={{ cursor: 'pointer' }}
                onClick={(e) => {
                  // TODO: Adicionar nota na posição clicada
                  console.log('Clicou para adicionar nota');
                }}
              />
              
              {/* Placeholder para notas */}
              <text x="400" y="100" fontSize="14" fill="#999" textAnchor="middle">
                Clique aqui para adicionar notas
              </text>
            </Staff>
          </StaffContainer>
        </ScoreCanvas>
      </EditorContent>
    </EditorContainer>
  );
};
