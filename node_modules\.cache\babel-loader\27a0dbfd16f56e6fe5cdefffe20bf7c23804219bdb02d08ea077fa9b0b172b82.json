{"ast": null, "code": "import React,{createContext,useContext,useEffect,useState}from'react';import{signInWithEmailAndPassword,createUserWithEmailAndPassword,signOut,onAuthStateChanged,updateProfile}from'firebase/auth';import{auth}from'../services/firebase';// Interface para o contexto de autenticação\nimport{jsx as _jsx}from\"react/jsx-runtime\";// Criar o contexto\nconst AuthContext=/*#__PURE__*/createContext(undefined);// Hook personalizado para usar o contexto\nexport const useAuth=()=>{const context=useContext(AuthContext);if(context===undefined){throw new Error('useAuth deve ser usado dentro de um AuthProvider');}return context;};// Provider do contexto de autenticação\nexport const AuthProvider=_ref=>{let{children}=_ref;const[currentUser,setCurrentUser]=useState(null);const[loading,setLoading]=useState(true);// Função para fazer login\nconst login=async(email,password)=>{await signInWithEmailAndPassword(auth,email,password);};// Função para registrar novo usuário\nconst register=async(email,password,displayName)=>{const{user}=await createUserWithEmailAndPassword(auth,email,password);await updateProfile(user,{displayName});};// Função para fazer logout\nconst logout=async()=>{await signOut(auth);};// Efeito para monitorar mudanças no estado de autenticação\nuseEffect(()=>{const unsubscribe=onAuthStateChanged(auth,user=>{setCurrentUser(user);setLoading(false);});return unsubscribe;},[]);const value={currentUser,login,register,logout,loading};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:!loading&&children});};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "signInWithEmailAndPassword", "createUserWithEmailAndPassword", "signOut", "onAuthStateChanged", "updateProfile", "auth", "jsx", "_jsx", "AuthContext", "undefined", "useAuth", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "currentUser", "setCurrentUser", "loading", "setLoading", "login", "email", "password", "register", "displayName", "user", "logout", "unsubscribe", "value", "Provider"], "sources": ["D:/Dev/partitura_digital/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { \n  User, \n  signInWithEmailAndPassword, \n  createUserWithEmailAndPassword, \n  signOut, \n  onAuthStateChanged,\n  updateProfile\n} from 'firebase/auth';\nimport { auth } from '../services/firebase';\n\n// Interface para o contexto de autenticação\ninterface AuthContextType {\n  currentUser: User | null;\n  login: (email: string, password: string) => Promise<void>;\n  register: (email: string, password: string, displayName: string) => Promise<void>;\n  logout: () => Promise<void>;\n  loading: boolean;\n}\n\n// Criar o contexto\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Hook personalizado para usar o contexto\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth deve ser usado dentro de um AuthProvider');\n  }\n  return context;\n};\n\n// Provider do contexto de autenticação\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Função para fazer login\n  const login = async (email: string, password: string): Promise<void> => {\n    await signInWithEmailAndPassword(auth, email, password);\n  };\n\n  // Função para registrar novo usuário\n  const register = async (email: string, password: string, displayName: string): Promise<void> => {\n    const { user } = await createUserWithEmailAndPassword(auth, email, password);\n    await updateProfile(user, { displayName });\n  };\n\n  // Função para fazer logout\n  const logout = async (): Promise<void> => {\n    await signOut(auth);\n  };\n\n  // Efeito para monitorar mudanças no estado de autenticação\n  useEffect(() => {\n    const unsubscribe = onAuthStateChanged(auth, (user) => {\n      setCurrentUser(user);\n      setLoading(false);\n    });\n\n    return unsubscribe;\n  }, []);\n\n  const value: AuthContextType = {\n    currentUser,\n    login,\n    register,\n    logout,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {!loading && children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAC7E,OAEEC,0BAA0B,CAC1BC,8BAA8B,CAC9BC,OAAO,CACPC,kBAAkB,CAClBC,aAAa,KACR,eAAe,CACtB,OAASC,IAAI,KAAQ,sBAAsB,CAE3C;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBASA;AACA,KAAM,CAAAC,WAAW,cAAGZ,aAAa,CAA8Ba,SAAS,CAAC,CAEzE;AACA,MAAO,MAAM,CAAAC,OAAO,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,OAAO,CAAGd,UAAU,CAACW,WAAW,CAAC,CACvC,GAAIG,OAAO,GAAKF,SAAS,CAAE,CACzB,KAAM,IAAI,CAAAG,KAAK,CAAC,kDAAkD,CAAC,CACrE,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,YAAqD,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAChF,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGlB,QAAQ,CAAc,IAAI,CAAC,CACjE,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGpB,QAAQ,CAAC,IAAI,CAAC,CAE5C;AACA,KAAM,CAAAqB,KAAK,CAAG,KAAAA,CAAOC,KAAa,CAAEC,QAAgB,GAAoB,CACtE,KAAM,CAAAtB,0BAA0B,CAACK,IAAI,CAAEgB,KAAK,CAAEC,QAAQ,CAAC,CACzD,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAAA,CAAOF,KAAa,CAAEC,QAAgB,CAAEE,WAAmB,GAAoB,CAC9F,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAAxB,8BAA8B,CAACI,IAAI,CAAEgB,KAAK,CAAEC,QAAQ,CAAC,CAC5E,KAAM,CAAAlB,aAAa,CAACqB,IAAI,CAAE,CAAED,WAAY,CAAC,CAAC,CAC5C,CAAC,CAED;AACA,KAAM,CAAAE,MAAM,CAAG,KAAAA,CAAA,GAA2B,CACxC,KAAM,CAAAxB,OAAO,CAACG,IAAI,CAAC,CACrB,CAAC,CAED;AACAP,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6B,WAAW,CAAGxB,kBAAkB,CAACE,IAAI,CAAGoB,IAAI,EAAK,CACrDR,cAAc,CAACQ,IAAI,CAAC,CACpBN,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CAEF,MAAO,CAAAQ,WAAW,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,KAAsB,CAAG,CAC7BZ,WAAW,CACXI,KAAK,CACLG,QAAQ,CACRG,MAAM,CACNR,OACF,CAAC,CAED,mBACEX,IAAA,CAACC,WAAW,CAACqB,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAb,QAAA,CAChC,CAACG,OAAO,EAAIH,QAAQ,CACD,CAAC,CAE3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}