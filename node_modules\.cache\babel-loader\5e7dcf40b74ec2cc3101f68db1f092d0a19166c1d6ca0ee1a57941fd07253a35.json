{"ast": null, "code": "import parse from './parse.js';\nimport { unsafeStringify } from './stringify.js';\nexport default function v1ToV6(uuid) {\n  const v1Bytes = typeof uuid === 'string' ? parse(uuid) : uuid;\n  const v6Bytes = _v1ToV6(v1Bytes);\n  return typeof uuid === 'string' ? unsafeStringify(v6Bytes) : v6Bytes;\n}\nfunction _v1ToV6(v1Bytes) {\n  return Uint8Array.of((v1Bytes[6] & 0x0f) << 4 | v1Bytes[7] >> 4 & 0x0f, (v1Bytes[7] & 0x0f) << 4 | (v1Bytes[4] & 0xf0) >> 4, (v1Bytes[4] & 0x0f) << 4 | (v1Bytes[5] & 0xf0) >> 4, (v1Bytes[5] & 0x0f) << 4 | (v1Bytes[0] & 0xf0) >> 4, (v1Bytes[0] & 0x0f) << 4 | (v1Bytes[1] & 0xf0) >> 4, (v1Bytes[1] & 0x0f) << 4 | (v1Bytes[2] & 0xf0) >> 4, 0x60 | v1Bytes[2] & 0x0f, v1Bytes[3], v1Bytes[8], v1Bytes[9], v1Bytes[10], v1Bytes[11], v1Bytes[12], v1Bytes[13], v1Bytes[14], v1Bytes[15]);\n}", "map": {"version": 3, "names": ["parse", "unsafeStringify", "v1ToV6", "uuid", "v1Bytes", "v6Bytes", "_v1ToV6", "Uint8Array", "of"], "sources": ["D:/Dev/partitura_digital/node_modules/uuid/dist/esm-browser/v1ToV6.js"], "sourcesContent": ["import parse from './parse.js';\nimport { unsafeStringify } from './stringify.js';\nexport default function v1ToV6(uuid) {\n    const v1Bytes = typeof uuid === 'string' ? parse(uuid) : uuid;\n    const v6Bytes = _v1ToV6(v1Bytes);\n    return typeof uuid === 'string' ? unsafeStringify(v6Bytes) : v6Bytes;\n}\nfunction _v1ToV6(v1Bytes) {\n    return Uint8Array.of(((v1Bytes[6] & 0x0f) << 4) | ((v1Bytes[7] >> 4) & 0x0f), ((v1Bytes[7] & 0x0f) << 4) | ((v1Bytes[4] & 0xf0) >> 4), ((v1Bytes[4] & 0x0f) << 4) | ((v1Bytes[5] & 0xf0) >> 4), ((v1Bytes[5] & 0x0f) << 4) | ((v1Bytes[0] & 0xf0) >> 4), ((v1Bytes[0] & 0x0f) << 4) | ((v1Bytes[1] & 0xf0) >> 4), ((v1Bytes[1] & 0x0f) << 4) | ((v1Bytes[2] & 0xf0) >> 4), 0x60 | (v1Bytes[2] & 0x0f), v1Bytes[3], v1Bytes[8], v1Bytes[9], v1Bytes[10], v1Bytes[11], v1Bytes[12], v1Bytes[13], v1Bytes[14], v1Bytes[15]);\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAC9B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,eAAe,SAASC,MAAMA,CAACC,IAAI,EAAE;EACjC,MAAMC,OAAO,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAGH,KAAK,CAACG,IAAI,CAAC,GAAGA,IAAI;EAC7D,MAAME,OAAO,GAAGC,OAAO,CAACF,OAAO,CAAC;EAChC,OAAO,OAAOD,IAAI,KAAK,QAAQ,GAAGF,eAAe,CAACI,OAAO,CAAC,GAAGA,OAAO;AACxE;AACA,SAASC,OAAOA,CAACF,OAAO,EAAE;EACtB,OAAOG,UAAU,CAACC,EAAE,CAAE,CAACJ,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAMA,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAI,IAAK,EAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAK,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,EAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAK,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,EAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAK,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,EAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAK,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,EAAG,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,GAAK,CAACA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,EAAE,IAAI,GAAIA,OAAO,CAAC,CAAC,CAAC,GAAG,IAAK,EAAEA,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,CAAC;AAC5f", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}