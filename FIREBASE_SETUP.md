# Configuração do Firebase para Partitura Digital

## Passo 1: Criar Projeto no Firebase

1. Acesse [Firebase Console](https://console.firebase.google.com/)
2. Clique em "Adicionar projeto"
3. Digite o nome do projeto: `partitura-digital`
4. Aceite os termos e continue
5. Desabilite o Google Analytics (opcional para este projeto)
6. Clique em "Criar projeto"

## Passo 2: Configurar Authentication

1. No painel do Firebase, clique em "Authentication"
2. Clique em "Começar"
3. V<PERSON> para a aba "Sign-in method"
4. Clique em "Email/senha"
5. Ative "Email/senha"
6. Clique em "Salvar"

## Passo 3: Configurar Firestore Database

1. No painel do Firebase, clique em "Firestore Database"
2. Clique em "Criar banco de dados"
3. Selecione "Iniciar no modo de teste"
4. Escolha uma localização próxima (ex: southamerica-east1)
5. Clique em "Concluído"

## Passo 4: Obter Configurações do Projeto

1. No painel do Firebase, clique no ícone de engrenagem ⚙️
2. Clique em "Configurações do projeto"
3. Role para baixo até "Seus aplicativos"
4. Clique no ícone "</>" (Web)
5. Digite o nome do app: `partitura-digital-web`
6. NÃO marque "Firebase Hosting"
7. Clique em "Registrar app"
8. Copie o objeto `firebaseConfig`

## Passo 5: Configurar o Projeto Local

1. Abra o arquivo `src/services/firebase.ts`
2. Substitua as configurações placeholder pelas suas:

```typescript
const firebaseConfig = {
  apiKey: "sua-api-key-aqui",
  authDomain: "seu-projeto.firebaseapp.com",
  projectId: "seu-projeto-id",
  storageBucket: "seu-projeto.appspot.com",
  messagingSenderId: "seu-sender-id",
  appId: "seu-app-id"
};
```

## Passo 6: Configurar Regras do Firestore

1. No Firestore Database, vá para "Regras"
2. Substitua as regras padrão por:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Regras para partituras - apenas usuários autenticados podem acessar suas próprias partituras
    match /scores/{scoreId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
  }
}
```

3. Clique em "Publicar"

## Passo 7: Testar a Aplicação

1. Execute `npm start` no terminal
2. Acesse `http://localhost:3000`
3. Teste o registro de um novo usuário
4. Teste o login com as credenciais criadas

## Estrutura de Dados no Firestore

O sistema criará automaticamente a coleção `scores` com a seguinte estrutura:

```
scores/
  {scoreId}/
    - id: string
    - title: string
    - composer: string (opcional)
    - key: { note: string, mode: string }
    - timeSignature: { numerator: number, denominator: number }
    - tempo: number (opcional)
    - staffs: array
    - lyrics: array
    - createdAt: timestamp
    - updatedAt: timestamp
    - userId: string
```

## Próximos Passos

Após configurar o Firebase, você pode:

1. Testar o sistema de autenticação
2. Começar a desenvolver o editor de partituras
3. Implementar as funcionalidades de salvamento
4. Adicionar mais instrumentos e recursos

## Solução de Problemas

- **Erro de CORS**: Certifique-se de que o domínio está autorizado no Firebase
- **Erro de autenticação**: Verifique se as configurações do Firebase estão corretas
- **Erro de permissão**: Verifique as regras do Firestore

Para mais ajuda, consulte a [documentação oficial do Firebase](https://firebase.google.com/docs).
