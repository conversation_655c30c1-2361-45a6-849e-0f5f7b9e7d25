import React from 'react';
import { MusicalNote, InstrumentType, ClefType } from '../../types/music';
import { MusicalNote as MusicalNoteComponent } from '../MusicalNote/MusicalNote';
import { getInstrumentTemplate } from '../../utils/instrumentTemplates';

interface StaffSystemProps {
  instrument: InstrumentType;
  notes: MusicalNote[];
  onStaffClick: (event: React.MouseEvent<SVGRectElement>, staffIndex: number) => void;
  onNoteRemove: (noteId: string) => void;
  zoomLevel: number;
}

export const StaffSystem: React.FC<StaffSystemProps> = ({
  instrument,
  notes,
  onStaffClick,
  onNoteRemove,
  zoomLevel
}) => {
  const template = getInstrumentTemplate(instrument);
  const staffCount = template.staffCount;
  
  // Configurações para diferentes tipos de pauta
  const getStaffConfig = (staffIndex: number) => {
    if (instrument === 'piano' && staffCount === 2) {
      return {
        clef: staffIndex === 0 ? 'treble' : 'bass',
        yOffset: staffIndex * 175,
        notePositions: staffIndex === 0 ? {
          // Clave de Sol (pauta superior)
          'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,
          'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140
        } : {
          // Clave de Fá (pauta inferior)
          'C4': 215, 'B3': 225, 'A3': 235, 'G3': 245, 'F3': 255,
          'E3': 265, 'D3': 275, 'C3': 285, 'B2': 295, 'A2': 305, 'G2': 315
        }
      };
    }
    
    // Configuração padrão para instrumentos de pauta única
    return {
      clef: template.clef,
      yOffset: 0,
      notePositions: {
        'A5': 40, 'G5': 50, 'F5': 60, 'E5': 70, 'D5': 80,
        'C5': 90, 'B4': 100, 'A4': 110, 'G4': 120, 'F4': 130, 'E4': 140
      }
    };
  };

  const renderStaff = (staffIndex: number) => {
    const config = getStaffConfig(staffIndex);
    const staffY = config.yOffset;
    
    return (
      <g key={staffIndex}>
        {/* Linhas da pauta */}
        {[0, 1, 2, 3, 4].map(line => (
          <line
            key={line}
            x1="50"
            y1={50 + staffY + line * 20}
            x2="750"
            y2={50 + staffY + line * 20}
            stroke="#333"
            strokeWidth="1"
          />
        ))}
        
        {/* Clave */}
        <text x="20" y={90 + staffY} fontSize="40" fill="#333">
          {config.clef === 'treble' ? '𝄞' : 
           config.clef === 'bass' ? '𝄢' : 
           config.clef === 'alto' ? '𝄡' : '𝄞'}
        </text>
        
        {/* Compasso (apenas na primeira pauta) */}
        {staffIndex === 0 && (
          <>
            <text x="80" y={75 + staffY} fontSize="16" fill="#333">4</text>
            <text x="80" y={95 + staffY} fontSize="16" fill="#333">4</text>
          </>
        )}
        
        {/* Linha divisória inicial */}
        <line 
          x1="110" 
          y1={50 + staffY} 
          x2="110" 
          y2={130 + staffY} 
          stroke="#333" 
          strokeWidth="2"
        />
        
        {/* Divisões de compasso */}
        {[1, 2, 3, 4].map(measure => (
          <line
            key={measure}
            x1={120 + (measure * 150)}
            y1={50 + staffY}
            x2={120 + (measure * 150)}
            y2={130 + staffY}
            stroke="#999"
            strokeWidth="1"
          />
        ))}
        
        {/* Números dos compassos (apenas na primeira pauta) */}
        {staffIndex === 0 && [1, 2, 3, 4].map(measure => (
          <text
            key={measure}
            x={120 + ((measure - 1) * 150) + 75}
            y="35"
            fontSize="12"
            fill="#666"
            textAnchor="middle"
          >
            {measure}
          </text>
        ))}
        
        {/* Área clicável para adicionar notas */}
        <rect 
          x="120" 
          y={40 + staffY} 
          width="600" 
          height="100" 
          fill="transparent" 
          style={{ cursor: 'crosshair' }}
          onClick={(e) => onStaffClick(e, staffIndex)}
        />
        
        {/* Renderizar notas desta pauta */}
        {notes
          .filter(note => note.position.staff === staffIndex)
          .map((note) => {
            const x = 120 + ((note.position.measure - 1) * 150) + (note.position.beat * 30);
            const noteKey = `${note.name}${note.octave}`;
            const y = (config.notePositions as any)[noteKey] || (90 + staffY);

            return (
              <MusicalNoteComponent
                key={note.id}
                note={note}
                x={x}
                y={y}
                onRemove={() => onNoteRemove(note.id)}
              />
            );
          })}
        
        {/* Instruções (apenas se não há notas) */}
        {notes.filter(n => n.position.staff === staffIndex).length === 0 && (
          <text 
            x="400" 
            y={100 + staffY} 
            fontSize="14" 
            fill="#999" 
            textAnchor="middle"
          >
            {staffIndex === 0 ? 'Clique na pauta para adicionar notas' : 
             staffCount === 2 ? 'Pauta inferior (mão esquerda)' : ''}
          </text>
        )}
        
        {/* Conectores entre pautas (para piano) */}
        {instrument === 'piano' && staffCount === 2 && staffIndex === 0 && (
          <>
            {/* Linha conectora esquerda */}
            <line
              x1="50"
              y1="50"
              x2="50"
              y2="305"
              stroke="#333"
              strokeWidth="3"
            />
            {/* Linha conectora direita */}
            <line
              x1="750"
              y1="50"
              x2="750"
              y2="305"
              stroke="#333"
              strokeWidth="1"
            />
          </>
        )}
      </g>
    );
  };

  const svgHeight = staffCount === 2 ? 350 : 200;

  return (
    <svg 
      viewBox={`0 0 800 ${svgHeight}`}
      style={{ 
        width: '100%',
        height: `${svgHeight}px`,
        border: '1px solid #e9ecef',
        borderRadius: '8px',
        background: 'white',
        marginBottom: '2rem',
        transform: `scale(${zoomLevel})`,
        transformOrigin: 'top left'
      }}
    >
      {Array.from({ length: staffCount }, (_, index) => renderStaff(index))}
      
      {/* Instruções gerais */}
      {notes.length === 0 && (
        <text x="400" y={svgHeight - 20} fontSize="12" fill="#999" textAnchor="middle">
          Clique em uma nota para removê-la • Use o painel lateral para selecionar ferramentas
        </text>
      )}
    </svg>
  );
};
