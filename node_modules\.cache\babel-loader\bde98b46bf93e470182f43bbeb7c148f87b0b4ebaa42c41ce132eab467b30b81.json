{"ast": null, "code": "import { collection, doc, addDoc, updateDoc, deleteDoc, getDocs, getDoc, query, where, orderBy, Timestamp } from 'firebase/firestore';\nimport { db } from './firebase';\nconst SCORES_COLLECTION = 'scores';\n\n// Converter Score para formato do Firestore\nconst scoreToFirestore = score => ({\n  ...score,\n  createdAt: Timestamp.fromDate(score.createdAt),\n  updatedAt: Timestamp.fromDate(score.updatedAt)\n});\n\n// Converter documento do Firestore para Score\nconst firestoreToScore = doc => ({\n  id: doc.id,\n  ...doc.data(),\n  createdAt: doc.data().createdAt.toDate(),\n  updatedAt: doc.data().updatedAt.toDate()\n});\nexport class ScoreService {\n  // Criar nova partitura\n  static async createScore(scoreData, userId) {\n    const now = new Date();\n    const score = {\n      ...scoreData,\n      userId,\n      createdAt: now,\n      updatedAt: now\n    };\n    const docRef = await addDoc(collection(db, SCORES_COLLECTION), scoreToFirestore(score));\n    return docRef.id;\n  }\n\n  // Atualizar partitura existente\n  static async updateScore(scoreId, updates) {\n    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);\n    const updateData = {\n      ...updates,\n      updatedAt: Timestamp.fromDate(new Date())\n    };\n    await updateDoc(scoreRef, updateData);\n  }\n\n  // Deletar partitura\n  static async deleteScore(scoreId) {\n    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);\n    await deleteDoc(scoreRef);\n  }\n\n  // Buscar partitura por ID\n  static async getScore(scoreId) {\n    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);\n    const scoreSnap = await getDoc(scoreRef);\n    if (scoreSnap.exists()) {\n      return firestoreToScore(scoreSnap);\n    }\n    return null;\n  }\n\n  // Buscar todas as partituras de um usuário\n  static async getUserScores(userId) {\n    const q = query(collection(db, SCORES_COLLECTION), where('userId', '==', userId), orderBy('updatedAt', 'desc'));\n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => firestoreToScore(doc));\n  }\n\n  // Buscar partituras por título (busca parcial)\n  static async searchScoresByTitle(userId, searchTerm) {\n    const q = query(collection(db, SCORES_COLLECTION), where('userId', '==', userId), orderBy('title'));\n    const querySnapshot = await getDocs(q);\n    const scores = querySnapshot.docs.map(doc => firestoreToScore(doc));\n\n    // Filtrar localmente por título (Firestore não suporta busca parcial nativa)\n    return scores.filter(score => score.title.toLowerCase().includes(searchTerm.toLowerCase()));\n  }\n\n  // Duplicar partitura\n  static async duplicateScore(scoreId, newTitle) {\n    const originalScore = await this.getScore(scoreId);\n    if (!originalScore) {\n      throw new Error('Partitura não encontrada');\n    }\n    const duplicatedScore = {\n      ...originalScore,\n      title: newTitle\n    };\n    return await this.createScore(duplicatedScore, originalScore.userId);\n  }\n}", "map": {"version": 3, "names": ["collection", "doc", "addDoc", "updateDoc", "deleteDoc", "getDocs", "getDoc", "query", "where", "orderBy", "Timestamp", "db", "SCORES_COLLECTION", "scoreToFirestore", "score", "createdAt", "fromDate", "updatedAt", "firestoreToScore", "id", "data", "toDate", "ScoreService", "createScore", "scoreData", "userId", "now", "Date", "doc<PERSON>ef", "updateScore", "scoreId", "updates", "scoreRef", "updateData", "deleteScore", "getScore", "scoreSnap", "exists", "getUserScores", "q", "querySnapshot", "docs", "map", "searchScoresByTitle", "searchTerm", "scores", "filter", "title", "toLowerCase", "includes", "duplicateScore", "newTitle", "originalScore", "Error", "duplicatedScore"], "sources": ["D:/Dev/partitura_digital/src/services/scoreService.ts"], "sourcesContent": ["import { \n  collection, \n  doc, \n  addDoc, \n  updateDoc, \n  deleteDoc, \n  getDocs, \n  getDoc, \n  query, \n  where, \n  orderBy, \n  Timestamp \n} from 'firebase/firestore';\nimport { db } from './firebase';\nimport { Score } from '../types/music';\n\nconst SCORES_COLLECTION = 'scores';\n\n// Converter Score para formato do Firestore\nconst scoreToFirestore = (score: Omit<Score, 'id'>) => ({\n  ...score,\n  createdAt: Timestamp.fromDate(score.createdAt),\n  updatedAt: Timestamp.fromDate(score.updatedAt)\n});\n\n// Converter documento do Firestore para Score\nconst firestoreToScore = (doc: any): Score => ({\n  id: doc.id,\n  ...doc.data(),\n  createdAt: doc.data().createdAt.toDate(),\n  updatedAt: doc.data().updatedAt.toDate()\n});\n\nexport class ScoreService {\n  // Criar nova partitura\n  static async createScore(scoreData: Omit<Score, 'id' | 'createdAt' | 'updatedAt'>, userId: string): Promise<string> {\n    const now = new Date();\n    const score: Omit<Score, 'id'> = {\n      ...scoreData,\n      userId,\n      createdAt: now,\n      updatedAt: now\n    };\n\n    const docRef = await addDoc(collection(db, SCORES_COLLECTION), scoreToFirestore(score));\n    return docRef.id;\n  }\n\n  // Atualizar partitura existente\n  static async updateScore(scoreId: string, updates: Partial<Omit<Score, 'id' | 'createdAt' | 'userId'>>): Promise<void> {\n    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);\n    const updateData = {\n      ...updates,\n      updatedAt: Timestamp.fromDate(new Date())\n    };\n    \n    await updateDoc(scoreRef, updateData);\n  }\n\n  // Deletar partitura\n  static async deleteScore(scoreId: string): Promise<void> {\n    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);\n    await deleteDoc(scoreRef);\n  }\n\n  // Buscar partitura por ID\n  static async getScore(scoreId: string): Promise<Score | null> {\n    const scoreRef = doc(db, SCORES_COLLECTION, scoreId);\n    const scoreSnap = await getDoc(scoreRef);\n    \n    if (scoreSnap.exists()) {\n      return firestoreToScore(scoreSnap);\n    }\n    \n    return null;\n  }\n\n  // Buscar todas as partituras de um usuário\n  static async getUserScores(userId: string): Promise<Score[]> {\n    const q = query(\n      collection(db, SCORES_COLLECTION),\n      where('userId', '==', userId),\n      orderBy('updatedAt', 'desc')\n    );\n    \n    const querySnapshot = await getDocs(q);\n    return querySnapshot.docs.map(doc => firestoreToScore(doc));\n  }\n\n  // Buscar partituras por título (busca parcial)\n  static async searchScoresByTitle(userId: string, searchTerm: string): Promise<Score[]> {\n    const q = query(\n      collection(db, SCORES_COLLECTION),\n      where('userId', '==', userId),\n      orderBy('title')\n    );\n    \n    const querySnapshot = await getDocs(q);\n    const scores = querySnapshot.docs.map(doc => firestoreToScore(doc));\n    \n    // Filtrar localmente por título (Firestore não suporta busca parcial nativa)\n    return scores.filter(score => \n      score.title.toLowerCase().includes(searchTerm.toLowerCase())\n    );\n  }\n\n  // Duplicar partitura\n  static async duplicateScore(scoreId: string, newTitle: string): Promise<string> {\n    const originalScore = await this.getScore(scoreId);\n    if (!originalScore) {\n      throw new Error('Partitura não encontrada');\n    }\n\n    const duplicatedScore: Omit<Score, 'id' | 'createdAt' | 'updatedAt'> = {\n      ...originalScore,\n      title: newTitle\n    };\n\n    return await this.createScore(duplicatedScore, originalScore.userId);\n  }\n}\n"], "mappings": "AAAA,SACEA,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,SAAS,QACJ,oBAAoB;AAC3B,SAASC,EAAE,QAAQ,YAAY;AAG/B,MAAMC,iBAAiB,GAAG,QAAQ;;AAElC;AACA,MAAMC,gBAAgB,GAAIC,KAAwB,KAAM;EACtD,GAAGA,KAAK;EACRC,SAAS,EAAEL,SAAS,CAACM,QAAQ,CAACF,KAAK,CAACC,SAAS,CAAC;EAC9CE,SAAS,EAAEP,SAAS,CAACM,QAAQ,CAACF,KAAK,CAACG,SAAS;AAC/C,CAAC,CAAC;;AAEF;AACA,MAAMC,gBAAgB,GAAIjB,GAAQ,KAAa;EAC7CkB,EAAE,EAAElB,GAAG,CAACkB,EAAE;EACV,GAAGlB,GAAG,CAACmB,IAAI,CAAC,CAAC;EACbL,SAAS,EAAEd,GAAG,CAACmB,IAAI,CAAC,CAAC,CAACL,SAAS,CAACM,MAAM,CAAC,CAAC;EACxCJ,SAAS,EAAEhB,GAAG,CAACmB,IAAI,CAAC,CAAC,CAACH,SAAS,CAACI,MAAM,CAAC;AACzC,CAAC,CAAC;AAEF,OAAO,MAAMC,YAAY,CAAC;EACxB;EACA,aAAaC,WAAWA,CAACC,SAAwD,EAAEC,MAAc,EAAmB;IAClH,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;IACtB,MAAMb,KAAwB,GAAG;MAC/B,GAAGU,SAAS;MACZC,MAAM;MACNV,SAAS,EAAEW,GAAG;MACdT,SAAS,EAAES;IACb,CAAC;IAED,MAAME,MAAM,GAAG,MAAM1B,MAAM,CAACF,UAAU,CAACW,EAAE,EAAEC,iBAAiB,CAAC,EAAEC,gBAAgB,CAACC,KAAK,CAAC,CAAC;IACvF,OAAOc,MAAM,CAACT,EAAE;EAClB;;EAEA;EACA,aAAaU,WAAWA,CAACC,OAAe,EAAEC,OAA4D,EAAiB;IACrH,MAAMC,QAAQ,GAAG/B,GAAG,CAACU,EAAE,EAAEC,iBAAiB,EAAEkB,OAAO,CAAC;IACpD,MAAMG,UAAU,GAAG;MACjB,GAAGF,OAAO;MACVd,SAAS,EAAEP,SAAS,CAACM,QAAQ,CAAC,IAAIW,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,MAAMxB,SAAS,CAAC6B,QAAQ,EAAEC,UAAU,CAAC;EACvC;;EAEA;EACA,aAAaC,WAAWA,CAACJ,OAAe,EAAiB;IACvD,MAAME,QAAQ,GAAG/B,GAAG,CAACU,EAAE,EAAEC,iBAAiB,EAAEkB,OAAO,CAAC;IACpD,MAAM1B,SAAS,CAAC4B,QAAQ,CAAC;EAC3B;;EAEA;EACA,aAAaG,QAAQA,CAACL,OAAe,EAAyB;IAC5D,MAAME,QAAQ,GAAG/B,GAAG,CAACU,EAAE,EAAEC,iBAAiB,EAAEkB,OAAO,CAAC;IACpD,MAAMM,SAAS,GAAG,MAAM9B,MAAM,CAAC0B,QAAQ,CAAC;IAExC,IAAII,SAAS,CAACC,MAAM,CAAC,CAAC,EAAE;MACtB,OAAOnB,gBAAgB,CAACkB,SAAS,CAAC;IACpC;IAEA,OAAO,IAAI;EACb;;EAEA;EACA,aAAaE,aAAaA,CAACb,MAAc,EAAoB;IAC3D,MAAMc,CAAC,GAAGhC,KAAK,CACbP,UAAU,CAACW,EAAE,EAAEC,iBAAiB,CAAC,EACjCJ,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAEiB,MAAM,CAAC,EAC7BhB,OAAO,CAAC,WAAW,EAAE,MAAM,CAC7B,CAAC;IAED,MAAM+B,aAAa,GAAG,MAAMnC,OAAO,CAACkC,CAAC,CAAC;IACtC,OAAOC,aAAa,CAACC,IAAI,CAACC,GAAG,CAACzC,GAAG,IAAIiB,gBAAgB,CAACjB,GAAG,CAAC,CAAC;EAC7D;;EAEA;EACA,aAAa0C,mBAAmBA,CAAClB,MAAc,EAAEmB,UAAkB,EAAoB;IACrF,MAAML,CAAC,GAAGhC,KAAK,CACbP,UAAU,CAACW,EAAE,EAAEC,iBAAiB,CAAC,EACjCJ,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAEiB,MAAM,CAAC,EAC7BhB,OAAO,CAAC,OAAO,CACjB,CAAC;IAED,MAAM+B,aAAa,GAAG,MAAMnC,OAAO,CAACkC,CAAC,CAAC;IACtC,MAAMM,MAAM,GAAGL,aAAa,CAACC,IAAI,CAACC,GAAG,CAACzC,GAAG,IAAIiB,gBAAgB,CAACjB,GAAG,CAAC,CAAC;;IAEnE;IACA,OAAO4C,MAAM,CAACC,MAAM,CAAChC,KAAK,IACxBA,KAAK,CAACiC,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACL,UAAU,CAACI,WAAW,CAAC,CAAC,CAC7D,CAAC;EACH;;EAEA;EACA,aAAaE,cAAcA,CAACpB,OAAe,EAAEqB,QAAgB,EAAmB;IAC9E,MAAMC,aAAa,GAAG,MAAM,IAAI,CAACjB,QAAQ,CAACL,OAAO,CAAC;IAClD,IAAI,CAACsB,aAAa,EAAE;MAClB,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;IAC7C;IAEA,MAAMC,eAA8D,GAAG;MACrE,GAAGF,aAAa;MAChBL,KAAK,EAAEI;IACT,CAAC;IAED,OAAO,MAAM,IAAI,CAAC5B,WAAW,CAAC+B,eAAe,EAAEF,aAAa,CAAC3B,MAAM,CAAC;EACtE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}