{"ast": null, "code": "import React,{useState}from'react';import styled from'styled-components';import{useAuth}from'../../contexts/AuthContext';import{Icons}from'../../utils/icons';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AuthContainer=styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 2rem;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n    opacity: 0.3;\n  }\n`;const AuthCard=styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-radius: 20px;\n  padding: 3rem;\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);\n  width: 100%;\n  max-width: 450px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  z-index: 1;\n`;const Logo=styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  font-size: 2rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n`;const Title=styled.h2`\n  text-align: center;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n  font-weight: 600;\n`;const Form=styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;const InputGroup=styled.div`\n  position: relative;\n`;const InputIcon=styled.div`\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #6b7280;\n`;const Input=styled.input`\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 3rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n  box-sizing: border-box;\n\n  &:focus {\n    outline: none;\n    border-color: #3b82f6;\n  }\n\n  &::placeholder {\n    color: #9ca3af;\n  }\n`;const PasswordToggle=styled.button`\n  position: absolute;\n  right: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 0.25rem;\n\n  &:hover {\n    color: #374151;\n  }\n`;const SubmitButton=styled.button`\n  width: 100%;\n  padding: 0.75rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: transform 0.2s;\n  margin-top: 1rem;\n\n  &:hover {\n    transform: translateY(-1px);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;const SwitchAuth=styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  color: #6b7280;\n`;const SwitchLink=styled.button`\n  background: none;\n  border: none;\n  color: #3b82f6;\n  cursor: pointer;\n  font-weight: 600;\n  text-decoration: underline;\n\n  &:hover {\n    color: #2563eb;\n  }\n`;const ErrorMessage=styled.div`\n  background-color: #fee2e2;\n  color: #dc2626;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;const SuccessMessage=styled.div`\n  background-color: #d1fae5;\n  color: #065f46;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;export const RegisterPage=_ref=>{let{onSwitchToLogin}=_ref;const[name,setName]=useState('');const[email,setEmail]=useState('');const[password,setPassword]=useState('');const[confirmPassword,setConfirmPassword]=useState('');const[showPassword,setShowPassword]=useState(false);const[showConfirmPassword,setShowConfirmPassword]=useState(false);const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');const{register}=useAuth();const handleSubmit=async e=>{e.preventDefault();if(!name||!email||!password||!confirmPassword){setError('Por favor, preencha todos os campos');return;}if(password!==confirmPassword){setError('As senhas não coincidem');return;}if(password.length<6){setError('A senha deve ter pelo menos 6 caracteres');return;}setLoading(true);setError('');setSuccess('');try{await register(email,password,name);setSuccess('Conta criada com sucesso! Você será redirecionado...');}catch(error){console.error('Erro no registro:',error);if(error.code==='auth/email-already-in-use'){setError('Este email já está em uso');}else if(error.code==='auth/weak-password'){setError('A senha é muito fraca');}else if(error.code==='auth/invalid-email'){setError('Email inválido');}else{setError('Erro ao criar conta. Tente novamente.');}}finally{setLoading(false);}};return/*#__PURE__*/_jsx(AuthContainer,{children:/*#__PURE__*/_jsxs(AuthCard,{children:[/*#__PURE__*/_jsxs(Logo,{children:[/*#__PURE__*/_jsx(Icons.Music,{size:32}),\"Partitura Digital\"]}),/*#__PURE__*/_jsx(Title,{children:\"Criar nova conta\"}),error&&/*#__PURE__*/_jsx(ErrorMessage,{children:error}),success&&/*#__PURE__*/_jsx(SuccessMessage,{children:success}),/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(InputIcon,{children:/*#__PURE__*/_jsx(Icons.User,{size:20})}),/*#__PURE__*/_jsx(Input,{type:\"text\",placeholder:\"Seu nome\",value:name,onChange:e=>setName(e.target.value),required:true})]}),/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(InputIcon,{children:/*#__PURE__*/_jsx(Icons.Mail,{size:20})}),/*#__PURE__*/_jsx(Input,{type:\"email\",placeholder:\"Seu email\",value:email,onChange:e=>setEmail(e.target.value),required:true})]}),/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(InputIcon,{children:/*#__PURE__*/_jsx(Icons.Lock,{size:20})}),/*#__PURE__*/_jsx(Input,{type:showPassword?'text':'password',placeholder:\"Sua senha\",value:password,onChange:e=>setPassword(e.target.value),required:true}),/*#__PURE__*/_jsx(PasswordToggle,{type:\"button\",onClick:()=>setShowPassword(!showPassword),children:showPassword?/*#__PURE__*/_jsx(Icons.EyeOff,{size:20}):/*#__PURE__*/_jsx(Icons.Eye,{size:20})})]}),/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(InputIcon,{children:/*#__PURE__*/_jsx(Icons.Lock,{size:20})}),/*#__PURE__*/_jsx(Input,{type:showConfirmPassword?'text':'password',placeholder:\"Confirme sua senha\",value:confirmPassword,onChange:e=>setConfirmPassword(e.target.value),required:true}),/*#__PURE__*/_jsx(PasswordToggle,{type:\"button\",onClick:()=>setShowConfirmPassword(!showConfirmPassword),children:showConfirmPassword?/*#__PURE__*/_jsx(Icons.EyeOff,{size:20}):/*#__PURE__*/_jsx(Icons.Eye,{size:20})})]}),/*#__PURE__*/_jsx(SubmitButton,{type:\"submit\",disabled:loading,children:loading?'Criando conta...':'Criar conta'})]}),/*#__PURE__*/_jsxs(SwitchAuth,{children:[\"J\\xE1 tem uma conta?\",' ',/*#__PURE__*/_jsx(SwitchLink,{onClick:onSwitchToLogin,children:\"Fazer login\"})]})]})});};", "map": {"version": 3, "names": ["React", "useState", "styled", "useAuth", "Icons", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "AuthCard", "Logo", "Title", "h2", "Form", "form", "InputGroup", "InputIcon", "Input", "input", "PasswordToggle", "button", "SubmitButton", "SwitchAuth", "SwitchLink", "ErrorMessage", "SuccessMessage", "RegisterPage", "_ref", "onSwitchToLogin", "name", "setName", "email", "setEmail", "password", "setPassword", "confirmPassword", "setConfirmPassword", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "loading", "setLoading", "error", "setError", "success", "setSuccess", "register", "handleSubmit", "e", "preventDefault", "length", "console", "code", "children", "Music", "size", "onSubmit", "User", "type", "placeholder", "value", "onChange", "target", "required", "Mail", "Lock", "onClick", "Eye<PERSON>ff", "Eye", "disabled"], "sources": ["D:/Dev/partitura_digital/src/pages/Auth/RegisterPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Icons } from '../../utils/icons';\n\nconst AuthContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 2rem;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n    opacity: 0.3;\n  }\n`;\n\nconst AuthCard = styled.div`\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  border-radius: 20px;\n  padding: 3rem;\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);\n  width: 100%;\n  max-width: 450px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  z-index: 1;\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  font-size: 2rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n`;\n\nconst Title = styled.h2`\n  text-align: center;\n  color: #2c3e50;\n  margin-bottom: 2rem;\n  font-weight: 600;\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n`;\n\nconst InputGroup = styled.div`\n  position: relative;\n`;\n\nconst InputIcon = styled.div`\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #6b7280;\n`;\n\nconst Input = styled.input`\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 3rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n  box-sizing: border-box;\n\n  &:focus {\n    outline: none;\n    border-color: #3b82f6;\n  }\n\n  &::placeholder {\n    color: #9ca3af;\n  }\n`;\n\nconst PasswordToggle = styled.button`\n  position: absolute;\n  right: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  background: none;\n  border: none;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 0.25rem;\n\n  &:hover {\n    color: #374151;\n  }\n`;\n\nconst SubmitButton = styled.button`\n  width: 100%;\n  padding: 0.75rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 0.5rem;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: transform 0.2s;\n  margin-top: 1rem;\n\n  &:hover {\n    transform: translateY(-1px);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n`;\n\nconst SwitchAuth = styled.div`\n  text-align: center;\n  margin-top: 1.5rem;\n  color: #6b7280;\n`;\n\nconst SwitchLink = styled.button`\n  background: none;\n  border: none;\n  color: #3b82f6;\n  cursor: pointer;\n  font-weight: 600;\n  text-decoration: underline;\n\n  &:hover {\n    color: #2563eb;\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  background-color: #fee2e2;\n  color: #dc2626;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;\n\nconst SuccessMessage = styled.div`\n  background-color: #d1fae5;\n  color: #065f46;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n  text-align: center;\n  font-size: 0.875rem;\n`;\n\ninterface RegisterPageProps {\n  onSwitchToLogin: () => void;\n}\n\nexport const RegisterPage: React.FC<RegisterPageProps> = ({ onSwitchToLogin }) => {\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  const { register } = useAuth();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!name || !email || !password || !confirmPassword) {\n      setError('Por favor, preencha todos os campos');\n      return;\n    }\n\n    if (password !== confirmPassword) {\n      setError('As senhas não coincidem');\n      return;\n    }\n\n    if (password.length < 6) {\n      setError('A senha deve ter pelo menos 6 caracteres');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      await register(email, password, name);\n      setSuccess('Conta criada com sucesso! Você será redirecionado...');\n    } catch (error: any) {\n      console.error('Erro no registro:', error);\n      if (error.code === 'auth/email-already-in-use') {\n        setError('Este email já está em uso');\n      } else if (error.code === 'auth/weak-password') {\n        setError('A senha é muito fraca');\n      } else if (error.code === 'auth/invalid-email') {\n        setError('Email inválido');\n      } else {\n        setError('Erro ao criar conta. Tente novamente.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <AuthContainer>\n      <AuthCard>\n        <Logo>\n          <Icons.Music size={32} />\n          Partitura Digital\n        </Logo>\n\n        <Title>Criar nova conta</Title>\n\n        {error && <ErrorMessage>{error}</ErrorMessage>}\n        {success && <SuccessMessage>{success}</SuccessMessage>}\n\n        <Form onSubmit={handleSubmit}>\n          <InputGroup>\n            <InputIcon>\n              <Icons.User size={20} />\n            </InputIcon>\n            <Input\n              type=\"text\"\n              placeholder=\"Seu nome\"\n              value={name}\n              onChange={(e) => setName(e.target.value)}\n              required\n            />\n          </InputGroup>\n\n          <InputGroup>\n            <InputIcon>\n              <Icons.Mail size={20} />\n            </InputIcon>\n            <Input\n              type=\"email\"\n              placeholder=\"Seu email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n            />\n          </InputGroup>\n\n          <InputGroup>\n            <InputIcon>\n              <Icons.Lock size={20} />\n            </InputIcon>\n            <Input\n              type={showPassword ? 'text' : 'password'}\n              placeholder=\"Sua senha\"\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              required\n            />\n            <PasswordToggle\n              type=\"button\"\n              onClick={() => setShowPassword(!showPassword)}\n            >\n              {showPassword ? <Icons.EyeOff size={20} /> : <Icons.Eye size={20} />}\n            </PasswordToggle>\n          </InputGroup>\n\n          <InputGroup>\n            <InputIcon>\n              <Icons.Lock size={20} />\n            </InputIcon>\n            <Input\n              type={showConfirmPassword ? 'text' : 'password'}\n              placeholder=\"Confirme sua senha\"\n              value={confirmPassword}\n              onChange={(e) => setConfirmPassword(e.target.value)}\n              required\n            />\n            <PasswordToggle\n              type=\"button\"\n              onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n            >\n              {showConfirmPassword ? <Icons.EyeOff size={20} /> : <Icons.Eye size={20} />}\n            </PasswordToggle>\n          </InputGroup>\n\n          <SubmitButton type=\"submit\" disabled={loading}>\n            {loading ? 'Criando conta...' : 'Criar conta'}\n          </SubmitButton>\n        </Form>\n\n        <SwitchAuth>\n          Já tem uma conta?{' '}\n          <SwitchLink onClick={onSwitchToLogin}>\n            Fazer login\n          </SwitchLink>\n        </SwitchAuth>\n      </AuthCard>\n    </AuthContainer>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,KAAK,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1C,KAAM,CAAAC,aAAa,CAAGP,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGT,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAE,IAAI,CAAGV,MAAM,CAACQ,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAG,KAAK,CAAGX,MAAM,CAACY,EAAE;AACvB;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,IAAI,CAAGb,MAAM,CAACc,IAAI;AACxB;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGf,MAAM,CAACQ,GAAG;AAC7B;AACA,CAAC,CAED,KAAM,CAAAQ,SAAS,CAAGhB,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAS,KAAK,CAAGjB,MAAM,CAACkB,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGnB,MAAM,CAACoB,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGrB,MAAM,CAACoB,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAE,UAAU,CAAGtB,MAAM,CAACQ,GAAG;AAC7B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAe,UAAU,CAAGvB,MAAM,CAACoB,MAAM;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAI,YAAY,CAAGxB,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAiB,cAAc,CAAGzB,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAMD,MAAO,MAAM,CAAAkB,YAAyC,CAAGC,IAAA,EAAyB,IAAxB,CAAEC,eAAgB,CAAC,CAAAD,IAAA,CAC3E,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACgC,KAAK,CAAEC,QAAQ,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACkC,QAAQ,CAAEC,WAAW,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACoC,eAAe,CAAEC,kBAAkB,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACsC,YAAY,CAAEC,eAAe,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACwC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAAC0C,OAAO,CAAEC,UAAU,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC4C,KAAK,CAAEC,QAAQ,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC8C,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAE1C,KAAM,CAAEgD,QAAS,CAAC,CAAG9C,OAAO,CAAC,CAAC,CAE9B,KAAM,CAAA+C,YAAY,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACjDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,GAAI,CAACrB,IAAI,EAAI,CAACE,KAAK,EAAI,CAACE,QAAQ,EAAI,CAACE,eAAe,CAAE,CACpDS,QAAQ,CAAC,qCAAqC,CAAC,CAC/C,OACF,CAEA,GAAIX,QAAQ,GAAKE,eAAe,CAAE,CAChCS,QAAQ,CAAC,yBAAyB,CAAC,CACnC,OACF,CAEA,GAAIX,QAAQ,CAACkB,MAAM,CAAG,CAAC,CAAE,CACvBP,QAAQ,CAAC,0CAA0C,CAAC,CACpD,OACF,CAEAF,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAChB,KAAK,CAAEE,QAAQ,CAAEJ,IAAI,CAAC,CACrCiB,UAAU,CAAC,sDAAsD,CAAC,CACpE,CAAE,MAAOH,KAAU,CAAE,CACnBS,OAAO,CAACT,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CACzC,GAAIA,KAAK,CAACU,IAAI,GAAK,2BAA2B,CAAE,CAC9CT,QAAQ,CAAC,2BAA2B,CAAC,CACvC,CAAC,IAAM,IAAID,KAAK,CAACU,IAAI,GAAK,oBAAoB,CAAE,CAC9CT,QAAQ,CAAC,uBAAuB,CAAC,CACnC,CAAC,IAAM,IAAID,KAAK,CAACU,IAAI,GAAK,oBAAoB,CAAE,CAC9CT,QAAQ,CAAC,gBAAgB,CAAC,CAC5B,CAAC,IAAM,CACLA,QAAQ,CAAC,uCAAuC,CAAC,CACnD,CACF,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACEtC,IAAA,CAACG,aAAa,EAAA+C,QAAA,cACZhD,KAAA,CAACG,QAAQ,EAAA6C,QAAA,eACPhD,KAAA,CAACI,IAAI,EAAA4C,QAAA,eACHlD,IAAA,CAACF,KAAK,CAACqD,KAAK,EAACC,IAAI,CAAE,EAAG,CAAE,CAAC,oBAE3B,EAAM,CAAC,cAEPpD,IAAA,CAACO,KAAK,EAAA2C,QAAA,CAAC,kBAAgB,CAAO,CAAC,CAE9BX,KAAK,eAAIvC,IAAA,CAACoB,YAAY,EAAA8B,QAAA,CAAEX,KAAK,CAAe,CAAC,CAC7CE,OAAO,eAAIzC,IAAA,CAACqB,cAAc,EAAA6B,QAAA,CAAET,OAAO,CAAiB,CAAC,cAEtDvC,KAAA,CAACO,IAAI,EAAC4C,QAAQ,CAAET,YAAa,CAAAM,QAAA,eAC3BhD,KAAA,CAACS,UAAU,EAAAuC,QAAA,eACTlD,IAAA,CAACY,SAAS,EAAAsC,QAAA,cACRlD,IAAA,CAACF,KAAK,CAACwD,IAAI,EAACF,IAAI,CAAE,EAAG,CAAE,CAAC,CACf,CAAC,cACZpD,IAAA,CAACa,KAAK,EACJ0C,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtBC,KAAK,CAAEhC,IAAK,CACZiC,QAAQ,CAAGb,CAAC,EAAKnB,OAAO,CAACmB,CAAC,CAACc,MAAM,CAACF,KAAK,CAAE,CACzCG,QAAQ,MACT,CAAC,EACQ,CAAC,cAEb1D,KAAA,CAACS,UAAU,EAAAuC,QAAA,eACTlD,IAAA,CAACY,SAAS,EAAAsC,QAAA,cACRlD,IAAA,CAACF,KAAK,CAAC+D,IAAI,EAACT,IAAI,CAAE,EAAG,CAAE,CAAC,CACf,CAAC,cACZpD,IAAA,CAACa,KAAK,EACJ0C,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAE9B,KAAM,CACb+B,QAAQ,CAAGb,CAAC,EAAKjB,QAAQ,CAACiB,CAAC,CAACc,MAAM,CAACF,KAAK,CAAE,CAC1CG,QAAQ,MACT,CAAC,EACQ,CAAC,cAEb1D,KAAA,CAACS,UAAU,EAAAuC,QAAA,eACTlD,IAAA,CAACY,SAAS,EAAAsC,QAAA,cACRlD,IAAA,CAACF,KAAK,CAACgE,IAAI,EAACV,IAAI,CAAE,EAAG,CAAE,CAAC,CACf,CAAC,cACZpD,IAAA,CAACa,KAAK,EACJ0C,IAAI,CAAEtB,YAAY,CAAG,MAAM,CAAG,UAAW,CACzCuB,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAE5B,QAAS,CAChB6B,QAAQ,CAAGb,CAAC,EAAKf,WAAW,CAACe,CAAC,CAACc,MAAM,CAACF,KAAK,CAAE,CAC7CG,QAAQ,MACT,CAAC,cACF5D,IAAA,CAACe,cAAc,EACbwC,IAAI,CAAC,QAAQ,CACbQ,OAAO,CAAEA,CAAA,GAAM7B,eAAe,CAAC,CAACD,YAAY,CAAE,CAAAiB,QAAA,CAE7CjB,YAAY,cAAGjC,IAAA,CAACF,KAAK,CAACkE,MAAM,EAACZ,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGpD,IAAA,CAACF,KAAK,CAACmE,GAAG,EAACb,IAAI,CAAE,EAAG,CAAE,CAAC,CACtD,CAAC,EACP,CAAC,cAEblD,KAAA,CAACS,UAAU,EAAAuC,QAAA,eACTlD,IAAA,CAACY,SAAS,EAAAsC,QAAA,cACRlD,IAAA,CAACF,KAAK,CAACgE,IAAI,EAACV,IAAI,CAAE,EAAG,CAAE,CAAC,CACf,CAAC,cACZpD,IAAA,CAACa,KAAK,EACJ0C,IAAI,CAAEpB,mBAAmB,CAAG,MAAM,CAAG,UAAW,CAChDqB,WAAW,CAAC,oBAAoB,CAChCC,KAAK,CAAE1B,eAAgB,CACvB2B,QAAQ,CAAGb,CAAC,EAAKb,kBAAkB,CAACa,CAAC,CAACc,MAAM,CAACF,KAAK,CAAE,CACpDG,QAAQ,MACT,CAAC,cACF5D,IAAA,CAACe,cAAc,EACbwC,IAAI,CAAC,QAAQ,CACbQ,OAAO,CAAEA,CAAA,GAAM3B,sBAAsB,CAAC,CAACD,mBAAmB,CAAE,CAAAe,QAAA,CAE3Df,mBAAmB,cAAGnC,IAAA,CAACF,KAAK,CAACkE,MAAM,EAACZ,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGpD,IAAA,CAACF,KAAK,CAACmE,GAAG,EAACb,IAAI,CAAE,EAAG,CAAE,CAAC,CAC7D,CAAC,EACP,CAAC,cAEbpD,IAAA,CAACiB,YAAY,EAACsC,IAAI,CAAC,QAAQ,CAACW,QAAQ,CAAE7B,OAAQ,CAAAa,QAAA,CAC3Cb,OAAO,CAAG,kBAAkB,CAAG,aAAa,CACjC,CAAC,EACX,CAAC,cAEPnC,KAAA,CAACgB,UAAU,EAAAgC,QAAA,EAAC,sBACO,CAAC,GAAG,cACrBlD,IAAA,CAACmB,UAAU,EAAC4C,OAAO,CAAEvC,eAAgB,CAAA0B,QAAA,CAAC,aAEtC,CAAY,CAAC,EACH,CAAC,EACL,CAAC,CACE,CAAC,CAEpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}