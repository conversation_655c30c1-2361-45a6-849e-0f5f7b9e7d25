{"ast": null, "code": "import React,{useState}from'react';import styled from'styled-components';import{v4 as uuidv4}from'uuid';import{jsxs as _jsxs,jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";const LyricsContainer=styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;const LyricsHeader=styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n`;const LyricsTitle=styled.h3`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n`;const AddLyricButton=styled.button`\n  padding: 0.5rem 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n  }\n`;const LyricsGrid=styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n`;const LyricCard=styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n  position: relative;\n`;const LyricPosition=styled.div`\n  font-size: 0.8rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;const LyricText=styled.input`\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #dee2e6;\n  border-radius: 6px;\n  font-size: 1rem;\n  margin-bottom: 0.5rem;\n  box-sizing: border-box;\n  \n  &:focus {\n    outline: none;\n    border-color: #667eea;\n  }\n`;const LyricActions=styled.div`\n  display: flex;\n  gap: 0.5rem;\n`;const ActionButton=styled.button`\n  padding: 0.25rem 0.5rem;\n  border: none;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props=>props.variant==='danger'?`\n    background: #e74c3c;\n    color: white;\n    &:hover { background: #c0392b; }\n  `:`\n    background: #6c757d;\n    color: white;\n    &:hover { background: #5a6268; }\n  `}\n`;const AddLyricForm=styled.div`\n  background: #e3f2fd;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  border: 2px solid #bbdefb;\n`;const FormRow=styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  align-items: end;\n`;const FormGroup=styled.div`\n  flex: 1;\n  \n  label {\n    display: block;\n    margin-bottom: 0.25rem;\n    font-size: 0.9rem;\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input, select {\n    width: 100%;\n    padding: 0.5rem;\n    border: 1px solid #ced4da;\n    border-radius: 6px;\n    font-size: 0.9rem;\n    box-sizing: border-box;\n    \n    &:focus {\n      outline: none;\n      border-color: #667eea;\n    }\n  }\n`;const FormActions=styled.div`\n  display: flex;\n  gap: 0.5rem;\n`;const FormButton=styled.button`\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 6px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props=>props.variant==='primary'?`\n    background: #667eea;\n    color: white;\n    &:hover { background: #5a67d8; }\n  `:`\n    background: #6c757d;\n    color: white;\n    &:hover { background: #5a6268; }\n  `}\n`;const EmptyState=styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;const LyricsPreview=styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  border: 2px solid #e9ecef;\n  margin-top: 1rem;\n`;const PreviewTitle=styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;const PreviewText=styled.div`\n  line-height: 1.8;\n  font-size: 1.1rem;\n  color: #333;\n  white-space: pre-wrap;\n`;export const LyricsEditor=_ref=>{let{lyrics,notes,onLyricsChange,title}=_ref;const[showAddForm,setShowAddForm]=useState(false);const[newLyric,setNewLyric]=useState({text:'',measure:1,beat:1});const handleAddLyric=()=>{if(!newLyric.text.trim())return;const lyric={id:uuidv4(),text:newLyric.text.trim(),position:{measure:newLyric.measure,beat:newLyric.beat}};onLyricsChange([...lyrics,lyric]);setNewLyric({text:'',measure:1,beat:1});setShowAddForm(false);};const handleUpdateLyric=(id,text)=>{const updatedLyrics=lyrics.map(lyric=>lyric.id===id?{...lyric,text}:lyric);onLyricsChange(updatedLyrics);};const handleDeleteLyric=id=>{const updatedLyrics=lyrics.filter(lyric=>lyric.id!==id);onLyricsChange(updatedLyrics);};// Ordenar letras por posição\nconst sortedLyrics=[...lyrics].sort((a,b)=>{if(a.position.measure!==b.position.measure){return a.position.measure-b.position.measure;}return a.position.beat-b.position.beat;});// Gerar preview das letras\nconst generatePreview=()=>{return sortedLyrics.map(lyric=>lyric.text).join(' ');};// Obter compassos disponíveis baseado nas notas\nconst availableMeasures=notes.length>0?Array.from(new Set(notes.map(note=>note.position.measure))).sort((a,b)=>a-b):[1];return/*#__PURE__*/_jsxs(LyricsContainer,{children:[/*#__PURE__*/_jsxs(LyricsHeader,{children:[/*#__PURE__*/_jsxs(LyricsTitle,{children:[\"\\uD83C\\uDFA4 Letras - \",title]}),/*#__PURE__*/_jsx(AddLyricButton,{onClick:()=>setShowAddForm(!showAddForm),children:showAddForm?'❌ Cancelar':'➕ Adicionar Letra'})]}),showAddForm&&/*#__PURE__*/_jsxs(AddLyricForm,{children:[/*#__PURE__*/_jsxs(FormRow,{children:[/*#__PURE__*/_jsxs(FormGroup,{children:[/*#__PURE__*/_jsx(\"label\",{children:\"Texto da Letra:\"}),/*#__PURE__*/_jsx(LyricText,{value:newLyric.text,onChange:e=>setNewLyric({...newLyric,text:e.target.value}),placeholder:\"Digite a palavra ou frase...\",autoFocus:true})]}),/*#__PURE__*/_jsxs(FormGroup,{style:{flex:'0 0 120px'},children:[/*#__PURE__*/_jsx(\"label\",{children:\"Compasso:\"}),/*#__PURE__*/_jsx(\"select\",{value:newLyric.measure,onChange:e=>setNewLyric({...newLyric,measure:parseInt(e.target.value)}),children:availableMeasures.map(measure=>/*#__PURE__*/_jsx(\"option\",{value:measure,children:measure},measure))})]}),/*#__PURE__*/_jsxs(FormGroup,{style:{flex:'0 0 100px'},children:[/*#__PURE__*/_jsx(\"label\",{children:\"Tempo:\"}),/*#__PURE__*/_jsxs(\"select\",{value:newLyric.beat,onChange:e=>setNewLyric({...newLyric,beat:parseInt(e.target.value)}),children:[/*#__PURE__*/_jsx(\"option\",{value:1,children:\"1\"}),/*#__PURE__*/_jsx(\"option\",{value:2,children:\"2\"}),/*#__PURE__*/_jsx(\"option\",{value:3,children:\"3\"}),/*#__PURE__*/_jsx(\"option\",{value:4,children:\"4\"})]})]})]}),/*#__PURE__*/_jsxs(FormActions,{children:[/*#__PURE__*/_jsx(FormButton,{variant:\"primary\",onClick:handleAddLyric,children:\"\\u2705 Adicionar\"}),/*#__PURE__*/_jsx(FormButton,{variant:\"secondary\",onClick:()=>setShowAddForm(false),children:\"\\u274C Cancelar\"})]})]}),sortedLyrics.length===0?/*#__PURE__*/_jsx(EmptyState,{children:\"Adicione letras para sincronizar com sua partitura\"}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(LyricsGrid,{children:sortedLyrics.map(lyric=>/*#__PURE__*/_jsxs(LyricCard,{children:[/*#__PURE__*/_jsxs(LyricPosition,{children:[\"\\uD83D\\uDCCD Compasso \",lyric.position.measure,\", Tempo \",lyric.position.beat]}),/*#__PURE__*/_jsx(LyricText,{value:lyric.text,onChange:e=>handleUpdateLyric(lyric.id,e.target.value)}),/*#__PURE__*/_jsx(LyricActions,{children:/*#__PURE__*/_jsx(ActionButton,{variant:\"danger\",onClick:()=>handleDeleteLyric(lyric.id),children:\"\\uD83D\\uDDD1\\uFE0F Excluir\"})})]},lyric.id))}),/*#__PURE__*/_jsxs(LyricsPreview,{children:[/*#__PURE__*/_jsx(PreviewTitle,{children:\"\\uD83D\\uDCDD Preview da Letra\"}),/*#__PURE__*/_jsx(PreviewText,{children:generatePreview()||'Nenhuma letra adicionada ainda...'})]})]})]});};", "map": {"version": 3, "names": ["React", "useState", "styled", "v4", "uuidv4", "jsxs", "_jsxs", "jsx", "_jsx", "Fragment", "_Fragment", "Lyrics<PERSON><PERSON><PERSON>", "div", "LyricsHeader", "LyricsTitle", "h3", "AddLyricButton", "button", "<PERSON><PERSON><PERSON>", "LyricCard", "LyricPosition", "LyricText", "input", "LyricActions", "ActionButton", "props", "variant", "AddLyricForm", "FormRow", "FormGroup", "FormActions", "FormButton", "EmptyState", "LyricsPreview", "PreviewTitle", "h4", "PreviewText", "LyricsEditor", "_ref", "lyrics", "notes", "onLyricsChange", "title", "showAddForm", "setShowAddForm", "newLyric", "setNewLyric", "text", "measure", "beat", "handleAddLyric", "trim", "lyric", "id", "position", "handleUpdateLyric", "updatedLyrics", "map", "handleDeleteLyric", "filter", "sortedLyrics", "sort", "a", "b", "generatePreview", "join", "availableMeasures", "length", "Array", "from", "Set", "note", "children", "onClick", "value", "onChange", "e", "target", "placeholder", "autoFocus", "style", "flex", "parseInt"], "sources": ["D:/Dev/partitura_digital/src/components/LyricsEditor/LyricsEditor.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { Lyrics, MusicalNote } from '../../types/music';\nimport { v4 as uuidv4 } from 'uuid';\n\nconst LyricsContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst LyricsHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n`;\n\nconst LyricsTitle = styled.h3`\n  margin: 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n`;\n\nconst AddLyricButton = styled.button`\n  padding: 0.5rem 1rem;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\n  }\n`;\n\nconst LyricsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n`;\n\nconst LyricCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n  position: relative;\n`;\n\nconst LyricPosition = styled.div`\n  font-size: 0.8rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;\n\nconst LyricText = styled.input`\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #dee2e6;\n  border-radius: 6px;\n  font-size: 1rem;\n  margin-bottom: 0.5rem;\n  box-sizing: border-box;\n  \n  &:focus {\n    outline: none;\n    border-color: #667eea;\n  }\n`;\n\nconst LyricActions = styled.div`\n  display: flex;\n  gap: 0.5rem;\n`;\n\nconst ActionButton = styled.button<{ variant?: 'danger' }>`\n  padding: 0.25rem 0.5rem;\n  border: none;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'danger' ? `\n    background: #e74c3c;\n    color: white;\n    &:hover { background: #c0392b; }\n  ` : `\n    background: #6c757d;\n    color: white;\n    &:hover { background: #5a6268; }\n  `}\n`;\n\nconst AddLyricForm = styled.div`\n  background: #e3f2fd;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  border: 2px solid #bbdefb;\n`;\n\nconst FormRow = styled.div`\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  align-items: end;\n`;\n\nconst FormGroup = styled.div`\n  flex: 1;\n  \n  label {\n    display: block;\n    margin-bottom: 0.25rem;\n    font-size: 0.9rem;\n    font-weight: 600;\n    color: #495057;\n  }\n  \n  input, select {\n    width: 100%;\n    padding: 0.5rem;\n    border: 1px solid #ced4da;\n    border-radius: 6px;\n    font-size: 0.9rem;\n    box-sizing: border-box;\n    \n    &:focus {\n      outline: none;\n      border-color: #667eea;\n    }\n  }\n`;\n\nconst FormActions = styled.div`\n  display: flex;\n  gap: 0.5rem;\n`;\n\nconst FormButton = styled.button<{ variant?: 'primary' | 'secondary' }>`\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 6px;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  ${props => props.variant === 'primary' ? `\n    background: #667eea;\n    color: white;\n    &:hover { background: #5a67d8; }\n  ` : `\n    background: #6c757d;\n    color: white;\n    &:hover { background: #5a6268; }\n  `}\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;\n\nconst LyricsPreview = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1.5rem;\n  border: 2px solid #e9ecef;\n  margin-top: 1rem;\n`;\n\nconst PreviewTitle = styled.h4`\n  margin: 0 0 1rem 0;\n  color: #495057;\n  font-size: 1.1rem;\n`;\n\nconst PreviewText = styled.div`\n  line-height: 1.8;\n  font-size: 1.1rem;\n  color: #333;\n  white-space: pre-wrap;\n`;\n\ninterface LyricsEditorProps {\n  lyrics: Lyrics[];\n  notes: MusicalNote[];\n  onLyricsChange: (lyrics: Lyrics[]) => void;\n  title: string;\n}\n\nexport const LyricsEditor: React.FC<LyricsEditorProps> = ({ \n  lyrics, \n  notes, \n  onLyricsChange, \n  title \n}) => {\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newLyric, setNewLyric] = useState({\n    text: '',\n    measure: 1,\n    beat: 1\n  });\n\n  const handleAddLyric = () => {\n    if (!newLyric.text.trim()) return;\n    \n    const lyric: Lyrics = {\n      id: uuidv4(),\n      text: newLyric.text.trim(),\n      position: {\n        measure: newLyric.measure,\n        beat: newLyric.beat\n      }\n    };\n    \n    onLyricsChange([...lyrics, lyric]);\n    setNewLyric({ text: '', measure: 1, beat: 1 });\n    setShowAddForm(false);\n  };\n\n  const handleUpdateLyric = (id: string, text: string) => {\n    const updatedLyrics = lyrics.map(lyric =>\n      lyric.id === id ? { ...lyric, text } : lyric\n    );\n    onLyricsChange(updatedLyrics);\n  };\n\n  const handleDeleteLyric = (id: string) => {\n    const updatedLyrics = lyrics.filter(lyric => lyric.id !== id);\n    onLyricsChange(updatedLyrics);\n  };\n\n  // Ordenar letras por posição\n  const sortedLyrics = [...lyrics].sort((a, b) => {\n    if (a.position.measure !== b.position.measure) {\n      return a.position.measure - b.position.measure;\n    }\n    return a.position.beat - b.position.beat;\n  });\n\n  // Gerar preview das letras\n  const generatePreview = () => {\n    return sortedLyrics.map(lyric => lyric.text).join(' ');\n  };\n\n  // Obter compassos disponíveis baseado nas notas\n  const availableMeasures = notes.length > 0 \n    ? Array.from(new Set(notes.map(note => note.position.measure))).sort((a, b) => a - b)\n    : [1];\n\n  return (\n    <LyricsContainer>\n      <LyricsHeader>\n        <LyricsTitle>🎤 Letras - {title}</LyricsTitle>\n        <AddLyricButton onClick={() => setShowAddForm(!showAddForm)}>\n          {showAddForm ? '❌ Cancelar' : '➕ Adicionar Letra'}\n        </AddLyricButton>\n      </LyricsHeader>\n\n      {showAddForm && (\n        <AddLyricForm>\n          <FormRow>\n            <FormGroup>\n              <label>Texto da Letra:</label>\n              <LyricText\n                value={newLyric.text}\n                onChange={(e) => setNewLyric({ ...newLyric, text: e.target.value })}\n                placeholder=\"Digite a palavra ou frase...\"\n                autoFocus\n              />\n            </FormGroup>\n            <FormGroup style={{ flex: '0 0 120px' }}>\n              <label>Compasso:</label>\n              <select\n                value={newLyric.measure}\n                onChange={(e) => setNewLyric({ ...newLyric, measure: parseInt(e.target.value) })}\n              >\n                {availableMeasures.map(measure => (\n                  <option key={measure} value={measure}>\n                    {measure}\n                  </option>\n                ))}\n              </select>\n            </FormGroup>\n            <FormGroup style={{ flex: '0 0 100px' }}>\n              <label>Tempo:</label>\n              <select\n                value={newLyric.beat}\n                onChange={(e) => setNewLyric({ ...newLyric, beat: parseInt(e.target.value) })}\n              >\n                <option value={1}>1</option>\n                <option value={2}>2</option>\n                <option value={3}>3</option>\n                <option value={4}>4</option>\n              </select>\n            </FormGroup>\n          </FormRow>\n          <FormActions>\n            <FormButton variant=\"primary\" onClick={handleAddLyric}>\n              ✅ Adicionar\n            </FormButton>\n            <FormButton variant=\"secondary\" onClick={() => setShowAddForm(false)}>\n              ❌ Cancelar\n            </FormButton>\n          </FormActions>\n        </AddLyricForm>\n      )}\n\n      {sortedLyrics.length === 0 ? (\n        <EmptyState>\n          Adicione letras para sincronizar com sua partitura\n        </EmptyState>\n      ) : (\n        <>\n          <LyricsGrid>\n            {sortedLyrics.map(lyric => (\n              <LyricCard key={lyric.id}>\n                <LyricPosition>\n                  📍 Compasso {lyric.position.measure}, Tempo {lyric.position.beat}\n                </LyricPosition>\n                <LyricText\n                  value={lyric.text}\n                  onChange={(e) => handleUpdateLyric(lyric.id, e.target.value)}\n                />\n                <LyricActions>\n                  <ActionButton \n                    variant=\"danger\"\n                    onClick={() => handleDeleteLyric(lyric.id)}\n                  >\n                    🗑️ Excluir\n                  </ActionButton>\n                </LyricActions>\n              </LyricCard>\n            ))}\n          </LyricsGrid>\n\n          <LyricsPreview>\n            <PreviewTitle>📝 Preview da Letra</PreviewTitle>\n            <PreviewText>\n              {generatePreview() || 'Nenhuma letra adicionada ainda...'}\n            </PreviewText>\n          </LyricsPreview>\n        </>\n      )}\n    </LyricsContainer>\n  );\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CAEtC,OAASC,EAAE,GAAI,CAAAC,MAAM,KAAQ,MAAM,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEpC,KAAM,CAAAC,eAAe,CAAGT,MAAM,CAACU,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGX,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAE,WAAW,CAAGZ,MAAM,CAACa,EAAE;AAC7B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGd,MAAM,CAACe,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGhB,MAAM,CAACU,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAO,SAAS,CAAGjB,MAAM,CAACU,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAQ,aAAa,CAAGlB,MAAM,CAACU,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAS,SAAS,CAAGnB,MAAM,CAACoB,KAAK;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGrB,MAAM,CAACU,GAAG;AAC/B;AACA;AACA,CAAC,CAED,KAAM,CAAAY,YAAY,CAAGtB,MAAM,CAACe,MAA8B;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIQ,KAAK,EAAIA,KAAK,CAACC,OAAO,GAAK,QAAQ,CAAG;AAC1C;AACA;AACA;AACA,GAAG,CAAG;AACN;AACA;AACA;AACA,GAAG;AACH,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGzB,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAgB,OAAO,CAAG1B,MAAM,CAACU,GAAG;AAC1B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAiB,SAAS,CAAG3B,MAAM,CAACU,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAkB,WAAW,CAAG5B,MAAM,CAACU,GAAG;AAC9B;AACA;AACA,CAAC,CAED,KAAM,CAAAmB,UAAU,CAAG7B,MAAM,CAACe,MAA6C;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIQ,KAAK,EAAIA,KAAK,CAACC,OAAO,GAAK,SAAS,CAAG;AAC3C;AACA;AACA;AACA,GAAG,CAAG;AACN;AACA;AACA;AACA,GAAG;AACH,CAAC,CAED,KAAM,CAAAM,UAAU,CAAG9B,MAAM,CAACU,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAqB,aAAa,CAAG/B,MAAM,CAACU,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAsB,YAAY,CAAGhC,MAAM,CAACiC,EAAE;AAC9B;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGlC,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC,CASD,MAAO,MAAM,CAAAyB,YAAyC,CAAGC,IAAA,EAKnD,IALoD,CACxDC,MAAM,CACNC,KAAK,CACLC,cAAc,CACdC,KACF,CAAC,CAAAJ,IAAA,CACC,KAAM,CAACK,WAAW,CAAEC,cAAc,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAC4C,QAAQ,CAAEC,WAAW,CAAC,CAAG7C,QAAQ,CAAC,CACvC8C,IAAI,CAAE,EAAE,CACRC,OAAO,CAAE,CAAC,CACVC,IAAI,CAAE,CACR,CAAC,CAAC,CAEF,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAI,CAACL,QAAQ,CAACE,IAAI,CAACI,IAAI,CAAC,CAAC,CAAE,OAE3B,KAAM,CAAAC,KAAa,CAAG,CACpBC,EAAE,CAAEjD,MAAM,CAAC,CAAC,CACZ2C,IAAI,CAAEF,QAAQ,CAACE,IAAI,CAACI,IAAI,CAAC,CAAC,CAC1BG,QAAQ,CAAE,CACRN,OAAO,CAAEH,QAAQ,CAACG,OAAO,CACzBC,IAAI,CAAEJ,QAAQ,CAACI,IACjB,CACF,CAAC,CAEDR,cAAc,CAAC,CAAC,GAAGF,MAAM,CAAEa,KAAK,CAAC,CAAC,CAClCN,WAAW,CAAC,CAAEC,IAAI,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,IAAI,CAAE,CAAE,CAAC,CAAC,CAC9CL,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,KAAM,CAAAW,iBAAiB,CAAGA,CAACF,EAAU,CAAEN,IAAY,GAAK,CACtD,KAAM,CAAAS,aAAa,CAAGjB,MAAM,CAACkB,GAAG,CAACL,KAAK,EACpCA,KAAK,CAACC,EAAE,GAAKA,EAAE,CAAG,CAAE,GAAGD,KAAK,CAAEL,IAAK,CAAC,CAAGK,KACzC,CAAC,CACDX,cAAc,CAACe,aAAa,CAAC,CAC/B,CAAC,CAED,KAAM,CAAAE,iBAAiB,CAAIL,EAAU,EAAK,CACxC,KAAM,CAAAG,aAAa,CAAGjB,MAAM,CAACoB,MAAM,CAACP,KAAK,EAAIA,KAAK,CAACC,EAAE,GAAKA,EAAE,CAAC,CAC7DZ,cAAc,CAACe,aAAa,CAAC,CAC/B,CAAC,CAED;AACA,KAAM,CAAAI,YAAY,CAAG,CAAC,GAAGrB,MAAM,CAAC,CAACsB,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC9C,GAAID,CAAC,CAACR,QAAQ,CAACN,OAAO,GAAKe,CAAC,CAACT,QAAQ,CAACN,OAAO,CAAE,CAC7C,MAAO,CAAAc,CAAC,CAACR,QAAQ,CAACN,OAAO,CAAGe,CAAC,CAACT,QAAQ,CAACN,OAAO,CAChD,CACA,MAAO,CAAAc,CAAC,CAACR,QAAQ,CAACL,IAAI,CAAGc,CAAC,CAACT,QAAQ,CAACL,IAAI,CAC1C,CAAC,CAAC,CAEF;AACA,KAAM,CAAAe,eAAe,CAAGA,CAAA,GAAM,CAC5B,MAAO,CAAAJ,YAAY,CAACH,GAAG,CAACL,KAAK,EAAIA,KAAK,CAACL,IAAI,CAAC,CAACkB,IAAI,CAAC,GAAG,CAAC,CACxD,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAG1B,KAAK,CAAC2B,MAAM,CAAG,CAAC,CACtCC,KAAK,CAACC,IAAI,CAAC,GAAI,CAAAC,GAAG,CAAC9B,KAAK,CAACiB,GAAG,CAACc,IAAI,EAAIA,IAAI,CAACjB,QAAQ,CAACN,OAAO,CAAC,CAAC,CAAC,CAACa,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAGC,CAAC,CAAC,CACnF,CAAC,CAAC,CAAC,CAEP,mBACEzD,KAAA,CAACK,eAAe,EAAA6D,QAAA,eACdlE,KAAA,CAACO,YAAY,EAAA2D,QAAA,eACXlE,KAAA,CAACQ,WAAW,EAAA0D,QAAA,EAAC,wBAAY,CAAC9B,KAAK,EAAc,CAAC,cAC9ClC,IAAA,CAACQ,cAAc,EAACyD,OAAO,CAAEA,CAAA,GAAM7B,cAAc,CAAC,CAACD,WAAW,CAAE,CAAA6B,QAAA,CACzD7B,WAAW,CAAG,YAAY,CAAG,mBAAmB,CACnC,CAAC,EACL,CAAC,CAEdA,WAAW,eACVrC,KAAA,CAACqB,YAAY,EAAA6C,QAAA,eACXlE,KAAA,CAACsB,OAAO,EAAA4C,QAAA,eACNlE,KAAA,CAACuB,SAAS,EAAA2C,QAAA,eACRhE,IAAA,UAAAgE,QAAA,CAAO,iBAAe,CAAO,CAAC,cAC9BhE,IAAA,CAACa,SAAS,EACRqD,KAAK,CAAE7B,QAAQ,CAACE,IAAK,CACrB4B,QAAQ,CAAGC,CAAC,EAAK9B,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEE,IAAI,CAAE6B,CAAC,CAACC,MAAM,CAACH,KAAM,CAAC,CAAE,CACpEI,WAAW,CAAC,8BAA8B,CAC1CC,SAAS,MACV,CAAC,EACO,CAAC,cACZzE,KAAA,CAACuB,SAAS,EAACmD,KAAK,CAAE,CAAEC,IAAI,CAAE,WAAY,CAAE,CAAAT,QAAA,eACtChE,IAAA,UAAAgE,QAAA,CAAO,WAAS,CAAO,CAAC,cACxBhE,IAAA,WACEkE,KAAK,CAAE7B,QAAQ,CAACG,OAAQ,CACxB2B,QAAQ,CAAGC,CAAC,EAAK9B,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEG,OAAO,CAAEkC,QAAQ,CAACN,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAC,CAAE,CAAAF,QAAA,CAEhFN,iBAAiB,CAACT,GAAG,CAACT,OAAO,eAC5BxC,IAAA,WAAsBkE,KAAK,CAAE1B,OAAQ,CAAAwB,QAAA,CAClCxB,OAAO,EADGA,OAEL,CACT,CAAC,CACI,CAAC,EACA,CAAC,cACZ1C,KAAA,CAACuB,SAAS,EAACmD,KAAK,CAAE,CAAEC,IAAI,CAAE,WAAY,CAAE,CAAAT,QAAA,eACtChE,IAAA,UAAAgE,QAAA,CAAO,QAAM,CAAO,CAAC,cACrBlE,KAAA,WACEoE,KAAK,CAAE7B,QAAQ,CAACI,IAAK,CACrB0B,QAAQ,CAAGC,CAAC,EAAK9B,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEI,IAAI,CAAEiC,QAAQ,CAACN,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAC,CAAE,CAAAF,QAAA,eAE9EhE,IAAA,WAAQkE,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BhE,IAAA,WAAQkE,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BhE,IAAA,WAAQkE,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,cAC5BhE,IAAA,WAAQkE,KAAK,CAAE,CAAE,CAAAF,QAAA,CAAC,GAAC,CAAQ,CAAC,EACtB,CAAC,EACA,CAAC,EACL,CAAC,cACVlE,KAAA,CAACwB,WAAW,EAAA0C,QAAA,eACVhE,IAAA,CAACuB,UAAU,EAACL,OAAO,CAAC,SAAS,CAAC+C,OAAO,CAAEvB,cAAe,CAAAsB,QAAA,CAAC,kBAEvD,CAAY,CAAC,cACbhE,IAAA,CAACuB,UAAU,EAACL,OAAO,CAAC,WAAW,CAAC+C,OAAO,CAAEA,CAAA,GAAM7B,cAAc,CAAC,KAAK,CAAE,CAAA4B,QAAA,CAAC,iBAEtE,CAAY,CAAC,EACF,CAAC,EACF,CACf,CAEAZ,YAAY,CAACO,MAAM,GAAK,CAAC,cACxB3D,IAAA,CAACwB,UAAU,EAAAwC,QAAA,CAAC,oDAEZ,CAAY,CAAC,cAEblE,KAAA,CAAAI,SAAA,EAAA8D,QAAA,eACEhE,IAAA,CAACU,UAAU,EAAAsD,QAAA,CACRZ,YAAY,CAACH,GAAG,CAACL,KAAK,eACrB9C,KAAA,CAACa,SAAS,EAAAqD,QAAA,eACRlE,KAAA,CAACc,aAAa,EAAAoD,QAAA,EAAC,wBACD,CAACpB,KAAK,CAACE,QAAQ,CAACN,OAAO,CAAC,UAAQ,CAACI,KAAK,CAACE,QAAQ,CAACL,IAAI,EACnD,CAAC,cAChBzC,IAAA,CAACa,SAAS,EACRqD,KAAK,CAAEtB,KAAK,CAACL,IAAK,CAClB4B,QAAQ,CAAGC,CAAC,EAAKrB,iBAAiB,CAACH,KAAK,CAACC,EAAE,CAAEuB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC9D,CAAC,cACFlE,IAAA,CAACe,YAAY,EAAAiD,QAAA,cACXhE,IAAA,CAACgB,YAAY,EACXE,OAAO,CAAC,QAAQ,CAChB+C,OAAO,CAAEA,CAAA,GAAMf,iBAAiB,CAACN,KAAK,CAACC,EAAE,CAAE,CAAAmB,QAAA,CAC5C,4BAED,CAAc,CAAC,CACH,CAAC,GAfDpB,KAAK,CAACC,EAgBX,CACZ,CAAC,CACQ,CAAC,cAEb/C,KAAA,CAAC2B,aAAa,EAAAuC,QAAA,eACZhE,IAAA,CAAC0B,YAAY,EAAAsC,QAAA,CAAC,+BAAmB,CAAc,CAAC,cAChDhE,IAAA,CAAC4B,WAAW,EAAAoC,QAAA,CACTR,eAAe,CAAC,CAAC,EAAI,mCAAmC,CAC9C,CAAC,EACD,CAAC,EAChB,CACH,EACc,CAAC,CAEtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}