{"ast": null, "code": "let getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n  if (!getRandomValues) {\n    if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n    getRandomValues = crypto.getRandomValues.bind(crypto);\n  }\n  return getRandomValues(rnds8);\n}", "map": {"version": 3, "names": ["getRandomValues", "rnds8", "Uint8Array", "rng", "crypto", "Error", "bind"], "sources": ["D:/Dev/partitura_digital/node_modules/uuid/dist/esm-browser/rng.js"], "sourcesContent": ["let getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n    if (!getRandomValues) {\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n        }\n        getRandomValues = crypto.getRandomValues.bind(crypto);\n    }\n    return getRandomValues(rnds8);\n}\n"], "mappings": "AAAA,IAAIA,eAAe;AACnB,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;AAChC,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC1B,IAAI,CAACH,eAAe,EAAE;IAClB,IAAI,OAAOI,MAAM,KAAK,WAAW,IAAI,CAACA,MAAM,CAACJ,eAAe,EAAE;MAC1D,MAAM,IAAIK,KAAK,CAAC,0GAA0G,CAAC;IAC/H;IACAL,eAAe,GAAGI,MAAM,CAACJ,eAAe,CAACM,IAAI,CAACF,MAAM,CAAC;EACzD;EACA,OAAOJ,eAAe,CAACC,KAAK,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}