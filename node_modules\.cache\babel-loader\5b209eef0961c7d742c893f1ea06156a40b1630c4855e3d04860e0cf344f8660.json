{"ast": null, "code": "const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default {\n  randomUUID\n};", "map": {"version": 3, "names": ["randomUUID", "crypto", "bind"], "sources": ["D:/Dev/partitura_digital/node_modules/uuid/dist/esm-browser/native.js"], "sourcesContent": ["const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default { randomUUID };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACD,UAAU,IAAIC,MAAM,CAACD,UAAU,CAACE,IAAI,CAACD,MAAM,CAAC;AACvG,eAAe;EAAED;AAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}