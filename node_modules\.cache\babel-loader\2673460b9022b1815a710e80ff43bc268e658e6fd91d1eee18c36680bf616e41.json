{"ast": null, "code": "var _jsxFileName = \"D:\\\\Dev\\\\partitura_digital\\\\src\\\\components\\\\ChordView\\\\ChordView.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChordContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n_c = ChordContainer;\nconst ChordTitle = styled.h3`\n  margin: 0 0 1.5rem 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n  text-align: center;\n`;\n_c2 = ChordTitle;\nconst ChordGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n`;\n_c3 = ChordGrid;\nconst MeasureCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n`;\n_c4 = MeasureCard;\nconst MeasureNumber = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;\n_c5 = MeasureNumber;\nconst ChordSymbol = styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n  text-align: center;\n`;\n_c6 = ChordSymbol;\nconst ChordNotes = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  text-align: center;\n`;\n_c7 = ChordNotes;\nconst EmptyState = styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;\n_c8 = EmptyState;\n// Função para determinar o acorde baseado nas notas\nconst analyzeChord = notes => {\n  if (notes.length === 0) return '';\n\n  // Filtrar apenas notas (não pausas) e aplicar acidentes\n  const processedNotes = notes.filter(note => !note.isRest).map(note => {\n    let noteName = note.name;\n\n    // Aplicar acidentes\n    if (note.accidental === 'sharp') {\n      const sharpMap = {\n        'C': 'C#',\n        'D': 'D#',\n        'F': 'F#',\n        'G': 'G#',\n        'A': 'A#'\n      };\n      noteName = sharpMap[noteName] || noteName;\n    } else if (note.accidental === 'flat') {\n      const flatMap = {\n        'D': 'Db',\n        'E': 'Eb',\n        'G': 'Gb',\n        'A': 'Ab',\n        'B': 'Bb'\n      };\n      noteName = flatMap[noteName] || noteName;\n    }\n    return noteName;\n  }).filter((name, index, arr) => arr.indexOf(name) === index); // Remover duplicatas\n\n  if (processedNotes.length === 0) return '';\n  if (processedNotes.length === 1) return processedNotes[0];\n\n  // Normalizar notas para análise (converter tudo para sustenidos)\n  const normalizeNote = note => {\n    const enharmonics = {\n      'Db': 'C#',\n      'Eb': 'D#',\n      'Gb': 'F#',\n      'Ab': 'G#',\n      'Bb': 'A#'\n    };\n    return enharmonics[note] || note;\n  };\n  const normalizedNotes = processedNotes.map(normalizeNote).sort();\n\n  // Definir acordes conhecidos\n  const chordPatterns = {\n    // Acordes maiores\n    'C,E,G': 'C',\n    'C#,F,G#': 'C#',\n    'D,F#,A': 'D',\n    'D#,G,A#': 'D#',\n    'E,G#,B': 'E',\n    'F,A,C': 'F',\n    'F#,A#,C#': 'F#',\n    'G,B,D': 'G',\n    'G#,C,D#': 'G#',\n    'A,C#,E': 'A',\n    'A#,D,F': 'A#',\n    'B,D#,F#': 'B',\n    // Acordes menores\n    'C,D#,G': 'Cm',\n    'C#,E,G#': 'C#m',\n    'D,F,A': 'Dm',\n    'D#,F#,A#': 'D#m',\n    'E,G,B': 'Em',\n    'F,G#,C': 'Fm',\n    'F#,A,C#': 'F#m',\n    'G,A#,D': 'Gm',\n    'G#,B,D#': 'G#m',\n    'A,C,E': 'Am',\n    'A#,C#,F': 'A#m',\n    'B,D,F#': 'Bm',\n    // Acordes de sétima\n    'C,E,G,A#': 'C7',\n    'D,F#,A,C': 'D7',\n    'E,G#,B,D': 'E7',\n    'F,A,C,D#': 'F7',\n    'G,B,D,F': 'G7',\n    'A,C#,E,G': 'A7',\n    'B,D#,F#,A': 'B7'\n  };\n  const noteString = normalizedNotes.join(',');\n\n  // Verificar padrões conhecidos\n  if (chordPatterns[noteString]) {\n    return chordPatterns[noteString];\n  }\n\n  // Se não encontrou um padrão, tentar identificar pela fundamental\n  const root = normalizedNotes[0];\n  const intervals = normalizedNotes.slice(1);\n  if (intervals.length >= 2) {\n    // Tentar identificar como acorde básico\n    return `${root}?`; // Indica acorde não identificado com fundamental\n  }\n\n  // Retornar notas individuais\n  return processedNotes.join(' - ');\n};\nexport const ChordView = ({\n  notes,\n  title\n}) => {\n  // Agrupar notas por compasso\n  const measureMap = new Map();\n  notes.forEach(note => {\n    const measure = note.position.measure;\n    if (!measureMap.has(measure)) {\n      measureMap.set(measure, []);\n    }\n    measureMap.get(measure).push(note);\n  });\n\n  // Converter para array ordenado\n  const measures = Array.from(measureMap.entries()).sort(([a], [b]) => a - b).map(([measureNumber, measureNotes]) => ({\n    number: measureNumber,\n    notes: measureNotes.sort((a, b) => a.position.beat - b.position.beat),\n    chord: analyzeChord(measureNotes)\n  }));\n  return /*#__PURE__*/_jsxDEV(ChordContainer, {\n    children: [/*#__PURE__*/_jsxDEV(ChordTitle, {\n      children: [\"\\uD83C\\uDFB8 Cifras - \", title]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), measures.length === 0 ? /*#__PURE__*/_jsxDEV(EmptyState, {\n      children: \"Adicione algumas notas na partitura para ver as cifras aqui\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(ChordGrid, {\n      children: measures.map(measure => /*#__PURE__*/_jsxDEV(MeasureCard, {\n        children: [/*#__PURE__*/_jsxDEV(MeasureNumber, {\n          children: [\"Compasso \", measure.number]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ChordSymbol, {\n          children: measure.chord || '—'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ChordNotes, {\n          children: measure.notes.filter(note => !note.isRest).map(note => `${note.name}${note.octave}`).join(', ') || 'Pausas'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 15\n        }, this)]\n      }, measure.number, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 188,\n    columnNumber: 5\n  }, this);\n};\n_c9 = ChordView;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"ChordContainer\");\n$RefreshReg$(_c2, \"ChordTitle\");\n$RefreshReg$(_c3, \"ChordGrid\");\n$RefreshReg$(_c4, \"MeasureCard\");\n$RefreshReg$(_c5, \"MeasureNumber\");\n$RefreshReg$(_c6, \"ChordSymbol\");\n$RefreshReg$(_c7, \"ChordNotes\");\n$RefreshReg$(_c8, \"EmptyState\");\n$RefreshReg$(_c9, \"ChordView\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "ChordTitle", "h3", "_c2", "ChordGrid", "_c3", "MeasureCard", "_c4", "MeasureNumber", "_c5", "ChordSymbol", "_c6", "ChordNotes", "_c7", "EmptyState", "_c8", "analyzeChord", "notes", "length", "processedNotes", "filter", "note", "isRest", "map", "noteName", "name", "accidental", "sharpMap", "flatMap", "index", "arr", "indexOf", "normalizeNote", "enharmonics", "normalizedNotes", "sort", "chordPatterns", "noteString", "join", "root", "intervals", "slice", "ChordView", "title", "measureMap", "Map", "for<PERSON>ach", "measure", "position", "has", "set", "get", "push", "measures", "Array", "from", "entries", "a", "b", "measureNumber", "measureNotes", "number", "beat", "chord", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "octave", "_c9", "$RefreshReg$"], "sources": ["D:/Dev/partitura_digital/src/components/ChordView/ChordView.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { MusicalNote, NoteName } from '../../types/music';\n\nconst ChordContainer = styled.div`\n  background: rgba(255,255,255,0.95);\n  border-radius: 16px;\n  padding: 2rem;\n  margin: 1rem;\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n`;\n\nconst ChordTitle = styled.h3`\n  margin: 0 0 1.5rem 0;\n  color: #2c3e50;\n  font-size: 1.3rem;\n  text-align: center;\n`;\n\nconst ChordGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n`;\n\nconst MeasureCard = styled.div`\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 1rem;\n  border: 2px solid #e9ecef;\n`;\n\nconst MeasureNumber = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n`;\n\nconst ChordSymbol = styled.div`\n  font-size: 1.5rem;\n  font-weight: bold;\n  color: #2c3e50;\n  margin-bottom: 0.5rem;\n  text-align: center;\n`;\n\nconst ChordNotes = styled.div`\n  font-size: 0.9rem;\n  color: #666;\n  text-align: center;\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  color: #666;\n  font-style: italic;\n  padding: 2rem;\n`;\n\ninterface ChordViewProps {\n  notes: MusicalNote[];\n  title: string;\n}\n\n// Função para determinar o acorde baseado nas notas\nconst analyzeChord = (notes: MusicalNote[]): string => {\n  if (notes.length === 0) return '';\n\n  // Filtrar apenas notas (não pausas) e aplicar acidentes\n  const processedNotes = notes\n    .filter(note => !note.isRest)\n    .map(note => {\n      let noteName = note.name;\n\n      // Aplicar acidentes\n      if (note.accidental === 'sharp') {\n        const sharpMap: { [key: string]: string } = {\n          'C': 'C#', 'D': 'D#', 'F': 'F#', 'G': 'G#', 'A': 'A#'\n        };\n        noteName = sharpMap[noteName] || noteName;\n      } else if (note.accidental === 'flat') {\n        const flatMap: { [key: string]: string } = {\n          'D': 'Db', 'E': 'Eb', 'G': 'Gb', 'A': 'Ab', 'B': 'Bb'\n        };\n        noteName = flatMap[noteName] || noteName;\n      }\n\n      return noteName;\n    })\n    .filter((name, index, arr) => arr.indexOf(name) === index); // Remover duplicatas\n\n  if (processedNotes.length === 0) return '';\n  if (processedNotes.length === 1) return processedNotes[0];\n\n  // Normalizar notas para análise (converter tudo para sustenidos)\n  const normalizeNote = (note: string): string => {\n    const enharmonics: { [key: string]: string } = {\n      'Db': 'C#', 'Eb': 'D#', 'Gb': 'F#', 'Ab': 'G#', 'Bb': 'A#'\n    };\n    return enharmonics[note] || note;\n  };\n\n  const normalizedNotes = processedNotes.map(normalizeNote).sort();\n\n  // Definir acordes conhecidos\n  const chordPatterns: { [key: string]: string } = {\n    // Acordes maiores\n    'C,E,G': 'C',\n    'C#,F,G#': 'C#',\n    'D,F#,A': 'D',\n    'D#,G,A#': 'D#',\n    'E,G#,B': 'E',\n    'F,A,C': 'F',\n    'F#,A#,C#': 'F#',\n    'G,B,D': 'G',\n    'G#,C,D#': 'G#',\n    'A,C#,E': 'A',\n    'A#,D,F': 'A#',\n    'B,D#,F#': 'B',\n\n    // Acordes menores\n    'C,D#,G': 'Cm',\n    'C#,E,G#': 'C#m',\n    'D,F,A': 'Dm',\n    'D#,F#,A#': 'D#m',\n    'E,G,B': 'Em',\n    'F,G#,C': 'Fm',\n    'F#,A,C#': 'F#m',\n    'G,A#,D': 'Gm',\n    'G#,B,D#': 'G#m',\n    'A,C,E': 'Am',\n    'A#,C#,F': 'A#m',\n    'B,D,F#': 'Bm',\n\n    // Acordes de sétima\n    'C,E,G,A#': 'C7',\n    'D,F#,A,C': 'D7',\n    'E,G#,B,D': 'E7',\n    'F,A,C,D#': 'F7',\n    'G,B,D,F': 'G7',\n    'A,C#,E,G': 'A7',\n    'B,D#,F#,A': 'B7'\n  };\n\n  const noteString = normalizedNotes.join(',');\n\n  // Verificar padrões conhecidos\n  if (chordPatterns[noteString]) {\n    return chordPatterns[noteString];\n  }\n\n  // Se não encontrou um padrão, tentar identificar pela fundamental\n  const root = normalizedNotes[0];\n  const intervals = normalizedNotes.slice(1);\n\n  if (intervals.length >= 2) {\n    // Tentar identificar como acorde básico\n    return `${root}?`; // Indica acorde não identificado com fundamental\n  }\n\n  // Retornar notas individuais\n  return processedNotes.join(' - ');\n};\n\nexport const ChordView: React.FC<ChordViewProps> = ({ notes, title }) => {\n  // Agrupar notas por compasso\n  const measureMap = new Map<number, MusicalNote[]>();\n  \n  notes.forEach(note => {\n    const measure = note.position.measure;\n    if (!measureMap.has(measure)) {\n      measureMap.set(measure, []);\n    }\n    measureMap.get(measure)!.push(note);\n  });\n  \n  // Converter para array ordenado\n  const measures = Array.from(measureMap.entries())\n    .sort(([a], [b]) => a - b)\n    .map(([measureNumber, measureNotes]) => ({\n      number: measureNumber,\n      notes: measureNotes.sort((a, b) => a.position.beat - b.position.beat),\n      chord: analyzeChord(measureNotes)\n    }));\n\n  return (\n    <ChordContainer>\n      <ChordTitle>🎸 Cifras - {title}</ChordTitle>\n      \n      {measures.length === 0 ? (\n        <EmptyState>\n          Adicione algumas notas na partitura para ver as cifras aqui\n        </EmptyState>\n      ) : (\n        <ChordGrid>\n          {measures.map(measure => (\n            <MeasureCard key={measure.number}>\n              <MeasureNumber>Compasso {measure.number}</MeasureNumber>\n              <ChordSymbol>\n                {measure.chord || '—'}\n              </ChordSymbol>\n              <ChordNotes>\n                {measure.notes\n                  .filter(note => !note.isRest)\n                  .map(note => `${note.name}${note.octave}`)\n                  .join(', ') || 'Pausas'}\n              </ChordNotes>\n            </MeasureCard>\n          ))}\n        </ChordGrid>\n      )}\n    </ChordContainer>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGvC,MAAMC,cAAc,GAAGH,MAAM,CAACI,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,cAAc;AAQpB,MAAMG,UAAU,GAAGN,MAAM,CAACO,EAAE;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,UAAU;AAOhB,MAAMG,SAAS,GAAGT,MAAM,CAACI,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACM,GAAA,GAJID,SAAS;AAMf,MAAME,WAAW,GAAGX,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GALID,WAAW;AAOjB,MAAME,aAAa,GAAGb,MAAM,CAACI,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GALID,aAAa;AAOnB,MAAME,WAAW,GAAGf,MAAM,CAACI,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GANID,WAAW;AAQjB,MAAME,UAAU,GAAGjB,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAJID,UAAU;AAMhB,MAAME,UAAU,GAAGnB,MAAM,CAACI,GAAG;AAC7B;AACA;AACA;AACA;AACA,CAAC;AAACgB,GAAA,GALID,UAAU;AAYhB;AACA,MAAME,YAAY,GAAIC,KAAoB,IAAa;EACrD,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;;EAEjC;EACA,MAAMC,cAAc,GAAGF,KAAK,CACzBG,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,CAAC,CAC5BC,GAAG,CAACF,IAAI,IAAI;IACX,IAAIG,QAAQ,GAAGH,IAAI,CAACI,IAAI;;IAExB;IACA,IAAIJ,IAAI,CAACK,UAAU,KAAK,OAAO,EAAE;MAC/B,MAAMC,QAAmC,GAAG;QAC1C,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE;MACnD,CAAC;MACDH,QAAQ,GAAGG,QAAQ,CAACH,QAAQ,CAAC,IAAIA,QAAQ;IAC3C,CAAC,MAAM,IAAIH,IAAI,CAACK,UAAU,KAAK,MAAM,EAAE;MACrC,MAAME,OAAkC,GAAG;QACzC,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE,IAAI;QAAE,GAAG,EAAE;MACnD,CAAC;MACDJ,QAAQ,GAAGI,OAAO,CAACJ,QAAQ,CAAC,IAAIA,QAAQ;IAC1C;IAEA,OAAOA,QAAQ;EACjB,CAAC,CAAC,CACDJ,MAAM,CAAC,CAACK,IAAI,EAAEI,KAAK,EAAEC,GAAG,KAAKA,GAAG,CAACC,OAAO,CAACN,IAAI,CAAC,KAAKI,KAAK,CAAC,CAAC,CAAC;;EAE9D,IAAIV,cAAc,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;EAC1C,IAAIC,cAAc,CAACD,MAAM,KAAK,CAAC,EAAE,OAAOC,cAAc,CAAC,CAAC,CAAC;;EAEzD;EACA,MAAMa,aAAa,GAAIX,IAAY,IAAa;IAC9C,MAAMY,WAAsC,GAAG;MAC7C,IAAI,EAAE,IAAI;MAAE,IAAI,EAAE,IAAI;MAAE,IAAI,EAAE,IAAI;MAAE,IAAI,EAAE,IAAI;MAAE,IAAI,EAAE;IACxD,CAAC;IACD,OAAOA,WAAW,CAACZ,IAAI,CAAC,IAAIA,IAAI;EAClC,CAAC;EAED,MAAMa,eAAe,GAAGf,cAAc,CAACI,GAAG,CAACS,aAAa,CAAC,CAACG,IAAI,CAAC,CAAC;;EAEhE;EACA,MAAMC,aAAwC,GAAG;IAC/C;IACA,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,GAAG;IACb,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,UAAU,EAAE,IAAI;IAChB,OAAO,EAAE,GAAG;IACZ,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,GAAG;IACb,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,GAAG;IAEd;IACA,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,KAAK;IAChB,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,KAAK;IACjB,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,KAAK;IAChB,OAAO,EAAE,IAAI;IACb,SAAS,EAAE,KAAK;IAChB,QAAQ,EAAE,IAAI;IAEd;IACA,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,IAAI;IAChB,SAAS,EAAE,IAAI;IACf,UAAU,EAAE,IAAI;IAChB,WAAW,EAAE;EACf,CAAC;EAED,MAAMC,UAAU,GAAGH,eAAe,CAACI,IAAI,CAAC,GAAG,CAAC;;EAE5C;EACA,IAAIF,aAAa,CAACC,UAAU,CAAC,EAAE;IAC7B,OAAOD,aAAa,CAACC,UAAU,CAAC;EAClC;;EAEA;EACA,MAAME,IAAI,GAAGL,eAAe,CAAC,CAAC,CAAC;EAC/B,MAAMM,SAAS,GAAGN,eAAe,CAACO,KAAK,CAAC,CAAC,CAAC;EAE1C,IAAID,SAAS,CAACtB,MAAM,IAAI,CAAC,EAAE;IACzB;IACA,OAAO,GAAGqB,IAAI,GAAG,CAAC,CAAC;EACrB;;EAEA;EACA,OAAOpB,cAAc,CAACmB,IAAI,CAAC,KAAK,CAAC;AACnC,CAAC;AAED,OAAO,MAAMI,SAAmC,GAAGA,CAAC;EAAEzB,KAAK;EAAE0B;AAAM,CAAC,KAAK;EACvE;EACA,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAwB,CAAC;EAEnD5B,KAAK,CAAC6B,OAAO,CAACzB,IAAI,IAAI;IACpB,MAAM0B,OAAO,GAAG1B,IAAI,CAAC2B,QAAQ,CAACD,OAAO;IACrC,IAAI,CAACH,UAAU,CAACK,GAAG,CAACF,OAAO,CAAC,EAAE;MAC5BH,UAAU,CAACM,GAAG,CAACH,OAAO,EAAE,EAAE,CAAC;IAC7B;IACAH,UAAU,CAACO,GAAG,CAACJ,OAAO,CAAC,CAAEK,IAAI,CAAC/B,IAAI,CAAC;EACrC,CAAC,CAAC;;EAEF;EACA,MAAMgC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACX,UAAU,CAACY,OAAO,CAAC,CAAC,CAAC,CAC9CrB,IAAI,CAAC,CAAC,CAACsB,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CACzBnC,GAAG,CAAC,CAAC,CAACoC,aAAa,EAAEC,YAAY,CAAC,MAAM;IACvCC,MAAM,EAAEF,aAAa;IACrB1C,KAAK,EAAE2C,YAAY,CAACzB,IAAI,CAAC,CAACsB,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACT,QAAQ,CAACc,IAAI,GAAGJ,CAAC,CAACV,QAAQ,CAACc,IAAI,CAAC;IACrEC,KAAK,EAAE/C,YAAY,CAAC4C,YAAY;EAClC,CAAC,CAAC,CAAC;EAEL,oBACE/D,OAAA,CAACC,cAAc;IAAAkE,QAAA,gBACbnE,OAAA,CAACI,UAAU;MAAA+D,QAAA,GAAC,wBAAY,EAACrB,KAAK;IAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,EAE3Cf,QAAQ,CAACnC,MAAM,KAAK,CAAC,gBACpBrB,OAAA,CAACiB,UAAU;MAAAkD,QAAA,EAAC;IAEZ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,gBAEbvE,OAAA,CAACO,SAAS;MAAA4D,QAAA,EACPX,QAAQ,CAAC9B,GAAG,CAACwB,OAAO,iBACnBlD,OAAA,CAACS,WAAW;QAAA0D,QAAA,gBACVnE,OAAA,CAACW,aAAa;UAAAwD,QAAA,GAAC,WAAS,EAACjB,OAAO,CAACc,MAAM;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eACxDvE,OAAA,CAACa,WAAW;UAAAsD,QAAA,EACTjB,OAAO,CAACgB,KAAK,IAAI;QAAG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACdvE,OAAA,CAACe,UAAU;UAAAoD,QAAA,EACRjB,OAAO,CAAC9B,KAAK,CACXG,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,CAAC,CAC5BC,GAAG,CAACF,IAAI,IAAI,GAAGA,IAAI,CAACI,IAAI,GAAGJ,IAAI,CAACgD,MAAM,EAAE,CAAC,CACzC/B,IAAI,CAAC,IAAI,CAAC,IAAI;QAAQ;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA,GAVGrB,OAAO,CAACc,MAAM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWnB,CACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CACZ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAErB,CAAC;AAACE,GAAA,GAjDW5B,SAAmC;AAAA,IAAA1C,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAuD,GAAA;AAAAC,YAAA,CAAAvE,EAAA;AAAAuE,YAAA,CAAApE,GAAA;AAAAoE,YAAA,CAAAlE,GAAA;AAAAkE,YAAA,CAAAhE,GAAA;AAAAgE,YAAA,CAAA9D,GAAA;AAAA8D,YAAA,CAAA5D,GAAA;AAAA4D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}